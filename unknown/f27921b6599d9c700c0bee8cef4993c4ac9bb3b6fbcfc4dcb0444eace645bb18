import { useMutation } from '@tanstack/react-query'

import { getCheckPromocode, userAppleLogin, userFaceBookLogin } from '../utils/apiCalls'

export const useFacebookLoginMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['userFacebookLogin'],
    mutationFn: (data) => userFaceBookLogin(data),
    onSuccess,
    onError
  })
}

export const useAppleLoginMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['userAppleLogin'],
    mutationFn: (data) => userAppleLogin(data),
    onSuccess,
    onError
  })
}


export const useCheckPromocodeMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['getCheckPromocode'],
    mutationFn: (data) => getCheckPromocode(data),
    onSuccess,
    onError
  })
}