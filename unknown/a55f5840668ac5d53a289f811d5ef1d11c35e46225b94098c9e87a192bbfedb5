import React, { useEffect, useState } from 'react';
import {
  Button,
  Grid,
  IconButton,
  InputAdornment,
  OutlinedInput,
  TextField,
  Typography,
  FormControl,
  Box,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import QRCode from 'react-qr-code';
import { useDisabled2FAMutation, useGenerateOtp2FAMutation, useGetProfileMutation, useVerifyOtp2FAMutation } from '../../../../reactQuery';
import { toast } from 'react-hot-toast';
import { twoFactorSchema, twoFactorSchemaForSocial } from '../schema';

export const TwoFactor = ({ user }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [isSocialLogin, setSocialLogin] = useState('0');
  const [authSecret, setAuthSecret] = useState(null);
  const [authUrl, setAuthUrl] = useState(null);

  const { mutate: generateOtp2FA } = useGenerateOtp2FAMutation({
    onSuccess: (data) => {
      setAuthSecret(data?.data?.result?.authSecret);
      setAuthUrl(data?.data?.result?.authUrl);
    },
    onError: (error) => {
      console.error('Error generating OTP:', error);
    },
  });

  const { mutate: verifyOtp2FA } = useVerifyOtp2FAMutation({
    onSuccess: (data) => {
      setIs2FAEnabled(data.authEnable);
      toast.success(`${data?.data?.message}`)
      getProfileMutation();
      reset();
    },
    onError: (error) => {
      console.error('Error verifying OTP:', error?.response?.data?.errors[0]?.description);
      // toast.error(`${error?.response?.data?.errors[0]?.description}`)
    },
  });

  const { mutate: disable2FA } = useDisabled2FAMutation({
    onSuccess: (data) => {
      setIs2FAEnabled(false);
      setAuthSecret(null);
      setAuthUrl(null);
      toast.success(`${data?.data?.message}`)
      getProfileMutation();
    },
    onError: (error) => {
      console.error('Error disabling 2FA:', error);
      // toast.error(`${error?.response?.data?.errors[0]?.description}`)
    },
  });

  const { mutate: getProfileMutation, isLoading } = useGetProfileMutation({
    onSuccess: (res) => {
      setIs2FAEnabled(res?.data?.data?.authEnable);
      setAuthSecret(res?.data?.data?.authSecret);
      setAuthUrl(res?.data?.data?.authUrl);
      setSocialLogin(res?.data?.data?.signInMethod)
    },
    onError: (error) => {
      console.log('Error getting profile:', error);
    },
  });

  useEffect(() => {
    setIs2FAEnabled(user?.authEnable);
    setAuthSecret(user?.authSecret);
    setAuthUrl(user?.authUrl);
    setSocialLogin(user?.signInMethod)
  }, [user]);

  const { register, handleSubmit, setValue, formState: { errors }, reset } = useForm({
    resolver: yupResolver(isSocialLogin === '0' ? twoFactorSchema : twoFactorSchemaForSocial),
    defaultValues: {
      code: '',
      password: '',
    },
  });


  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const onSubmit = (data) => {
    verifyOtp2FA({
      token: (data.code),
      password: btoa(data.password),
    });
  };

  const handleEnable2FA = () => {
    generateOtp2FA();
    // setIs2FAEnabled(true);
  };

  const handleDisable2FA = () => {
    disable2FA();
  };

  const handleCopyCode = (authSecret) => {
    const isCopySuccessfull = copyToClipBoard(
      `${authSecret}`
    )
    if (!isCopySuccessfull) {
      return toast.error('Failed to copy code!');
    }
    toast.success('2FA copied!');
    return null
  }

  const copyToClipBoard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (err) {
      console.error('Failed to copy: ', err);
      return false;
    }
  };

  return (
    <Box className='genral-tab'>
      <Grid className='setting-card-header'>
        <Typography variant='h4'>Two Factor Authentication</Typography>
      </Grid>
      <Grid className='setting-card-details'>
        {!isLoading ? (is2FAEnabled ? <>
          <Typography>You have successfully enabled two-factor authentication.</Typography>
          <Button type='button' className='btn btn-primary' onClick={handleDisable2FA}>
            Disable 2FA
          </Button>
        </> :
          <>
            <Typography>To keep your account extra secure, leave two-factor authentication enabled.</Typography>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid container spacing={1}>
                <Grid item xs={12} lg={6}>
                  <Grid className='input-wrap copy-input'>
                    <TextField
                      variant='outlined'
                      value={authSecret || ''}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position='end'>
                            <IconButton onClick={() => handleCopyCode(authSecret)}>
                              <ContentCopyIcon />
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      fullWidth
                      // disabled
                      placeholder='Copy this code to your authenticator app'
                    />
                  </Grid>
                  <Grid className='input-wrap copy-input '>
                    {authUrl ? (
                      <Grid className='scanner'>
                        <Typography>Don't let anyone see this!</Typography>
                        <Grid className='scanner-inner' style={{}}>
                          <QRCode
                            size={256}
                            style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                            value={authUrl}
                            viewBox={`0 0 256 256`}
                          />
                        </Grid>
                      </Grid>
                    ) : (
                      <Typography>Click on Enable button to Generate QR code.</Typography>
                    )}
                  </Grid>
                </Grid>

                <Grid item xs={12} lg={6}>
                  <Grid className='input-wrap'>
                    <FormControl variant='outlined' error={!!errors?.code}>
                      <Tooltip title='Scan QR to get 2FA code'>
                        <OutlinedInput
                          type='text'
                          {...register('code')}
                          placeholder='Enter 2FA code'
                          autoComplete="code"
                          error={!!errors?.code}
                        />
                      </Tooltip>
                      {errors?.code && <Typography variant='caption' color='error'>{errors?.code?.message}</Typography>}
                    </FormControl>
                  </Grid>

                  {isSocialLogin === "0" &&
                    <Grid className='input-wrap'>
                      <FormControl variant='outlined' error={!!errors?.password}>
                        <OutlinedInput
                          type={showPassword ? 'text' : 'password'}
                          {...register('password')}
                          placeholder='Enter Password'
                          autoComplete="new-password"
                          endAdornment={
                            <InputAdornment position='end'>
                              <IconButton
                                aria-label='toggle password visibility'
                                onClick={handleClickShowPassword}
                                onMouseDown={handleMouseDownPassword}
                                edge='end'
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          }
                          label=''
                          error={!!errors?.password}
                        />
                        {errors?.password && <Typography variant='caption' color='error'>{errors?.password?.message}</Typography>}
                      </FormControl>
                    </Grid>}
                </Grid>
              </Grid>

              <Grid className='setting-card-footer'>
                {!is2FAEnabled && !authSecret &&
                  <Button type='button' className='btn btn-primary' onClick={handleEnable2FA}>
                    Enable 2FA
                  </Button>}
                <Button type='submit' className='btn btn-primary'>
                  Save
                </Button>
              </Grid>
            </form></>) :
          <Grid style={{ textAlign: 'center' }}>
            <CircularProgress size={24} style={{ marginLeft: 8 }} />
          </Grid>}
      </Grid>
    </Box>
  );
};
