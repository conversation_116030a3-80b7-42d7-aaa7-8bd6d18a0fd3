import * as React from 'react'
import useStyles from './style'
import PropTypes from 'prop-types'
import Tabs from '@mui/material/Tabs'
import Tab from '@mui/material/Tab'
import { Box, Button, Grid, Typography, FormControl, InputLabel, Select, MenuItem } from '@mui/material'
import whitePlay from '../../../components/ui-kit/icons/svg/white-play-button.svg'
import TabAllGames from '../../../components/ui-kit/icons/svg/tab-all-games.svg'
import CasinoCard from '../../../components/ui-kit/icons/utils/casinoGames.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import { CasinoQuery } from '../../../reactQuery'
import Signup from '../../../components/Modal/Signup'
import { useSubCategoryOnLoadStore } from '../../../store/store'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import shieldWhite from '../../../components/ui-kit/icons/webp/shield-white.webp'
import LazyImage from '../../../utils/lazyImage'
/* eslint-disable multiline-ternary */

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  }
}

const ProviderCategorySection = () => {
  const portalStore = usePortalStore()
  const [value, setValue] = React.useState(0)
  const [tabName, setTabName] = React.useState('')
  const [providerData, setProviderData] = React.useState('')
  const [provider, setProvider] = React.useState('')
  const [game, setGame] = React.useState('')
  const { data } = CasinoQuery.getProviderListQuery()
  const state = useSubCategoryOnLoadStore((state) => state)

  React.useEffect(() => {
    setProviderData(data)
  }, [data])

  const handleChange = (event, newValue) => {
    setValue(newValue)
    setTabName(state.subCategories ? state.subCategories[newValue]?.name : '')
  }
  const classes = useStyles()
  const handleProviderChange = (event) => {
    setProvider(event.target.value)
  }
  const handleGameChange = (event) => {
    setValue(state.subCategories?.findIndex((x) => x?.name === event.target.value))
    setGame(event.target.value)
  }
  const handleShowMore = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  return (
    <>
      <section
        className={classes.providerSection}
        id='hot-games'
        aria-labelledby='socialGamesHeading'
        data-tracking='Home.PlaySocialGames.Section'
      >
        <Grid className='provider-content-wrap'>
          <Grid className='inner-heading'>
            <Typography variant='h4'>
              Play and Enjoy Exclusive <span>Social Casino Games</span>
            </Typography>
            <Typography>
              Explore a massive collection of games designed for every type of player! From slots featuring exciting
              themes to thrilling jackpot games with massive rewards, from pragmatic playing to hacksaw—there are social
              casino games for everyone. Try your luck at popular table games like blackjack and roulette, or dive into
              our ever-growing library of fresh, exclusive titles. With weekly updates, you’ll always find something new
              to explore. Don’t forget the amazing offers that can boost your gameplay—making it even more fun to play
              games and win big.
            </Typography>
          </Grid>
          <nav className='theme-tabs' role='tablist'>
            <Tabs
              value={value}
              onChange={handleChange}
              aria-label='basic tabs example'
              data-tracking='Home.PlaySocialGames.HotGames.Tab'
            >
              {state.subCategories &&
                state.subCategories
                  ?.slice()
                  .sort((a, b) => (a.name === 'Hot Games' ? -1 : b.name === 'Hot Games' ? 1 : 0)) // Reorder 'Hot Games' to the first position
                  .map((data, idx) => {
                    return (
                      <Tab
                        key={idx}
                        label={data?.name}
                        {...a11yProps(idx)}
                        icon={
                          // <img src={data?.imageUrl?.thumbnail || TabAllGames} alt='All Games' />
                          <LazyImage src={data?.imageUrl?.thumbnail || TabAllGames} alt='All Games' />
                        }
                      />
                    )
                  })}
            </Tabs>
          </nav>
          <CustomTabPanel value={value} index={value} sx={{ Padding: '0' }}>
            <Grid className='theme-tab-content-wrap'>
              <Grid className='inner-heading'>
                {state.subCategories?.length > 0 ? (
                  <Grid className='theme-select game-category-mob'>
                    <FormControl variant='outlined' style={{ Minwidth: 180 }}>
                      <InputLabel id='provider-select-label'>Games</InputLabel>
                      <Select
                        labelId='provider-select-label'
                        id='provider-select'
                        value={game}
                        onChange={handleGameChange}
                        label='Provider Name here'
                        MenuProps={{
                          PaperProps: {
                            style: {
                              backgroundColor: '#000000', // black background for menu
                              color: '#ffffff' // white text color for menu
                            }
                          },
                          disableScrollLock: true
                        }}
                      >
                        <MenuItem value=''>
                          <em>None</em>
                        </MenuItem>
                        {state.subCategories?.map((data, idx) => {
                          return (
                            <MenuItem key={idx} value={data?.name}>
                              {data?.name}
                            </MenuItem>
                          )
                        })}
                      </Select>
                    </FormControl>
                  </Grid>
                ) : (
                  ''
                )}
                {providerData?.length > 0 ? (
                  <Grid className='theme-select provider-select-wrap'>
                    <FormControl variant='outlined' style={{ Minwidth: 180 }}>
                      <InputLabel id='provider-select-label'>Providers</InputLabel>
                      <Select
                        labelId='provider-select-label'
                        id='provider-select'
                        value={provider}
                        onChange={handleProviderChange}
                        label='Provider Name here'
                        disableScrollLock
                        MenuProps={{
                          disableScrollLock: true,
                          PaperProps: {
                            style: {
                              backgroundColor: '#000000', // black background for menu
                              color: '#ffffff' // white text color for menu
                            }
                          }
                          // disableScrollLock: true,
                        }}
                      >
                        {providerData?.map((data, idx) => {
                          return (
                            <MenuItem key={idx} value={data?.masterCasinoProviderId} className='provider-menu-options'>
                              {/* <img src={data?.thumbnailUrl} /> */}
                              <LazyImage src={data?.thumbnailUrl} alt={`master-casino-provider-${idx}`} />
                            </MenuItem>
                          )
                        })}
                      </Select>
                    </FormControl>
                  </Grid>
                ) : (
                  ''
                )}
              </Grid>
            </Grid>
            <Grid className='games-grid-wrap'>
              {state.subCategories && state.subCategories[value]?.subCategoryGames
                ? state.subCategories[value].subCategoryGames.map((data, i) => (
                    <figure
                      className='casino-card'
                      onClick={handleShowMore}
                      data-tracking='Home.PlaySocialGames.FistOfDestruction.Card'
                      key={data?.id || i} // Use unique key if available
                    >
                      {/* <img src={data?.imageUrl || CasinoCard} alt='Casino Card' /> */}
                      <LazyImage src={data?.imageUrl || CasinoCard} alt='Casino Card' />
                      <Grid className='overlay-box'>
                        {/* <img src={whitePlay} alt='Play' /> */}
                        <LazyImage src={whitePlay} alt='Play' />
                        <Typography>{data?.name}</Typography>
                      </Grid>
                    </figure>
                  ))
                : null}
            </Grid>
          </CustomTabPanel>
        </Grid>

        <Grid className='btn-wrap'>
          <Button
            className='btn btn-secondary'
            onClick={handleShowMore}
            data-tracking='Home.PlaySocialGames.ViewMoreGames.Btn'
          >
            <ArrowCircleRightOutlinedIcon />
            View More Games
          </Button>
        </Grid>
        <Grid className='banner-disclaimer'>
          {/* <img src={shieldWhite} alt='Secure' /> */}
          <LazyImage src={shieldWhite} alt='Secure' />
          <Typography>Trusted by countless players across the US</Typography>
        </Grid>
      </section>
    </>
  )
}

export default ProviderCategorySection
