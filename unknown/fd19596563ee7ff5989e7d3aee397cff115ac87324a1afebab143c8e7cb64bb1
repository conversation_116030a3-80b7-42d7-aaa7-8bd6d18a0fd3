const DOCUMENT_EVENTS = [
  'mousemove',
  'mousedown',
  'click',
  'touchmove',
  'touchstart',
  'touchend',
  'keydown',
  'keypress'
]

export class IdleTimer {
  constructor (onIdleTimeout, timeout, socket, socketEvents = []) {
    this.onIdleTimeout = onIdleTimeout
    this.timeout = timeout
    this.socket = socket
    this.socketEvents = socketEvents
    this.timer = null
    this.active = false
    this.iframeListeners = []

    this.resetTimer = this.resetTimer.bind(this)
    this.handleIframeMessage = this.handleIframeMessage.bind(this)
    this.handleWindowFocusBlur = this.resetTimer.bind(this)
  }

  activate () {
    if (!this.active) {
      this.bindEvents()
      this.listenForIframeMessages() // For controlled iframes
      this.detectIframeFocus() // Detects user switching to iframe
      this.bindIframeHoverEvents() // Mouse over iframe detection
      this.bindSocketEvents()
      this.startTimer()
      this.active = true
    }
  }

  deactivate () {
    if (this.active) {
      this.unbindEvents()
      window.removeEventListener('message', this.resetTimer)
      window.removeEventListener('blur', this.resetTimer)
      window.removeEventListener('focus', this.resetTimer)
      this.unbindSocketEvents()
      clearTimeout(this.timer)
      this.active = false
    }
  }

  resetTimer () {
    clearTimeout(this.timer)
    this.startTimer()
  }

  startTimer () {
    this.timer = setTimeout(this.onIdleTimeout, this.timeout)
  }

  bindEvents () {
    window.addEventListener('scroll', this.resetTimer, { capture: true, passive: true })
    window.addEventListener('load', this.resetTimer, { once: true })

    DOCUMENT_EVENTS.forEach((eventType) => {
      document.addEventListener(eventType, this.resetTimer)
    })

    this.bindIframeEvents()
  }

  unbindEvents () {
    window.removeEventListener('scroll', this.resetTimer, { capture: true })
    window.removeEventListener('load', this.resetTimer)

    DOCUMENT_EVENTS.forEach((eventType) => {
      document.removeEventListener(eventType, this.resetTimer)
    })

    this.unbindIframeEvents()
  }

  handleIframeMessage (event) {
    if (event.data && event.data.event === 'userActive') {
      this.resetTimer()
    }
  }

  bindIframeEvents () {
    const iframes = document.querySelectorAll('iframe')

    iframes.forEach((iframe) => {
      try {
        const iframeDoc = iframe.contentWindow.document
        DOCUMENT_EVENTS.forEach((eventType) => {
          iframeDoc.addEventListener(eventType, this.resetTimer)
          this.iframeListeners.push({ iframe, eventType })
        })

        iframe.addEventListener('mouseenter', this.resetTimer)
        iframe.addEventListener('mouseleave', this.resetTimer)
      } catch (error) {
        console.warn('Cannot bind events to cross-origin iframe:', error)
      }
    })
  }

  unbindIframeEvents () {
    this.iframeListeners.forEach(({ iframe, eventType }) => {
      try {
        const iframeDoc = iframe.contentWindow.document
        iframeDoc.removeEventListener(eventType, this.resetTimer)
      } catch (error) {
        console.warn('Cannot unbind events from cross-origin iframe:', error)
      }
    })

    document.querySelectorAll('iframe').forEach((iframe) => {
      iframe.removeEventListener('mouseenter', this.resetTimer)
      iframe.removeEventListener('mouseleave', this.resetTimer)
    })

    this.iframeListeners = []
  }

  // ✅ Bind socket events
  bindSocketEvents () {
    if (!this.socket || !this.socketEvents.length) return

    this.socketEvents.forEach((event) => {
      this.socket.on(event, this.resetTimer)
    })
  }

  // ✅ Unbind socket events to avoid memory leaks
  unbindSocketEvents () {
    if (!this.socket || !this.socketEvents.length) return

    this.socketEvents.forEach((event) => {
      this.socket.off(event, this.resetTimer)
    })
  }

  // ✅ Listen for activity inside controlled cross-origin iframes using postMessage
  listenForIframeMessages () {
    window.addEventListener('message', (event) => {
      if (event.data && event.data.event === 'userActive') {
        this.resetTimer()
      }
    })
  }

  // ✅ Detect when user switches focus between iframe and main page
  detectIframeFocus () {
    window.addEventListener('blur', this.resetTimer) // User clicked inside iframe
    window.addEventListener('focus', this.resetTimer) // User returned to main page
  }

  // ✅ Detect mouse entering or leaving an iframe
  bindIframeHoverEvents () {
    const iframes = document.querySelectorAll('iframe')

    iframes.forEach((iframe) => {
      iframe.addEventListener('mouseenter', this.resetTimer) // Mouse enters iframe
      iframe.addEventListener('mouseleave', this.resetTimer) // Mouse leaves iframe
    })
  }

  unbindIframeHoverEvents () {
    const iframes = document.querySelectorAll('iframe')

    iframes.forEach((iframe) => {
      iframe.removeEventListener('mouseenter', this.resetTimer)
      iframe.removeEventListener('mouseleave', this.resetTimer)
    })
  }
}
