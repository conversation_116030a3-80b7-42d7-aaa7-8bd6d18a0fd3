import { makeStyles } from '@mui/styles'

import { GlowIcon } from '../../components/ui-kit/icons/utils/index'
import { tierBanner, VipBanner } from '../../components/ui-kit/icons/webp'
import { LobbyRight, Container } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    '& .tier-banner': {
      backgroundRepeat: 'no-repeat',
      minHeight: theme.spacing(22),
      backgroundImage: `url(${tierBanner})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      width: '100%',
      position: 'relative',
      borderRadius: theme.spacing(1.875),
      padding: theme.spacing(1, 2),
      marginBottom: theme.spacing(3.125),
      [theme.breakpoints.down('lg')]: {
        minHeight: theme.spacing(10),
        padding: theme.spacing(1)
      },
      [theme.breakpoints.down('md')]: {
        minHeight: theme.spacing(5),
        borderRadius: theme.spacing(0.625)
      },
      '& img': {
        width: '100%',
        borderRadius: theme.spacing(1.25)
      },
      '& .tier-banner-content': {
        '& .MuiTypography-body1': {
          fontSize: theme.spacing(4),
          lineHeight: theme.spacing(3),
          fontWeight: theme.typography.fontWeightExtraBold,
          [theme.breakpoints.down(1400)]: {
            fontSize: theme.spacing(3),
            lineHeight: theme.spacing(3)
          },
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(2),
            lineHeight: theme.spacing(2)
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1),
            lineHeight: theme.spacing(1)
          }
        },
        '& .MuiTypography-h4': {
          fontSize: theme.spacing(4),
          color: theme.colors.YellowishOrange,
          fontWeight: theme.typography.fontWeightExtraBold,
          [theme.breakpoints.down(1400)]: {
            fontSize: theme.spacing(3),
            lineHeight: theme.spacing(3)
          },
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(2),
            lineHeight: theme.spacing(2)
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1),
            lineHeight: theme.spacing(1)
          }
        }
      },
      '& .tier-banner-icon': {
        textAlign: 'center',
        '& img': {
          width: theme.spacing(18),
          [theme.breakpoints.down('sm')]: {
            width: theme.spacing(8.75)
          }
        }
      },
      '& .MuiGrid-container': {
        alignItems: 'center'
      }
    },
    '& .vip-banner-wrap': {
      backgroundRepeat: 'no-repeat',
      minHeight: theme.spacing(32.5625),
      backgroundImage: `url(${VipBanner})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      width: '100%',
      position: 'relative',
      padding: theme.spacing(4),
      margin: theme.spacing(2, 0),
      display: 'flex',
      alignItems: 'flex-end',
      [theme.breakpoints.down('lg')]: {
        minHeight: theme.spacing(17.5),
        padding: theme.spacing(2)
      },
      [theme.breakpoints.down('md')]: {
        minHeight: theme.spacing(17.5),
        padding: theme.spacing(2)
      },
      [theme.breakpoints.down('sm')]: {
        minHeight: theme.spacing(7.5),
        padding: theme.spacing(1)
      },
      '& .vip-banner-content': {
        '& button': {
          minWidth: theme.spacing(13.5),
          [theme.breakpoints.down('sm')]: {
            minWidth: 'auto',
            fontSize: theme.spacing(0.75)
          }
        }
      }
    },
    '& .tier-grid-section-wrap': {
      background: theme.colors.tierGradient,
      borderRadius: theme.spacing(2.5),
      padding: theme.spacing(0.313),
      overflow: 'hidden',
      [theme.breakpoints.down(991)]: {
        background: 'transparent',
        padding: '0',
        borderRadius: 0
      },
      [theme.breakpoints.down('md')]: {
        background: 'transparent',
        padding: '0',
        borderRadius: 0
      },
      '& .tier-grid-section': {
        background: theme.colors.tierGridBg,
        borderRadius: theme.spacing(2.5),
        padding: theme.spacing(2, 4.625),
        border: `1px solid ${theme.colors.packageInnerCard}`,
        minHeight: theme.spacing(30.625),
        position: 'relative',
        [theme.breakpoints.down(1400)]: {
          padding: theme.spacing(1)
        },

        [theme.breakpoints.down(991)]: {
          padding: theme.spacing(1, 0),
          background: 'transparent',
          border: 'none',
          maxWidth: theme.spacing(37.5),
          margin: '0 auto'
        },

        [theme.breakpoints.down('sm')]: {
          maxWidth: theme.spacing(20)
        },
        '& .tier-grid ': {
          display: 'grid',
          gridTemplateColumns: 'repeat(6, 1fr)',
          gap: theme.spacing(2),
          [theme.breakpoints.down(1460)]: {
            transform: 'scale(0.8) translate(-10px)'
          },
          [theme.breakpoints.down(1300)]: {
            transform: 'scale(0.75) translate(-80px)'
          },
          [theme.breakpoints.down(1199)]: {
            transform: 'scale(0.85) translate(-50%, -50%)',
            position: 'absolute',
            left: '44%',
            top: '45%'
          },
          [theme.breakpoints.down(991)]: {
            gridTemplateColumns: 'repeat(1, 1fr)',
            gap: theme.spacing(4),
            transform: 'none',
            position: 'unset'
          },
          textAlign: 'center',
          '& .tier-grid-details': {
            background: theme.colors.packageDetailsBg,
            borderRadius: theme.spacing(0.5625),
            border: `1px solid ${theme.colors.packageInnerCard}`,
            padding: theme.spacing(0.625, 0.625),
            maxWidth: theme.spacing(11.625),
            position: 'relative',
            zIndex: 2,
            margin: '0 auto',
            [theme.breakpoints.down(991)]: {
              padding: theme.spacing(0.625, 0.313),
              margin: 'unset'
            },

            [theme.breakpoints.down(991)]: {
              padding: theme.spacing(0.625, 0.313),
              margin: 'unset'
            },

            '& .tier-coins-wrap': {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              '& .tier-coins': {
                display: 'flex',
                gap: theme.spacing(0.313),
                fontSize: theme.spacing(0.75),
                fontWeight: theme.typography.fontWeightMedium,
                alignItems: 'center',
                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.75)
                },
                [theme.breakpoints.down('sm')]: {
                  gap: theme.spacing(0.313),
                  fontSize: theme.spacing(0.5)
                },
                '& img': {
                  width: theme.spacing(1),
                  height: theme.spacing(1)
                }
              }
            },
            '& button': {
              marginTop: theme.spacing(0.8125),
              minWidth: theme.spacing(7.625),
              minHeight: theme.spacing(2.125),
              fontSize: theme.spacing(0.875),
              lineHeight: theme.spacing(1),
              [theme.breakpoints.down('sm')]: {
                fontSize: theme.spacing(0.625),
                lineHeight: theme.spacing(0.4375),
                minHeight: theme.spacing(1.5625),
                minWidth: theme.spacing(5.9375),
                marginTop: theme.spacing(0.313)
              },
              [theme.breakpoints.down('sm')]: {
                fontSize: theme.spacing(0.625),
                lineHeight: theme.spacing(0.4375),
                minHeight: theme.spacing(1.5625),
                minWidth: theme.spacing(5.9375),
                marginTop: theme.spacing(0.313)
              }
            }
          },
          '& .tier-position-icon': {
            marginBottom: theme.spacing(2.375),
            position: 'relative',
            zIndex: 3,
            [theme.breakpoints.down('sm')]: {
              marginBottom: 0,
              width: '100%',
              textAlign: 'left',
              paddingLeft: theme.spacing(0),
              maxWidth: theme.spacing(4.5)
            },
            '& img': {
              width: theme.spacing(8),
              margin: '0 auto',
              [theme.breakpoints.down('lg')]: {
                width: theme.spacing(7)
              },
              [theme.breakpoints.down('sm')]: {
                width: theme.spacing(4.5)
              }
            },
            '& .tier-name': {
              position: 'absolute',
              fontSize: theme.spacing(1),
              fontWeight: theme.typography.fontWeightExtraBold,
              transform: 'translate(-50%, -50%)',
              top: '50%',
              left: '50%',
              textTransform: 'capitalize',
              [theme.breakpoints.down('lg')]: {
                fontSize: theme.spacing(0.875)
              },
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(0.75)
              },
              [theme.breakpoints.down('sm')]: {
                fontSize: theme.spacing(0.625)
                // left: "24%",
              },
              [theme.breakpoints.down(370)]: {
                fontSize: theme.spacing(0.625)
                // left: "28%",
              }
            }
          },
          '& .tier-grid-card': {
            position: 'relative',
            [theme.breakpoints.down(991)]: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            },
            '&:nth-child(2), &:nth-child(4), &:nth-child(6)': {
              marginTop: theme.spacing(8.75),
              [theme.breakpoints.down(991)]: {
                flexDirection: 'row-reverse',
                margin: '0'
              },
              '& .tier-progress-wrap': {
                '& .MuiLinearProgress-root': {
                  top: theme.spacing(2.6),
                  transform: 'rotate(-40deg) translate(146px, -20px)',

                  [theme.breakpoints.down(1400)]: {
                    transform: 'rotate(-36deg) translate(112px)',
                    top: theme.spacing(0.75)
                  },
                  [theme.breakpoints.down(1370)]: {
                    transform: 'rotate(-42deg) translate(112px)',
                    top: theme.spacing(0.75)
                  },

                  [theme.breakpoints.down(1199)]: {
                    transform: 'rotate(-41deg) translate(120px)'
                  },

                  [theme.breakpoints.down(991)]: {
                    transform: 'rotate(-22deg) translate(50px, 16px) rotate(180deg)',
                    top: theme.spacing(7.3)
                  },
                  [theme.breakpoints.down('sm')]: {
                    transform: 'rotate(-32deg) translate(70px, -14px) rotate(180deg)'
                  }
                }
              },
              '& .tier-position-icon': {
                [theme.breakpoints.down(991)]: {
                  textAlign: 'right'
                },

                '& .tier-name': {
                  [theme.breakpoints.down('sm')]: {
                    fontSize: theme.spacing(0.625)
                    // right: "20%",
                    // left: "auto",
                  }
                }
              }
            },
            '& .tier-progress-wrap': {
              position: 'absolute',
              height: '100%',
              width: '100%',
              top: theme.spacing(3),
              '& .MuiLinearProgress-root': {
                background: theme.colors.tierProgressBg,
                height: theme.spacing(1),
                width: '100%',
                border: `1px solid ${theme.colors.tierProgressBorder}`,
                transform: 'rotate(39deg) translate(129px)',
                boxShadow: `2px 2px 6px 2px ${theme.colors.tierShadow}`,
                zIndex: 0,
                [theme.breakpoints.down(1400)]: {
                  transform: 'rotate(36deg) translate(130px)'
                },
                [theme.breakpoints.down(1370)]: {
                  transform: 'rotate(42deg) translate(122px)'
                },
                [theme.breakpoints.down(1199)]: {
                  transform: 'rotate(36deg) translate(110px)'
                },
                [theme.breakpoints.down(991)]: {
                  transform: 'rotate(25deg) translate(61px)',
                  left: theme.spacing(-4.1875),
                  top: theme.spacing(5.1875),
                  height: theme.spacing(1.2)
                },
                [theme.breakpoints.down('sm')]: {
                  transform: 'rotate(34deg) translate(68px)',
                  left: theme.spacing(-3.1875),
                  top: theme.spacing(1.1875),
                  height: theme.spacing(1.2)
                }
              },
              '& .MuiLinearProgress-barColorPrimary': {
                backgroundColor: theme.colors.tierProgressClr
              }
            }
          },
          '&.active-position': {
            '&  .tier-position-icon': {
              position: 'relative',
              [theme.breakpoints.down('md')]: {
                textAlign: 'left'
              },
              '&:before': {
                position: 'absolute',
                left: '50%',
                top: '50%',
                content: "''",
                background: `url(${GlowIcon})`,
                filter: 'drop-shadow(0px 16px 10px #04973E)',
                backgroundSize: 'cover',
                backgroundRepeat: 'no-repeat',
                height: theme.spacing(9.75),
                width: '100%',
                transform: 'translate(-50%, -50%) scale(1.5)',

                [theme.breakpoints.down('lg')]: {
                  height: theme.spacing(8),
                  width: theme.spacing(8),
                  left: theme.spacing(0.5)
                },
                [theme.breakpoints.down(1400)]: {
                  left: '50%',
                  top: '50%'
                },
                [theme.breakpoints.down('md')]: {
                  height: theme.spacing(9),
                  width: theme.spacing(9)
                },
                [theme.breakpoints.down('sm')]: {
                  height: theme.spacing(6),
                  width: theme.spacing(6)
                }
              }
            }
          }
        }
      }
    },
    '& .weekly-bonus-section': {
      margin: theme.spacing(4, 0),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      [theme.breakpoints.down('sm')]: {
        flexDirection: 'column',
        gap: theme.spacing(1)
      },
      '& .weekly-bonus-left': {
        [theme.breakpoints.down('sm')]: {
          width: '100%'
        },
        '& button': {
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing(1),
          '& .tier-coins': {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(0.313),
            '& img': {
              [theme.breakpoints.down('sm')]: {
                width: theme.spacing(1.2)
              }
            }
          }
        }
      },
      '& p': {
        color: theme.colors.textWhite,
        fontWeight: theme.typography.fontWeightBold,
        fontSize: theme.spacing(1.125),
        marginBottom: theme.spacing(1),
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75),
          marginBottom: theme.spacing(0.313)
        }
      },
      '& button': {
        fontSize: theme.spacing(1.25),
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        }
      }
    },
    '& .tier-table-wrap': {
      background: theme.colors.tierTableBg,
      borderRadius: theme.spacing(0.625),
      padding: theme.spacing(1.25),
      '& .table-filter': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(0.375)
      },
      '& .MuiTable-root': {
        '& .MuiTableCell-head': {
          border: 'none',
          padding: theme.spacing(0.625, 1),
          fontSize: theme.spacing(1),
          color: theme.colors.textWhite,
          fontWeight: theme.fontWeightMedium
        },
        '& .MuiTableCell-root': {
          border: 'none',
          padding: theme.spacing(0.625, 1),
          fontSize: theme.spacing(1),
          color: theme.colors.textWhite,
          fontWeight: '400',
          [theme.breakpoints.down('md')]: {
            minWidth: theme.spacing(6.25)
          }
        },
        '& .check-icon': {
          color: theme.colors.tableCheck,
          '& svg': {
            fontSize: theme.spacing(1.2)
          }
        },
        '& .MuiTableBody-root': {
          '& .MuiTableRow-root': {
            '&:nth-child(odd)': {
              // boxShadow: theme.shadows[19],
              boxShadow: '0 0 2px  inset #293937',
              // border: `1px solid ${theme.colors.inputBorder}`,
              borderRadius: theme.spacing(0.625)
            }
          }
        }
      },
      '& .MuiTableRow-head': {
        '& .MuiTableCell-root': {
          '&.tier-table-card': {
            background: theme.colors.coinBundle,
            borderRadius: theme.spacing(0.625),
            fontSize: theme.spacing(1.125),
            color: theme.colors.YellowishOrange,
            padding: theme.spacing(1),
            textAlign: 'center',
            fontWeight: theme.typography.fontWeightBold
          }
        }
      }
    },
    '& .tier-grid-wrapper': {
      display: 'none'
    }
  },
  wrapper: {
    ...Container(theme)
  }
}))
