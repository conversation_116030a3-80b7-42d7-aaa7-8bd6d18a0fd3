import { makeStyles } from '@mui/styles'

import { MaintenanceBg } from '../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
    errorPageWrap: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        width: "100%",
        [theme.breakpoints.down('sm')]: {
            padding: theme.spacing(1),
        },
        "& .error-content-wrap": {
            backgroundImage: `url(${MaintenanceBg})`,
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
            padding: theme.spacing(2),
            textAlign: "center",
            minWidth: theme.spacing(32.5625),
            borderRadius: theme.spacing(0.625),
            [theme.breakpoints.down('sm')]: {
                minWidth: "100%",
            },
            "& h1": {
                marginBottom: theme.spacing(.9375),
                color: theme.colors.errorTextYellow,
                fontSize: theme.spacing(1.625),
                fontWeight: theme.typography.fontWeightExtraBold,
                lineHeight: theme.spacing(1.5625),

            },
            "& p": {
                fontSize: theme.spacing(.9375),
                fontWeight: theme.typography.fontWeightBold,
                "&.more-details-cta": {
                    marginTop: theme.spacing(1.125),
                    cursor: "pointer",
                }
            },
            "& .MuiButton-text": {
                fontSize: theme.spacing(1.25),
                fontWeight: theme.typography.fontWeightExtraBold,
                color: theme.colors.greenText,
                textDecorationColor: theme.colors.greenText,
                marginTop: theme.spacing(0.313),
                display: "inline-block",
                textDecoration: "underline",
            },
            "& .error-icon": {
                textAlign: "center",
                margin: theme.spacing(1, 0, 2),
                "& img": {
                    width: theme.spacing(18.75),
                    [theme.breakpoints.down('sm')]: {
                        width: "100%",
                    },
                }
            }
        }
    },
    noDataWrap: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        width: "100%",
        "& .btn-wrap": {
            margin: theme.spacing(1, 0),
        },
        "& .error-content-wrap": {
            backgroundImage: `url(${MaintenanceBg})`,
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
            padding: theme.spacing(2),
            textAlign: "center",
            minWidth: theme.spacing(32.5625),
            borderRadius: theme.spacing(0.625),
            [theme.breakpoints.down('sm')]: {
                minWidth: "100%",
            },
            "& h1": {
                marginBottom: theme.spacing(.9375),
                color: theme.colors.errorTextYellow,
                fontSize: theme.spacing(1.625),
                fontWeight: theme.typography.fontWeightExtraBold,
                lineHeight: theme.spacing(1.5625),

            },
            "& p": {
                fontSize: theme.spacing(.9375),
                fontWeight: theme.typography.fontWeightBold,
                maxWidth: theme.spacing(25),
                margin: "0 auto",
                "&.more-details-cta": {
                    margin: "1rem auto",
                    cursor: "pointer",
                    color: theme.colors.errorTextYellow,
                    fontWeight: theme.typography.fontWeightBold,
                    textAlign: "center"
                }
            },
            "& .steps-wrap": {
                border: `1px solid ${theme.colors.nodataBorder}`,
                borderRadius: theme.spacing(0.625),
                padding: theme.spacing(1),
                "& ol": {
                    display: "flex",
                    alignItems: "flex-start",
                    justifyContent: "center",
                    gap: theme.spacing(1),
                    [theme.breakpoints.down('sm')]: {
                        flexDirection: "column",
                    },
                    "& li": {
                        margin: theme.spacing(0, 1),
                        position: "relative",
                        "&:before": {
                            position: "absolute",
                            right: theme.spacing(-1.125),
                            top: 0,
                            content: "''",
                            height: theme.spacing(2.8125),
                            width: "1px",
                            background: theme.colors.nodataBorder,
                            [theme.breakpoints.down('sm')]: {
                                display: "none"
                            },
                        },
                        "& p": {
                            maxWidth: theme.spacing(9.375),
                            textAlign: "left"
                        },
                        "&:last-child": {
                            "&:before": {
                                display: "none"
                            }
                        }
                    }
                },
                "&.error-wrap": {
                    overflowY: 'auto',
                    maxHeight: '380px',
                    "& ol": {
                        maxWidth: theme.spacing(25),
                        display: "block",
                        alignItems: "flex-start",
                        justifyContent: "center",
                        gap: theme.spacing(1),
                        "& li": {
                            width: "100%",
                            // margin: "0",
                            padding: theme.spacing(0.625),
                            "& p": {
                                maxWidth: "unset"
                            },
                            "&:before": {
                                display: "none"
                            }
                        }
                    }
                }
            },
            "& .error-icon": {
                textAlign: "center",
                margin: theme.spacing(1, 0, 2),
                "& img": {
                    width: theme.spacing(18.75),
                    [theme.breakpoints.down('sm')]: {
                        width: "100%",
                    },
                }
            }
        }
    }
}))

