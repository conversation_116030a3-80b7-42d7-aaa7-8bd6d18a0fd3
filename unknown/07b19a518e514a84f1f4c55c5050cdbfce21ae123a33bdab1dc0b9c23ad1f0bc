import { create } from 'zustand'

const useDynamicGamepageStore = create((set) => ({
  dynamicGamePageData: null, // default value
  allGamePages:null,
  setAllGamePages: (data) => set({ allGamePages: data }),
  clearAllGamePages: () => set({ allGamePages: null }),
  setDynamicGamePageData: (data) => set({ dynamicGamePageData: data }),
  clearDynamicGamePageData: () => set({ dynamicGamePageData: null }),
}))

export default useDynamicGamepageStore
