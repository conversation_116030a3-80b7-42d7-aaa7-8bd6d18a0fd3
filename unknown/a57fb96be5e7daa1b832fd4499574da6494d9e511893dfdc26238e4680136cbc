import { useState } from 'react'

export const usePaysafePayment = () => {
  const [packageData, setPackageData] = useState(null)
  const [isPaymentScreenLoading, setIsPaymentScreenLoading] = useState(false)
  const [depositInitiated, setDepositInitiated] = useState(false)
  const [depositInitFailed, setDepositInitFailed] = useState({})
  const [demoInstance, setDemoInstance] = useState(null)

  return {
    packageData,
    setPackageData,
    isPaymentScreenLoading,
    setIsPaymentScreenLoading,
    depositInitiated,
    setDepositInitiated,
    depositInitFailed,
    setDepositInitFailed,
    demoInstance,
    setDemoInstance
  }
}

export default usePaysafePayment
