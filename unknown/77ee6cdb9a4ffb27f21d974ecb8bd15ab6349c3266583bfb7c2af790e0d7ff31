import makeRequest from "../network/apis"
import { METHODS } from "../network/constants"

const getRequest = (url, params) => makeRequest(url, METHODS.get, {}, params)

const postRequest = (url, data ,headers = null) => makeRequest(url, METHODS.post, data, {}, headers)

const putRequest = (url, data, headers = null) => makeRequest(url, METHODS.put, data, {}, headers)

const patchRequest = (url, data, headers = null) => makeRequest(url, METHODS.patch, data, {}, headers)

const deleteRequest = (url, data) => makeRequest(url, METHODS.delete, data)

export { getRequest, postRequest, putRequest, deleteRequest,patchRequest }
