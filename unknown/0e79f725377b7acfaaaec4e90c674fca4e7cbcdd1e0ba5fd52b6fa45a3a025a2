
import { useState } from 'react'
import { useIdleTimer } from 'react-idle-timer'
export default function useIdle (props) {
  const {
    onIdle,
    idleTime
  } = props
  const [isIdle, setIsIdle] = useState()
  const handleOnIdle = () => {
    setIsIdle(true)
    onIdle()
  }
  const idleTimer = useIdleTimer({
    timeout: idleTime,
    onIdle: handleOnIdle,
    debounce: 500
  })
  return {
    idleTimer,
    isIdle
  }
}
