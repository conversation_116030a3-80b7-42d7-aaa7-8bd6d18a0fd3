import Signup from '../../../components/Modal/Signup'
import shieldBlack from '../../../components/ui-kit/icons/webp/shield-black.webp'
import playFreeMob from '../../../components/ui-kit/icons/webp/Play-Your-Way-Its-Always-Free.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import useStyles from './style'
import { Button, Grid, Typography } from '@mui/material'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import LazyImage from '../../../utils/lazyImage'

const NewGamesWeek = () => {
  const classes = useStyles()
  const portalStore = usePortalStore()
  const handlePayForFree = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  return (
    <section className={classes.newGamesWrap}>
      <Grid className='inner-container'>
        <Grid className='free-content-wrap'>
          <Grid container spacing={1}>
            <Grid item xs={12} md={6} lg={6}>
              <figure className='mob-img-wrap'>
                {/* <img src={playFreeMob} alt='MobIcon' /> */}
                <LazyImage src={playFreeMob} alt='MobIcon' />
              </figure>
            </Grid>
            <Grid item xs={12} md={6} lg={6}>
              <Grid className='inner-heading'>
                <Typography variant='h3'>Play Your Way, It's Always Free</Typography>
                <Typography>Enjoy unlimited fun at The Money Factory without ever spending a dime.</Typography>
              </Grid>
              <Grid className='free-listing'>
                <ul>
                  <li>
                    <strong>Claim your free</strong> coins every single day.
                  </li>
                  <li>
                    <strong>Enter exciting contests</strong> and giveaways for more chances to win.
                  </li>
                  <li>
                    <strong>Play all of your</strong> favorite games for free, as long as you like.
                  </li>
                </ul>
                <Grid className='btn-wrap'>
                  <Button className='btn btn-primary' onClick={handlePayForFree}>
                    <ArrowCircleRightOutlinedIcon />
                    Play For Free Now
                  </Button>
                </Grid>
                <Grid className='banner-disclaimer'>
                  {/* <img src={shieldBlack} alt='Secure' /> */}
                  <LazyImage src={shieldBlack} alt='Secure' />
                  <Typography>Trusted by countless players across the US</Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </section>
  )
}

export default NewGamesWeek
