import React, { useEffect, useState } from 'react'
import gcCoinImg from '../../../src/assets/gc-coin-img.png'
import YellowButton from '../../components/Buttons/YellowButton'

function calculateRemainingTime() {
  const now = new Date().getTime()
  const targetTime = new Date('2023-10-10T17:00:00Z') // Original Time
  const timeDifference = targetTime - now

  if (timeDifference <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 }
  }

  const seconds = Math.floor((timeDifference / 1000) % 60)
  const minutes = Math.floor((timeDifference / 1000 / 60) % 60)
  const hours = Math.floor((timeDifference / 1000 / 3600) % 24)
  const days = Math.floor(timeDifference / 1000 / 3600 / 24)

  return { days, hours, minutes, seconds }
}

export default function LaunchTimer() {
  const [showLaunch, setShowLaunch] = useState(false)
  const [remainingTime, setRemainingTime] = useState(calculateRemainingTime())

  useEffect(() => {
    const intervalId = setInterval(() => {
      setRemainingTime(calculateRemainingTime())
    }, 1000)

    return () => clearInterval(intervalId)
  }, [])

  useEffect(() => {
    if (remainingTime?.hours === 0 && remainingTime?.minutes === 0 && remainingTime?.seconds === 0) {
      setShowLaunch(true)
    }
  }, [remainingTime])

  return (
    <div className='comming-soon-body'>
      <div className='main-section'>
        <div className='gold-logo'></div>
        <div className='comming-soon-heading'>
          <h1>
            <span className='were-text'>We're</span> <span className='text-highlight'>Launching </span> ON{' '}
            <span className='text-highlight'>10</span>/<span className='text-highlight'>10</span>/23 at{' '}
            <span className='text-highlight'>10</span>AM!!
          </h1>
        </div>
        <div className='countdown  disable-box'>
          <div className={`countdown-box  ${showLaunch && 'disable-box'}`}>
            <span className='count-day'>{remainingTime?.days}</span> <span className='common-count'>Days</span>
          </div>
          <div className={`countdown-box  ${showLaunch && 'disable-box'}`}>
            <span className='count-day'>{remainingTime?.hours}</span> <span className='common-count'>Hours</span>
          </div>
          <div className={`countdown-box  ${showLaunch && 'disable-box'}`}>
            <span className='count-day'>{remainingTime?.minutes}</span> <span className='common-count'>Minutes</span>
          </div>
          <div className={`countdown-box  ${showLaunch && 'disable-box'}`}>
            <span className='count-day'>{remainingTime?.seconds}</span> <span className='common-count'>Seconds</span>
          </div>
        </div>
        {showLaunch && (
          <div className='launch-btn'>
            <YellowButton onClick={() => location.reload()}>Launch</YellowButton>
          </div>
        )}
      </div>
      <div className='bg-coin'>
        <img className='gc-coin-first' src={gcCoinImg} />
        <img className='gc-coin-second' src={gcCoinImg} />
        <img className='gc-coin-third' src={gcCoinImg} />
        <img className='gc-coin-four' src={gcCoinImg} />
        <img className='gc-coin-five' src={gcCoinImg} />

        <img className='sc-coin-first' src={gcCoinImg} />
        <img className='sc-coin-second' src={gcCoinImg} />
        <img className='sc-coin-third' src={gcCoinImg} />
        <img className='sc-coin-four' src={gcCoinImg} />
      </div>
    </div>
  )
}
