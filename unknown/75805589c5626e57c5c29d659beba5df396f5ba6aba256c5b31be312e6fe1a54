import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import {
  getFavori<PERSON>,
  getLive<PERSON><PERSON>er,
  getProvider,
  getRecentGamesList,
  getSubcategory,
  toogleFav
} from '../utils/apiCalls'

const useGamesListQuery = ({ successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.CASINO_GET_GAMES],
    queryFn: (data) => {
      return getSubcategory(data)
    },
    onSuccess: (res) => {
      successToggler && successToggler(res)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false
  })
}

const useGamesListMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: [KeyTypes.CASINO_GET_GAMES],
    mutationFn: (data) => getSubcategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries(KeyTypes.CASINO_GET_GAMES)
    }
  })
}

const getSubcategoryListQuery = ({ enabled }) => {
  return useQuery({
    queryKey: [KeyTypes.CASINO_GET_SUBCATEGORY],
    queryFn: (data) => {
      return getSubcategory(data)
    },
    enabled,
    select: (res) => {
      return res?.data || {}
    },
    onError: (error) => {
      console.error('error', error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false
  })
}
const useSubcategoryListMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.CASINO_GET_SUBCATEGORY],
    mutationFn: (data) => getSubcategory(data),
    onSuccess: (res) => {
      successToggler && successToggler(res)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}
const getProviderListQuery = () => {
  return useQuery({
    queryKey: [KeyTypes.GET_PROVIDER_LIST],
    queryFn: () => {
      return getProvider()
    },
    // enabled,
    select: (res) => {
      return res?.data?.data || {}
    },
    onError: (error) => {
      console.error('error', error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false
  })
}

const getLiveWinners = ({ params, enabled }) => {
  return useQuery({
    queryKey: [KeyTypes.CASINO_GET_LIVE_WINNERS],
    queryFn: () => {
      return getLiveWinner(params)
    },
    enabled,
    select: (res) => {
      return res || {}
    },
    onError: (error) => {
      console.error('error', error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false
  })
}

const useFavGameListMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.CASINO_GET_FAVORITES],
    mutationFn: (data) => getFavorites(data),
    onSuccess: (res) => {
      successToggler && successToggler(res)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const useRecentPlayListMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.CASINO_GET_RECENT_GAMES],
    mutationFn: (data) => getRecentGamesList(data),
    onSuccess: (res) => {
      successToggler && successToggler(res)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const useFavToggleMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['favToggle'],
    mutationFn: (data) => toogleFav(data),
    onSuccess,
    onError,
    refetchOnMount: true
  })
}
export const casinoQuery = {
  useGamesListQuery,
  useGamesListMutation,
  getSubcategoryListQuery,
  getProviderListQuery,
  useFavGameListMutation,
  getLiveWinners,
  useRecentPlayListMutation,
  useSubcategoryListMutation
}

export default casinoQuery
