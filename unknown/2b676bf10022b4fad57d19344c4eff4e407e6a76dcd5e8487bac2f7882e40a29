import * as React from 'react'
import useStyles from './style'
import { <PERSON>, Grid, Link, Typography } from '@mui/material'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'
import { styled } from '@mui/material/styles'
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp'
import MuiAccordion from '@mui/material/Accordion'
import MuiAccordionSummary, { accordionSummaryClasses } from '@mui/material/AccordionSummary'
import MuiAccordionDetails from '@mui/material/AccordionDetails'
import { usePortalStore } from '../../../store/userPortalSlice'
import Signup from '../../../components/Modal/Signup'
import TrustpilotWidget from '../TrustpilotWidget'

const Accordion = styled((props) => <MuiAccordion disableGutters elevation={0} square {...props} />)(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  '&:not(:last-child)': {
    borderBottom: 0
  },
  '&::before': {
    display: 'none'
  }
}))
const AccordionSummary = styled((props) => (
  <MuiAccordionSummary expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />} {...props} />
))(({ theme }) => ({
  backgroundColor: 'rgba(0, 0, 0, .03)',
  flexDirection: 'row-reverse',
  [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]: {
    transform: 'rotate(90deg)'
  },
  [`& .${accordionSummaryClasses.content}`]: {
    marginLeft: theme.spacing(1)
  },
  ...theme.applyStyles('dark', {
    backgroundColor: 'rgba(255, 255, 255, .05)'
  })
}))
const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  padding: theme.spacing(2),
  borderTop: '1px solid rgba(0, 0, 0, .125)'
}))

const Testimonials = () => {
  const classes = useStyles()
  const [expanded, setExpanded] = React.useState('panel1')
  const portalStore = usePortalStore()
  const handlePayForFree = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }
  const handleChange = (panel) => (event, newExpanded) => {
    setExpanded(newExpanded ? panel : false)
  }
  return (
    <>
      <section className={classes.TestimonialsSection} id='reviews' data-tracking='Home.Reviews.Section'>
        <Grid className='inner-heading'>
          <Typography variant='h4'>What Our Players Say</Typography>
        </Grid>
        <TrustpilotWidget />
      </section>
      <section className={classes.faqWrap} id='faq' data-tracking='Home.FAQ.Section'>
        <Grid className='inner-container'>
          <Grid className='inner-heading'>
            <Typography variant='h4'>Frequently Asked Questions</Typography>
          </Grid>
          <Grid className='accordian-wrap'>
            <Accordion expanded={expanded === 'panel1'} onChange={handleChange('panel1')}>
              <AccordionSummary aria-controls='panel1d-content' id='panel1d-header'>
                <Typography component='span'>What is a Social Casino?</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography>
                  Think of a social casino as your ultimate fun zone! It’s an online platform where you get to enjoy
                  thrilling casino-style games without ever wagering real money. Whether you’re here to spin, win, or
                  simply have a blast with friends, our social casino online brings all the excitement right to your
                  fingertips—no pressure, just pure entertainment!
                </Typography>
              </AccordionDetails>
            </Accordion>
            <Accordion expanded={expanded === 'panel2'} onChange={handleChange('panel2')}>
              <AccordionSummary aria-controls='panel1d-content' id='panel1d-header'>
                <Typography component='span'>Is The Money Factory free to play?</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography>Absolutely! You can enjoy all our games without spending any money.</Typography>
                <Typography>
                  We offer free-to-play options that allow you to experience the thrill of gaming, and there’s no
                  obligation to purchase anything.
                </Typography>
              </AccordionDetails>
            </Accordion>
            <Accordion expanded={expanded === 'panel3'} onChange={handleChange('panel3')}>
              <AccordionSummary aria-controls='panel1d-content' id='panel1d-header'>
                <Typography component='span'>How do I start playing on The Money Factory?</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography>
                  Starting is easy! Simply
                  <Link href='javascript:void(0);' onClick={handlePayForFree}>
                    create an account by clicking here,
                  </Link>
                  and you’ll be ready to go. Once you’re verified, you can begin playing your favorite games right away.
                  There’s no need to download anything – just log in and start having fun!
                </Typography>
              </AccordionDetails>
            </Accordion>
            <Accordion expanded={expanded === 'panel4'} onChange={handleChange('panel4')}>
              <AccordionSummary aria-controls='panel1d-content' id='panel1d-header'>
                <Typography component='span'>How can I claim my welcome offer on The Money Factory?</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography>
                  Grabbing your welcome offer couldn’t be easier! Once you sign up and complete our super-quick
                  registration, your offer will be ready and waiting. Imagine starting your adventure with a stack of
                  offer coins—dive straight into your favorite social casino games and let the good times roll!
                </Typography>
              </AccordionDetails>
            </Accordion>
            <Accordion expanded={expanded === 'panel5'} onChange={handleChange('panel5')}>
              <AccordionSummary aria-controls='panel1d-content' id='panel1d-header'>
                <Typography component='span'>Can I play The Money Factory games on mobile?</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography>
                  You bet! The Money Factory lets you take the excitement wherever you go. Play all your favorite social
                  casino games right on your mobile device, whether you’re out and about or lounging at home. Enjoy
                  seamless gameplay anytime, anywhere—because the fun never stops!
                </Typography>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </section>
    </>
  )
}

export default Testimonials
