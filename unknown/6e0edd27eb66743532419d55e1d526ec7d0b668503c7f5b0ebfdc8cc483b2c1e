import React, { useEffect } from 'react'
import '../../../src/App.css'
import { Box, Button, Typography } from '@mui/material'
import { useUserStore } from '../../store/useUserSlice'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import GiftIcon from '../../components/ui-kit/icons/svg/gift.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import useStyles from '../Lobby/Lobby.styles'
import { formatPriceWithCommas, formatValueWithB, formatValueWithK } from '../../utils/helpers'
import { usePortalStore } from '../../store/userPortalSlice'
import { getLoginToken } from '../../utils/storageUtils'
import Signin from '../../components/Modal/Signin'
import { useGetProfileMutation } from '../../reactQuery'
import StepperForm from '../../components/StepperForm'

const CoinBundles = ({ packageData }) => {
  const auth = useUserStore((state) => state)
  const classes = useStyles()
  const [selectedPackages, setSelectedPackages] = React.useState(null)
  const [packages, setPackages] = React.useState([])
  const [fromCoinBundles, setFromCoinBundles] = React.useState(!auth.isAuthenticate)
  const portalStore = usePortalStore((state) => state)

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      portalStore.openPortal(
        () => <StepperForm stepperCalledFor='purchase' packageDetails={selectedPackages} />,
        'StepperModal'
      )
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const handleBuyNow = () => {
    if (!!getLoginToken() || auth.isAuthenticate) {
      ;(function () {
        window._conv_q = window._conv_q || []
        _conv_q.push(['pushRevenue', 'credit', selectedPackages, '100466670'])
      })()
      getProfileMutation.mutate()
    } else {
      portalStore.openPortal(
        () => <Signin fromCoinBundles={fromCoinBundles} selectedPackages={selectedPackages} />,
        'loginModal'
      )
    }
  }

  useEffect(() => {
    if (packageData?.packageData?.rows?.length > 0) {
      let sliceArray
      if (typeof packageData?.packageData?.rows?.[1] === 'undefined') {
        setSelectedPackages(packageData?.packageData?.rows?.[0])
        sliceArray = [...packageData?.packageData?.rows]
        setPackages(sliceArray)
      } else if (packageData?.packageData?.rows.length <= 6) {
        setSelectedPackages(packageData?.packageData?.rows?.[1])
        sliceArray = [...packageData?.packageData?.rows]
        setPackages(sliceArray)
      } else {
        setSelectedPackages(packageData?.packageData?.rows?.[1])
        sliceArray = packageData?.packageData?.rows?.slice(0, 6)
        setPackages(sliceArray)
      }
    }
  }, [packageData])

  return (
    <>
      {packageData?.packageData?.rows?.length > 0 && (
        <Box className={classes.bannerCoinBundle}>
          <Typography variant='h4'>Best coin bundle offers</Typography>
          <Box className='bundle-button'>
            {packages.length > 0 &&
              packages.map((item, index) => {
                return (
                  <Button
                    key={index}
                    className={`${selectedPackages && selectedPackages?.packageId === item?.packageId ? 'active' : ''}`}
                    onClick={() => setSelectedPackages(item)}
                  >
                    {selectedPackages && selectedPackages?.packageId === item?.packageId && (
                      <img src={GiftIcon} alt='gift' height='100%' width='100%' />
                    )}
                    <Typography variant='span'>$</Typography>
                    {item?.amount > 0 ? formatValueWithK(Number(item.amount)) : item?.amount}
                  </Button>
                )
              })}
          </Box>

          {selectedPackages && (
            <Box className='coin-box'>
              <Box className='bundle-coins'>
                <Box>
                  {' '}
                  <img src={usdchipIcon} alt='coinIcon' className='image1' height='100%' width='100%' />{' '}
                  {selectedPackages?.gcCoin > 0
                    ? formatPriceWithCommas(formatValueWithB(Number(selectedPackages?.gcCoin)))
                    : selectedPackages?.gcCoin}{' '}
                  GC
                </Box>
                <Box>
                  <img src={usdIcon} alt='usd' height='100%' width='100%' />{' '}
                  {selectedPackages?.scCoin > 0
                    ? formatPriceWithCommas(formatValueWithB(Number(selectedPackages?.scCoin)))
                    : selectedPackages?.scCoin}{' '}
                  SC
                </Box>
              </Box>
              <Button variant='contained' onClick={handleBuyNow}>
                Buy
              </Button>
            </Box>
          )}
        </Box>
      )}
    </>
  )
}
export default CoinBundles
