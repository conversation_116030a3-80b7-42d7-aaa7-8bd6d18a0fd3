import React, { useState, useEffect } from 'react'
import {
  Box,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography
} from '@mui/material'
import useStyles from '../Tier/Tier.styles'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'

import { styled } from '@mui/material/styles'
import Stack from '@mui/material/Stack'
import LinearProgress, { linearProgressClasses } from '@mui/material/LinearProgress'
import { useUserStore } from '../../store/useUserSlice'
import { useBannerStore } from '../../store/useBannerSlice'
import { useVipTiersMutation, useclaimTierBonus } from '../../reactQuery/rewardQuery'

import tierPosition1 from '../../components/ui-kit/icons/webp/position-1.webp'
import tierPosition2 from '../../components/ui-kit/icons/webp/position-2.webp'
import tierPosition3 from '../../components/ui-kit/icons/webp/position-3.webp'
import tierPosition4 from '../../components/ui-kit/icons/webp/position-4.webp'
import tierPosition5 from '../../components/ui-kit/icons/webp/position-5.webp'
import tierPosition6 from '../../components/ui-kit/icons/webp/position-6.webp'
import frame from '../../components/ui-kit/icons/webp/Frame.svg'
import toast from 'react-hot-toast'

import { useGetProfileMutation } from '../../reactQuery'
import BannerManagement from '../../components/BannerManagement'
import JackpotBadge from '../Jackpot/JackpotBadge'

const Tier = () => {
  const classes = useStyles()
  const [data, setData] = useState([])
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
    height: 10,
    borderRadius: 5,
    [`&.${linearProgressClasses.colorPrimary}`]: {
      backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800]
    },
    [`& .${linearProgressClasses.bar}`]: {
      borderRadius: 5,
      backgroundColor: theme.palette.mode === 'light' ? '#FDB72E' : '#2F240E'
    }
  }))

  const { mutate: vipTierMutation } = useVipTiersMutation({
    onSuccess: (res) => {
      let response = res?.data
      setData(response)
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const { mutate: mutationClaimTierBonus, isLoading: claimloading } = useclaimTierBonus({
    onSuccess: (res) => {
      toast.success('Tier bonus claimed successfully!')
      vipTierMutation()
      getProfileMutation.mutate()
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const handleClaimButton = (userId, bonusType) => {
    mutationClaimTierBonus({ userBonusId: userId, bonusType: bonusType })
  }

  const { tier } = useBannerStore((state) => state)
  useEffect(() => vipTierMutation(), [])

  const tierdata = data?.tierDetail || []
  const monthlyBonus = data?.monthlyTierDetail || {}
  const weeklyBonus = data?.weeklyTierDetail || {}
  const userDetail = data?.userCurrentTierStatus || {}
  const currentXp = userDetail?.currentXp || 0

  return (
    <>
      <Grid className={classes.lobbyRight}>
        <Grid className={classes.wrapper}>
          <Grid className='tier-banner-wrap'>
            <BannerManagement bannerData={tier} />
            {/* <Grid className='tier-banner'>
              <Grid container spacing={0.5}>
                <Grid item xs={6} lg={6}>
                  <Grid className='tier-banner-content'>
                    <Typography> {(rewardBanners && rewardBanners[0]?.textOne) || 'Enter the Ultimate'}</Typography>
                    <Typography variant='h4'>
                      {(rewardBanners && rewardBanners[0]?.textTwo) || 'Casino Tournament!'}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid item xs={6} lg={6}>
                  <Grid className='tier-banner-icon'>
                    <img src={tierBannerIcon} alt="Icon" />
                  </Grid>
                </Grid>
              </Grid>

            </Grid> */}
          </Grid>
          <Box className='tier-grid-section-wrap'>
            <Grid className='tier-grid-section'>
              <Box className='tier-grid'>
                <Grid
                  className={`tier-grid-card ${
                    userDetail?.currentTier?.name === tierdata[0]?.name && 'active-position'
                  }`}
                >
                  <Grid className='tier-position-icon'>
                    <img src={tierPosition1} alt='tier' />
                    <Typography variant='h4' className='tier-name'>
                      {tierdata[0]?.name || 'No Name'}
                    </Typography>
                  </Grid>
                  <Grid className='tier-grid-details'>
                    <Grid className='tier-coins-wrap'>
                      <Grid className='tier-coins'>
                        <img src={usdchipIcon} alt='Coin Icon' />
                        {tierdata[0]?.bonusGc || '0'} GC
                      </Grid>
                      <Grid className='tier-coins'>
                        <img src={usdIcon} alt='Coin Icon' />
                        {tierdata[0]?.bonusSc || '0'} SC
                      </Grid>
                    </Grid>
                  </Grid>
                  <Stack className='tier-progress-wrap'>
                    <BorderLinearProgress
                      variant='determinate'
                      value={userDetail?.level > 1 ? 100 : userDetail?.level === 1 ? userDetail?.percentage : 80}
                    />
                  </Stack>
                </Grid>
                <Grid
                  className={`tier-grid-card ${
                    userDetail?.currentTier?.name === tierdata[1]?.name && 'active-position'
                  }`}
                >
                  <Grid className='tier-position-icon'>
                    {currentXp < Number(tierdata[1]?.requiredXp) ? (
                      <img src={frame} alt='tier' />
                    ) : (
                      <img src={tierPosition2} alt='tier' />
                    )}
                    {/* <img src={mint2} alt="tier" /> */}
                    <Typography variant='h4' className='tier-name'>
                      {tierdata[1]?.name || 'No Name'}
                    </Typography>
                  </Grid>
                  <Grid className='tier-grid-details'>
                    <Grid className='tier-coins-wrap'>
                      <Grid className='tier-coins'>
                        <img src={usdchipIcon} alt='Coin Icon' />
                        {tierdata[1]?.bonusGc || '0'} GC
                      </Grid>
                      <Grid className='tier-coins'>
                        <img src={usdIcon} alt='Coin Icon' />
                        {tierdata[1]?.bonusSc || '0'} SC
                      </Grid>
                    </Grid>
                    <Button
                      variant='contained'
                      className='btn btn-primary'
                      onClick={() => handleClaimButton(tierdata[1]?.tierUserBonusId || null, 'tier-bonus')}
                      disabled={!tierdata[1]?.isUserClaimEligible || false}
                    >
                      Claim
                    </Button>
                  </Grid>
                  <Stack className='tier-progress-wrap'>
                    <BorderLinearProgress
                      variant='determinate'
                      value={userDetail?.level > 2 ? 100 : userDetail?.level === 2 ? userDetail?.percentage : 0}
                    />
                  </Stack>
                </Grid>
                <Grid
                  className={`tier-grid-card ${
                    userDetail?.currentTier?.name === tierdata[2]?.name && 'active-position'
                  }`}
                >
                  <Grid className='tier-position-icon'>
                    {currentXp < Number(tierdata[2]?.requiredXp) ? (
                      <img src={frame} alt='tier' />
                    ) : (
                      <img src={tierPosition3} alt='tier' />
                    )}
                    <Typography variant='h4' className='tier-name'>
                      {tierdata[2]?.name || 'No Name'}
                    </Typography>
                    {/* <img src={vault1} alt="tier" /> */}
                  </Grid>
                  <Grid className='tier-grid-details'>
                    <Grid className='tier-coins-wrap'>
                      <Grid className='tier-coins'>
                        <img src={usdchipIcon} alt='Coin Icon' />
                        {tierdata[2]?.bonusGc || '0'} GC
                      </Grid>
                      <Grid className='tier-coins'>
                        <img src={usdIcon} alt='Coin Icon' />
                        {tierdata[2]?.bonusSc || '0'} SC
                      </Grid>
                    </Grid>
                    <Button
                      variant='contained'
                      className='btn btn-primary'
                      onClick={() => handleClaimButton(tierdata[2]?.tierUserBonusId || null, 'tier-bonus')}
                      disabled={!tierdata[2]?.isUserClaimEligible || false}
                    >
                      Claim
                    </Button>
                  </Grid>
                  <Stack className='tier-progress-wrap'>
                    <BorderLinearProgress
                      variant='determinate'
                      value={userDetail?.level > 3 ? 100 : userDetail?.level === 3 ? userDetail?.percentage : 0}
                    />
                  </Stack>
                </Grid>
                <Grid
                  className={`tier-grid-card ${
                    userDetail?.currentTier?.name === tierdata[3]?.name && 'active-position'
                  }`}
                >
                  <Grid className='tier-position-icon'>
                    {currentXp < Number(tierdata[3]?.requiredXp) ? (
                      <img src={frame} alt='tier' />
                    ) : (
                      <img src={tierPosition4} alt='tier' />
                    )}
                    <Typography variant='h4' className='tier-name'>
                      {tierdata[3]?.name || 'No Name'}
                    </Typography>
                    {/* <img src={forge1} alt="tier" /> */}
                  </Grid>
                  <Grid className='tier-grid-details'>
                    <Grid className='tier-coins-wrap'>
                      <Grid className='tier-coins'>
                        <img src={usdchipIcon} alt='Coin Icon' />
                        {tierdata[3]?.bonusGc || '0'} GC
                      </Grid>
                      <Grid className='tier-coins'>
                        <img src={usdIcon} alt='Coin Icon' />
                        {tierdata[3]?.bonusSc || '0'} SC
                      </Grid>
                    </Grid>
                    <Button
                      variant='contained'
                      className='btn btn-primary'
                      onClick={() => handleClaimButton(tierdata[3]?.tierUserBonusId || null, 'tier-bonus')}
                      disabled={!tierdata[3]?.isUserClaimEligible || false}
                    >
                      Claim
                    </Button>
                  </Grid>
                  <Stack className='tier-progress-wrap'>
                    <BorderLinearProgress
                      variant='determinate'
                      value={userDetail?.level > 4 ? 100 : userDetail?.level === 4 ? userDetail?.percentage : 0}
                    />
                  </Stack>
                </Grid>
                <Grid
                  className={`tier-grid-card ${
                    userDetail?.currentTier?.name === tierdata[4]?.name && 'active-position'
                  }`}
                >
                  <Grid className='tier-position-icon'>
                    {currentXp < Number(tierdata[4]?.requiredXp) ? (
                      <img src={frame} alt='tier' />
                    ) : (
                      <img src={tierPosition5} alt='tier' />
                    )}
                    {/* <img src={reserve1} alt="tier" /> */}
                    <Typography variant='h4' className='tier-name'>
                      {tierdata[4]?.name || 'No Name'}
                    </Typography>
                  </Grid>
                  <Grid className='tier-grid-details'>
                    <Grid className='tier-coins-wrap'>
                      <Grid className='tier-coins'>
                        <img src={usdchipIcon} alt='Coin Icon' />
                        {tierdata[4]?.bonusGc || '0'} GC
                      </Grid>
                      <Grid className='tier-coins'>
                        <img src={usdIcon} alt='Coin Icon' />
                        {tierdata[4]?.bonusSc || '0'} SC
                      </Grid>
                    </Grid>
                    <Button
                      variant='contained'
                      className='btn btn-primary'
                      onClick={() => handleClaimButton(tierdata[4]?.tierUserBonusId || null, 'tier-bonus')}
                      disabled={!tierdata[4]?.isUserClaimEligible || false}
                    >
                      Claim
                    </Button>
                  </Grid>
                  <Stack className='tier-progress-wrap'>
                    <BorderLinearProgress
                      variant='determinate'
                      value={userDetail?.level > 5 ? 100 : userDetail?.level === 5 ? userDetail?.percentage : 0}
                    />
                  </Stack>
                </Grid>
                <Grid
                  className={`tier-grid-card ${
                    userDetail?.currentTier?.name === tierdata[5]?.name && 'active-position'
                  }`}
                >
                  <Grid className='tier-position-icon'>
                    {currentXp < Number(tierdata[5]?.requiredXp) ? (
                      <img src={frame} alt='tier' />
                    ) : (
                      <img src={tierPosition6} alt='tier' />
                    )}
                    {/* <img src={empire1} alt="tier" /> */}
                    <Typography variant='h4' className='tier-name'>
                      {tierdata[5]?.name || 'No Name'}
                    </Typography>
                  </Grid>
                  <Grid className='tier-grid-details'>
                    <Grid className='tier-coins-wrap'>
                      <Grid className='tier-coins'>
                        <img src={usdchipIcon} alt='Coin Icon' />
                        {tierdata[5]?.bonusGc || '0'} GC
                      </Grid>
                      <Grid className='tier-coins'>
                        <img src={usdIcon} alt='Coin Icon' />
                        {tierdata[5]?.bonusSc || '0'} SC
                      </Grid>
                    </Grid>
                    <Button
                      variant='contained'
                      className='btn btn-primary'
                      onClick={() => handleClaimButton(tierdata[5]?.tierUserBonusId || null, 'tier-bonus')}
                      disabled={!tierdata[5]?.isUserClaimEligible || false}
                    >
                      Claim
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Box>
          <Box className='weekly-bonus-section'>
            {weeklyBonus?.isUserWeeklyClaimEligible && (
              <Grid className='weekly-bonus-left'>
                <Typography> You have received your weekly bonus</Typography>
                <Button
                  type='button'
                  className='btn btn-primary'
                  disabled={!weeklyBonus?.isUserWeeklyClaimEligible}
                  onClick={() =>
                    handleClaimButton(weeklyBonus?.weeklyTierBonusData?.userBonusId || null, 'weekly-tier-bonus')
                  }
                >
                  <Grid className='tier-coins'>
                    <img src={usdchipIcon} alt='Coin Icon' />
                    {`${weeklyBonus?.weeklyTierBonusData?.gcAmount || 0} GC`}
                  </Grid>
                  <Grid className='tier-coins'>
                    <img src={usdIcon} alt='Coin Icon' />
                    {`${weeklyBonus?.weeklyTierBonusData?.scAmount || 0} SC`}
                  </Grid>
                </Button>
              </Grid>
            )}
            {monthlyBonus?.isUserMonthlyClaimEligible && (
              <Grid className='weekly-bonus-left'>
                <Typography> You have received your Monthly bonus</Typography>
                <Button
                  type='button'
                  className='btn btn-primary'
                  disabled={!monthlyBonus?.isUserMonthlyClaimEligible}
                  onClick={() =>
                    handleClaimButton(monthlyBonus?.monthlyTierBonusData?.userBonusId || null, 'monthly-tier-bonus')
                  }
                >
                  <Grid className='tier-coins'>
                    <img src={usdchipIcon} alt='Coin Icon' />
                    {`${monthlyBonus?.monthlyTierBonusData?.gcAmount || 0} GC`}
                  </Grid>
                  <Grid className='tier-coins'>
                    <img src={usdIcon} alt='Coin Icon' />
                    {`${monthlyBonus?.monthlyTierBonusData?.scAmount || 0} SC`}
                  </Grid>
                </Button>
              </Grid>
            )}
          </Box>
          <Box className='tier-table-wrap'>
            <TableContainer>
              <Table
                aria-label='simple table'
                sx={{
                  borderSpacing: '10px', // Add spacing between cells
                  borderCollapse: 'separate' // Ensure spacing works
                }}
              >
                <TableHead>
                  <TableRow>
                    <TableCell className='tier-table-card' sx={{ pr: 2 }}></TableCell>
                    <TableCell className='tier-table-card' sx={{ pr: 2 }}>
                      {tierdata[0]?.name || 'No Name'}
                    </TableCell>
                    <TableCell className='tier-table-card' sx={{ pr: 2 }}>
                      {tierdata[1]?.name || 'No Name'}
                    </TableCell>
                    <TableCell className='tier-table-card' sx={{ pr: 2 }}>
                      {tierdata[2]?.name || 'No Name'}
                    </TableCell>
                    <TableCell className='tier-table-card' sx={{ pr: 2 }}>
                      {tierdata[3]?.name || 'No Name'}
                    </TableCell>
                    <TableCell className='tier-table-card' sx={{ pr: 2 }}>
                      {tierdata[4]?.name || 'No Name'}
                    </TableCell>
                    <TableCell className='tier-table-card' sx={{ pr: 2 }}>
                      {tierdata[5]?.name || 'No Name'}
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell>Required Experience</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[0]?.requiredXp || 'No Data'}XP</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[1]?.requiredXp || 'No Data'}XP</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[2]?.requiredXp || 'No Data'}XP</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[3]?.requiredXp || 'No Data'}XP</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[4]?.requiredXp || 'No Data'}XP</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[5]?.requiredXp || 'No Data'}XP</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>GC Bonus</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[0]?.bonusGc || '0'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[1]?.bonusGc || 'No Data'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[2]?.bonusGc || 'No Data'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[3]?.bonusGc || 'No Data'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[4]?.bonusGc || 'No Data'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[5]?.bonusGc || 'No Data'}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>SC Bonus</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[0]?.bonusSc || '0'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[1]?.bonusSc || 'No Data'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[2]?.bonusSc || 'No Data'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[3]?.bonusSc || 'No Data'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[4]?.bonusSc || 'No Data'}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>{tierdata[5]?.bonusSc || 'No Data'}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
          {/* <Box className="weekly-bonus-section">
            <Grid className="weekly-bonus-left">
              <Typography>Yayaya you have received your weekly bonus</Typography>
              <Button type="button" className='btn btn-primary'>$50,000</Button>
            </Grid>
            <Grid className="weekly-bonus-left">
              <Typography>Yayaya you have received your Monthly bonus</Typography>
              <Button type="button" className='btn btn-primary'>$30,000</Button>
            </Grid>
          </Box> */}
          {/* <Box className="tier-table-wrap">
            <TableContainer >
              <Grid className='table-filter'>
                <Grid className='tier-table-card'></Grid>
                <Grid className='tier-table-card'>Nexus</Grid>
                <Grid className='tier-table-card'>Mint</Grid>
                <Grid className='tier-table-card'>Vault</Grid>
                <Grid className='tier-table-card'>Forge</Grid>
                <Grid className='tier-table-card'>Reserve</Grid>
                <Grid className='tier-table-card'>Empire</Grid>
              </Grid>
              <Table aria-label="simple table">
                <TableHead>
                  <TableRow >
                    <TableCell>Required Experience</TableCell>
                    <TableCell >0XP</TableCell>
                    <TableCell >1000XP</TableCell>
                    <TableCell >10000XP</TableCell>
                    <TableCell >35000XP</TableCell>
                    <TableCell >100000XP</TableCell>
                    <TableCell >250000XP</TableCell>
                  </TableRow>

                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell>GC Bonus</TableCell>
                    <TableCell>0</TableCell>
                    <TableCell>7,500</TableCell>
                    <TableCell>15,000</TableCell>
                    <TableCell>35,000</TableCell>
                    <TableCell>40,000</TableCell>
                    <TableCell>50,000</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>SC Bonus</TableCell>
                    <TableCell>1</TableCell>
                    <TableCell>5</TableCell>
                    <TableCell>20</TableCell>
                    <TableCell>40</TableCell>
                    <TableCell>75</TableCell>
                    <TableCell>100</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Community Chat</TableCell>
                    <TableCell></TableCell>
                    <TableCell className='check-icon'><CheckBoxIcon /> </TableCell>
                    <TableCell className='check-icon'><CheckBoxIcon /> </TableCell>
                    <TableCell className='check-icon'><CheckBoxIcon /> </TableCell>
                    <TableCell className='check-icon'><CheckBoxIcon /> </TableCell>
                    <TableCell className='check-icon'><CheckBoxIcon /> </TableCell>
                  </TableRow>

                </TableBody>
              </Table>
            </TableContainer>
          </Box>
          <Box className="vip-banner-wrap">
            <Grid className='vip-banner-content'>
              <Button type='button' className='btn btn-primary'>Get In Touch</Button>
            </Grid>
          </Box> */}

          {/* <Box className="vip-banner-wrap">
            <Grid className='vip-banner-content'>
              <Button type='button' className='btn btn-primary'>Get In Touch</Button>
            </Grid>
          </Box> */}

          <Grid className='tier-grid-wrapper'>
            <Grid className='tier-grid-section'>
              <Grid className='tier-grid-wrap'>
                {/* <Grid className='tier-grid-card-wrap'>
                  <Grid className='tier-grid-card tier-one'>
                    <Grid className='tier-position-icon'>
                      <img src={Nexus} alt="Medal" />
                    </Grid>

                  </Grid>
                </Grid> */}
                {/* {data?.map((tier, index) =>
                  (userDetails?.tierDetail && tier?.level) < userDetails?.tierDetail?.currentTier?.level ? (
                    <Grid className='tier-grid-card-wrap'>
                      <Grid className={`tier-grid-card tier-${index}`}>
                        <Grid className='tier-position-icon'>
                          <img src={tier?.icon} alt='Medal' />

                        </Grid>
                        <Grid className='tier-position-details'>
                          <Grid className='coin-details'>
                            <Grid>
                              <img src={usdchipIcon} alt='walletIcon' className='image1' />
                              <Typography>
                                {tier?.bonusGc > 0
                                  ? formatPriceWithCommas(formatValueWithB(Number(tier?.bonusGc)))
                                  : tier?.bonusGc}{' '}
                                GC
                              </Typography>
                            </Grid>
                            <Grid>
                              <img src={usdIcon} alt='walletIcon' className='image1' />
                              <Typography>
                                {tier?.bonusSc > 0
                                  ? formatPriceWithCommas(formatValueWithB(Number(tier?.bonusSc)))
                                  : tier?.bonusSc}{' '}
                                SC
                              </Typography>
                            </Grid>
                          </Grid>
                          <Typography variant='h4'>{tier?.requiredXp} XP</Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                  ) : userDetails?.tierDetail && tier?.level === userDetails?.tierDetail?.currentTier?.level ? (
                    <Grid className='tier-grid-card-wrap valut-rank'>
                      <Grid className={`tier-grid-card tier-${index}`}>
                        <Grid className='active-glow'>
                          <img src={GlowIcon} alt='Glow' />
                        </Grid>
                        <Grid className='tier-position-icon'>
                          <img src={tier?.icon} alt='Medal' />
                        </Grid>
                        <Grid className='tier-position-details'>
                          <Grid className='coin-details'>
                            <Grid>
                              <img src={usdchipIcon} alt='walletIcon' className='image1' />
                              <Typography>
                                {tier?.bonusGc > 0
                                  ? formatPriceWithCommas(formatValueWithB(Number(tier?.bonusGc)))
                                  : tier?.bonusGc}{' '}
                                GC
                              </Typography>
                            </Grid>
                            <Grid>
                              <img src={usdIcon} alt='walletIcon' className='image1' />
                              <Typography>
                                {tier?.bonusSc > 0
                                  ? formatPriceWithCommas(formatValueWithB(Number(tier?.bonusSc)))
                                  : tier?.bonusSc}{' '}
                                SC
                              </Typography>
                            </Grid>
                          </Grid>
                          <Typography variant='h4'>{tier?.requiredXp} XP</Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                  ) : (
                    <Grid className={`tier-grid-card-wrap ${userDetails?.tierDetail ? 'disabled' : ''}`}>
                      <Grid className={`tier-grid-card tier-${index}`}>
                        <Grid className='tier-position-icon'>
                          <img src={tier?.icon} alt='Medal' />
                        </Grid>
                        <Grid className='tier-position-details'>
                          <Grid className='coin-details'>
                            <Grid>
                              <img src={usdchipIcon} alt='walletIcon' className='image1' />
                              <Typography>
                                {tier?.bonusGc > 0
                                  ? formatPriceWithCommas(formatValueWithB(Number(tier?.bonusGc)))
                                  : tier?.bonusGc}{' '}
                                GC
                              </Typography>
                            </Grid>
                            <Grid>
                              <img src={usdIcon} alt='walletIcon' className='image1' />
                              <Typography>
                                {tier?.bonusSc > 0
                                  ? formatPriceWithCommas(formatValueWithB(Number(tier?.bonusSc)))
                                  : tier?.bonusSc}{' '}
                                SC
                              </Typography>
                            </Grid>
                          </Grid>
                          <Typography variant='h4'>{tier?.requiredXp} XP</Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                  )
                )} */}
              </Grid>

              <Grid className='tier-progress-wrap'>
                <Stack>
                  <BorderLinearProgress variant='determinate' value={0} />
                </Stack>
                {/* <Grid className='tier-progress-mark'>
                  {data?.map((tier, index) =>
                    userDetails?.tierDetail && tier?.level <= userDetails?.tierDetail?.currentTier?.level ? (
                      <Grid>
                        <img src={ActiveCheck} alt='Active' />
                      </Grid>
                    ) : (
                      <Grid>
                        <img src={WaitingIcon} alt='Waiting' />
                      </Grid>
                    )
                  )}
                </Grid> */}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <JackpotBadge />
      </Grid>
    </>
  )
}

export default Tier
