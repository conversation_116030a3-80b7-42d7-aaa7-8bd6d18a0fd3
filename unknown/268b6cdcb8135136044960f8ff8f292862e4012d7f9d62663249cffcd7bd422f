import React from 'react'
import { <PERSON>po<PERSON>, <PERSON><PERSON>, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material'
import joinTournament from '../../../components/ui-kit/icons/webp/join-tournament.webp'

const TournamentJoinModal = ({ showJoinModal, setShowJoinModal, handleJoinTournament, tournamentData }) => {
  return (
    <Dialog
      open={showJoinModal}
      onClose={() => setShowJoinModal(false)}
      PaperProps={{
        sx: {
          background: 'linear-gradient(145deg, #0D0D0D, #111111)',
          borderRadius: '16px !important',
          border: '1px solid #F7B85B',
          width: '100%',
          maxWidth: '460px',
          textAlign: 'center',
          boxShadow: '0 0 15px rgba(0,0,0,0.5)'
        }
      }}
    >
      <img src={joinTournament} />
      <DialogTitle
        sx={{
          fontSize: '36px',
          background: 'linear-gradient(180deg, #FFF5DA 33.07%, #D19049 73.68%)',
          WebkitBackgroundClip: 'text',
          textShadow: '0px 1.63px 1.63px #B5B5B540',
          WebkitTextFillColor: 'transparent',
          fontWeight: 800,
          padding: '0'
        }}
      >
        JOIN TOURNAMENT
      </DialogTitle>

      <DialogContent>
        <Typography
          sx={{
            color: '#fff',
            fontSize: '18px',
            fontWeight: 600
          }}
        >
          You're not part of this tournament. Do you want to join and play now?
        </Typography>
      </DialogContent>

      <DialogActions
        sx={{
          justifyContent: 'center',
          gap: '20px',
          paddingBottom: '20px'
        }}
      >
        {/* Cancel Button */}
        <Button
          variant='outlined'
          onClick={() => setShowJoinModal(false)}
          sx={{
            color: '#CCCCCC',
            borderColor: '#CCCCCC',
            borderRadius: '30px',
            px: '25px',
            py: '10px',
            fontSize: '16px',
            lineHeight: 1.5,
            textTransform: 'none',
            fontWeight: 600,
            maxWidth: '120px',
            width: '100%',
            backgroundColor: 'transparent',
            transition: '0.3s',
            '&:hover': {
              backgroundColor: '#CCCCCC',
              color: '#000'
            }
          }}
        >
          Cancel
        </Button>

        {/* Yes, Join Button */}
        <Button
          variant='contained'
          onClick={() =>
            handleJoinTournament(
              tournamentData?.entryCoin === 'SC' ? '/cms/tournament-sc-terms' : '/cms/tournament-gc-terms',
              tournamentData?.entryCoin,
              tournamentData?.tournamentId,
              tournamentData
            )
          }
          sx={{
            backgroundColor: '#FDB72E',
            color: '#000',
            borderRadius: '30px',
            px: '25px',
            py: '10px',
            fontSize: '16px',
            lineHeight: 1.5,
            textTransform: 'none',
            fontWeight: 600,
            border: '2px solid #FDB72E',
            transition: '0.3s',
            maxWidth: '120px',
            width: '100%',
            '&:hover': {
              backgroundColor: 'transparent',
              color: '#FDB72E'
            }
          }}
        >
          Yes, Join
        </Button>
      </DialogActions>

    </Dialog>
  )
}

export default TournamentJoinModal
