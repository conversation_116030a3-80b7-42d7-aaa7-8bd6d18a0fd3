import React from 'react'
import useStyles from './LinerProgressbar.styles'
import { Box } from '@mui/material'

const LinerProgreeBar = ({ rangeValue }) => {
  const classes = useStyles()
  return (
    <Box className={classes.linerProgressWrap}>
      <input
        style={{
          background: `linear-gradient(90deg, #FDB72E ${rangeValue}%, #929292 1%)`
        }}
        className='range-input'
        type='range'
        value={rangeValue}
        min='0'
        max='100'
      />
    </Box>
  )
}

export default LinerProgreeBar
