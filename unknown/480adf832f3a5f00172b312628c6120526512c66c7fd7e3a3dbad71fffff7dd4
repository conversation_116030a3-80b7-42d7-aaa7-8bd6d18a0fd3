import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from '../../../../reactQuery/KeyTypes'
import { getApplicablePromocode } from '../../../../utils/apiCalls'

export const useApplicablePromocodeListQuery = ({ params, enabled, onSuccess, onError }) => {
  return useQuery({
    queryKey: [KeyTypes.APPLICABLE_PROMO],
    queryFn: () => {
      return getApplicablePromocode(params)
    },
    onSuccess,
    onError,
    enabled: enabled,
    refetchOnWindowFocus: false,
    retry: false
  })
}

export const usePromocodeList = () => {
  return {
    useApplicablePromocodeListQuery
  }
}

export default usePromocodeList
