import React, { useState } from 'react'
import { Box, Typography } from '@mui/material'
import useStyles from './TournamentDetail.styles'
import { KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material'
import { useTournamentStore } from '../../../store/useTournamentStore'

const TournamentGuidelineCard = () => {
  const classes = useStyles()
  const [isGuidelinesOpen, setIsGuidelinesOpen] = useState(false)
  const tournamentData = useTournamentStore((state) => state?.tournamentData)

  return (
    <Box className={classes.tournamentPara}>
      <Typography
        variant='h4'
        component='h4'
        onClick={() => setIsGuidelinesOpen(!isGuidelinesOpen)}
        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
      >
        Tournament Participation Guidelines
        {isGuidelinesOpen ? (
          <KeyboardArrowUp style={{ marginLeft: '8px' }} />
        ) : (
          <KeyboardArrowDown style={{ marginLeft: '8px' }} />
        )}
      </Typography>
      {isGuidelinesOpen && (
        <Typography
          variant='p'
          component='p'
          style={{ fontSize: '16px' }}
          dangerouslySetInnerHTML={{
            __html: tournamentData?.description?.replace(/\n/g, '<br />')
          }}
        />
      )}
    </Box>
  )
}

export default TournamentGuidelineCard
