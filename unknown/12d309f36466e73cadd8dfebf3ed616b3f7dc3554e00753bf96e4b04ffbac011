import create from 'zustand'

const useStepperStore = create((set) => ({
  activeStep: 0,
  steps: [],
  stepperCalledFor: '',
  handleNext: () => set((state) => ({ activeStep: Math.min(state.activeStep + 1, state.steps.length - 1) })),
  handleBack: () => set((state) => ({ activeStep: Math.max(state.activeStep - 1, 0) })),
  setSteps: (newSteps) => set({ steps: newSteps }),
  setActiveStep: (newActiveStep) => set({ activeStep: newActiveStep }),
  setStepperCalledFor: (stepperCalledFor) => set({ stepperCalledFor })
}))

export default useStepperStore
