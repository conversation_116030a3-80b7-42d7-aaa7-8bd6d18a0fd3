import { Box, Grid, Typography, Button } from '@mui/material'
import React from 'react'
import useStyles from './NotAvailable.styles'
import { useNavigate } from 'react-router-dom'
import noDataGraphic from '../../components/ui-kit/icons/webp/nod-data.webp'
import { useGeolocationGameplayUpdate } from '../../store/store'

/* eslint-dsiable multiline-ternary */

const NotAvailable = () => {
  const navigate = useNavigate()
  const geocomplyErrorMsg = useGeolocationGameplayUpdate((state) => state.geocomplyErrorMsg)
  const troubleShoot = useGeolocationGameplayUpdate((state) => state.troubleShoot)

  const handleRefresh = () => {
    // resetBoundary()
    // resetErrorBoundary()
    navigate('/')
  }
  const classes = useStyles()

  return (
    <>
      <Box className={classes.noDataWrap}>
        <Grid className='error-content-wrap'>
          <Grid className='error-icon'>
            <img src={noDataGraphic} alt='No Data' />
          </Grid>
          <Typography variant='h1'> Thank you for your interest </Typography>
          <Typography>
            {geocomplyErrorMsg
              ? geocomplyErrorMsg
              : 'Unfortunately, The Money Factory is not available in this region.If you believe you should have access, please contact us.'}
          </Typography>
          <Typography className='more-details-cta'> If you think there is an issue then follow below steps.</Typography>
          {troubleShoot?.length > 0 ? (
            <Grid className='steps-wrap error-wrap'>
              <ol>
                {troubleShoot.map((msg, index) => (
                  <li key={index}>
                    <Typography>{msg}</Typography>
                  </li>
                ))}
              </ol>
            </Grid>
          ) : (
            <Grid className='steps-wrap'>
              <ol>
                <li>
                  <Typography>Check if the website has location access.</Typography>
                </li>
                <li>
                  <Typography>If no, then enable the location </Typography>
                </li>
                <li>
                  <Typography>Reload the website </Typography>
                </li>
              </ol>
            </Grid>
          )}
          <Grid className='btn-wrap'>
            <Button onClick={handleRefresh} className='btn btn-primary'>
              {' '}
              Go to Home{' '}
            </Button>
          </Grid>
        </Grid>
      </Box>
    </>
  )
}

export default NotAvailable

// import React from 'react';
// import {
//     Grid,
//     Typography,
//     Box,
//     Button,
// } from '@mui/material';
// import useStyles from './MaintenancePopuo.styles';
// import { Maintenance } from '../../components/ui-kit/icons/webp';
// import { BrandLogo } from '../../components/ui-kit/icons/brand';
// import { useNavigate } from 'react-router-dom';

// const NotAvailable = () => {
//     const navigate = useNavigate();

//     return (
//         <Grid style={{ padding: '100px' }}>
//             <Grid>
//                 <Grid container spacing={1}>
//                     <Grid item xs={12} sm={6} md={12} style={{ display: 'flex', justifyContent: "center" }}>
//                         <Box className="img-box" style={{ maxWidth: '650px' }}>
//                             <img src={BrandLogo} alt='Maintenance' />
//                         </Box>
//                     </Grid>
//                     <Grid item xs={12} sm={6} md={12}>
//                         <Box className="text-box">
//                             {/* <Typography className='title-text'>Thank you for your interest</Typography> */}
//                             <Typography className='subtitle-text' style={{ fontSize: '35px' }}>Thank you for your interest</Typography>
//                             <Typography className='subtitle-text'>Unfortunately, The Money Factory is not available in this region.
//                                 If you believe you should have access, please contact us.</Typography>
//                             <Typography className='subtitle-text'>If you think there is an issue then follow below steps</Typography>
//                             <Typography className='subtitle-text'>1. Check if the website has location access</Typography>
//                             <Typography className='subtitle-text'>2. If no, then enable the location</Typography>
//                             <Typography className='subtitle-text'>3. Reload the website </Typography>
//                             {/* <Typography className='pera-text'>
//                                 Please check back in some time. If you have any urgent issues, feel free to contact our support team <a className='pera-text' href='mailto:<EMAIL>' target='_blank' rel="noreferrer">here</a>.
//                             </Typography> */}
//                             <Button variant='contained' className='btn btn-primary' onClick={() => navigate('/')} >Go to Home</Button>
//                         </Box>
//                     </Grid>
//                 </Grid>
//             </Grid>
//         </Grid>
//     );
// };

// export default NotAvailable;
