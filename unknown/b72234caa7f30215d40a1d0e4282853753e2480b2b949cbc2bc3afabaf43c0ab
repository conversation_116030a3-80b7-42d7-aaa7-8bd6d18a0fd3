import React, { useState, useEffect, useRef } from 'react'
import { Typography } from '@mui/material'

const PaymentTimer = ({ initialTime, onTimeout }) => {
  const [timeLeft, setTimeLeft] = useState(initialTime)
  const startTimeRef = useRef(Date.now())

  useEffect(() => {
    // Reset the timer when the popup opens
    startTimeRef.current = Date.now()
    setTimeLeft(initialTime)

    const updateTimer = () => {
      const elapsedTime = Math.floor((Date.now() - startTimeRef.current) / 1000)
      const remainingTime = Math.max(initialTime - elapsedTime, 0)

      setTimeLeft(remainingTime)

      if (remainingTime === 0) {
        clearInterval(timer)
        onTimeout()
      }
    }

    const timer = setInterval(updateTimer, 1000)

    // Handle screen lock/unlock to sync time
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        updateTimer() // Sync timer when user unlocks the screen
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      clearInterval(timer)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [initialTime, onTimeout]) //

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  return <Typography className='timer-text'>{formatTime(timeLeft)}</Typography>
}

export default PaymentTimer
