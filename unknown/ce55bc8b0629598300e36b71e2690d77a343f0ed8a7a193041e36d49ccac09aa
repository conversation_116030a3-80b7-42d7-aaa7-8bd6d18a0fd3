import { makeStyles } from '@mui/styles'

import {
  leftRoundedArrow,
  rightRoundedArrow,
  socialCasinoBg2,
  testimonalsBg
} from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  TestimonialsSection: {
    background: `url(${testimonalsBg})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    padding: theme.spacing(4, 0, 0),
    backgroundRepeat: 'no-repeat',
    [theme.breakpoints.down('lg')]: {
      padding: theme.spacing(2, 1)
    },
    [theme.breakpoints.down('md')]: {
      padding: `${theme.spacing(1)} !important`
    },
    '& .testimonials-card-wrap': {
      maxWidth: `${theme.spacing(68.875)} !important`,
      margin: '0 auto',
      paddingTop: theme.spacing(0),

      '& .testimonials-card': {
        background: theme.colors.testimonalsCardBg,
        padding: theme.spacing(2, 2.5),
        textAlign: 'left',
        borderRadius: theme.spacing(1.25),
        overflow: 'auto',
        position: 'relative',
        minHeight: '413px',
        display: 'flex',
        flexDirection: 'column',

        [theme.breakpoints.down('sm')]: {
          minHeight: '360px'
        },

        '& p': {
          fontWeight: theme.typography.fontWeightMedium,
          fontSize: theme.spacing(1.25),
          lineHeight: 1.6,
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1.25)
          }
        },
        '& .quoteicon-wrap': {
          '& img': {
            width: theme.spacing(1.875),
            height: theme.spacing(1.875),
            opacity: '0.5'
          }
        },
        '& .author-wrap': {
          marginTop: 'auto',
          '& h4': {
            fontWeight: theme.typography.fontWeightBold,
            fontSize: theme.spacing(1.25)
          }
        }
      },

      '& .swiper-button-prev': {
        left: '0% !important'
      },
      '& .swiper-button-prev, & .swiper-button-next': {
        backgroundImage: `url(${leftRoundedArrow})`,
        backgroundSize: theme.spacing(2),
        height: theme.spacing(2),
        width: theme.spacing(2),
        position: 'absolute',
        top: '50%',
        transform: 'translateY(-50%)',
        zIndex: 10,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        '&:after': {
          display: 'none'
        },
        [theme.breakpoints.down('sm')]: {
          left: '61%',
          height: theme.spacing(1.5),
          width: theme.spacing(1.5),
          backgroundSize: theme.spacing(1.5)
        }
      },
      '& .swiper-button-next': {
        backgroundImage: `url(${rightRoundedArrow})`,
        left: 'auto !important',
        right: theme.spacing(1.25),
        [theme.breakpoints.down('sm')]: {
          right: '0'
        }
      },
      '& .swiper-slide': {
        position: 'relative',

        '&:after': {
          content: "''",
          width: 'calc(100%)',
          height: 'calc(100%)',
          border: `1px solid ${theme.colors.YellowishOrange}`,
          position: 'absolute',
          left: theme.spacing(0.625),
          top: theme.spacing(0.625),
          borderRadius: theme.spacing(1.25),
          zIndex: -1,
          [theme.breakpoints.down('sm')]: {
            width: 'calc(92%)',
            height: 'calc(95%)',
            left: theme.spacing(1.25),
            top: theme.spacing(1.25)
          }
        }
      }
    },
    '& .swiper': {
      paddingBottom: theme.spacing(2.75),
      paddingRight: theme.spacing(1.25),
      [theme.breakpoints.down('sm')]: {
        paddingRight: theme.spacing(0),
        transform: 'scale(0.9)',
        paddingBottom: theme.spacing(0.75)
      },
      '& .swiper-slide': {
        height: '100%',
        [theme.breakpoints.down('sm')]: {
          padding: theme.spacing(0.875)
        }
      }
    },
    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(2, 1)
    },
    '& .cta-wrap': {
      '& button': {
        '& span': {
          [theme.breakpoints.down('md')]: {
            display: 'none'
          }
        }
      }
    }
  },
  faqWrap: {
    margin: theme.spacing(2, 0),
    background: `url(${socialCasinoBg2})`,
    backgroundSize: 'cover',
    backgroundPosition: 'top center',
    backgroundRepeat: 'no-repeat',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 1),
      margin: theme.spacing(0, 0)
    },
    '& .accordian-wrap': {
      '& .MuiPaper-root': {
        backgroundColor: 'transparent !important',
        border: `1px solid ${theme.colors.accordianBorder}`,
        borderRadius: `${theme.spacing(1.25)} !important`,
        marginBottom: theme.spacing(1),
        '&.Mui-expanded': {
          borderColor: theme.colors.YellowishOrange
        },

        '& .MuiAccordionSummary-root': {
          padding: theme.spacing(1.1, 1.25),
          position: 'relative',
          '& .MuiAccordionSummary-content': {
            margin: '0',
            '& .MuiTypography-root': {
              fontSize: theme.spacing(1.375),
              fontWeight: theme.typography.fontWeightExtraBold,
              color: theme.colors.textWhite,
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(1.125),
                width: '90%'
              }
            },
            '&.Mui-expanded': {
              '& .MuiTypography-root': {
                color: theme.colors.YellowishOrange
              }
            }
          },
          '& .MuiAccordionSummary-expandIconWrapper': {
            position: 'absolute',
            right: theme.spacing(2),
            transform: 'rotate(90deg)',
            color: theme.colors.YellowishOrange,

            '&.Mui-expanded': {
              transform: 'rotate(-90deg)'
            },
            '& svg': {
              width: theme.spacing(1.125),
              height: theme.spacing(1.125)
            }
          }
        },
        '& .MuiAccordionDetails-root': {
          padding: theme.spacing(1.5, 2),
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(1)
          },
          '& .MuiTypography-root': {
            color: theme.colors.textWhite,
            fontSize: theme.spacing(1.25),
            lineHeight: theme.spacing(1.75),
            fontWeight: theme.typography.fontWeightMedium,
            marginBottom: theme.spacing(1),
            '& a': {
              color: theme.colors.YellowishOrange,
              textDecoration: 'none',
              padding: theme.spacing(0, 0.2)
            }
          }
        }
      }
    }
  }
}))
