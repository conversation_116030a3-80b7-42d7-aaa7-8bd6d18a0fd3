import React, { useEffect, useState } from 'react'

const TrustpilotWidget = () => {
  const [scriptLoaded, setScriptLoaded] = useState(false)

  useEffect(() => {
    const loadTrustpilotScript = () => {
      if (
        !document.querySelector('script[src="https://widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js"]')
      ) {
        const script = document.createElement('script')
        script.src = 'https://widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js'
        script.async = true
        script.defer = true
        script.onload = () => {
          setScriptLoaded(true)
        }
        document.body.appendChild(script)
      } else {
        setScriptLoaded(true)
      }
    }

    loadTrustpilotScript()

    return () => {
      const script = document.querySelector(
        'script[src="https://widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js"]'
      )
      if (script) {
        document.body.removeChild(script)
      }
    }
  }, [])

  useEffect(() => {
    if (scriptLoaded && window.Trustpilot) {
      window.Trustpilot.loadFromElement(document.getElementById('trustpilot-widget'))
    }
  }, [scriptLoaded])

  return (
    <div
      id='trustpilot-widget'
      className='trustpilot-widget'
      data-locale='en-US'
      data-template-id='539ad0ffdec7e10e686debd7'
      data-businessunit-id='660b5b2ed7e83f40c6b3fbbb'
      data-style-height='330px'
      data-style-width='100%'
      data-theme='dark'
      data-tags='LandingPage'
      data-stars='5'
      data-review-languages='en'
    >
      <a href='https://www.trustpilot.com/review/themoneyfactory.com' target='_blank' rel='noreferrer'>
        Trustpilot
      </a>
    </div>
  )
}

export default TrustpilotWidget
