export const AGE_RESTRICTION_19 = [ '2', '31']

export const stateListConst = (data) => {
  const tempStateList = data?.filter((item) => {
    item.label = item.name
    item.value = item.state_id
    return true
  })
  return tempStateList || []
}

export const CityListConst = (data) => {
  const tempCityList = data?.filter((item) => {
    item.label = item.name
    item.value = item.city_id
    return true
  })
  return tempCityList || []
}

export const CryptoCurrencyConst= [
  { value: 'BTC', label: 'Bitcoin' },
  { value: 'ETH', label: 'Ethereum' },
  { value: 'USDT', label: 'Tether (ERC20)' },
  { value: 'USDT_TRC20', label: 'Tether (TRC20)' },
  { value: 'USDC', label: 'USD Coin (ERC20)' },
  { value: 'USDC_TRC20', label: 'USD Coin (TRC20)' },
];

export const limitType = {  
  1:'daily',
  2:'weekly',
  3:'monthly'
}