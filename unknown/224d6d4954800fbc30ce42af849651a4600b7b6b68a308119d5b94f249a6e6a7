import { createTheme } from '@mui/material'

import { colors } from './colors/index'
export const theme = createTheme({
  spacing: (factor) => `${1 * factor}rem`,
  components: {
    MuiDrawer: {
      styleOverrides: {
        paper: {
          background: colors.darkNavyBlue,
          color: colors.white,
          fill: colors.white
        }
      }
    },

    MuiCssBaseline: {
      styleOverrides: {
        body: {
          '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
            width: '0.4em',
            height: '0.2em'
          },
          '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
            backgroundColor: colors.grey500,
            height: '50px',
            border: '6px solid transparent',
            borderRadius: '15px'
          },
          '&::-webkit-scrollbar-thumb:focus, & *::-webkit-scrollbar-thumb:focus': {
            backgroundColor: colors.grey500
          },
          '&::-webkit-scrollbar-thumb:active, & *::-webkit-scrollbar-thumb:active': {
            backgroundColor: colors.grey500
          },
          '&::-webkit-scrollbar-thumb:hover, & *::-webkit-scrollbar-thumb:hover': {
            backgroundColor: '#555'
          },
          '&::-webkit-scrollbar-corner, & *::-webkit-scrollbar-corner': {
            backgroundColor: colors.grey500
          }
        }
      }
    }
  },

  typography: {
    button: {
      textTransform: 'none'
    },

    // fontFamilyOne:      'Rajdhani, sans-serif',
    // fontFamilyTwo:'Raleway, sans-serif',
    // fontFamilyThree:'Marlide Display',
    fontFamily: [
      'Rajdhani, sans-serif'



    ],
    fontWeightRegular: 300,
    fontWeightMedium: 400,
    fontWeightSemiBold: 500,
    fontWeightBold: 600,
    fontWeightExtraBold: 700,
    fontWeightBoldBlack: 800
  },
  border: {
    primaryBorder: '#49C7F4',
    secondaryBorder: 'rgba(145, 184, 194, 0.15)',
    darkBorder: 'rgba(0, 72, 100, 0.50)',
    dark: '#253539',
    active: '#33c16c',
    green: '#55FA97',
    grey: '#4d575b',
    transparent: 'transparent'
  },

  shadows: [
    'none',
    '0px -4px 2px 0px rgba(0, 0, 0, 0.25) inset',
    '0px 6px 6px rgba(0, 0, 0, 0.25)',
    "1px 0px 12px 8px rgb(255 151 77 / 50%)",
    "3.500623941421509px 3.500623941421509px 12.252184px rgba(203, 184, 186, 0.20)",
    "0.5px 3.50062px 7px 0px rgba(203, 184, 186, 0.14)",
    "0px 1px 1px #D28138",
    "3px 0px 4px 0px rgba(0, 0, 0, 0.25)",
    "0px 0px 18px 0px rgba(255, 190, 82, 0.60)",
    "0px 2px 1px 0px rgba(255, 255, 255, 0.25) inset, 0px 0px 7px 1px #076220",
    "0px 0px 36.88px 0px rgba(0, 0, 0, 0.29)",
    "0px 2px 2px rgb(122 119 119 / 46%)",
    "0px 2px 2px rgb(0 0 0 / 41%)",
    "0px 6px 10px 0px #FF781633",
    "0px 3px 6px 0px #FFA53840",
    "3px 4px 6px 0px #00000073 inset",
    "0px 3px 6px 0px #FFA53833",
    "0px 4px 4px 0px #00000040",
    "0px 4px 4px #00000059",
    "0px 4px 4px 0px #FDB72E66",
    "inset 0px 4px 4px 0px #00000008, 0px 4px 4px 0px #63626217",
    "0px 4px 4px 0px #FDB72E66, inset 0px 4px 3px 0px #00000066",
    '1px 4px 0px rgba(0, 0, 0, 0.6)',
    "0px 3.75px 3.75px 0px #00000099",
    "0px 0px 3.76px 0.94px inset #FFA53899",
    "3px 3px 10px 0px rgba(0,0,0,0.5)",
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null
  ],
  drawer: {
    width: 240
  },
  colors: colors,
  drawerWidth: 300,

  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },
})
