import React, { useEffect, useState } from 'react'
import { Box, Typography } from '@mui/material'
import MaintananceBg from '../../components/ui-kit/icons/webp/maintance-bg.webp'
import logoutBg from '../../components/ui-kit/icons/webp/maintanance-logout.webp'
import closeIcon from '../../components/ui-kit/icons/png/sidebar-cross.png'
import { usePortalStore } from '../../store/userPortalSlice'

const style = {
  maxWidth: 300,
  backgroundImage: `url(${MaintananceBg})`,
  backgroundSize: 'cover',
  borderRadius: '10px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  padding: '5rem 1rem',
  '& img': {
    maxWidth: '180px',
    width: '100%'
  },
  '& p': {
    fontSize: '1.25rem',
    fontWeight: '600',
    marginTop: '1.5rem',
    lineHeight: '1.1',
    textAlign: 'center',
    color: '#fff'
  },
  '& .close-icon': {
    position: 'absolute',
    top: '1rem',
    right: '1rem',
    cursor: 'pointer',
    width: '0.875rem'
  }
}

export default function BasicModal({ initialMinutes }) {
  // Store time in seconds
  const [remainingTime, setRemainingTime] = useState(Math.floor(initialMinutes * 60))
  const portalStore = usePortalStore((state) => state)

  useEffect(() => {
    const interval = setInterval(() => {
      setRemainingTime((prev) => {
        if (prev <= 1) {
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000) // update every second

    return () => clearInterval(interval)
  }, [])

  const handleClose = () => {
    portalStore.closePortal()
  }

  const minutes = Math.floor(remainingTime / 60)

  return (
    <div>
      <Box sx={style} aria-labelledby='modal-modal-title' aria-describedby='modal-modal-description'>
        {minutes > 1 && <img className='close-icon' onClick={handleClose} src={closeIcon} alt='Close' />}
        <img src={logoutBg} alt='Logout' />
        <Typography variant='body1'>
          System maintenance will begin in <strong>{minutes}</strong> minutes.
          {minutes >= 2 && ' Please log out to prevent any interruptions.'}
        </Typography>
      </Box>
    </div>
  )
}
