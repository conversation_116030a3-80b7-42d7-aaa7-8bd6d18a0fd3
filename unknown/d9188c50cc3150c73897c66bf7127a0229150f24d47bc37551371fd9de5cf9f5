import { useMutation, useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import {
  getTournament, getTournamentDetail, joinTournament
} from '../utils/apiCalls'

const getTournamentQuery = ({ params, successToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_TOURNAMENT, params?.coinType],
    queryFn: () => {
      return getTournament(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    }
  })
}

const getTournamentDetailQuery = ({ enabled, params, successToggler, errorToggler, }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_TOURNAMENT_DETAIL],
    queryFn: () => {
      return getTournamentDetail(params?.tournamentId)
    },
    enabled,
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const useJoinTournamentMutation = ({ successJoinToggler, errorJoinToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.JOIN_TOURNAMENT],
    mutationFn: (data) => joinTournament(data),
    onSuccess: (data) => {
      successJoinToggler && successJoinToggler(data)
    },
    onError: (error) => {
      errorJoinToggler && errorJoinToggler(error)
    }
  })
}

export const tournamentQuery = {
  getTournamentQuery,
  getTournamentDetailQuery,
  useJoinTournamentMutation,
  }
export default tournamentQuery
