import { useState, useEffect } from "react";

const useSeon = () => {
  const [sessionLogin<PERSON>ey, setSessionLogin<PERSON>ey] = useState("");

  useEffect(() => {
    const loadSeonScript = () => {
      return new Promise((resolve, reject) => {
        if (window.seon) {
          resolve(); // SEON already loaded
          return;
        }

        const script = document.createElement("script");
        script.src = "https://cdn.seonintelligence.com/js/v6/agent.umd.js";
        script.defer = true;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error("Failed to load SEON script"));
        document.body.appendChild(script);
      });
    };

    const initSeon = async (config) => {
      
      try {
        if (!window.seon) throw new Error("SEON is not available");
        
        window.seon.init();
        const session = await window.seon.getSession(config);
        setSessionLoginKey(session);
      } catch (error) {
        console.error("Error initializing SEON session:", error);
      }
    };

    loadSeonScript().then(() => {
      if ("geolocation" in navigator) {
        navigator.permissions.query({ name: "geolocation" }).then((permissionStatus) => {
          const geolocationConfig =
            permissionStatus.state === "granted"
              ? { canPrompt: true, enabled: true, maxAgeSeconds: 0 }
              : { canPrompt: false };

          initSeon({
            geolocation: geolocationConfig,
            networkTimeoutMs: 2000,
            fieldTimeoutMs: 2000,
            region: "eu",
            silentMode: true,
          });
        });
      } else {
        initSeon({
          geolocation: { canPrompt: false },
          networkTimeoutMs: 2000,
          fieldTimeoutMs: 2000,
          region: "eu",
          silentMode: true,
        });
      }
    });
  }, []);

  return sessionLoginKey;
};

export default useSeon;