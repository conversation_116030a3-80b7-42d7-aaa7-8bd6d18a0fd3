import React, { useEffect, useState } from 'react'
import moment from 'moment'
import { Box, Typography, Grid } from '@mui/material'
import CircularProgressbar from '../ CircularProgressbar'
import LiveCounterBg from '../../../components/ui-kit/icons/svg/live-counter-bg.svg'
/* eslint-disable multiline-ternary */

const GiveAwayCountDownTimer = ({ startDateTime, endDateTime, isActive }) => {
  const countTimeLeft = (startDateTime, endDateTime, duration) => {
    const totalDays = moment.duration(moment.utc(endDateTime).diff(moment.utc(startDateTime))).asDays()

    const percentageDays = (duration.days() / totalDays) * 100
    const percentageHours = (duration.hours() / 24) * 100
    const percentageMinutes = (duration.minutes() / 60) * 100
    const percentageSeconds = (duration.seconds() / 60) * 100

    return { percentageDays, percentageHours, percentageMinutes, percentageSeconds }
  }

  // Function to calculate remaining time
  const calculateTimeLeft = () => {
    const currentTime = moment.utc().startOf('second')
    const startTime = moment.utc(startDateTime).startOf('second')
    const endTime = moment.utc(endDateTime).startOf('second')

    if (currentTime.isBefore(startTime)) {
      const duration = moment.duration(startTime.diff(currentTime))
      const { percentageDays, percentageHours, percentageMinutes, percentageSeconds } = countTimeLeft(
        startDateTime,
        endDateTime,
        duration
      )
      return { duration, status: 'upcoming', percentageDays, percentageHours, percentageMinutes, percentageSeconds }
    }

    if (currentTime.isSameOrAfter(startTime) && currentTime.isBefore(endTime)) {
      const duration = moment.duration(endTime.diff(currentTime))
      const { percentageDays, percentageHours, percentageMinutes, percentageSeconds } = countTimeLeft(
        startDateTime,
        endDateTime,
        duration
      )
      return { duration, status: 'live', percentageDays, percentageHours, percentageMinutes, percentageSeconds }
    }

    if (currentTime.isAfter(endTime)) {
      return {
        duration: moment.duration(0),
        status: 'ended',
        percentageDays: 0,
        percentageHours: 0,
        percentageMinutes: 0,
        percentageSeconds: 0
      }
    }
  }

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    // Clear interval on component unmount
    return () => clearInterval(timer)
  }, [startDateTime, endDateTime])

  // Render the countdown based on the current state
  const renderCountdown = () => {
    if (!timeLeft) return null
    const { duration, status, percentageDays, percentageHours, percentageMinutes, percentageSeconds } = timeLeft
    return (
      <>
        {/* new timer  */}
        <Box className='giveaway-counter-wrap'>
          <Grid
            className={`giveaway-badge ${
              status === 'upcoming' ? 'upcoming-badge' : status === 'live' ? 'live-badge' : 'ended-badge'
            }`}
          >
            <span></span> {status === 'upcoming' ? 'Upcoming' : status === 'live' ? 'Live' : 'Ended'}
          </Grid>
          <Grid className='giveaway-counter-content'>
            {status === 'live' ? (
              <Grid className='live-counter-bg'>
                <img src={LiveCounterBg} alt='Live badge' />
                <Grid className='live-counter-text'>
                  <Typography variant='h4'>{duration.days() < 10 ? '0' + duration.days() : duration.days()}</Typography>
                  <Typography>Days</Typography>
                </Grid>
              </Grid>
            ) : (
              <Grid className='giveaway-counter-text'>
                <Grid className='giveaway-counter'>
                  <Grid className='giveaway-counter-progress'>
                    <CircularProgressbar
                      daysLeft={duration.days() < 10 ? '0' + duration.days() : duration.days()}
                      percentage={percentageDays}
                    />
                  </Grid>
                </Grid>
                <Typography variant='h4'>Days</Typography>
              </Grid>
            )}

            {status === 'live' ? (
              <Grid className='live-counter-bg'>
                <img src={LiveCounterBg} alt='Live badge' />
                <Grid className='live-counter-text'>
                  <Typography variant='h4'>
                    {' '}
                    {duration.hours() < 10 ? '0' + duration.hours() : duration.hours()}
                  </Typography>
                  <Typography>Hours</Typography>
                </Grid>
              </Grid>
            ) : (
              <Grid className='giveaway-counter-text'>
                <Grid className='giveaway-counter'>
                  <Grid className='giveaway-counter-progress'>
                    <CircularProgressbar
                      daysLeft={duration.hours() < 10 ? '0' + duration.hours() : duration.hours()}
                      percentage={percentageHours}
                    />
                  </Grid>
                </Grid>
                <Typography variant='h4'>Hours</Typography>
              </Grid>
            )}

            {status === 'live' ? (
              <Grid className='live-counter-bg'>
                <img src={LiveCounterBg} alt='Live badge' />
                <Grid className='live-counter-text'>
                  <Typography variant='h4'>
                    {' '}
                    {duration.minutes() < 10 ? '0' + duration.minutes() : duration.minutes()}
                  </Typography>
                  <Typography>Minutes</Typography>
                </Grid>
              </Grid>
            ) : (
              <Grid className='giveaway-counter-text'>
                <Grid className='giveaway-counter'>
                  <Grid className='giveaway-counter-progress'>
                    <CircularProgressbar
                      daysLeft={duration.minutes() < 10 ? '0' + duration.minutes() : duration.minutes()}
                      percentage={percentageMinutes}
                    />
                  </Grid>
                </Grid>
                <Typography variant='h4'>Minutes</Typography>
              </Grid>
            )}

            {status === 'live' ? (
              <Grid className='live-counter-bg'>
                <img src={LiveCounterBg} alt='Live badge' />
                <Grid className='live-counter-text'>
                  <Typography variant='h4'>
                    {' '}
                    {duration.seconds() < 10 ? '0' + duration.seconds() : duration.seconds()}
                  </Typography>
                  <Typography>Seconds</Typography>
                </Grid>
              </Grid>
            ) : (
              <Grid className='giveaway-counter-text'>
                <Grid className='giveaway-counter'>
                  <Grid className='giveaway-counter-progress'>
                    <CircularProgressbar
                      daysLeft={duration.seconds() < 10 ? '0' + duration.seconds() : duration.seconds()}
                      percentage={percentageSeconds}
                    />
                  </Grid>
                </Grid>
                <Typography variant='h4'>Seconds</Typography>
              </Grid>
            )}
          </Grid>
        </Box>
      </>
    )
  }

  return renderCountdown()
}

export default GiveAwayCountDownTimer
