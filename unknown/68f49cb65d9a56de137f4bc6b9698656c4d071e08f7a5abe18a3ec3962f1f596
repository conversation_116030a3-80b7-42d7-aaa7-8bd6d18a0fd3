import { makeStyles } from '@mui/styles'

import { socailSectionBg } from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  SocialSection: {
    padding: theme.spacing(4.0625, 0, 5),
    marginTop: theme.spacing(-5),
    backgroundImage: `url(${socailSectionBg})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 0)
    },
    '& .socail-section-content': {
      maxWidth: theme.spacing(71.25),
      margin: '0 auto',
      [theme.breakpoints.down('lg')]: {
        padding: theme.spacing(0, 1)
      },
      '& .social-card': {
        marginBottom: theme.spacing(1),
        '& .social-card-icon-wrap': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          left: theme.spacing(-5),
          top: theme.spacing(0.625),
          [theme.breakpoints.down('md')]: {
            left: 0,
            top: theme.spacing(1.25),
            justifyContent: 'start'
          },
          [theme.breakpoints.down('sm')]: {
            paddingLeft: theme.spacing(1.25)
          },
          '& > img': {
            width: theme.spacing(11.25),
            height: theme.spacing(11.25),
            aspectRatio: '1/1',
            objectFit: 'contain',
            opacity: '0.5',
            [theme.breakpoints.down('md')]: {
              width: theme.spacing(6.25),
              height: theme.spacing(6.25)
            }
          },
          '& .social-card-graphic': {
            position: 'absolute',
            transform: 'translate(-50%, -50%)',
            left: '71%',
            top: theme.spacing(4.375),
            [theme.breakpoints.down('md')]: {
              top: theme.spacing(2.5),
              left: '50%'
            },
            '& img': {
              width: theme.spacing(6.25),
              height: theme.spacing(6.25),
              aspectRatio: '1/1',
              objectFit: 'contain',
              [theme.breakpoints.down('md')]: {
                width: theme.spacing(5),
                height: theme.spacing(5)
              }
            },
            '&:before': {
              position: 'absolute',
              content: "''",
              left: '0',
              top: '0',
              background: theme.colors.YellowishOrange,
              height: theme.spacing(3.125),
              width: theme.spacing(3.125),
              borderRadius: '100%',
              opacity: '0.2',
              [theme.breakpoints.down('md')]: {
                background: theme.colors.iconEffect
              }
            }
          }
        },
        '& h4': {
          fontSize: theme.spacing(1.75),
          fontWeight: '800',
          marginBottom: theme.spacing(1),
          [theme.breakpoints.down('md')]: {
            textAlign: 'center'
          }
        },
        '& p': {
          fontSize: theme.spacing(1.25),
          fontWeight: '300'
        }
      }
    },
    '& .btn-wrap': {
      paddingTop: theme.spacing(2),
      [theme.breakpoints.down('sm')]: {
        flexDirection: 'column',
        alignItems: 'center',
        '& .btn': {
          width: 'auto'
        }
      }
    }
  }
}))
