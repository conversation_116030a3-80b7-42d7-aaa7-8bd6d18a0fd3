import js from '@eslint/js'
import pluginImport from 'eslint-plugin-import'
import pluginUnusedImports from 'eslint-plugin-unused-imports'

export default [
  {
    ignores: [
      'dist/',
      'node_modules/',
      'src/icons/legacy-icon.js',
      'src/components/ui-kit/icons/svg/index.js',
      'src/components/ui-kit/icons/webp/index.js'
    ]
  },
  js.configs.recommended,
  {
    languageOptions: {
      globals: {
        window: 'readonly',
        document: 'readonly',
        navigator: 'readonly',
        console: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        requestAnimationFrame: 'readonly',
        cancelAnimationFrame: 'readonly',
        optimoveSDK: 'readonly',
        _CaptainUpWidget: 'readonly',
        localStorage: 'readonly',
      },
      ecmaVersion: 2021,
      sourceType: 'module'
    },
    plugins: {
      import: pluginImport,
      'unused-imports': pluginUnusedImports
    },
    rules: {
      'import/order': [
        'error',
        {
          groups: ['builtin', 'external', 'internal', ['parent', 'sibling', 'index']],
          'newlines-between': 'always',
          alphabetize: {
            order: 'asc',
            caseInsensitive: true
          }
        }
      ],
      // Disable default no-unused-vars
      'no-unused-vars': 'off',
      // Use the unused-imports version instead
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_'
        }
      ]
    }
  }
]
