import React, { useEffect } from 'react'
import { handleLogout } from './network/services/user.services'
import { useUserStore } from './store/useUserSlice'
import { deleteCookie } from './utils/cookiesCollection'
import { openErrorToaster } from './network/helper/toaster.helpers'
import errorMessages from './network/messages/errorMessages'
import { usePortalStore } from './store/store'
import useAuthStore from './store/useAuthStore'

const ReferralWrapper = ({ children }) => {
  const isAuthenticate = useUserStore((state) => state.isAuthenticate)
  const { setPathCookieCheck } = useAuthStore()

  const portalStore = usePortalStore((state) => state)
  useEffect(() => {
    if (!isAuthenticate) {
      deleteCookie('path')
      setPathCookieCheck(false)
      handleLogout()
      openErrorToaster(errorMessages.unAuthorized)
      portalStore.closePortal()
    }
    // deleteCookie('path')
    // setPathCookieCheck(false)
  }, [isAuthenticate])

  return <>{children}</>
}

export default ReferralWrapper
