import { openErrorToaster } from './toaster.helpers'
// import { getTranslatedErrorMessage } from '../helper/translation.helpers'
import errorMessages from '../messages/errorMessages'
import { handleLogout } from '../services/user.services'

const handleCriticalErrorCodes = (errors) => {
  let hasCriticalErrors = false
  errors.forEach((error) => {
    if (error?.errorCode === 3008 || error?.errorCode === 3082) {
      handleLogout()
      openErrorToaster(error?.description)
      hasCriticalErrors = true
    }
  })
  return hasCriticalErrors
}

const handleExceptionFailed = (errors) => {
  let exceptionHandled = false
  errors.forEach((error) => {
    if (error?.description && error.errorCode === 3074) {
      openErrorToaster(error?.description)
      exceptionHandled = true
    }
  })
  return exceptionHandled
}

export const handleApiErrors = (error) => {
  if (error?.code === 'ERR_NETWORK') {
    openErrorToaster(error?.message)
  } else {
    const { errors } = error?.response?.data || []
    if (errors.length > 0) {
      const hasCriticalErrors = handleCriticalErrorCodes(errors)
      let exceptionHandled = false
      if (!hasCriticalErrors && error.response?.status === 417) {
        exceptionHandled = handleExceptionFailed(errors)
      }
      if (!hasCriticalErrors && !exceptionHandled) {
        const apiErrorCode = errors[0].errorCode
        const apiErrorDescription = errors[0].description
        if (apiErrorCode) {
          // const errorMessage = getTranslatedErrorMessage(apiErrorCode) || apiErrorDescription || errorMessages.internalServerError;
          const errorMessage = apiErrorDescription || errorMessages.internalServerError
          openErrorToaster(errorMessage)
        }
      }
    }
  }
  //   return Promise.reject(error.response.data.errors)
  return Promise.reject(error)
}
