// import {toast} from 'react-hot-toast';

// export const openSuccessToaster = (message) => {
//     toast.success(message);
// }

// export const openErrorToaster = (message) => {
//   toast.error(message);
// }

import TagManager from 'react-gtm-module'
import { toast } from 'react-hot-toast'

const activeToasts = new Set() // Track currently active toasts

export const openSuccessToaster = (message) => {
  const toastId = `success-${message}`
  if (!activeToasts.has(toastId)) {
    activeToasts.add(toastId)
    toast.success(message, {
      id: toastId
    })
    TagManager.dataLayer({
      dataLayer: {
        event: 'toast',
        event_type: 'success-toast',
        message: message
      }
    })
    activeToasts.delete(toastId)
  }
}

export const openErrorToaster = (message) => {
  const toastId = `error-${message}`
  if (!activeToasts.has(toastId)) {
    activeToasts.add(toastId)
    toast.error(message, {
      id: toastId
    })
    TagManager.dataLayer({
      dataLayer: {
        event: 'toast',
        event_type: 'error-toast',
        message: message
      }
    })
    activeToasts.delete(toastId)
  }
}
