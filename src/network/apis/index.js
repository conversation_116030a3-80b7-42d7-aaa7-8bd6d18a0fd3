
import { METHODS } from "../constants";
import { microServices,clients } from "./microservice"

const makeRequest =  async(url, method, data = {}, params = {}, headers = null) => {
  if (!headers) {
    headers = {
      'Content-Type': 'application/json'
    }
  }
  const server = microServices.SERVICE_URL_1;

  if (method === METHODS.delete) {
    return clients[server][method](url, { data, headers });
  }

  return clients[server][method](
    url,
    method === METHODS.get ? { headers, params } : data,  
    { headers, params }  
  );
}




export default makeRequest;