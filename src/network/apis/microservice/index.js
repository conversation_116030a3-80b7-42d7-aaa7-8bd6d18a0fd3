import axios from 'axios'

import config from '../../../config/app.config'
import requestInterceptor from '../../interceptors/request.interceptor'
import responseInterceptor from '../../interceptors/response.interceptor'

const clients = {}
const microServices = {}

// Define multiple service URLs
const microServicesURLs = {
  SERVICE_URL_1: `${config.apiGateways.BASE_URL_1}`,
}

// Create Axios client
const axiosClient = (baseUrl, config) => axios.create({
  baseURL: baseUrl,
  ...config
})

// Loop through baseUrls to create axios instances
for (const key in microServicesURLs) {
 
  const axiosInstance = axiosClient(microServicesURLs[key], {
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json'
    },
    withCredentials: true  // Add global settings for all instances
  })
  microServices[key] = key;
  clients[key] = axiosInstance;

  // Apply request and response interceptors
  requestInterceptor(axiosInstance)
  responseInterceptor(axiosInstance)
}

// Export clients and microServices
export {
  clients,
  microServices
}
