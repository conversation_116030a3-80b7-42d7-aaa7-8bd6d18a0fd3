const errorMessages = {
  internalServerError: 'Internal Server Error',
  unAuthorized: 'Unauthorized access. Please log in again.',
  usernameOrEmailNotMatch: 'Username/Email or Password does not match',
  userAlreadyExists: 'User already exists',
  userNotExists: 'User does not exists',
  forbiddenError: 'User was logged out due to some reason, please login again',
  PgeNotFound: 'Something went wrong',
  requestTimeOut: 'Request Timeout',

  RequestInputValidationError: 'Please check the request data',
  ResponseInputValidationError: 'Response validation failed please refer json schema of response',
  SocketRequestInputValidationError: 'Please check the request data',
  SocketResponseValidationError: 'Response validation of socket failed please refer json schema of response',
  InternalServerError: 'Internal Server Error',
  InvalidSocketArgumentError: 'Please provide, proper arguments eventName, [payloadObject, and [callback',
  EmailExistErrorType: 'Email already exists.',
  UnAuthorize: 'Unauthorized ',
  InvalidToken: 'Either access token not passed or it is expired',
  UserNotExists: 'User does not exists',
  EmailAlreadyVerified: 'Email already verified',
  DocumentUploadError: 'Document Upload Error',
  PhoneExistErrorType: 'This phone number is already associated with another account. Please use a different number.',
  UserBelow18ErrorType: 'User Below 18 Year Error',
  PhoneVerificationFailedErrorType: 'Wrong code. Please try again.',
  PhoneAlreadyVerifiedErrorType: 'Phone Already Verified Error',
  OtpSendErrorType: 'Otp Send Error',
  EmailNotVerifiedErrorType: 'Email Not Verified Error',
  ResetPasswordTokenErrorType: 'Reset Password Token Error',
  NewPasswordAndPasswordSameErrorType: 'New Password And Password Equal Error',
  InvalidPhoneOrCodeErrorType: 'Please use a valid, permanent phone number.',
  InvalidOtpErrorType: 'The Code you entered is invalid. Please try again.',
  NumberUnverifiedErrorType: 'Unverified Phone Number',
  InvalidNumberErrorType: 'Invalid Phone Number',
  InvalidId: 'Id must be a number',
  FileSizeTooLargeErrorType: 'File Size Too Large',
  FileTypeNotSupportedErrorType: 'File Type Not Supported',
  FileNotFound: 'File is required',
  GameNotFound: 'Game not found',
  ProviderNotFound: 'Provider not found',
  BonusNotFound: 'Bonus not found',
  PhoneNotVerified: 'Phone Not Verified',
  BonusAlreadyClaimed: 'Bonus Already Claimed',
  InvalidDateClaimed: 'End Date Must Be Greater Than Start Date',
  InvalidOldPassword: 'Invalid Old Password',
  WrongRequestInFavorite: 'Wrong Request',
  TokenRequired: 'access token required for socket connection',
  TransactionBankingError: 'something went wrong',
  MinimumBalanceError: 'Coins should be more than minimum redeemable coins',
  ProviderInactiveError: 'Casino Provider Is Inactive',
  KycRequiredError: 'Account verification is required',
  RedeemableCoinsError: 'Insufficient redeemable coins',
  EmailVerificationError: 'Wrong code. Please try again.',
  ResendVerificationMailError: 'Resend Verification Mail Failed',
  AccessDenied: 'Platform not available from your region',
  EmailNotVerified: 'Email is not verified',
  EmailOtpVerificationError: 'Wrong code. Please try again.',
  UserNameExistErrorType: 'UserName already Exist',
  UserEmailNotExistErrorType: 'Email not found. Please try again.',
  MaximumResendAttemptsExceededError: 'Maximum resend attempts exceeded. Please try again later.',
  PaysafeError: 'Paysafe Error',
  FirebaseUserEMailNotFoundError: 'Firebase user email not found Error.',
  FirebaseVerificationLinkGenerateError: 'Firebase verification link generate error.',
  FirebaseVerificationLinkNotSentError: 'Firebase verification link not sent error.',
  SendEmailError: 'Failed to send verification mail',
  PasswordValidationFailedError:
    'Your password should contain at least 8 characters, one upper case letter, one lower case letter, one number, and one special character.',
  VerifyEmailTokenErrorType: 'Verify Email Token Error',
  SendSmsError: 'Failed to send verification SMS',
  TermsAndConditionError: 'Terms and Condition is not accepted',
  LoginEmailError: 'Email address or Phone not found',
  LoginPasswordError: 'Incorrect Password',
  UserNameRequiredErrorType: 'UserName is Required',
  PostalCodeNotExistError: 'Postal Code Not Exist ',
  SomethingWentWrong: 'Something went wrong',
  DepositLimitReached: 'Deposit limit reached',
  RequestNotFound: 'Redeem request not found.',
  InvalidFile: 'Invalid File.',
  UserUidExistErrorType: 'User already exists.',
  LimitTypeOrAmountRequire: 'limitType or amount require for time and purchase type',
  SelfExclusionRequire: 'selfExclusion require self exclusion type',
  SessionReminderTimeRequire: 'sessionReminder require for session type',
  TimeBreakDurationRequire: 'timeBreakDuration require for session TIME BREAK type',
  MaxPasswordAttemptError: 'Maximum Password Attempts Exceeded. Please Contact Admin for Assistance.',
  UserAccountBannedError: 'Your Account is Banned. Please Contact Admin for Resolution',
  UserAccountSelfExcludedError: 'Your Account is Self Excluded',
  SuccessPurchaseRequired: 'Successful package purchase required',
  MultiLogin: 'Already login in another device ',
  UserBan: 'You account is banned please contact to admin',
  UserNotExistRegisterFirstErrorType: 'User does not exist please Register first',
  UserInActive: 'Your Account is Inactive. Please Contact Admin for Resolution',
  ActionNotAllowedErrorType: 'Action not allowed',
  DailyLimitExceedsWeeklyLimit: 'Daily Limit Exceeds Weekly Limit',
  DailyLimitExceedsMonthlyLimit: 'Daily Limit Exceeds Monthly Limit',
  WeeklyLimitLessThanDailyLimit: 'Weekly Limit Less Than Daily Limit',
  WeeklyLimitExceedsMonthlyLimit: 'Weekly LimitExceeds Monthly Limit',
  MonthlyLimitLessThanDailyLimit: 'Monthly Limit Less Than Daily Limit',
  MonthlyLimitLessThanWeeklyLimit: 'Monthly Limit Less Than Weekly Limit',
  ResponsibleSettingNotFond: 'Responsible Gambling Setting Not Fond',
  UserExistWithSameDetailError: 'An account already exists with these details - please contact our Customer Support.',
  ExpiredOtp: 'Your OTP has been expired. Please try again.',
  UserSsnFailedError: 'User ssn request failed',
  DailyDepositTimeError: 'You can not update daily deposit limit upto 24 hrs',
  WeeklyDepositTimeError: 'You can not update weekly deposit limit upto 24 hrs',
  MonthlyDepositTimeError: 'You can not update monthly deposit limit upto 24 hrs',
  DailyBetTimeError: 'You can not update daily bet limit upto 24 hrs',
  WeeklyBetTimeError: 'You can not update weekly bet limit upto 24 hrs',
  MonthlyBetTimeError: 'You can not update monthly bet limit upto 24 hrs',
  RecordAlreadyExistsError: 'Record already exists',
  IncompleteProfileErrorTypeError: 'First complete your profile',
  ssnAlreadyApprovedError: 'First complete your profile',
  YouCannotChangeYourSsnAnymore: 'You have reached maximum limit of updating  your SSN.',
  UserSSNFailedErrorType: 'SSN verification failed.',
  SsnAlreadyInProgressError: 'SSN verification already in progress !',
  TimeBreakError: 'You are on time break',
  WaitTimeError: 'Please wait for 60 sec to resend OTP',
  KYCAlreadyApprovedError: 'Your Kyc is already verified !',
  kycVerificationAlreadyInProgressError: 'KYC verification already in progress !',
  RemoveTimeError: 'You can not remove limit upto 24 hrs',
  PatternDoesNotMatchError: 'Regex pattern check Failed!',
  SamePasswordError: "New and old password can't be same",
  PasswordDoesNotMatchError: "Password and confirm password doesn't match",
  PhoneNumberAlreadyExistError: 'Account associated with this phone number already exists.',
  UserNameDoesNotExistError: 'Please add a username to continue',
  InsufficientBalance: 'Insufficient Balance',
  PersonalBonusNotFound: 'Personal bonus not found',
  PersonalBonusAlreadyClaimed: 'Personal bonus already claimed',
  PersonalBonusNotClaimedByCreateUser: 'Personal bonus not claimed by create user',
  InvalidBonusCodeError: 'Invalid Bonus Code',
  AffiliatesNotExistsErrorType: 'Affiliate not exist error.',
  InvalidAffiliateCode: 'Invalid Affiliate Code or Affiliate is not active',
  EmailWithAffiliateApplied: 'Email with this Affiliate already apply',
  WithdrawRequestDoesNotExistErrorType: 'Withdraw request does not exists',
  TournamentAlreadyExistWithGivenTimeFrameErrorType: 'Tournament Not Exist Error',
  TournamentNotFoundErrorType: 'Tournament Not Found Error',
  TournamentNotExistErrorType: 'Tournament Not Exist ',
  UserAlreadyJoinedTournamentErrorType: 'User Already Joined Tournament',
  TournamentIsClosedForFurtherRegistrationsError: 'Player limit has been reached for this tournament.',
  TournamentFinishedError: 'Tournament is finished!',
  PasswordCannotBeSameAsOldPasswordError: 'Password Cannot Be Same As Old Password!',
  UserAssociatedWithSocialLoginErrorType: 'Please use social login to login!',
  GiveawaysNotFoundErrorType: 'Giveaways Not Found Error',
  NoAnyGiveawaysActive: 'No Any Giveaways Active Error',
  TemporaryEmailError: 'It looks like you’ve entered a temporary email. Please use a permanent email address.',
  MaximumRedeemableLimitReachedError:
    'Amount is higher than the maximum redeemable amount that can be requested at once.',
  InternalUsersCannotRedeemError: 'Test users cannot redeem from the platform.',
  FirstPurchaseBonusNotFound: 'First Purchase Bonus Not Found',
  FirstPurchaseBonusAlreadyClaimed: 'First Purchase Bonus Already Claimed',
  FirstDepositAlreadyDone: 'First Deposit Already Done',
  InvalidSignedRequest: 'Invalid signed request',
  PurchasedLimitReached: 'Purchase limit reached',
  WelcomePurchaseBonusExpired: 'Welcome purchase bonus expire',
  PromocodeNotExistError: 'Promocode not exists.',
  PromocodeAvailedLimitReachedError: 'Promocode max users availed limit reached ',
  PromocodePerUserLimitReachedError: 'Promocode per user limit reached ',
  PromocodeNotApplicableError: 'Promocode not applicable on this package',
  WelcomePurchasePackageIsOnlyAvailableOnceError: 'Welcome purchase package can only be purchased once.',
  PackageCoolDownPeriodError: 'Too many requests, please wait for few minutes before purchasing the package.',
  TransactionNotFoundError: 'Transaction not found!',
  YouCannotWithdrawMoreThanAllowedGcLimitError: 'You cannot withdraw more than allowed Gc limit Error',
  YouCannotWithdrawMoreThanAllowedScLimitError: 'You cannot withdraw more than allowed Sc limit Error',
  OtpVerificationFailedError: 'Otp Verification Failed Error',
  UserNotEnabledTwoFactorAuthenticationError: 'User Not Enabled 2FA for desposit or wihtdraw vault',
  PercentageValueShouldNotBeMoreThan100Error: 'Percentage Value Should Not Be More Than 100',
  IncorrectPasswordError: 'Incorrect Password',
  Enabled2faTokenMustBeRequiredError: 'Enabled 2fa Token Must Be Required',
  InsufficientScBalanceError: 'Insufficient Sc Balance Error',
  PasswordMustBeRequiredError: 'Password Must Be Required',
  Your2FACodeIsIncorrectError: 'Your 2FA Code Is Incorrect',
  UserTwoFactorAuthIsDisabledError: 'UserTwoFactorAuthIsDisabled',
  PasswordNotExistErrorType: 'Uses Password Not Exist Error',
  MaxAttemptForResetPasswordErrorType: 'Maximum Password Attempts Exceeded. Please Contact Admin for Assistance.',
  InvalidAmountError: 'Invalid Amount.',
  YouCannotDepositMoreThanAllowedGcLimitError: 'You Cannot Deposit More Than Allowed GcLimit.',
  YouCannotDepositMoreThanAllowedScLimitError: 'You Cannot Deposit More Than Allowed ScLimit Error.',
  MaxAttemptsReachedForPhoneVerification:
    'Max Attempts Reached For Phone Verification, please try after 10-15 minutes.',
  MaxResetPasswordAttemptErrorType: 'Maximum Password Attempts Exceeded. Please Contact Admin for Assistance.',
  EmailNotFoundError: 'Email is not registered, please signup!',
  RestrictedUserError: 'You are restricted to play games, please contact support for more information!',
  CmsNotFoundErrorType: 'Cms Not Found',
  ReferralBonusDoesNotExistForThisUserError: 'Referral Bonus Does Not Exist For This User!',
  ReferredUserIsNotExistError: 'Referred User Is Not Exist!',
  ReferralUserKycVerificationRequiredError: 'Referral User Kyc Verification Required!',
  ProfaneUserNameError: 'The username contains prohibited or inappropriate language.'
}

export default errorMessages
