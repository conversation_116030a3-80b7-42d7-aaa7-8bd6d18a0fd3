import errorMessages from './errorMessages'

const errorCodeMappedViaMessage = new Map([

  [3001, errorMessages.RequestInputValidationError],
  [3002, errorMessages.ResponseInputValidationError],
  [3003, errorMessages.SocketRequestInputValidationError],
  [3004, errorMessages.SocketResponseValidationError],
  [3005, errorMessages.InternalServerError],
  [3006, errorMessages.InvalidSocketArgumentError],
  [3007, errorMessages.EmailExistErrorType],
  [3008, errorMessages.UserInActive],
  [3009, errorMessages.UserInActive],
  [3010, errorMessages.UnAuthorize],
  [3011, errorMessages.UserInActive],
  [3012, errorMessages.InvalidToken],
  [3013, errorMessages.UserNotExists],
  [3014, errorMessages.EmailAlreadyVerified],
  [3015, errorMessages.DocumentUploadError],
  [3016, errorMessages.PhoneExistErrorType],
  [3017, errorMessages.UserBelow18ErrorType],
  [3018, errorMessages.PhoneVerificationFailedErrorType],
  [3019, errorMessages.PhoneAlreadyVerifiedErrorType],
  [3020, errorMessages.OtpSendErrorType],
  [3021, errorMessages.EmailNotVerifiedErrorType],
  [3022, errorMessages.ResetPasswordTokenErrorType],
  [3023, errorMessages.NewPasswordAndPasswordSameErrorType],
  [3024, errorMessages.InvalidPhoneOrCodeErrorType],
  [3025, errorMessages.InvalidOtpErrorType],
  [3025, errorMessages.ExpiredOtp],
  [3026, errorMessages.NumberUnverifiedErrorType],
  [3027, errorMessages.InvalidNumberErrorType],
  [3028, errorMessages.InvalidId],
  [3028, errorMessages.DailyDepositTimeError],
  [3028, errorMessages.WeeklyDepositTimeError],
  [3028, errorMessages.MonthlyDepositTimeError],
  [3028, errorMessages.DailyBetTimeError],
  [3028, errorMessages.WeeklyBetTimeError],
  [3028, errorMessages.MonthlyBetTimeError],
  [3028, errorMessages.RecordAlreadyExistsError],
  [3028, errorMessages.RemoveTimeError],
  [3029, errorMessages.FileSizeTooLargeErrorType],
  [3030, errorMessages.FileTypeNotSupportedErrorType],
  [3031, errorMessages.FileNotFound],
  [3032, errorMessages.GameNotFound],
  [3033, errorMessages.ProviderNotFound],
  [3034, errorMessages.BonusNotFound],
  [3035, errorMessages.PhoneNotVerified],
  [3036, errorMessages.BonusAlreadyClaimed],
  [3037, errorMessages.InvalidDateClaimed],
  [3038, errorMessages.InvalidOldPassword],
  [3039, errorMessages.WrongRequestInFavorite],
  [3039, errorMessages.TokenRequired],
  [3040, errorMessages.TransactionBankingError],
  [3041, errorMessages.MinimumBalanceError],
  [3042, errorMessages.ProviderInactiveError],
  [3043, errorMessages.KycRequiredError],
  [3044, errorMessages.RedeemableCoinsError],
  [3045, errorMessages.EmailVerificationError],
  [3046, errorMessages.ResendVerificationMailError],
  [3047, errorMessages.AccessDenied],
  [3048, errorMessages.AccessDenied],
  [3049, errorMessages.AccessDenied],
  [3050, errorMessages.EmailNotVerified], 
  [3051, errorMessages.EmailOtpVerificationError],  
  [3052, errorMessages.UserNameExistErrorType],  
  [3053, errorMessages.UserEmailNotExistErrorType],
  [3054, errorMessages.MaximumResendAttemptsExceededError],
  [3055, errorMessages.PaysafeError],
  [3056, errorMessages.FirebaseUserEMailNotFoundError],
  [3057, errorMessages.FirebaseVerificationLinkGenerateError],
  [3058, errorMessages.FirebaseVerificationLinkNotSentError],
  [3059, errorMessages.SendEmailError],
  [3060, errorMessages.PasswordValidationFailedError],
  [3061, errorMessages.VerifyEmailTokenErrorType],
  [3062, errorMessages.SendSmsError],
  [3063, errorMessages.TermsAndConditionError],
  [3063, errorMessages.UserExistWithSameDetailError],
  [3063, errorMessages.UserSsnFailedError],
  [3063, errorMessages.IncompleteProfileErrorTypeError],
  [3063, errorMessages.ssnAlreadyApprovedError],
  [3063, errorMessages.KYCAlreadyApprovedError],  
  [3064, errorMessages.LoginEmailError],
  [3065, errorMessages.LoginPasswordError],
  [3066, errorMessages.UserNameRequiredErrorType],
  [3067, errorMessages.BonusNotFound],
  [3067, errorMessages.YouCannotChangeYourSsnAnymore],
  [3068, errorMessages.PostalCodeNotExistError],
  [3068, errorMessages.UserSSNFailedErrorType],
  [3069, errorMessages.SomethingWentWrong],
  [3069, errorMessages.SsnAlreadyInProgressError],
  [3069, errorMessages.kycVerificationAlreadyInProgressError],
  [3070, errorMessages.DepositLimitReached],
  [3070, errorMessages.PatternDoesNotMatchError],
  [3071, errorMessages.RequestNotFound],
  [3071, errorMessages.SamePasswordError],
  [3172, errorMessages.InvalidFile],
  [3173, errorMessages.UserUidExistErrorType],
  [3174, errorMessages.LimitTypeOrAmountRequire],
  [3175, errorMessages.SelfExclusionRequire],
  [3176, errorMessages.SessionReminderTimeRequire],
  [3177, errorMessages.TimeBreakDurationRequire],
  [3178, errorMessages.MaxPasswordAttemptError],
  [3179, errorMessages.UserAccountBannedError],
  [3179, errorMessages.UserAccountSelfExcludedError],
  [3180, errorMessages.SuccessPurchaseRequired],
  [3081, errorMessages.MultiLogin],
  [3081, errorMessages.InvalidAffiliateCode],
  [3082, errorMessages.UserBan],
  [3082, errorMessages.EmailWithAffiliateApplied],
  [3082, errorMessages.WithdrawRequestDoesNotExistErrorType],
  [3183, errorMessages.UserNotExistRegisterFirstErrorType],  
  [3184, errorMessages.UserInActive],
  [3185, errorMessages.UserAccountBannedError],
  [3186, errorMessages.ActionNotAllowedErrorType],
  [3187, errorMessages.DailyLimitExceedsWeeklyLimit],
  [3187, errorMessages.DailyLimitExceedsMonthlyLimit],
  [3187, errorMessages.WeeklyLimitLessThanDailyLimit],
  [3187, errorMessages.WeeklyLimitExceedsMonthlyLimit],
  [3187, errorMessages.MonthlyLimitLessThanDailyLimit],
  [3187, errorMessages.MonthlyLimitLessThanWeeklyLimit],
  [3187, errorMessages.ResponsibleSettingNotFond],  
  [3170, errorMessages.TimeBreakError],
  [3171, errorMessages.WaitTimeError],
  [3072, errorMessages.PasswordDoesNotMatchError],
  [3073, errorMessages.PhoneNumberAlreadyExistError],
  [3074, errorMessages.UserNameDoesNotExistError],
  [3075, errorMessages.InsufficientBalance],
  [3076, errorMessages.PersonalBonusNotFound],  
  [3077, errorMessages.PersonalBonusAlreadyClaimed],
  [3078, errorMessages.PersonalBonusNotClaimedByCreateUser],
  [3079, errorMessages.InvalidBonusCodeError],
  [3080, errorMessages.AffiliatesNotExistsErrorType],
  [3083, errorMessages.TournamentAlreadyExistWithGivenTimeFrameErrorType],
  [3084, errorMessages.TournamentNotFoundErrorType],
  [3085, errorMessages.TournamentNotExistErrorType],
  [3086, errorMessages.UserAlreadyJoinedTournamentErrorType],
  [3087, errorMessages.TournamentIsClosedForFurtherRegistrationsError],
  [3088, errorMessages.TournamentFinishedError],
  [3089, errorMessages.PasswordCannotBeSameAsOldPasswordError],
  [3089, errorMessages.UserAssociatedWithSocialLoginErrorType],
  [3090, errorMessages.GiveawaysNotFoundErrorType],
  [3091, errorMessages.NoAnyGiveawaysActive],
  [3091, errorMessages.TemporaryEmailError],
  [3092, errorMessages.MaximumRedeemableLimitReachedError],
  [3093, errorMessages.InternalUsersCannotRedeemError],
  [3094, errorMessages.BonusNotFound],
  [3095, errorMessages.FirstPurchaseBonusNotFound],
  [3096, errorMessages.FirstPurchaseBonusAlreadyClaimed],
  [3097, errorMessages.FirstDepositAlreadyDone],
  [3098, errorMessages.InvalidSignedRequest],
  [3099, errorMessages.PurchasedLimitReached],  
  [3100, errorMessages.WelcomePurchaseBonusExpired],
  [3101, errorMessages.PromocodeNotExistError],
  [3102, errorMessages.PromocodeAvailedLimitReachedError],
  [3103, errorMessages.PromocodePerUserLimitReachedError],
  [3104, errorMessages.PromocodeNotApplicableError],
  [3100, errorMessages.WelcomePurchasePackageIsOnlyAvailableOnceError],
  [3101, errorMessages.PackageCoolDownPeriodError],
  [3101, errorMessages.TransactionNotFoundError],
  [3101, errorMessages.YouCannotWithdrawMoreThanAllowedGcLimitError],
  [3102, errorMessages.YouCannotWithdrawMoreThanAllowedScLimitError],
  [3103, errorMessages.OtpVerificationFailedError],
  [3104, errorMessages.UserNotEnabledTwoFactorAuthenticationError],
  [3105, errorMessages.PercentageValueShouldNotBeMoreThan100Error],
  [3106, errorMessages.IncorrectPasswordError],
  [3107, errorMessages.Enabled2faTokenMustBeRequiredError],
  [3108, errorMessages.InsufficientScBalanceError],
  [3109, errorMessages.PasswordMustBeRequiredError],
  [3110, errorMessages.Your2FACodeIsIncorrectError],
  [3111, errorMessages.UserTwoFactorAuthIsDisabledError],
  [3112, errorMessages.PasswordNotExistErrorType],
  [3113, errorMessages.MaxAttemptForResetPasswordErrorType],
  [3114, errorMessages.InvalidAmountError],
  [3115, errorMessages.YouCannotDepositMoreThanAllowedGcLimitError],
  [3116, errorMessages.YouCannotDepositMoreThanAllowedScLimitError],
  [3117, errorMessages.MaxAttemptsReachedForPhoneVerification],
  [3118, errorMessages.MaxResetPasswordAttemptErrorType],
  [3119, errorMessages.EmailNotFoundError],
  [3120, errorMessages.RestrictedUserError],
  [3121, errorMessages.CmsNotFoundErrorType],
  [3118, errorMessages.ReferralBonusDoesNotExistForThisUserError],
  [3119, errorMessages.ReferredUserIsNotExistError],
  [3120, errorMessages.ReferralUserKycVerificationRequiredError],
  [3118, errorMessages.ProfaneUserNameError],
])

export default errorCodeMappedViaMessage
