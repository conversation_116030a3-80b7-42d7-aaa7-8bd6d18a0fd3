import TagManager from 'react-gtm-module'

import useAuthStore from '../../store/useAuthStore'
import { usePortalStore } from '../../store/userPortalSlice'
import { useUserStore } from '../../store/useUserSlice'
import { userLogout } from '../../utils/apiCalls'
import { navigateTo } from '../../utils/navigation'
import { getLoginToken } from '../../utils/storageUtils'


export const handleLogout = () => {
  const usePortal = usePortalStore.getState()
  const userStore = useUserStore.getState()
  const { setAuthenticated, setPathCookieCheck } = useAuthStore.getState()

  TagManager.dataLayer({
    dataLayer: {
      event: 'logout',
      email: userStore.userDetails?.email,
      user_id: userStore.userDetails?.userId
    }
  })

  if (getLoginToken()) {
    usePortal.closePortal()
    userLogout()
    setAuthenticated(false)
    setPathCookieCheck(false)
    optimoveSDK.API.signOutUser()
    userStore.logout()
    navigateTo('/')
  }
}
