import { handleApiErrors } from '../../helper/error.handler.helpers'
import { openErrorToaster } from '../../helper/toaster.helpers'
import errorMessages from '../../messages/errorMessages'

export const errorHandler = async (error) => {
  if (error.response?.status === 403) {
    // handleLogout()
    openErrorToaster(errorMessages.unAuthorized)
    return Promise.reject(error)
  } else if (error.response?.status === 401) {
    // handleLogout()
    openErrorToaster(error?.response?.data.errors[0].description)
    return Promise.reject(error)
  } else if (error.response?.status === 404) {
    // handleLogout()
    openErrorToaster(errorMessages.PgeNotFound)
    return Promise.reject(error)
  } else if (error.response?.status === 500) {
    openErrorToaster(errorMessages.InternalServerError)
    return Promise.reject(error)
  }
  return handleApiErrors(error)
}
