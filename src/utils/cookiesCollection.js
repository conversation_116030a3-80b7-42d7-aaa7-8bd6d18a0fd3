export const setCookie = (name, value, days) => {
    const expires = new Date(Date.now() + days * 864e5).toUTCString();
    document.cookie = name + '=' + encodeURIComponent(value) + '; expires=' + expires + '; path=/';
};
export const setSeonCookie = (name, value) => {
    document.cookie = name + '=' + encodeURIComponent(value) + '; path=/';
};
export const setPopCalled = (name, value) => {
    document.cookie = name + '=' + encodeURIComponent(value);
};

export const setreferralCode = (name, value) => {
    document.cookie = name + '=' + encodeURIComponent(value);
};
export const setReferralSocialCookie = (name, value, domain) => {
    document.cookie =
        `${name}=${encodeURIComponent(value)};` +
        ` domain=${domain};` +
        ` path=/;` +
        ` secure;`
        ;          // Adjust "strict" to "lax" or "none" as needed
};
export const deleteRefferalCookie = (name) => {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    document.cookie = `${name}=; domain=.themoneyfactory.com; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}
export const deleteCookie = (name) => {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    document.cookie = `${name}=; domain=.themoneyfactory.com; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

export const deleteVipRoute = (name) => {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
  }
  
export const setCookieSocial = (name, value) => {
    // const expires = new Date(Date.now() + seconds * 1000).toUTCString();
    document.cookie = name + '=' + encodeURIComponent(value);
};

export const getCookie = (name) => {
    return document.cookie.split('; ').reduce((r, v) => {
        const parts = v.split('=');
        return parts[0] === name ? decodeURIComponent(parts[1]) : r;
    }, '');
};

// export const setAccessTokenCookie = (name, value, domain) => {
//     document.cookie = 
//         `${name}=${encodeURIComponent(value)};` +
//         ` domain=${domain};` +
//         ` path=/;` +
//         ` secure;` +
//         `SameSite=None` ;// Adjust "strict" to "lax" or "none" as needed
//         console.log("setAccessTokenCookie ::",document.cookie);

// };

export const setAccessTokenCookie = (name, value, domain) => {

    document.cookie = `${name}=; domain=${domain}; path=/home; expires=Thu, 01 Jan 1970 00:00:00 UTC;`;
    const prefixedDomain = domain.startsWith('.') ? domain : `.${domain}`;
    const cookieString = `${name}=${encodeURIComponent(value)};` + ` domain=${prefixedDomain};` + ` path=/;` + ` secure;` + ` SameSite=None;`; document.cookie = cookieString;
};

export const deleteAccessTokenCookies = (name) => {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

export const getVipRoute = (name) =>{
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return decodeURIComponent(parts.pop().split(';').shift())
    return null
  }

