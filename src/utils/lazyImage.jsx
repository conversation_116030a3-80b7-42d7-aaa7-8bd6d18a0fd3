import React, { useMemo, useCallback } from 'react'
import { Img } from 'react-image'
import { useInView } from 'react-intersection-observer'
import { useMediaQuery, useTheme } from '@mui/material'
import DefaultFallback from '../components/ui-kit/icons/svg/fallbackTest.svg' // Keep it lightweight

const LazyImageComponent = React.forwardRef((props, forwardedRef) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme?.breakpoints?.down('sm'))

  const {
    src,
    alt = 'Image',
    placeholder = DefaultFallback,
    fallback = DefaultFallback,
    loaderStyles,
    style,
    onError,
    lazy = true,
    threshold = 0.1,
    className = '',
    ...rest
  } = props

  const finalFallback = fallback || DefaultFallback
  const finalPlaceholder = placeholder || DefaultFallback

  const resolvedLoaderStyles = useMemo(
    () => loaderStyles || { maxHeight: isMobile ? '100px' : '250px' },
    [loaderStyles, isMobile]
  )

  const resolvedStyle = useMemo(() => style || { backgroundColor: 'transparent' }, [style])



  const { ref: inViewRef, inView } = useInView({
    triggerOnce: true,
    threshold
  })

  const setRef = useCallback(
    (node) => {
      if (typeof forwardedRef === 'function') {
        forwardedRef(node)
      } else if (forwardedRef) {
        forwardedRef.current = node
      }
      inViewRef(node)
    },
    [forwardedRef, inViewRef]
  )

  const LoaderImg = useMemo(
    () => (
      <img
        src={finalPlaceholder}
        alt='Loading...'
        style={resolvedLoaderStyles}
        className={className}
        loading='lazy'
        decoding='async'
        {...rest}
      />
    ),
    [finalPlaceholder, resolvedLoaderStyles, rest]
  )

  const FallbackImg = useMemo(
    () =>
      onError || (
        <img src={finalFallback} alt='Error loading image' style={resolvedLoaderStyles} decoding='async' {...rest} />
      ),
    [onError, finalFallback, resolvedLoaderStyles, rest]
  )

  // 👉 For eager-loading (LCP or critical images)
  if (!lazy) {
    return (
      <img
        src={src}
        alt={alt}
        style={resolvedStyle}
        loading='eager'
        decoding='async'
        fetchPriority='high'
        ref={setRef}
        className={className}
        {...rest}
      />
    )
  }

  // 👉 Before in-view, show placeholder
  if (!inView) {
    return (
      <img
        ref={setRef}
        src={finalPlaceholder}
        alt='Loading...'
        style={resolvedLoaderStyles}
        loading='lazy'
        decoding='async'
        fetchPriority='low'
        className={className}
        {...rest}
      />
    )
  }

  // 👉 When in view, render lazy image
  return (
    <Img
      ref={setRef}
      src={src}
      alt={alt}
      loader={LoaderImg}
      unloader={FallbackImg}
      style={resolvedStyle}
      decoding='async'
      className={className}
      {...rest}
    />
  )
})

LazyImageComponent.displayName = 'LazyImage'
export default React.memo(LazyImageComponent)
