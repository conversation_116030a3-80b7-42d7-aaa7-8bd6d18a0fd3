
export const formatDateYMD = (date, type = 'MDY') => {
  const d = new Date(date)
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  const year = d.getFullYear()

  if (month.length < 2) month = '0' + month
  if (day.length < 2) day = '0' + day

  return type === 'MDY' ? [month, day,year].join('-') : [day, month, year].join('/')
}

export const profileDataFormate = (date, type = 'YMD') => {
  const d = new Date(date)
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  const year = d.getFullYear()

  if (month.length < 2) month = '0' + month
  if (day.length < 2) day = '0' + day

  return type === 'YMD' ? [month,day,year].join('-') : [month,day,year].join('/')
}

export const getDateDaysAgo = (days) => {
  const now = new Date()
  now.setDate(now.getDate() - days)
  return formatDateYMD(now)
}

export const getDateTime = (dateTime) => { 
  const d = new Date(dateTime)
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  const year = d.getFullYear()
  let hours = d.getHours()
  let minutes = d.getMinutes()
  const ampm = hours >= 12 ? 'PM' : 'AM'
  hours = hours % 12
  hours = hours || 12 // the hour '0' should be '12'
  minutes = minutes < 10 ? '0' + minutes : minutes
  const time = hours + ':' + minutes + ' ' + ampm

  if (month.length < 2) month = '0' + month
  if (day.length < 2) day = '0' + day

  const formatedDateTime = `${day}-${month}-${year} ${time}`

  return formatedDateTime
}

export const formatDateToLocalISOString = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
};

export const formatDateWithOrdinal = (utcString) => {
  const date = new Date(utcString);
  
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string provided');
  }

  // Month names
  const months = [
    'Jan', 'Feb', 'March', 'April', 'May', 'June',
    'July', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'
  ];

  // Get day and month
  const day = date.getDate();
  const month = months[date.getMonth()];

  // Add ordinal suffix to day
  let suffix = 'th';
  if (day % 10 === 1 && day !== 11) {
    suffix = 'st';
  } else if (day % 10 === 2 && day !== 12) {
    suffix = 'nd';
  } else if (day % 10 === 3 && day !== 13) {
    suffix = 'rd';
  }

  return `${month} ${day}${suffix}`;
}


export const convertToPacificShortFormat = (utcString) => {
  const date = new Date(utcString);

  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string provided');
  }

  const options = {
    timeZone: 'America/Los_Angeles',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  };

  const formatter = new Intl.DateTimeFormat('en-US', options);
  return formatter.format(date);
};

