export const loadLottieScript = () => {
  const scriptId = 'lottie-player-script'

  // Avoid adding the script more than once
  if (!document.getElementById(scriptId)) {
    const script = document.createElement('script')
    script.id = scriptId
    script.src = 'https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js'
    script.async = true

    script.onload = () => {
      console.log('Lottie script loaded')
    }

    script.onerror = () => {
      console.error('Failed to load Lottie script')
    }

    document.body.appendChild(script)
  }
}
