import { io } from 'socket.io-client'
const URL = import.meta.env.VITE_SOCKET_URL

let _walletSocket
let _liveWinnerSocket
let _tournamentLeaderboardSocket
let _tournamentEndSocket
let _maintenanceSocket
let _paymentSocket
let _jackpotSocket
let _pragmaticJackpotSocket

export const socket = (() => {
  if (!_walletSocket) {
    _walletSocket = io(`${URL}/wallet`, {
      transports: ['websocket'],
      withCredentials: true,
      autoConnect: false
    })
  }
  return _walletSocket
})()

export const liveWinnerSocket = (() => {
  if (!_liveWinnerSocket) {
    _liveWinnerSocket = io(`${URL}/live-winners`, {
      transports: ['websocket'],
      withCredentials: true,
      autoConnect: false
    })
  }
  return _liveWinnerSocket
})()

export const tournamentLeaderboardSocket = (() => {
  if (!_tournamentLeaderboardSocket) {
    _tournamentLeaderboardSocket = io(`${URL}/leader-board`, {
      transports: ['websocket'],
      withCredentials: true,
      autoConnect: false
    })
  }
  return _tournamentLeaderboardSocket
})()

export const tournamentEndSocket = (() => {
  if (!_tournamentEndSocket) {
    _tournamentEndSocket = io(`${URL}/tournament`, {
      transports: ['websocket'],
      withCredentials: true,
      autoConnect: false
    })
  }
  return _tournamentEndSocket
})()

export const maintenanceSocket = (() => {
  if (!_maintenanceSocket) {
    _maintenanceSocket = io(`${URL}/user-notification`, {
      transports: ['websocket'],
      withCredentials: true,
      autoConnect: false
    })
  }
  return _maintenanceSocket
})()

export const paymentSocket = (() => {
  if (!_paymentSocket) {
    _paymentSocket = io(`${URL}/payment`, {
      transports: ['websocket'],
      withCredentials: true,
      autoConnect: false
    })
  }
  return _paymentSocket
})()

export const jackpotSocket = (() => {
  if (!_jackpotSocket) {
    _jackpotSocket = io(`${URL}/jackpot-user`, {
      transports: ['websocket'],
      withCredentials: true,
      autoConnect: false
    })
  }
  return _jackpotSocket
})()

export const pragmaticJackpotSocket = (() => {
  if (!_pragmaticJackpotSocket) {
    _pragmaticJackpotSocket = io(`${URL}/pragmatic-jackpot`, {
      transports: ['websocket'],
      withCredentials: true,
      autoConnect: false
    })
  }
  return _pragmaticJackpotSocket
})()
