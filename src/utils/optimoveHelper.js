export function updateSDKPageVisit(PageURL, PageTitle, PageCategory, SDK_ID) {
    try {
        if (typeof optimoveSDK !== 'undefined' && optimoveSDK.API) {
            optimoveSDK.API.setPageVisit(PageURL, PageTitle, PageCategory, SDK_ID);
        } else {
            console.log("OPTIMOVE ERROR: SDK or API is not defined for updateSDKPageVisit");
        }
    } catch (error) {
        console.error("OPTIMOVE ERROR in updateSDKPageVisit:", error);
    }
}


export function emailEvent(pageKey, parameters,SDK_ID) {
    try {
        if (typeof optimoveSDK !== 'undefined' && optimoveSDK.API) {
            optimoveSDK.API.reportEvent(pageKey, parameters, SDK_ID);
        } else {
            console.log("OPTIMOVE ERROR: SDK or API is not defined for emailEvent");
        }
    } catch (error) {
        console.error("OPTIMOVE ERROR in emailEvent:", error);
    }

}

export function customEvent(eventKey, parameters,SDK_ID) {
    try {
        if (typeof optimoveSDK !== 'undefined' && optimoveSDK.API) {
            optimoveSDK.API.reportEvent(eventKey, parameters, SDK_ID);
        } else {
            console.log("OPTIMOVE ERROR: SDK or API is not defined for customEvent");
        }
    } catch (error) {
        console.error("OPTIMOVE ERROR in customEvent:", error);
    }
}
export function signoutEvent() {
    try {
        if (typeof optimoveSDK !== 'undefined' && optimoveSDK.API) {
            optimoveSDK.API.signOutUser();
        } else {
            console.log("OPTIMOVE ERROR: SDK or API is not defined for signoutEvent");
        }
    } catch (error) {
        console.error("OPTIMOVE ERROR in signoutEvent:", error);
    }
}


export function getInitialVisitorID() {
    try {
        if (typeof optimoveSDK !== 'undefined' && optimoveSDK.API) {
            optimoveSDK.API.getInitialVisitorID();
        } else {
            console.log("OPTIMOVE ERROR: SDK or API is not defined");
        }
    } catch (error) {
        // Log the error or handle it as needed
        console.error("OPTIMOVE ERROR: Unable to get visitor ID", error);
    }
}
