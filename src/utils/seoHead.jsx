import React from 'react'
import { Helmet } from 'react-helmet-async'
import { useLocation } from 'react-router-dom'

// --- Configuration Constants ---
const DEFAULT_TITLE = 'Play Social Casino Games Online for Free | The Money Factory'
const DEFAULT_DESCRIPTION =
  'Enjoy the best social casino games online for free! No deposit needed – just play, have fun, and win virtual prizes at The Money Factory.'
const DEFAULT_KEYWORDS = [
  'social casino games',
  'free casino games',
  'online casino',
  'virtual casino',
  'casino slots',
  'money factory',
  'free slots',
  'casino entertainment',
  'social gaming',
  'virtual prizes'
]
const DEFAULT_IMAGE = 'https://www.themoneyfactory.com/assets/brand-logo.d701cea0.webp' // Path to a default image for social sharing
const DEFAULT_URL = import.meta.env.VITE_DEFAULT_URL // website's base URL
const DEFAULT_TWITTER_HANDLE = 'themoneyfactory.com' // app's Twitter handle (e.g., @MyApp)
const DEFAULT_OG_TYPE = 'website' // Default Open Graph type

/**
 * Props:
 * @param {string} [title] - The specific title for the page. Will be appended with DEFAULT_TITLE.
 * @param {string} [description] - The meta description for the page.
 * @param {string[]} [keywords] - An array of keywords for the page.
 * @param {string} [imageUrl] - The URL of an image to be used for social sharing (OG and Twitter).
 * @param {string} [url] - The canonical URL for the current page.
 * @param {object} [twitter] - Object containing Twitter-specific meta tags.
 * @param {string} [twitter.card='summary_large_image'] - The type of Twitter card (e.g., 'summary', 'summary_large_image').
 * @param {string} [twitter.site=DEFAULT_TWITTER_HANDLE] - The Twitter handle for the website.
 * @param {string} [twitter.creator] - The Twitter handle for the content creator.
 * @param {string} [twitter.title] - Specific Twitter title, if different from general title.
 * @param {string} [twitter.description] - Specific Twitter description, if different from general description.
 * @param {string} [twitter.image] - Specific Twitter image URL, if different from general image.
 * @param {string} [twitter.imageAlt] - Alt text for the Twitter image.
 * @param {object} [og] - Object containing Open Graph-specific meta tags (for Facebook, LinkedIn, etc.).
 * @param {string} [og.title] - Specific OG title, if different from general title.
 * @param {string} [og.description] - Specific OG description, if different from general description.
 * @param {string} [og.type=DEFAULT_OG_TYPE] - The type of content (e.g., 'website', 'article', 'product').
 * @param {string} [og.url] - Specific OG URL, if different from general URL.
 * @param {string} [og.image] - Specific OG image URL, if different from general image.
 * @param {string} [og.imageAlt] - Alt text for the OG image.
 * @param {string} [og.siteName] - The name of your website for OG.
 * @param {string} [og.locale='en_US'] - The locale of the content (e.g., 'en_US', 'fr_FR').
 */

const SeoHead = ({ title, description, keywords, imageUrl, url, twitter, og }) => {
  const location = useLocation()
  const pageTitle = title ? `${title}` : DEFAULT_TITLE
  const pageDescription = description || DEFAULT_DESCRIPTION
  const pageKeywords = keywords && keywords.length > 0 ? keywords : DEFAULT_KEYWORDS
  const pageUrl = url || `${DEFAULT_URL}${location?.pathname}`
  const pageImage = imageUrl || DEFAULT_IMAGE

  return (
    <Helmet>
      {/* Standard Meta Tags */}
      <title>{pageTitle}</title>
      <meta name='description' content={pageDescription} />
      <meta name='keywords' content={pageKeywords.join(', ')} />
      <meta name='viewport' content='width=device-width, initial-scale=1.0' />
      <meta name='robots' content='index, follow' />
      <meta name='author' content='The Money Factory' />
      <meta httpEquiv='Content-Type' content='text/html; charset=utf-8' />
      <link rel='canonical' href={pageUrl} />

      {/* Performance and Resource Hints */}
      <link rel='preconnect' href='https://fonts.googleapis.com' />
      <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='anonymous' />
      <link rel='dns-prefetch' href='//www.google-analytics.com' />
      <link rel='dns-prefetch' href='//www.googletagmanager.com' />
      {/* Open Graph Tags (for Facebook, LinkedIn, etc.) */}
      <meta property='og:title' content={og?.title || pageTitle} />
      <meta property='og:description' content={og?.description || pageDescription} />
      <meta property='og:type' content={og?.type || DEFAULT_OG_TYPE} />
      <meta property='og:url' content={og?.url || pageUrl} />
      <meta property='og:image' content={og?.image || pageImage} />
      {og?.imageAlt && <meta property='og:image:alt' content={og.imageAlt} />}
      {og?.siteName && <meta property='og:site_name' content={og.siteName} />}
      {og?.locale && <meta property='og:locale' content={og.locale} />} {/* Default to 'en_US' if not provided */}
      {/* Twitter Card Tags */}
      <meta name='twitter:card' content={twitter?.card || 'summary_large_image'} />
      <meta name='twitter:site' content={twitter?.site || DEFAULT_TWITTER_HANDLE} />
      {twitter?.creator && <meta name='twitter:creator' content={twitter.creator} />}
      <meta name='twitter:title' content={twitter?.title || pageTitle} />
      <meta name='twitter:description' content={twitter?.description || pageDescription} />
      <meta name='twitter:image' content={twitter?.image || pageImage} />
      {twitter?.imageAlt && <meta name='twitter:image:alt' content={twitter.imageAlt} />}
    </Helmet>
  )
}

export default SeoHead
