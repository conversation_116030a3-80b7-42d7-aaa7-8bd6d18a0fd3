import React, { useEffect, useState } from 'react';
import { useImage } from 'react-image';
import { useInView } from 'react-intersection-observer';


const LazyBackgroundImage = ({
    src,
    placeholder = '/images/fallback-icon.svg',
    fallback = `../tenant/unnamed.webp`,
    children,
}) => {
    const [shouldLoad, setShouldLoad] = useState(false);
    const [finalImage, setFinalImage] = useState(placeholder);
    const [isVisible, setIsVisible] = useState(false);

    const { ref, inView } = useInView({
        triggerOnce: true,
        threshold: 0.1,
    });

    const { src: loadedSrc, isLoading, error } = useImage({
        srcList: shouldLoad ? [src] : [],
        useSuspense: false,
    });

    useEffect(() => {
        if (inView) {
            setShouldLoad(true);
        }
    }, [inView]);

    useEffect(() => {
        if (!shouldLoad) return;

        if (error) {
            setFinalImage(fallback || placeholder);
            setIsVisible(true);
        } else if (!isLoading && loadedSrc) {
            setFinalImage(loadedSrc);
            setIsVisible(true);
        } else if (isLoading) {
            setFinalImage(placeholder);
            setIsVisible(true);
        }
    }, [shouldLoad, loadedSrc, isLoading, error, fallback, placeholder]);

    return typeof children === 'function'
        ? children(finalImage, isVisible, ref)
        : null;
};

export default LazyBackgroundImage;