/**
 * Utility functions for image optimization and consistent aspect ratios
 * to prevent layout shifts and improve Lighthouse scores
 */

/**
 * Common aspect ratios used throughout the application
 */
export const AspectRatios = {
  // Common aspect ratios
  SQUARE: '1/1',
  LANDSCAPE_STANDARD: '16/9',
  LANDSCAPE_WIDE: '21/9',
  PORTRAIT_STANDARD: '3/4',
  BANNER_DESKTOP: '15/4', // Desktop banner ratio
  BANNER_MOBILE: '375/161', // Mobile banner ratio (approximately 2.33:1)
  GANG_IMAGE: '1239/556', // GangImg aspect ratio (approximately 2.23:1)
  PROVIDER_LOGO: '5/3', // Provider logo ratio
  GAME_CARD: '333/470', // Game card ratio (matches intrinsic dimensions)
};

/**
 * Get image style props with proper dimensions and aspect ratio
 * to prevent layout shifts
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.aspectRatio - The aspect ratio to use (e.g., '16/9', '1/1')
 * @param {number|string} options.width - The width in pixels or CSS units
 * @param {number|string} options.height - The height in pixels or CSS units
 * @param {string} options.objectFit - CSS object-fit property (contain, cover, etc.)
 * @param {Object} options.additionalStyles - Additional style properties to merge
 * @returns {Object} Style object with proper dimensions and aspect ratio
 */
export const getImageStyleProps = ({
  aspectRatio = AspectRatios.SQUARE,
  width = '100%',
  height = 'auto',
  objectFit = 'contain',
  additionalStyles = {}
}) => {
  return {
    width,
    height,
    aspectRatio,
    objectFit,
    display: 'block', // Prevents extra space below images
    ...additionalStyles
  };
};

/**
 * Get provider logo style props with consistent dimensions
 * 
 * @param {Object} options - Configuration options
 * @param {boolean} options.isMobile - Whether the current view is mobile
 * @param {Object} options.additionalStyles - Additional style properties to merge
 * @returns {Object} Style object for provider logos
 */
export const getProviderLogoStyles = ({
  isMobile = false,
  additionalStyles = {}
}) => {
  return getImageStyleProps({
    aspectRatio: AspectRatios.PROVIDER_LOGO,
    width: isMobile ? '48px' : '100px',
    height: 'auto',
    objectFit: 'contain',
    additionalStyles: {
      maxWidth: '100%',
      ...additionalStyles
    }
  });
};

/**
 * Get banner image style props with proper dimensions based on device
 * 
 * @param {Object} options - Configuration options
 * @param {boolean} options.isMobile - Whether the current view is mobile
 * @param {Object} options.additionalStyles - Additional style properties to merge
 * @returns {Object} Style object for banner images
 */
export const getBannerImageStyles = ({
  isMobile = false,
  additionalStyles = {}
}) => {
  return getImageStyleProps({
    aspectRatio: isMobile ? AspectRatios.BANNER_MOBILE : AspectRatios.BANNER_DESKTOP,
    width: '100%',
    height: 'auto',
    objectFit: 'cover',
    additionalStyles: {
      maxWidth: isMobile ? '375px' : '620px',
      margin: '0 auto',
      ...additionalStyles
    }
  });
};

/**
 * Get icon style props with consistent dimensions
 *
 * @param {Object} options - Configuration options
 * @param {number|string} options.size - The size in pixels or CSS units
 * @param {Object} options.additionalStyles - Additional style properties to merge
 * @returns {Object} Style object for icons
 */
export const getIconStyles = ({
  size = '24px',
  additionalStyles = {}
}) => {
  return getImageStyleProps({
    aspectRatio: AspectRatios.SQUARE,
    width: size,
    height: size,
    objectFit: 'contain',
    additionalStyles
  });
};

/**
 * Get GangImg style props with proper dimensions
 *
 * @param {Object} options - Configuration options
 * @param {Object} options.additionalStyles - Additional style properties to merge
 * @returns {Object} Style object for GangImg
 */
export const getGangImageStyles = ({
  additionalStyles = {}
}) => {
  return getImageStyleProps({
    aspectRatio: AspectRatios.GANG_IMAGE,
    width: '100%',
    height: 'auto',
    objectFit: 'contain',
    additionalStyles: {
      maxWidth: '620px',
      display: 'block',
      ...additionalStyles
    }
  });
};

export default {
  AspectRatios,
  getImageStyleProps,
  getProviderLogoStyles,
  getBannerImageStyles,
  getIconStyles,
  getGangImageStyles
};
