import { BrandLogoMob } from '../components/ui-kit/icons/brand'

export const initializeCaptainUpWidget = (userId, username, profileImage) => {
  const theme = {
    colors: {
      primary: '#FDB72E',
      secondary: '#FDB72E',
      bodyBackground: '#1f1f1f',
      cardBackground: '#161318',
      cardMenuBackground: '#363a42',
      menuBackground: '#29262e',
      progressBarBackground: '#646466',
      progressBarFill: '#FDB72E',
      footerBackground: '#1b191e',
      button: '#FDB72E',
      buttonHover: '#ffc14d',
      buttonInactive: '#17241c',
      buttonText: '#000000',
      buttonHoverText: '#1f0000',
      buttonInactiveText: '#ffffff',
      headingText: '#FDB72E',
      contentText: '#ffffff',
      footerText: '#ffffff',
      currencyText: '#ffffff',
      selectedMenuText: '#FDB72E',
      selectedItemBackground: '#919191',
      informationPopupBackground: '#FFFFFF',
      backdropColor: '#000000',
      backdropOpacity: 0.8,
      notificationPopupHeaderBackground: '#363a42',
      notificationPopupHeaderText: '#FFFFFF',
      notificationPopupBodyBackground: '#272d33',
      notificationPopupBodyText: '#FFFFFF',
      notificationPopupTitleText: '#69a5ff',
      notificationPopupCloseIcon: '#69a5ff',
      notificationPopupButtonBackground: '#69a5ff',
      notificationPopupButtonText: '#ffffff',
      notificationPopupButtonHoverBackground: '#4f2a5f',
      notificationPopupButtonHoverText: '#ffffff',
      sidebarBorderColor: '#FDB72E',
      sidebarBorderWidth: '1px',
      buttonBorderRadius: '8px',
      scrollBar: '#fdb72e',
      scrollBarBackground: '#ffffff51',
      mainMenuText: '#ffffff',
      errorHeadingText: '#ff0000'
    },
    fonts: {
      headerFontName: 'Inter',
      contentFontName: 'Inter'
    },
    gradientBackgroundColors: {
      progressBarGradient: 'linear-gradient(180deg, #FFEE8F 0%, #CC9418 100%)',
      buttonBackgroundGradient: 'linear-gradient(180deg, #B7D851 0%, #3F9943 100%)',
      buttonBackgroundGradientHover: 'linear-gradient(180deg, #ffc552 0%, #f3ce85 100%)',
      buttonBackgroundGradientDisabled: 'linear-gradient(180deg, #FDB72E 0%, #FFD481 100%)'
    },
    layout: 'popup'
  }
  _CaptainUpWidget.setTheme(theme) // for set the colors of the widget
  _CaptainUpWidget.setPopupSizeDesktop('large') // for set the widget size on desktop Acceptable values: ['large', 'normal']
  _CaptainUpWidget.setPopupSizeMobile('large') // for set the widget size on mobile Acceptable values: ['large', 'normal']
  _CaptainUpWidget.disableLoopForLevels(true) // for the disable the level infinite loop
  _CaptainUpWidget.enableModules('challenges, inbox') // for the set enable modules
  _CaptainUpWidget.hideCompletedChallengeOverlay() // for completed images visibility
  _CaptainUpWidget.disableLoopForLevels(true) // for disable the level slider infinite loop
  _CaptainUpWidget.setBorderSideBar(true) // for enable the sidebar border
  _CaptainUpWidget.setWidgetBorderRadius?.('5px') // for set the widget border radius
  _CaptainUpWidget.setChallengesCardView?.() // for enable the challenges card view UI
  const API_TOKEN = '676d81e0a12bfedf64fd013e'
  const CLIENT_TOKEN = '23f6015f5992f35728a91b78d24c3a6ff51edeba'
  const API_SECRET = 'c2f61c9a654db80efe40a6223e6db165b3c1a3ac488578e09a44e97a17358af6'
  _CaptainUpWidget.setAuthDetails(API_TOKEN, CLIENT_TOKEN, API_SECRET)
  _CaptainUpWidget.login(`${userId}`, username, profileImage || BrandLogoMob)
  // you can replace API_TOKEN with they API Key and CLIENT_TOKEN with they Client Token from the admin panel->settings page.
//   _CaptainUpWidget.login('123456789', 'RavenClaw', 'https://i.postimg.cc/SQtPjrvx/image.png') // for user login, unique user id, user name, avatar url (Optional)
}

export const removeCaptainUpWidget = () => {
	_CaptainUpWidget.logout({ reload: false })
}