import React, { useEffect } from 'react'
import { Navigate, useNavigate } from 'react-router-dom'
import { useUserStore } from './store/useUserSlice'
import { PlayerRoutes } from './routes'
import { usePortalStore } from './store/userPortalSlice'
import { deleteAccessTokenCookies, deleteVipRoute, getCookie, getVipRoute } from './utils/cookiesCollection'
import { openErrorToaster } from './network/helper/toaster.helpers'
import errorMessages from './network/messages/errorMessages'

const VipQuestionWrapper = ({ children }) => {
  const portalStore = usePortalStore();

  const navigate = useNavigate()
  const userStore = useUserStore()
  const isAllowed = Boolean(userStore?.userDetails?.isVipApproved)
  const accessCookie = getCookie('accessToken')
  const vipRoute = getVipRoute('vipRoute')
  let vipEmail = ''
  if(vipRoute){
    const urlObj = new URL(vipRoute,window.location.origin)
    vipEmail = urlObj.searchParams.get('email') || ''
  }

  function handleLogoutWithToast(navigateTo = '/') { 
    deleteVipRoute('vipRoute')
    portalStore.closePortal()
    if (accessCookie) {
      deleteAccessTokenCookies('accessToken')
    }
    userStore.logout()
    openErrorToaster(errorMessages.unAuthorized)
    navigate(navigateTo)
  }


  useEffect(() => {
    if(userStore?.userDetails?.email && vipEmail && vipEmail !== userStore?.userDetails?.email){
      handleLogoutWithToast()
      return
    }
    if (!isAllowed) {
      navigate('/')
    }
  }, [isAllowed, navigate, vipEmail, userStore?.userDetails?.email])

  if (!isAllowed) {
    return <Navigate to={PlayerRoutes.Lobby} replace />
  }

  return <>{children}</>
}

export default VipQuestionWrapper
