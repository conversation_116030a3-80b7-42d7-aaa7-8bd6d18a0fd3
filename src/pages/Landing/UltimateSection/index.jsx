import * as React from 'react'
import useStyles from './style'
import { Box, Button, Grid, Typography } from '@mui/material'
import ultimateGamingIcon from '../../../components/ui-kit/icons/webp/Ultimate-Gaming.webp'
import game1 from '../../../components/ui-kit/icons/webp/moneyfactory-games1.webp'
import game2 from '../../../components/ui-kit/icons/webp/slot-machine-reels.webp'
import shieldWhite from '../../../components/ui-kit/icons/webp/shield-white.webp'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import { usePortalStore } from '../../../store/userPortalSlice'
import Signup from '../../../components/Modal/Signup'
import LazyImage from '../../../utils/lazyImage'

const UltimateSection = () => {
  const classes = useStyles()
  const portalStore = usePortalStore()
  const handlePayForFree = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  return (
    <>
      <section className={classes.ultimateSectionWrap} data-tracking='Home.Ultimate.Section'>
        <Grid className='inner-container'>
          <Grid container spacing={1}>
            <Grid item xs={12} md={6} lg={6}>
              <Grid className='ultimate-left'>
                <Grid className='inner-heading'>
                  <Typography variant='h4'> Your Ultimate Gaming Destination</Typography>
                  <Typography>
                    The Money Factory offers a social casino experience like no other. Our platform is built for players
                    who love gaming without financial risks.
                  </Typography>
                </Grid>
                <Grid className='utltimate-info'>
                  <Typography variant='h4'>
                    With hundreds of exciting games to choose from, there’s something for everyone.
                  </Typography>
                </Grid>
                <Typography>
                  Enjoy free-to-play options with no purchase required. Experience the fun and excitement The Money
                  Factory has to offer.
                </Typography>
                <Grid className='cta-wrap'>
                  <Grid className='btn-wrap'>
                    <Button
                      className='btn btn-primary'
                      onClick={handlePayForFree}
                      data-tracking='Home.Ultimate.PlayForFree.Btn'
                    >
                      <ArrowCircleRightOutlinedIcon />
                      Play For Free{' '}
                    </Button>
                  </Grid>
                  <Grid className='banner-disclaimer'>
                    {/* <img src={shieldWhite} alt='Secure' /> */}
                    <LazyImage src={shieldWhite} alt='Secure'  />
                    <Typography>Trusted by countless players across the US</Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6} lg={6}>
              <Grid className='ultimate-right'>
                <figure className='ultimate-graphic'>
                  {/* <img src={ultimateGamingIcon} alt='Icon' /> */}
                  <LazyImage src={ultimateGamingIcon} alt='Icon'  />
                </figure>
                <Grid className='free-content-wrap'>
                  <Typography>Ready to discover the fun for yourself?</Typography>
                  <ul>
                    <li>
                      <strong>Claim your free</strong> coins every single day.
                    </li>
                    <li>
                      <strong>Enter exciting contests</strong> and giveaways for more chances to win.
                    </li>
                    <li>
                      <strong>Play all of your</strong> favorite games for free, as long as you like.
                    </li>
                  </ul>
                </Grid>
                <Grid className='games-wrap'>
                  <figure className='game-card'>
                    {/* <img src={game1} alt='Game' /> */}
                    <LazyImage src={game1} alt='Game'  />
                  </figure>
                  <figure className='game-card'>
                    {/* <img src={game2} alt='Game' /> */}
                    <LazyImage src={game2} alt='Game'  />
                  </figure>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </section>
    </>
  )
}

export default UltimateSection
