import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  ultimateSectionWrap: {
    padding: theme.spacing(5, 1),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1)
    },
    '& .ultimate-left': {
      maxWidth: theme.spacing(23.75),
      [theme.breakpoints.down('md')]: {
        maxWidth: '100%'
      },
      '& .inner-heading': {
        marginBottom: theme.spacing(1),
        textAlign: 'left',
        '& h4': {
          marginInlinBottom: theme.spacing(1)
        }
      },
      '& .utltimate-info': {
        borderLeft: `3px solid ${theme.colors.YellowishOrange}`,
        paddingLeft: theme.spacing(1.25),
        marginBottom: theme.spacing(1.5),
        '& h4': {
          fontSize: theme.spacing(1.25),
          fontWeight: '900',
          lineHeight: '1.6'
        }
      },
      '& p': {
        fontSize: theme.spacing(1.375),
        fontWeight: '400'
      },
      '& .btn-wrap': {
        justifyContent: 'flex-start'
      },
      '& .banner-disclaimer': {
        '& p': {
          fontSize: theme.spacing(1.125)
        }
      }
    },
    '& .ultimate-right': {
      position: 'relative',
      [theme.breakpoints.down('md')]: {
        display: 'flex',
        flexDirection: 'column-reverse'
      },
      '& .ultimate-graphic': {
        position: 'absolute',
        top: theme.spacing(-5.625),
        left: theme.spacing(-10),
        zIndex: 1,
        [theme.breakpoints.down('md')]: {
          left: 'auto',
          right: theme.spacing(9.375),
          top: 0,
          textAlign: 'right'
        },
        [theme.breakpoints.down('sm')]: {
          left: theme.spacing(-0.75)
        },
        '& img': {
          width: theme.spacing(26.875),
          [theme.breakpoints.down('md')]: {
            width: theme.spacing(11.75)
          }
        }
      },
      '& .free-content-wrap': {
        background: theme.colors.YellowishOrange,
        borderRadius: theme.spacing(1.25),
        padding: theme.spacing(1.25, 1.25, 1.25, 14),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1.25)
        },
        '& p': {
          color: theme.colors.textBlack,
          fontSize: theme.spacing(1.25),
          marginBottom: theme.spacing(1.5),
          fontWeight: theme.typography.fontWeightMedium,
          textAlign: 'left',
          width: '100%'
        },
        '& ul': {
          '& li': {
            paddingBottom: theme.spacing(0.625)
          }
        }
      },
      '& .games-wrap': {
        display: 'grid',
        // margin: theme.spacing(0.625),
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: theme.spacing(1),
        height: theme.spacing(6.25),
        maxWidth: theme.spacing(18.75),
        margin: '1.5rem 0 0.625rem auto',
        [theme.breakpoints.down('md')]: {
          gridTemplateColumns: 'repeat(1, 1fr)',
          height: theme.spacing(13.5),
          maxWidth: theme.spacing(8.75)
        },
        '& .game-card': {
          borderRadius: theme.spacing(1.25),
          overflow: 'hidden',
          [theme.breakpoints.down('md')]: {
            borderRadius: theme.spacing(1)
          },
          '& img': {
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }
        }
      }
    }
  }
}))
