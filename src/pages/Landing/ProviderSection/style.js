import { makeStyles } from '@mui/styles'

import { socalCasinoBg1 } from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  providerSection: {
    display: 'none',
    padding: theme.spacing(4, 5),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1)
    },
    '& .MuiBox-root': {
      padding: '0'
    },
    '& .theme-tabs': {
      marginTop: theme.spacing(1),
      padding: '0',
      [theme.breakpoints.down('md')]: {
        display: 'none'
      },
      '& .MuiTabs-flexContainer': {
        borderBottom: `2px solid ${theme.colors.textWhite}`
      },
      '& .MuiTabs-indicator': {
        background: theme.colors.YellowishOrange
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        fontWeight: '700',
        textTransform: 'capitalize',
        display: 'flex',
        flexDirection: 'row',
        minWidth: '8.75rem',
        whiteSpace: 'nowrap',
        alignItems: 'center',
        gap: theme.spacing(1),
        flex: 1,
        '& img': {
          margin: '0'
        }
      },
      '& MuiBox-root': {
        padding: '0 !important'
      }
    },
    '& .games-grid-wrap': {
      gap: '1rem',
      display: 'grid',
      gridTemplateColumns: 'repeat(7, 1fr)',

      [theme.breakpoints.down('md')]: {
        gridTemplateColumns: 'repeat(3, 1fr)'
      }
    },
    '& .show-btn-wrap': {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing(1),
      margin: theme.spacing(2, 0, 0),
      position: 'relative',
      justifyContent: 'flex-end',
      '&:before': {
        position: 'absolute',
        width: '100%',
        content: "''",
        height: '1px',
        left: '0',
        background: theme.colors.textWhite
      },
      [theme.breakpoints.down('md')]: {
        justifyContent: 'center'
      },

      '& .MuiDivider-root': {
        border: 'none',
        background: theme.colors.textWhite,
        // width:"100%",
        height: '2px',
        flex: '1',
        [theme.breakpoints.down('md')]: {
          display: 'none'
        }
      },
      '& .MuiButtonBase-root': {
        fontSize: theme.spacing(1.25),
        color: theme.colors.textBlack,
        padding: theme.spacing(0.625, 1.5),
        '&:hover': {
          color: theme.colors.textWhite
        },
        [theme.breakpoints.down('md')]: {
          // padding: theme.spacing(0.25, 0.5),
          fontSize: theme.spacing(1)
        }
      }
    },
    '& .theme-select': {
      marginTop: theme.spacing(1),
      '& .MuiFormLabel-root': {
        color: theme.colors.textWhite,
        fontWeight: '700'
      },
      '& .MuiSvgIcon-root': {
        color: theme.colors.textWhite
      },
      '& .MuiInputBase-root': {
        background: theme.colors.serviceCardBg,
        borderRadius: theme.spacing(1.875),
        color: theme.colors.textWhite,
        minWidth: '200px',
        [theme.breakpoints.down('md')]: {
          minWidth: '100%'
        },
        '&.Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: theme.colors.YellowishOrange,
            borderRadius: '30px'
          }
        }
      },
      [theme.breakpoints.down('md')]: {
        width: '100%'
      },
      '& .MuiFormControl-root': {
        [theme.breakpoints.down('md')]: {
          width: '100%'
        }
      }
    },
    '& .inner-heading': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: '10px',
      '& .text-h4': {
        [theme.breakpoints.down('md')]: {
          display: 'none'
        },
        fontWeight: '500'
      },
      '& .game-category-mob': {
        display: 'none',
        [theme.breakpoints.down('md')]: {
          display: 'block !important'
        }
      }
    },
    '& .provider-select-wrap': {
      textAlign: 'left',
      '& .MuiSelect-select': {
        textAlign: 'center'
      },
      '& img': {
        width: theme.spacing(5),
        margin: '0 auto'
      }
    }
  },

  betterSocialSection: {
    padding: theme.spacing(5, 1, 5),
    background: `url(${socalCasinoBg1})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 1)
    },
    '& .better-social-content': {
      maxWidth: theme.spacing(71.25),
      margin: '0 auto',
      '& .inner-heading': {
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(1)
        },
        '& h2': {
          marginBottom: theme.spacing(1)
        }
      },
      '& .games-grid-wrap': {
        gap: '1rem',
        display: 'grid',
        gridTemplateColumns: 'repeat(5, 1fr)',
        marginBottom: theme.spacing(2.5),
        [theme.breakpoints.down('md')]: {
          gridTemplateColumns: 'repeat(3, 1fr)'
        }
      }
    },
    '& .btn-wrap': {
      marginTop: theme.spacing(0.625)
    }
  }
}))
