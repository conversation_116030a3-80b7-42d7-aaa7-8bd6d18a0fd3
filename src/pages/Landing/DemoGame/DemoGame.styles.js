import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  demoGamesWrap: {
    margin: theme.spacing(2, 0),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1)
    },
    '& .view-demo': {
      padding: theme.spacing(1.5, 1.75),
      background: theme.colors.serviceCardBg,
      borderRadius: theme.spacing(2.5),
      maxWidth: theme.spacing(79.1231),
      minHeight: theme.spacing(44.4537),
      [theme.breakpoints.down('md')]: {
        minHeight: 'auto',
        borderRadius: theme.spacing(1),
        padding: theme.spacing(1)
      },
      margin: '0 auto',
      textAlign: 'center',
      '& .btn-primary': {
        marginTop: theme.spacing(1),
        minWidth: theme.spacing(17.375),
        fontSize: theme.spacing(1.25),
        [theme.breakpoints.down('md')]: {
          minWidth: 'auto',
          fontSize: theme.spacing(0.75)
        }
      },
      '& iframe': {
        height: theme.spacing(41.125),
        width: '100%',
        border: 'none',
        [theme.breakpoints.down('md')]: {
          height: theme.spacing(21.5081)
        },
        [theme.breakpoints.down('sm')]: {
          height: theme.spacing(13.0625)
        }
      },
      '& img': {
        width: '100%'
      }
    }
  }
}))
