import React, { useState } from 'react'
import demoGameImg from '../../../components/ui-kit/icons/webp/demo-game-img.webp'
import useStyles from './DemoGame.styles'
import { Button, Grid } from '@mui/material'
import { useGetDemoGameLaunch } from '../../../reactQuery'
import useGetDeviceType from '../../../utils/useGetDeviceType'
import Loader from '../../../components/Loader'

const DemoGame = () => {
  const classes = useStyles()
  const { isMobile } = useGetDeviceType()
  const [gameLoaded, setGameLoaded] = useState(false)
  const [gameUrl, setGameUrl] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleDemoGamePlay = () => {
    getDemoGameLaunchMutation.mutate({ isMobile: isMobile })
  }

  const getDemoGameLaunchMutation = useGetDemoGameLaunch({
    onSuccess: (res) => {
      // if (res?.data?.success === false) {
      //   navigateToLobby()
      // }
      setGameLoaded(true)
      setIsLoading(false)
      setGameUrl(res?.data?.gameUrl)
    },
    onError: (err) => {
      if (err?.response?.data?.errors.length > 0) {
        // toast.error(`${err?.response?.data?.errors[0]?.description}`)
      }
    }
  })
  return (
    <>
      <Grid className={classes.demoGamesWrap}>
        {!gameLoaded
          ? (
            <Grid className='view-demo'>
              <img src={demoGameImg} alt='demo-img' />
              <Button type='button' className='btn btn-primary' onClick={handleDemoGamePlay}>View Demo</Button>
            </Grid>)
          : (
            <Grid>
              {isLoading && <Loader />}
              <Grid className='view-demo'>
                <iframe key='gameIframe' height='100%' src={gameUrl || ''} width='100%' />
              </Grid>
            </Grid>)}
      </Grid>
    </>
  )
}

export default DemoGame
