import * as React from 'react'
import useStyles from './style'
import { Box, Button, Grid, InputAdornment, TextField, Typography } from '@mui/material'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import contactGraphic from '../../../components/ui-kit/icons/webp/contact-graphic.webp'
import shieldWhite from '../../../components/ui-kit/icons/webp/shield-white.webp'
import MailOutlineIcon from '@mui/icons-material/MailOutline'
import { usePortalStore } from '../../../store/userPortalSlice'
import Signup from '../../../components/Modal/Signup'
import LazyImage from '../../../utils/lazyImage'

const ContactSection = () => {
  const classes = useStyles()
  const portalStore = usePortalStore()
  const [email, setEmail] = React.useState('')
  const [errorMessage, setErrorMessage] = React.useState('')

  const handlePayForFree = () => {
    if (!email.trim()) {
      setErrorMessage('Email is required.')
      return
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      setErrorMessage('Please enter a valid email address.')
      return
    }

    setErrorMessage('')
    portalStore.openPortal(() => <Signup email={email} />, 'loginModal')
  }

  const handleEmailChange = (event) => {
    const emailValue = event.target.value
    setEmail(emailValue)

    if (errorMessage) {
      setErrorMessage('')
    }
  }

  return (
    <>
      <section className={classes.contactSectionWrap} data-tracking='Home.StartWinning.Section'>
        <Grid className='inner-container'>
          <Box className='winning-box-wrap'>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4} lg={4} className='order-1'>
                <Grid className='winningl-left-content'>
                  <Typography variant='h4'>Start Winning Today With The Money Factory</Typography>
                  <Typography>
                    The Money Factory is your go-to platform for social gaming. Join countless players who are already
                    enjoying the thrill of winning without the financial risk.
                  </Typography>
                </Grid>
              </Grid>
              <Grid item xs={12} md={4} lg={4} className='order-3'>
                <figure className='contact-graphic-wrap'>
                  {/* <img src={contactGraphic} alt='Contact' /> */}
                  <LazyImage src={contactGraphic} alt='Contact' />
                </figure>
              </Grid>
              <Grid item xs={12} md={4} lg={4} className='order-2'>
                <Grid className='winning-right-content'>
                  <Typography>
                    <strong>Create your account today</strong> and get up to 425% EXTRA!
                  </Typography>
                  <Grid className='landiang-email-input'>
                    <TextField
                      id='form-field-claim_footer_email'
                      placeholder='Enter your email now'
                      variant='outlined'
                      value={email}
                      onChange={handleEmailChange}
                      error={Boolean(errorMessage)} // Show error style
                      helperText={errorMessage} // Show error message
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position='start'>
                            <MailOutlineIcon />
                          </InputAdornment>
                        )
                      }}
                    />
                  </Grid>

                  <Grid className='btn-wrap'>
                    <Button
                      className='btn btn-primary'
                      data-tracking='Home.StartWinning.ClaimOffer.Btn'
                      onClick={handlePayForFree}
                    >
                      <ArrowCircleRightOutlinedIcon />
                      Claim Welcome Offer
                    </Button>
                  </Grid>
                  <Grid className='banner-disclaimer'>
                    {/* <img src={shieldWhite} alt='Secure' /> */}
                    <LazyImage src={shieldWhite} alt='Secure' />
                    <Typography>Trusted by countless players across the US</Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
        </Grid>
      </section>
    </>
  )
}

export default ContactSection
