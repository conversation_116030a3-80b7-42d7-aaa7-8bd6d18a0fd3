import Signup from '../../../components/Modal/Signup'
import Step1 from '../../../components/ui-kit/icons/svg/step1.svg'
import Step2 from '../../../components/ui-kit/icons/svg/step2.svg'
import Step3 from '../../../components/ui-kit/icons/svg/step3.svg'
import shieldWhite from '../../../components/ui-kit/icons/webp/shield-white.webp'
import getVerified from '../../../components/ui-kit/icons/webp/get-verified.webp'
import joinNow from '../../../components/ui-kit/icons/webp/join-now.webp'
import onlineGame from '../../../components/ui-kit/icons/webp/online-game.webp'

import { usePortalStore } from '../../../store/userPortalSlice'
import useStyles from './style'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import { Button, Grid, Typography } from '@mui/material'
import LazyImage from '../../../utils/lazyImage'

const SocialSection = () => {
  const portalStore = usePortalStore()
  const classes = useStyles()

  const handleViewMore = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  return (
    <section className={classes.SocialSection} id='how-it-works' data-tracking='Home.3steps.Section'>
      <Grid className='socail-section-content'>
        <Grid className='inner-heading'>
          <Typography variant='h2'>
            Join the <span>Social Casino</span> in 3 Simple Steps
          </Typography>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs={12} sm={4} md={4}>
            <article className='social-card'>
              <Grid className='social-card-icon-wrap'>
                {/* <img src={Step1} alt='Step' /> */}
                <LazyImage
                  src={Step1}
                  alt='Step1'
                  style={{
                    width: '90px',
                    height: '90px',
                    aspectRatio: '1/1',
                    objectFit: 'contain'
                  }}
                />
                <figure className='social-card-graphic'>
                  {/* <img src={joinNow} alt='' /> */}
                  <LazyImage
                    src={joinNow}
                    alt='join-now'
                    style={{
                      width: '50px',
                      height: '50px',
                      aspectRatio: '1/1',
                      objectFit: 'contain'
                    }}
                  />
                </figure>
              </Grid>
              <Typography variant='h4'>Join Now</Typography>
              <Typography>
                Ready for fun? Click the button below to create your account at our social casino online and start
                playing within moments. It’s that simple!
              </Typography>
            </article>
          </Grid>
          <Grid item xs={12} sm={4} md={4}>
            <article className='social-card'>
              <Grid className='social-card-icon-wrap'>
                {/* <img src={Step2} alt='Step' /> */}
                <LazyImage
                  src={Step2}
                  alt='Step2'
                  style={{
                    width: '90px',
                    height: '90px',
                    aspectRatio: '1/1',
                    objectFit: 'contain'
                  }}
                />
                <figure className='social-card-graphic'>
                  {/* <img src={getVerified} alt='' /> */}
                  <LazyImage
                    src={getVerified}
                    alt='getVerified'
                    style={{
                      width: '50px',
                      height: '50px',
                      aspectRatio: '1/1',
                      objectFit: 'contain'
                    }}
                  />
                </figure>
              </Grid>
              <Typography variant='h4'>Get Verified</Typography>
              <Typography>
                Registering takes just a few steps. New players receive an instant offer when they join! Easily verify
                your details to unlock all features and dive into our exciting games.
              </Typography>
            </article>
          </Grid>
          <Grid item xs={12} sm={4} md={4}>
            <article className='social-card'>
              <Grid className='social-card-icon-wrap'>
                {/* <img src={Step3} alt='Step' /> */}
                <LazyImage
                  src={Step3}
                  alt='Step3'
                  style={{
                    width: '90px',
                    height: '90px',
                    aspectRatio: '1/1',
                    objectFit: 'contain'
                  }}
                />
                <figure className='social-card-graphic'>
                  {/* <img src={onlineGame} alt='' /> */}
                  <LazyImage
                    src={onlineGame}
                    alt='online-games'
                    style={{
                      width: '50px',
                      height: '50px',
                      aspectRatio: '1/1',
                      objectFit: 'contain'
                    }}
                  />
                </figure>
              </Grid>
              <Typography variant='h4'>Play & Enjoy</Typography>
              <Typography>
                Jump into thrilling games with ease! Our free online casino offers quick access to countless fun
                experiences. Get started today and enjoy the excitement without the hassle.
              </Typography>
            </article>
          </Grid>
        </Grid>
        <Grid className='cta-wrap'>
          <Grid className='btn-wrap'>
            <Button className='btn btn-secondary' onClick={handleViewMore} data-tracking='Home.3steps.PlayForFree.Btn'>
              <ArrowCircleRightOutlinedIcon />
              Play For Free{' '}
            </Button>
            <Button className='btn btn-primary' onClick={handleViewMore} data-tracking='Home.3steps.ClaimOffer.Btn'>
              <ArrowCircleRightOutlinedIcon />
              Welcome Offer{' '}
            </Button>
          </Grid>
          <Grid className='banner-disclaimer'>
            {/* <img src={shieldWhite} alt='Secure' /> */}
            <LazyImage
              src={shieldWhite}
              alt='Secure'
              style={{
                width: '24px',
                height: '24px',
                aspectRatio: '1/1',
                objectFit: 'contain'
              }}
            />
            <Typography>Trusted by countless players across the US</Typography>
          </Grid>
        </Grid>
      </Grid>
    </section>
  )
}

export default SocialSection
