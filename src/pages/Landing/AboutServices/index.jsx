import LandingService1 from '../../../components/ui-kit/icons/landing/service-1.webp'
import LandingService2 from '../../../components/ui-kit/icons/landing/service-2.webp'
import LandingService3 from '../../../components/ui-kit/icons/landing/service-3.webp'
import useStyles from './style'
import { Box, Grid, Typography } from '@mui/material'

const AboutServices = () => {
  const classes = useStyles()

  return (
    <Grid className={classes.ServicesSection}>
      <Grid className='services-content'>
        <Grid container spacing={1}>
          <Grid item xs={12} lg={4}>
            <Grid className='service-card'>
              <Box className='service-card-icon'>
                <img src={LandingService1} alt='Service' />
              </Box>
              <Typography variant='h4'>24/7 Customer Help</Typography>
              <Typography>
                Get the assistance you need quickly and efficiently with our 24/7 customer help team.
              </Typography>
            </Grid>
          </Grid>
          <Grid item xs={12} lg={4}>
            <Grid className='service-card'>
              <Box className='service-card-icon'>
                <img src={LandingService2} alt='Service' />
              </Box>
              <Typography variant='h4'>New Games Weekly</Typography>
              <Typography>
                Get ready for a thrilling experience! We introduce exciting new Vegas-style casino games every week to
                keep you on the edge of your seat.
              </Typography>
            </Grid>
          </Grid>
          <Grid item xs={12} lg={4}>
            <Grid className='service-card'>
              <Box className='service-card-icon'>
                <img src={LandingService3} alt='Service' />
              </Box>
              <Typography variant='h4'>Get Verified Quick & Easy</Typography>
              <Typography>Start playing in minutes at our social casino!</Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default AboutServices
