import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  ServicesSection: {
    padding: theme.spacing(6, 1.5),

    [theme.breakpoints.down('lg')]: {
      padding: theme.spacing(6, 0.5)
    },
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 2)
    },

    '& .services-content': {
      maxWidth: '66rem',
      margin: '0 auto',
      '& .service-card': {
        background: theme.colors.serviceCardBg,
        padding: '2rem',
        borderRadius: '1.25rem',
        position: 'relative',
        // margin:"40px 0",
        textAlign: 'center',
        border: '1px solid transparent',
        height: '100%',
        transition: 'all 200ms ease-in-out',
        // border:`1px solid ${theme.colors.serviceCardBorder}`,
        '& .service-card-icon': {
          marginTop: '-9.75rem',
          textAlign: 'center',
          '& img': {
            [theme.breakpoints.down('md')]: {
              width: '80%',
              margin: '0 auto'
            }
          }
        },
        '& h4': {
          fontSize: '1.6525rem',
          fontWeight: '600',
          marginBottom: '0.625rem',
          marginTop: '1rem',
          [theme.breakpoints.down('md')]: {
            fontSize: '1.25rem'
          }
        },
        '& p': {
          fontSize: '1rem',
          fontWeight: '400'
        },
        '&:hover': {
          borderColor: theme.colors.serviceCardBorder,
          boxShadow: theme.shadows[13]
        }
      },

      '& .MuiGrid-container': {
        [theme.breakpoints.down('lg')]: {
          gap: theme.spacing(10)
        }
      }
    }
  }
}))
