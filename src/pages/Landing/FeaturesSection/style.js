import { makeStyles } from '@mui/styles'

import { socalCasinoBg1 } from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  featuresSectionWrap: {
    background: `url(${socalCasinoBg1})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    padding: theme.spacing(5, 1),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1)
    },
    '& .feature-card-wrap': {
      marginTop: theme.spacing(2),
      positin: 'relative',
      '& .feature-card': {
        background: theme.colors.coinBundle,
        borderRadius: theme.spacing(1.25),
        padding: theme.spacing(1.5625, 1.25),
        textAlign: 'center',
        height: '100%',
        '& h4': {
          fontSize: theme.spacing(1.375),
          fontWeight: theme.typography.fontWeightBold,
          lineHeight: '1.5',
          maxWidth: theme.spacing(11.5625),
          margin: '0 auto'
        },
        '& .feature-icon': {
          marginBottom: theme.spacing(0.313),
          '&:before': {
            content: "''",
            position: 'absolute',
            height: theme.spacing(3.125),
            width: theme.spacing(3.125),
            background: theme.colors.YellowishOrange,
            borderRadius: '100%',
            opacity: '0.15'
          },
          '& img': {
            width: theme.spacing(4.6875),
            height: theme.spacing(4.6875),
            margin: '0 auto'
          }
        }
      }
    },
    '& .inner-heading': {
      maxWidth: theme.spacing(43.375),
      margin: '0rem auto ',
      '& h4': {
        marginBottom: theme.spacing(1)
      }
    },
    '& .cta-wrap': {
      padding: theme.spacing(2, 0, 0),
      '& button': {
        '& span': {
          [theme.breakpoints.down('md')]: {
            display: 'none'
          }
        }
      },
      '& .btn-wrap': {
        flexDirection: 'row',
        [theme.breakpoints.down(360)]: {
          flexDirection: 'column'
        }
      }
    },
    '& .feature-graphic-wrap': {
      position: 'absolute',
      left: theme.spacing(0.625),
      top: theme.spacing(-2.5),
      [theme.breakpoints.down('lg')]: {
        left: theme.spacing(-11.25)
      },
      [theme.breakpoints.down('md')]: {
        display: 'none'
      },
      '& img': {
        width: theme.spacing(28.125),
        height: theme.spacing(28.125),
        [theme.breakpoints.down('md')]: {
          width: theme.spacing(15),
          height: theme.spacing(15),
          margin: '0 auto'
        }
      }
    },
    '& .feature-graphic-wrap-mob': {
      display: 'none',
      [theme.breakpoints.down('md')]: {
        display: 'block',
        textAlign: 'center',
        '& img': {
          width: theme.spacing(15),
          height: theme.spacing(15),
          margin: '0 auto'
        }
      }
    }
  }
}))
