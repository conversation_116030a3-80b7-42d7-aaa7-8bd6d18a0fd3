import * as React from 'react'
import useStyles from './style'
import { Box, Button, Grid, Typography } from '@mui/material'
import featureGraphic from '../../../components/ui-kit/icons/webp/featureGraphic.webp'
import featureIcon1 from '../../../components/ui-kit/icons/webp/feature-1.webp'
import featureIcon2 from '../../../components/ui-kit/icons/webp/feature-2.webp'
import featureIcon3 from '../../../components/ui-kit/icons/webp/feature-3.webp'
import featureIcon4 from '../../../components/ui-kit/icons/webp/feature-4.webp'
import shieldWhite from '../../../components/ui-kit/icons/webp/shield-white.webp'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import { usePortalStore } from '../../../store/userPortalSlice'
import Signup from '../../../components/Modal/Signup'
import LazyImage from '../../../utils/lazyImage'

const FeaturesSection = () => {
  const classes = useStyles()
  const portalStore = usePortalStore()
  const handlePayForFree = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  return (
    <>
      <section className={classes.featuresSectionWrap} data-tracking='Home.Features.Section'>
        <figure className='feature-graphic-wrap'>
          {/* <img src={featureGraphic} alt='Graphic' /> */}
          <LazyImage src={featureGraphic} alt='Graphic' />
        </figure>
        <Grid className='inner-container'>
          <Grid className='inner-heading'>
            <Typography variant='h4'>
              {' '}
              Features That Make The <br /> Money Factory Stand Out
            </Typography>
            <Typography>
              The Money Factory offers unique features that keep players coming back. Our platform is built to provide a
              fun, seamless, and secure experience.
            </Typography>
          </Grid>

          <figure className='feature-graphic-wrap-mob'>
            {/* <img src={featureGraphic} alt='Graphic' /> */}
            <LazyImage src={featureGraphic} alt='Graphic' />
          </figure>
          <Grid className='feature-card-wrap'>
            <Grid container spacing={1}>
              <Grid item xs={6} sm={6} lg={3}>
                <Grid className='feature-card'>
                  <Grid className='feature-icon'>
                    {/* <img src={featureIcon1} alt='Icon' /> */}
                    <LazyImage src={featureIcon1} alt='Icon' />
                  </Grid>
                  <h4>Seamless User Experience</h4>
                </Grid>
              </Grid>
              <Grid item xs={6} sm={6} lg={3}>
                <Grid className='feature-card'>
                  <Grid className='feature-icon'>
                    {/* <img src={featureIcon2} alt='Icon' /> */}
                    <LazyImage src={featureIcon2} alt='Icon' />
                  </Grid>
                  <h4>Wide Variety of Games</h4>
                </Grid>
              </Grid>
              <Grid item xs={6} sm={6} lg={3}>
                <Grid className='feature-card'>
                  <Grid className='feature-icon'>
                    {/* <img src={featureIcon3} alt='Icon' /> */}
                    <LazyImage src={featureIcon3} alt='Icon' />
                  </Grid>
                  <h4>Exceptional Customer Service</h4>
                </Grid>
              </Grid>
              <Grid item xs={6} sm={6} lg={3}>
                <Grid className='feature-card'>
                  <Grid className='feature-icon'>
                    {/* <img src={featureIcon4} alt='Icon' /> */}
                    <LazyImage src={featureIcon4} alt='Icon' />
                  </Grid>
                  <h4>Mobile-First Platform</h4>
                </Grid>
              </Grid>
            </Grid>
            <Grid className='cta-wrap'>
              <Grid className='btn-wrap'>
                <Button
                  className='btn btn-secondary'
                  onClick={handlePayForFree}
                  data-tracking='Home.Features.PlayFree.Btn'
                >
                  <ArrowCircleRightOutlinedIcon />
                  Play For Free
                </Button>
                <Button
                  className='btn btn-primary'
                  onClick={handlePayForFree}
                  data-tracking='Home.Features.WelcomeOffer.Btn'
                >
                  <ArrowCircleRightOutlinedIcon />
                  <span>Claim</span> Welcome Offer
                </Button>
              </Grid>
              <Grid className='banner-disclaimer'>
                {/* <img src={shieldWhite} alt='Secure' /> */}
                <LazyImage src={shieldWhite} alt='Secure' />
                <Typography>Trusted by countless players across the US</Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </section>
    </>
  )
}

export default FeaturesSection
