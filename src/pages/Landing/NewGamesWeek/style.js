import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  newGamesWrap: {
    minHeight: theme.spacing(38),
    background: theme.colors.YellowishOrange,
    padding: theme.spacing(5, 1),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 1)
    },
    '& .mob-img-wrap': {
      position: 'absolute',
      top: theme.spacing(-7),
      left: theme.spacing(-3.125),
      [theme.breakpoints.down('lg')]: {
        position: 'relative',
        top: theme.spacing(4.375),
        left: '0',
        textAlign: 'center'
      },
      [theme.breakpoints.down('md')]: {
        position: 'relative',
        top: theme.spacing(4.375),
        left: '0',
        textAlign: 'center'
      },
      '& img': {
        width: theme.spacing(37.5),
        [theme.breakpoints.down('md')]: {
          width: theme.spacing(20.5),
          margin: '0 auto'
        },
        [theme.breakpoints.down('sm')]: {
          width: '100%'
        }
      }
    },
    '& .free-content-wrap': {
      '& .inner-heading': {
        textAlign: 'left',
        [theme.breakpoints.down('md')]: {
          textAlign: 'center'
        },
        '& h4, & p': {
          color: theme.colors.textBlack
        },
        '& h4': {
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(2)
          }
        }
      },
      '& .btn-wrap': {
        justifyContent: 'flex-start',
        [theme.breakpoints.down('md')]: {
          justifyContent: 'center'
        },
        '& .btn-primary': {
          borderWidth: '2px',
          borderColor: theme.colors.textBlack,
          boxShadow: theme.shadows[23],
          fontSize: theme.spacing(1.25),
          '&:hover': {
            color: theme.colors.textBlack,
            boxShadow: 'none'
          }
        }
      },
      '& .banner-disclaimer': {
        justifyContent: 'flex-start',
        [theme.breakpoints.down('md')]: {
          justifyContent: 'center'
        },
        '& p': {
          color: theme.colors.textBlack
        }
      },
      '& .MuiGrid-container': {
        [theme.breakpoints.down('md')]: {
          flexDirection: 'column-reverse'
        }
      },
      '& ul': {
        [theme.breakpoints.down('md')]: {
          paddingLeft: theme.spacing(5)
        },
        [theme.breakpoints.down('sm')]: {
          paddingLeft: theme.spacing(2)
        }
      }
    }
  }
}))
