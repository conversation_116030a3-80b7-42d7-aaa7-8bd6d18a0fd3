import payment1 from '../../../components/ui-kit/icons/footer/payment-1.webp'
import payment2 from '../../../components/ui-kit/icons/footer/payment-2.webp'
import payment3 from '../../../components/ui-kit/icons/footer/payment-3.webp'
import payment4 from '../../../components/ui-kit/icons/footer/payment-4.webp'
import payment5 from '../../../components/ui-kit/icons/footer/payment-5.webp'
import useStyles from './style'
import { Grid, Typography } from '@mui/material'
import 'swiper/css'
import 'swiper/css/effect-coverflow'

const PaymentOptions = () => {
  const classes = useStyles()
  return (
    <Grid className={classes.pymentOptions}>
      <Grid className='inner-heading'>
        <Typography variant='h4'>100% Secure Payment Option</Typography>
      </Grid>
      <Grid className='payment-option-wrap'>
        <img src={payment1} alt='Payment' />
        <img src={payment2} alt='Payment' />
        <img src={payment3} alt='Payment' />
        <img src={payment4} alt='Payment' />
        <img src={payment5} alt='Payment' />
      </Grid>
    </Grid>
  )
}

export default PaymentOptions
