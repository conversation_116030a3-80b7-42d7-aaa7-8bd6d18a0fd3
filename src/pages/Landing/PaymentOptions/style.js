import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  pymentOptions: {
    padding: theme.spacing(4, 0),
    '& .payment-option-wrap': {
      display: 'grid',
      gap: theme.spacing(1),
      gridTemplateColumns: 'repeat(5, auto)',
      // padding:theme.spacing(1, 5),
      maxWidth: theme.spacing(50),
      margin: '0 auto',
      [theme.breakpoints.down('md')]: {
        gridTemplateColumns: 'repeat(3, auto)',
        justifyContent: 'space-evenly'
      },
      [theme.breakpoints.down('sm')]: {
        gridTemplateColumns: 'repeat(2, auto)'
      }
    }
  }
}))
