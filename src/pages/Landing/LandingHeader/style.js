import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  landingHeader: {
    padding: theme.spacing(0.625),
    position: 'fixed',
    width: '100%',
    left: '0',
    background: '#2C2B2C',
    top: '0',
    zIndex: 2,

    backdropFilter: 'blur(20px)',
    [theme.breakpoints.down('xl')]: {
      padding: theme.spacing(0)
    },
    [theme.breakpoints.down('lg')]: {
      padding: theme.spacing(0)
    },

    '& .brand-logo': {
      width: theme.spacing(6.875),
      height: theme.spacing(3.9375),
      // position: "relative",
      // top: theme.spacing(0.313),
      [theme.breakpoints.down('md')]: {
        width: theme.spacing(6.25),
        height: 'auto'
      }
    },
    '& .mob-right-group': {
      display: 'flex',
      alignItems: 'center',
      '& .btn': {
        [theme.breakpoints.down('md')]: {
          padding: '0.25rem 0.5rem !important',
          minHeight: '2.25rem !important',
          minWidth: '6rem',
          fontSize: '0.75rem !important'
        },
        [theme.breakpoints.down('sm')]: {
          minWidth: '4rem'
        },
        '& .btn-content': {
          display: 'flex',
          alignItems: 'center',
          gap: '0.25rem',
          [theme.breakpoints.down('md')]: {
            display: 'none'
          }
        },
        '& img': {
          width: '1.5rem',
          display: 'none',
          [theme.breakpoints.down('md')]: {
            display: 'block'
          }
        },
        '& .mob-text': {
          [theme.breakpoints.up('md')]: {
            display: 'none'
          }
        }
      },
      '& .header-right': {
        [theme.breakpoints.down('md')]: {
          gap: '0.5rem !important'
        },
        [theme.breakpoints.down('sm')]: {
          gap: '0.325rem !important'
        }
      },
      '& .mobile-toggle-menu': {
        [theme.breakpoints.down('md')]: {
          minWidth: '40px !important',
          marginTop: '0 !important'
        }
      },
      '& .cross-icon': {
        top: '0 !important'
      }
    },
    '& .header-right': {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing(0.625),
      [theme.breakpoints.down('lg')]: {
        // display: 'none'
      },
      '& .btn': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(0.313),
        padding: theme.spacing(0.625, 1.875),
        fontSize: theme.spacing(1.25),
        lineHeight: theme.spacing(1.375),
        fontWeight: '600',
        minHeight: theme.spacing(3.125)
      }
    },
    '& .header-menu-wrap': {
      display: 'flex',
      gap: theme.spacing(0.5),
      marginLeft: theme.spacing(3.4375),
      [theme.breakpoints.down('lg')]: {
        display: 'none'
      },
      '& a': {
        padding: theme.spacing(0, 1),
        fontSize: theme.spacing(1.25),
        fontWeight: theme.typography.fontWeightMedium,
        color: theme.colors.textWhite,
        textDecoration: 'none',
        '&:hover': {
          color: theme.colors.YellowishOrange
        },
        '&.active': {
          color: theme.colors.YellowishOrange
        }
      },
      '& button': {
        background: 'transparent',
        border: 'none',
        padding: theme.spacing(0, 1),
        color: theme.colors.textWhite,
        fontSize: theme.spacing(1.25),
        fontWeight: theme.typography.fontWeightMedium,
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      }
    },
    '& .landing-mob-menu': {
      position: 'fixed',
      background: theme.colors.textBlack,
      width: '100%',
      height: '100vh',
      left: '0',
      top: theme.spacing(7.5),
      padding: theme.spacing(2, 1),
      display: 'flex',
      alignItems: 'flex-start',
      justifyContent: 'center',
      transform: 'translateY(-300%)',
      transition: 'all 500ms ease-in-out',
      '& a': {
        padding: theme.spacing(0.8, 1),
        fontSize: theme.spacing(1.875),
        fontWeight: theme.typography.fontWeightExtraBold,
        color: theme.colors.textWhite,
        textDecoration: 'none',
        display: 'block',
        textAlign: 'center',
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      },
      '& button': {
        padding: theme.spacing(0.8, 1),
        fontSize: theme.spacing(1.875),
        fontWeight: theme.typography.fontWeightExtraBold,
        color: theme.colors.textWhite,
        background: 'transparent',
        border: 'transparent',
        textAlign: 'center',
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      },
      '& .mob-sidebar-btn': {
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing(1),
        padding: theme.spacing(1, 0),
        display: 'none',
        '& .btn': {
          gap: theme.spacing(0.2)
        }
      },
      '&.mob-menu-active': {
        transform: 'translateY(-0px)',
        [theme.breakpoints.down('md')]: {
          transform: 'translateY(-40px)'
        }
      },
      '& .menu-wrap': {
        marginTop: theme.spacing(5.3),
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }
    },
    '& .mobile-toggle-menu': {
      display: 'none',
      [theme.breakpoints.down('lg')]: {
        display: 'flex',
        padding: 0,
        justifyContent: 'flex-end'
        // marginTop: theme.spacing(-0.625)
      },
      '& img': {
        width: theme.spacing(1.8),
        height: theme.spacing(1.8)
      },
      '& .cross-icon': {
        display: 'none'
      },
      '&.btn-active': {
        '& .cross-icon': {
          display: 'block',
          color: theme.colors.textWhite,
          fontSize: theme.spacing(2.4),
          position: 'relative',
          left: theme.spacing(0.375),
          top: theme.spacing(0.313)
        },
        '& .menu-icon': {
          display: 'none'
        }
      }
    },
    '& .landing-header-content': {
      display: 'flex',
      alignItems: 'center',
      maxWidth: theme.spacing(77.5),
      margin: '0 auto',
      justifyContent: 'space-between',
      padding: theme.spacing(0.3125),
      [theme.breakpoints.down('lg')]: {
        padding: theme.spacing(0.625)
      }
    },
    '& .info-header': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: '#333234',
      width: '100%',
      padding: '0.25rem 2.5rem 0.25rem 0.5rem',
      '& p': {
        color: theme.colors.YellowishOrange,
        fontWeight: '500',
        lineHeight: '1',
        [theme.breakpoints.down('md')]: {
          fontSize: '0.75rem'
        }
      },
      '& span': {
        color: theme.colors.textWhite,
        minWidth: '57px'
      },
      '& .close-icon': {
        top: '5px',
        width: '0.75rem',
        right: '1rem',
        cursor: 'pointer'
      }
    }
  }
}))
