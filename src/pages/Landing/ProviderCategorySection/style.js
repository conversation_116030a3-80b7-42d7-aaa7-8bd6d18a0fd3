import { makeStyles } from '@mui/styles'

import { socialCasinoBg2 } from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  providerSection: {
    background: `url(${socialCasinoBg2})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    padding: theme.spacing(8, 1, 4),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(3, 1)
    },
    '& .provider-content-wrap': {
      maxWidth: theme.spacing(71.25),
      margin: '0 auto',
      '& .inner-heading': {
        flexDirection: 'column',
        '& h4': {
          '& span': {
            display: 'block'
          }
        }
      },
      '& .games-grid-wrap': {
        marginBottom: theme.spacing(2.5)
      }
    },
    '& .MuiBox-root': {
      padding: '0'
    },
    '& .theme-tabs': {
      marginTop: theme.spacing(1),
      padding: '0',
      // [theme.breakpoints.down('md')]: {
      //     display: "none",
      // },
      '& .MuiTabs-flexContainer': {
        overflowX: 'auto',
        // borderBottom: `2px solid ${theme.colors.textWhite}`,
        // justifyContent: "center",
        [theme.breakpoints.down('lg')]: {
          // flexWrap: 'wrap',
          paddingBottom: '4px',
          marginBottom: '8px',
          // justifyContent: 'center'
          '&::-webkit-scrollbar': {
            height: '2px'
          }
        }
      },
      '& .MuiTabs-indicator': {
        display: 'block',
        background: theme.colors.YellowishOrange,
        bottom: theme.spacing(0.625),
        [theme.breakpoints.down('lg')]: {
          display: 'none'
        }
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        fontWeight: '500',
        textTransform: 'capitalize',
        minHeight: 'auto',
        display: 'flex',
        flexDirection: 'row',
        minWidth: 'auto',
        whiteSpace: 'nowrap',
        alignItems: 'center',
        // flex: 1,
        fontSize: theme.spacing(1.25),
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(1)
        },
        '&.Mui-selected': {
          color: theme.colors.YellowishOrange,
          '&:after': {
            position: 'absolute',
            width: '100%',
            height: '3px',
            background: theme.colors.YellowishOrange,
            content: "''",
            left: '0',
            bottom: '0',
            display: 'none',
            [theme.breakpoints.down('lg')]: {
              display: 'block'
            }
          }
        },
        '& img': {
          margin: '0',
          display: 'none'
        }
      },
      '& MuiBox-root': {
        padding: '0 !important'
      }
    },
    '& .games-grid-wrap': {
      gap: '1rem',
      display: 'grid',
      gridTemplateColumns: 'repeat(5, 1fr)',
      [theme.breakpoints.down('md')]: {
        gridTemplateColumns: 'repeat(3, 1fr)'
      }
    },
    '& .btn-wrap': {
      marginTop: theme.spacing(0.625)
    },
    '& .show-btn-wrap': {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing(1),
      margin: theme.spacing(2, 0, 0),
      position: 'relative',
      justifyContent: 'flex-end',
      '&:before': {
        position: 'absolute',
        width: '100%',
        content: "''",
        height: '1px',
        left: '0',
        background: theme.colors.textWhite
      },
      [theme.breakpoints.down('md')]: {
        justifyContent: 'center'
      },

      '& .MuiDivider-root': {
        border: 'none',
        background: theme.colors.textWhite,
        // width:"100%",
        height: '2px',
        flex: '1',
        [theme.breakpoints.down('md')]: {
          display: 'none'
        }
      },
      '& .MuiButtonBase-root': {
        fontSize: theme.spacing(1.25),
        color: theme.colors.textBlack,
        padding: theme.spacing(0.625, 1.5),
        '&:hover': {
          color: theme.colors.textWhite
        },
        [theme.breakpoints.down('md')]: {
          // padding: theme.spacing(0.25, 0.5),
          fontSize: theme.spacing(1)
        }
      }
    },
    '& .theme-select': {
      display: 'none',
      marginTop: theme.spacing(1),
      '& .MuiFormLabel-root': {
        color: theme.colors.textWhite,
        fontWeight: '700'
      },
      '& .MuiSvgIcon-root': {
        color: theme.colors.textWhite
      },
      '& .MuiInputBase-root': {
        background: theme.colors.serviceCardBg,
        borderRadius: theme.spacing(1.875),
        color: theme.colors.textWhite,
        minWidth: '200px',
        [theme.breakpoints.down('md')]: {
          minWidth: '100%'
        },
        '&.Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: theme.colors.YellowishOrange,
            borderRadius: '30px'
          }
        }
      },
      [theme.breakpoints.down('md')]: {
        width: '100%'
      },
      '& .MuiFormControl-root': {
        [theme.breakpoints.down('md')]: {
          width: '100%'
        }
      }
    },
    '& .inner-heading': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: '10px',
      '& .text-h4': {
        [theme.breakpoints.down('md')]: {
          display: 'none'
        },
        fontWeight: '500'
      }
      // "& .game-category-mob": {
      //     display: "none",
      //     [theme.breakpoints.down("md")]: {
      //         display: "block !important",
      //     },
      // }
    },
    '& .provider-select-wrap': {
      display: 'none',
      textAlign: 'left',
      '& .MuiSelect-select': {
        textAlign: 'center'
      },
      '& img': {
        width: theme.spacing(5),
        margin: '0 auto'
      }
    }
  },

  betterSocialSection: {
    padding: theme.spacing(5, 1, 5),
    maxWidth: theme.spacing(71.25),
    margin: '0 auto',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(5, 1)
    },
    '& .inner-heading': {
      '& h4': {
        marginBottom: theme.spacing(1)
      }
    }
  }
}))
