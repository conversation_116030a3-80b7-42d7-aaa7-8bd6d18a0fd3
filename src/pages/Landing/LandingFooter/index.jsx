import { Grid, Typography } from '@mui/material'
import React from 'react'
import useStyles from './Footer.styles'
import { FooterQuerys } from './../../../../src/reactQuery/index'
import { Link } from 'react-router-dom'
import BrandLogo from '../../../components/ui-kit/icons/brand/brand-logo.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import CmsModal from '../../../components/CmsModal/CmsModal'
import Facebook from '../../../components/ui-kit/icons/webp/facebook.webp'
import Instagram from '../../../components/ui-kit/icons/webp/instagram.webp'
import Telegram from '../../../components/ui-kit/icons/webp/Telegram.webp'
import Twitter from '../../../components/ui-kit/icons/webp/twitter.webp'
import ApartmentIcon from '@mui/icons-material/Apartment'
import LocationOnIcon from '@mui/icons-material/LocationOn'
import MailOutlineIcon from '@mui/icons-material/MailOutline'
import { currectYear } from '../../../utils/helpers'
import { useSiteLogoStore } from '../../../store/store'
import LazyImage from '../../../utils/lazyImage'

const LandingFooter = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const { data: cmsLinks } = FooterQuerys.getCmsQuery()
  const cmsData =
    cmsLinks?.length > 0
      ? cmsLinks?.filter((info) => !info?.isHidden && info?.slug !== 'responsible-sweepstake-rules')
      : []
  // const about = cmsLinks?.length > 0 ? cmsLinks?.filter((info) => info?.slug.split("-")[0] === "about") : []
  // const support = cmsLinks?.length > 0 ? cmsLinks?.filter((info) => info?.slug.split("-")[0] === "support") : []
  // const responsibleGambling = cmsLinks?.length > 0 ? cmsLinks?.filter((info) => info?.slug.split("-")[0] === "responsible") : []
  const logoData = useSiteLogoStore((state) => state)

  const onLinkClick = (e, pathname) => {
    e.preventDefault()
    portalStore.openPortal(() => <CmsModal path={pathname} fromLanding />, 'cmsModal')
  }

  const scrollToSection = (e, id) => {
    e.preventDefault()
    const section = document.getElementById(id)
    if (section) {
      const yOffset = -100 // Adjust this value for how far from the top you want the section to appear
      const yPosition = section.getBoundingClientRect().top + window.pageYOffset + yOffset
      window.scrollTo({ top: yPosition, behavior: 'smooth' })
    }
  }

  return (
    <Grid className={classes.wrapper}>
      <Grid className='footer-content-wrap'>
        <Grid container spacing={{ md: 1, lg: 1.5 }} className='footer-container'>
          <Grid item xs={12} sm={12} md={3} className={classes.footerLogo}>
            <Grid className='footer-logo-content'>
              {/* <img src={logoData?.desktopLogo || BrandLogo} alt={BrandLogo} /> */}
              <LazyImage src={logoData?.desktopLogo || BrandLogo} alt='brand-log' lazy={true} />
              <Typography>
                Our mission is to provide a world-class social casino experience where players can enjoy the thrill of
                winning in a fun, fair, and risk-free environment.
              </Typography>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={2} className='order-3'>
            <Grid className='support-link'>
              <Typography variant='h4'>Links</Typography>
              <Link
                to=''
                onClick={(e) => {
                  scrollToSection(e, 'how-it-works')
                }}
                className='footerLink'
              >
                How It Works
              </Link>
              <Link to='/about-us' className='footerLink'>
                About
              </Link>
              <Link to='/games' className='footerLink'>
                Games
              </Link>
              <Link
                to=''
                onClick={(e) => {
                  scrollToSection(e, 'reviews')
                }}
                className='footerLink'
              >
                Reviews
              </Link>
              <Link to='/faq' className='footerLink'>
                FAQ
              </Link>
              <Link to='/contact-us' className='footerLink'>
                Contact Us
              </Link>
              <Link to='/blog' className='footerLink'>
                Blog
              </Link>
            </Grid>
            <Grid className='social-links mob-social-links'>
              <Typography variant='h4'>Follow Us</Typography>
              <a href='https://x.com/TMFcasino?s=03' target='_blank' rel='noreferrer'>
                {/* <img src={Twitter} alt='twitter Icon' /> */}
                <LazyImage src={Twitter} alt='twitter icon' lazy={true} />
              </a>
              <a
                href='https://www.instagram.com/themoneyfactory/?igsh=OTA0NjFscDMyZ3gz&utm_source=qr'
                target='_blank'
                rel='noreferrer'
              >
                {/* <img src={Instagram} alt='twitter Icon' /> */}
                <LazyImage src={Instagram} alt='instagram-icon' lazy={true} />
              </a>
              <a href='https://www.facebook.com/themoneyfactorycasino?mibextid=LQQJ4d' target='_blank' rel='noreferrer'>
                {/* <img src={Facebook} alt='twitter Icon' /> */}
                <LazyImage src={Facebook} alt='facebook-icon' lazy={true} />
              </a>

              <a href='https://t.me/TheMoneyFactoryUS' target='_blank' rel='noreferrer'>
                {/* <img src={Telegram} alt='twitter Icon' /> */}
                <LazyImage src={Telegram} alt='telegram-icon' lazy={true} />
              </a>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={3} className='order-2'>
            <Grid className='support-link'>
              <Typography variant='h4'>Promise</Typography>
              <Typography>
                We promise a fun and fair gaming experience for all. We are committed to responsible social gaming and
                providing exceptional customer support every step of the way.
              </Typography>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={3} className='order-4'>
            <Grid className='support-link'>
              <Typography variant='h4'>Contact</Typography>
              <Grid className='footerBottomDetail'>
                <Grid className='address-wrap'>
                  <Typography>
                    <ApartmentIcon />
                    <strong>Money Factory LLC</strong>
                  </Typography>
                  <Typography>
                    <LocationOnIcon />8 The Green, Unit #17655 Dover, DE 19901 USA
                  </Typography>
                  <a href='mailto:<EMAIL>'>
                    <MailOutlineIcon />
                    <EMAIL>
                  </a>
                </Grid>
                <Grid className='social-links web-social-links'>
                  <a href='https://x.com/TMFcasino?s=03' target='_blank' rel='noreferrer'>
                    {/* <img src={Twitter} alt='twitter Icon' /> */}
                    <LazyImage src={Twitter} alt='twitter icon' lazy={true} />
                  </a>
                  <a
                    href='https://www.instagram.com/themoneyfactory/?igsh=OTA0NjFscDMyZ3gz&utm_source=qr'
                    target='_blank'
                    rel='noreferrer'
                  >
                    {/* <img src={Instagram} alt='twitter Icon' /> */}
                    <LazyImage src={Instagram} alt='instagram-icon' lazy={true} />
                  </a>
                  <a
                    href='https://www.facebook.com/themoneyfactorycasino?mibextid=LQQJ4d'
                    target='_blank'
                    rel='noreferrer'
                  >
                    {/* <img src={Facebook} alt='twitter Icon' /> */}
                    <LazyImage src={Facebook} alt='facebook-icon' lazy={true} />
                  </a>

                  <a href='https://t.me/TheMoneyFactoryUS' target='_blank' rel='noreferrer'>
                    {/* <img src={Telegram} alt='twitter Icon' /> */}
                    <LazyImage src={Telegram} alt='telegram-icon' lazy={true} />
                  </a>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid className='landing-footer-text'>
          <Typography>
            The Money Factory is owned and operated by the Money Factory LLC of 8 The Green, Unit #17655, Dover, DE
            19901 USA. 18+ © The Money Factory LLC {currectYear}. All rights reserved.
          </Typography>
        </Grid>
      </Grid>
      <Grid className='landing-copyright-wrap'>
        <Typography>© Money Factory LLC</Typography>
        {cmsData && cmsData?.length > 0 ? (
          cmsData?.map((data, index) => {
            return (
              <Link to='' className='footerLink' onClick={(e) => onLinkClick(e, `/cms/${data?.slug}`)} key={index}>
                {data?.title?.EN}
              </Link>
            )
          })
        ) : (
          <></>
        )}
      </Grid>
    </Grid>
  )
}

export default LandingFooter
