import { makeStyles } from '@mui/styles'

import  FooterBg  from '../../../components/ui-kit/icons/svg/footer-bg.svg'
import { LobbyRight, Container } from '../../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    minHeight: 'auto',
    [theme.breakpoints.down('sm')]: {
      marginTop: '0'
    }
  },

  wrapper: {
    backgroundImage: `url(${FooterBg})`,
    padding: theme.spacing(2, 1),
    backgroundSize: 'cover',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 1)
    },
    '& .border-bottom': {
      borderBottom: '1px solid #545454'
    },
    '& .border': {
      marginLeft: theme.spacing(0),
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        border: 'none',
        padding: theme.spacing(0, 1)
      }
    },
    '& .footer-content-wrap': {
      ...Container(theme),
      '& .order-2': {
        order: '2'
      },
      '& .order-3': {
        order: '3'
      },
      '& .order-4': {
        order: '4'
      }
    },
    '& .footerBottomDetail': {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'flex-start',
      flexDirection: 'column',
      // [theme.breakpoints.down('sm')]: {
      //   alignItems: 'center'
      // },
      color: '#fff',
      [theme.breakpoints.down('md')]: {
        marginBottom: '10px',
        padding: '0'
      },
      '& .address-wrap': {
        '& p, & a': {
          display: 'flex',
          fontSize: theme.spacing(1.1),
          fontWeight: theme.typography.fontWeightMedium,
          margin: theme.spacing(0.313, 0),
          gap: theme.spacing(0.625),
          '& svg': {
            color: theme.colors.YellowishOrange
          }
        }
      }
    },
    '& .social-links': {
      marginTop: theme.spacing(1),
      [theme.breakpoints.down('md')]: {
        textAlign: 'center'
      },
      '& a': {
        display: 'inline-block',
        padding: theme.spacing(0.313),
        '& img': {
          width: theme.spacing(2.5)
        }
      },
      '&.web-social-links': {
        [theme.breakpoints.down('lg')]: {
          display: 'none'
        }
      },
      '&.mob-social-links': {
        display: 'none',
        [theme.breakpoints.down('lg')]: {
          display: 'block',
          marginTop: theme.spacing(2),
          textAlign: 'left'
        },
        '& h4': {
          textAlign: 'left',
          fontWeight: theme.typography.fontWeightBold,
          fontSize: theme.spacing(1.75)
        }
      }
    },
    '& .footerBottomText': {
      // borderBottom: '1px solid #545454',
      padding: '2rem 0',
      [theme.breakpoints.down('sm')]: {
        padding: '1rem 0'
      },
      '& p': {
        // color:theme.colors.footerText,
        fontWeight: 400,
        color: theme.colors.landingFooterText,
        [theme.breakpoints.down('md')]: {
          textAlign: 'center',
          fontSize: theme.spacing(0.75)
        }
      },
      '& .footer-contact': {
        marginTop: theme.spacing(2),
        '& a': {}
      },
      '& .payment-grid': {
        display: 'grid',
        gridTemplateColumns: 'repeat(4, 1fr)',
        gap: theme.spacing(0.625)
      }
    },
    '& a': {
      color: theme.colors.textWhite,
      textDecoration: 'none'
    },
    '& .footerDropdownParent': {
      [theme.breakpoints.down('md')]: {
        flexDirection: 'column'
      }
    },
    '& .about-footer': {
      padding: theme.spacing(0, 1, 2),
      borderBottom: '1px solid #625F60',
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(0, 1, 1)
      },
      '& p': {
        fontSize: theme.spacing(1),
        fontWeight: '500',
        color: theme.colors.landingFooterText,
        margin: '0 auto',
        maxWidth: theme.spacing(55.5625),
        textAlign: 'center',

        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75),
          marginTop: 0,
          padding: theme.spacing(0.625, 0),
          textAlign: 'center'
        }
      }
    },
    '& .support-link': {
      textAlign: 'center',
      fontSize: theme.spacing(1.125),
      position: 'relative',
      [theme.breakpoints.down('md')]: {
        textAlign: 'start'
      },
      [theme.breakpoints.down('sm')]: {
        margin: theme.spacing(1, 0)
      },

      '& h4': {
        fontSize: theme.spacing(1.75),
        fontWeight: '900',
        marginBottom: theme.spacing(1.5),
        textAlign: 'left',
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(0)
        }
      },
      '& p': {
        fontSize: theme.spacing(1.1),
        fontWeight: '400',
        textAlign: 'left'
      },
      '& a': {
        display: 'block',
        textAlign: 'left',
        fontWeight: '400',
        padding: theme.spacing(0.313, 0),
        [theme.breakpoints.down('md')]: {
          display: 'inline-block',
          width: '50%'
        }
      }
    },
    '& .landing-footer-text': {
      '& p': {
        maxWidth: theme.spacing(46.875),
        margin: '1.5rem auto',
        fontWeight: theme.typography.fontWeightMedium,
        fontSize: theme.spacing(1.1),
        textAlign: 'center',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1)
        }
      }
    },
    '& .mob-age': {
      textAlign: 'center',
      // [theme.breakpoints.up('md')]: {
      //   display: "none !important"
      // },
      '& a': {
        wordBreak: 'break-all',
        display: 'none',
        fontSize: theme.spacing(1),
        fontWeight: '500',
        [theme.breakpoints.down('sm')]: {
          display: 'block',
          fontSize: theme.spacing(0.7)
        }
      },
      '& img': {
        width: theme.spacing(6.25),
        [theme.breakpoints.down('md')]: {
          width: theme.spacing(3.25)
        }
      }
    },
    '& .web-age-instructions': {
      [theme.breakpoints.down('sm')]: {
        display: 'none !important'
      }
    },
    '& .support-link-wrap': {
      [theme.breakpoints.down('md')]: {
        display: 'none'
      }
    },
    '& .landing-copyright-wrap': {
      borderTop: `1px solid ${theme.colors.copyRIghtBorder}`,
      padding: theme.spacing(1.5, 0),
      display: 'flex',
      justifyContent: 'center',
      [theme.breakpoints.down('md')]: {
        flexWrap: 'wrap'
      },

      '& p, & a': {
        fontSize: theme.spacing(1.0625),
        fontWeight: '300',
        padding: theme.spacing(0, 0.4),
        [theme.breakpoints.down('md')]: {
          textAlign: 'center'
        }
      },
      '& a': {
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      }
    }
  },

  footerLogo: {
    display: 'flex',
    alignItems: 'start',
    flexDirection: 'column',
    // [theme.breakpoints.down('sm')]: {
    //   paddingLeft: '0 !important',
    // },

    [theme.breakpoints.down('md')]: {
      textAlign: 'center',
      justifyContent: 'center'
    },
    '& img': {
      width: theme.spacing(12),
      marginRight: 'auto',
      marginBottom: theme.spacing(1),
      [theme.breakpoints.down('md')]: {
        width: theme.spacing(6),
        margin: '0 auto 1.25rem'
      }
    },
    '& p': {
      lineHeight: '1.6',
      fontWeight: '400',
      fontSize: theme.spacing(1.125)
    },
    '& .footer-logo-content': {
      display: 'flex',
      alignItems: 'center',
      flexDirection: 'column',
      fontWeight: '500',
      position: 'relative',
      [theme.breakpoints.down('sm')]: {
        marginTop: theme.spacing(0),
        '&:before': {
          position: 'absolute',
          right: theme.spacing(-1.375),
          top: '0',
          content: "''",
          width: '1px',
          height: '100%',
          background: theme.colors.footerBorder
        }
      },

      '& img': {
        // margin:theme.spacing(1),
      },
      '& .MuiLink-root': {
        margin: theme.spacing(1),
        [theme.breakpoints.down('md')]: {
          display: 'none'
        }
      },
      '& .mob-age-wrap': {
        textAlign: 'center'
      }
    }
  },

  contentSection: {
    [theme.breakpoints.down('sm')]: {
      paddingLeft: '0 !important'
    },

    '& .footerLink': {
      color: theme.colors.textWhite,
      textDecoration: 'none',
      fontSize: theme.spacing(1),
      padding: theme.spacing(0.5, 0),
      cursor: 'pointer',
      textTransform: 'capitalize',
      wordWrap: 'break-word',
      fontWeight: '600',

      [theme.breakpoints.down('sm')]: {
        textAlign: 'center'
      }
    },
    '& .footer-cms-wrap': {
      display: 'flex',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      margin: '3rem auto 1rem',
      width: '100%',
      maxWidth: theme.spacing(50.375),
      '& .footerLink': {
        position: 'relative',
        fontSize: theme.spacing(1.125),
        fontWeight: '500',
        '&:before': {
          position: 'absolute',
          width: theme.spacing(0.313),
          height: theme.spacing(0.313),
          borderRadius: '100%',
          background: theme.colors.textWhite,
          content: "''",
          left: theme.spacing(-1),
          top: theme.spacing(1),
          [theme.breakpoints.down('md')]: {
            display: 'none'
          }
        },
        [theme.breakpoints.down('sm')]: {
          width: '100',
          fontSize: theme.spacing(0.75),
          textAlign: 'center'
        },
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      }
    },
    '& .footerDropdown': {
      '& em, & svg': {
        color: theme.colors.textWhite,
        position: 'static'
      },
      '& .MuiSelect-select': {
        minWidth: 'auto',
        overflow: 'visible !important',
        padding: '0',
        color: theme.colors.textWhite
      },
      '& .MuiFormControl-root': {
        padding: '0',
        margin: '0'
      },
      '& fieldset': {
        border: '0'
      }
    }
  }
}))
