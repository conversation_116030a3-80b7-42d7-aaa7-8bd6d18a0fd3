import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  newGamesWrap: {
    // minHeight: theme.spacing(38),
    background: theme.colors.YellowishOrange,
    padding: theme.spacing(5, 1),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 1, 0)
    },
    '& .mob-img-wrap': {
      position: 'absolute',
      top: '2px',
      [theme.breakpoints.down('md')]: {
        position: 'relative',
        top: theme.spacing(2),
        textAlign: 'center'
      },
      '& img': {
        width: theme.spacing(40.75),
        [theme.breakpoints.down('lg')]: {
          width: theme.spacing(30.5),
          margin: '0 auto'
        },

        [theme.breakpoints.down('sm')]: {
          width: '100%'
        }
      }
    },
    '& .free-content-wrap': {
      '& .inner-heading': {
        marginBottom: theme.spacing(1),
        textAlign: 'left',
        [theme.breakpoints.down('md')]: {
          textAlign: 'center'
        },
        '& h4': {
          color: theme.colors.textBlack,
          marginBottom: theme.spacing(1),
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(2)
          }
        },
        '& p': {
          color: theme.colors.textBlack,
          fontWeight: '400'
        }
      },

      '& .btn-wrap': {
        justifyContent: 'flex-start',
        [theme.breakpoints.down('md')]: {
          justifyContent: 'center'
        },
        '& .btn-primary': {
          borderWidth: '2px',
          borderColor: theme.colors.textBlack,
          boxShadow: theme.shadows[23],
          fontSize: theme.spacing(1.25),
          '&:hover': {
            color: theme.colors.textBlack,
            boxShadow: 'none'
          }
        }
      },
      '& .banner-disclaimer': {
        justifyContent: 'flex-start',
        [theme.breakpoints.down('md')]: {
          justifyContent: 'center'
        },
        '& p': {
          color: theme.colors.textBlack
        }
      }
    }

    // "& .no-purchase-main-wrap": {
    //     [theme.breakpoints.down('lg')]: {
    //         display: 'grid',
    //         justifyContent: 'center'
    //     },
    //     [theme.breakpoints.down('md')]: {
    //         display: "none"
    //     },
    // },
    // "& .no-purchase-main-wrap-mob": {
    //     [theme.breakpoints.up('md')]: {
    //         display: "none"
    //     },
    // },
    // "& .no-purchase-wrap": {
    //     background: theme.colors.noPurchaseBorder,
    //     padding: "1px",
    //     borderRadius: theme.spacing(1.25),
    //     display: "inline-block",
    //     overflow: "hidden",
    //     maxWidth: theme.spacing(30.875),
    //     [theme.breakpoints.down('md')]: {
    //         margin: "0 auto",
    //         display: "block"
    //     },
    //     "& .no-purchase-card": {
    //         background: theme.colors.noPurchaseBg,
    //         padding: theme.spacing(2),
    //         borderRadius: theme.spacing(1.25),
    //         textAlign: "center",
    //         "& h4": {
    //             fontSize: theme.spacing(1.5625),
    //             fontWeight: "700",
    //             marginBottom: theme.spacing(0.625),
    //         },
    //         "& p": {
    //             fontSize: theme.spacing(1.1875),
    //             fontWeight: "500",
    //         }

    //     }
    // },
    // "& .new-games-left": {
    //     textAlign: "center",
    //     "& h4": {
    //         fontSize: theme.spacing(1.5625),
    //         fontWeight: "700",
    //         textTransform: "uppercase",
    //         marginBottom: theme.spacing(0.625),
    //     },
    // },
    // "& .MuiGrid-container": {
    //     alignItems: "center",
    // },
    // "& .games-detail-section": {
    //     margin: theme.spacing(2, 0),
    //     "& img": {
    //         width: '100%',
    //         height: '100%',
    //         maxWidth: "80%"
    //     },
    //     "& .game-grid": {
    //         display: "grid",
    //         gridTemplateColumns: "repeat(2, 1fr)",
    //         [theme.breakpoints.down('sm')]: {
    //             gridTemplateColumns: "repeat(1, 1fr)",
    //         },
    //         gap: theme.spacing(1),
    //         "& .game-grid-card": {
    //             background: theme.colors.serviceCardBg,
    //             padding: "2rem",
    //             borderRadius: "1.25rem",
    //             border: "1px solid transparent",
    //             height: "100%",
    //             transition: "all 200ms ease-in-out",
    //             // border:`1px solid ${theme.colors.serviceCardBorder}`,
    //             [theme.breakpoints.down('md')]: {
    //                 borderColor: theme.colors.serviceCardBorder,
    //                 boxShadow: theme.shadows[13],
    //             },

    //             "& img": {
    //                 maxWidth: "80px !important",
    //             },
    //             "& h4": {
    //                 fontSize: "1.25rem",
    //                 fontWeight: "700",
    //                 marginBottom: "0.625rem",
    //                 marginTop: "1rem",
    //             },
    //             '& p': {
    //                 fontSize: "1rem",
    //                 fontWeight: "500",
    //             },
    //             "&:hover": {
    //                 borderColor: theme.colors.serviceCardBorder,
    //                 boxShadow: theme.shadows[13],
    //             },
    //             "& .game-grid-top": {
    //                 display: "flex",
    //                 alignItems: "center",
    //                 marginBottom: theme.spacing(2),
    //                 gap: theme.spacing(2),
    //                 "& h4": {
    //                     margin: "0",
    //                 },
    //                 "& p": {
    //                     textAlign: "left",
    //                 }
    //             }
    //         }
    //     }
    // }
  }
}))
