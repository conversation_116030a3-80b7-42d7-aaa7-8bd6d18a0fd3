import Signup from '../../../components/Modal/Signup'
import shieldBlack from '../../../components/ui-kit/icons/webp/shield-black.webp'
import weeklyGames from '../../../components/ui-kit/icons/webp/New-Games-Weekly.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import useStyles from './style'
import { Button, Grid, Typography } from '@mui/material'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import LazyImage from '../../../utils/lazyImage'

const WhyPlayMneyFactory = () => {
  const classes = useStyles()
  const portalStore = usePortalStore()
  const handlePayForFree = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  return (
    <section className={classes.newGamesWrap} data-tracking='Home.WhyPlay.Section'>
      <Grid className='inner-container'>
        <Grid className='free-content-wrap'>
          <Grid container spacing={1}>
            <Grid item xs={12} md={6} lg={6}>
              <Grid className='inner-heading'>
                <Typography variant='h4'>Why Play at The Money Factory?</Typography>
                <Typography>
                  The Money Factory offers a secure, fun, and risk-free gaming experience. With our social platform, you
                  can enjoy games without the financial risks of traditional gambling.
                </Typography>
              </Grid>
              <Grid className='free-listing'>
                <ul>
                  <li>
                    <strong>Safe Platform:</strong> Secure and smooth gameplay on all devices. Log in and play from
                    anywhere, anytime.
                  </li>
                  <li>
                    <strong>Free to Play:</strong> Have fun without spending any money.
                  </li>
                </ul>
                {/* <Button type='button' className='btn btn-primary' >Play For Free</Button> */}
                <Grid className='btn-wrap'>
                  <Button
                    className='btn btn-primary'
                    onClick={handlePayForFree}
                    data-tracking='Home.WhyPlay.PlayForFree.Btn'
                  >
                    <ArrowCircleRightOutlinedIcon />
                    Play For Free Now
                  </Button>
                </Grid>
                <Grid className='banner-disclaimer'>
                  {/* <img src={shieldBlack} alt='Secure' /> */}
                  <LazyImage src={shieldBlack} alt='Secure' />
                  <Typography>Trusted by countless players across the US</Typography>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6} lg={6}>
              <Grid className='mob-img-wrap'>
                {/* <img src={weeklyGames} alt='MobIcon' /> */}
                <LazyImage src={weeklyGames} alt='MobIcon' />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </section>
  )
}

export default WhyPlayMneyFactory
