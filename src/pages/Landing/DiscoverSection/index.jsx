import { lazy, Suspense } from 'react'
import Signup from '../../../components/Modal/Signup'
import shieldWhite from '../../../components/ui-kit/icons/webp/shield-white.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import useStyles from './style'
import { Button, Grid, Typography } from '@mui/material'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import LazyImage from '../../../utils/lazyImage'
const LazyVideoViewer = lazy(() => import('./LazyVideoViewer'))

const DiscoverSection = () => {
  const classes = useStyles()
  const portalStore = usePortalStore()
  const handlePayForFree = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  return (
    <section className={classes.newGamesWrap} data-tracking='Home.DiscoverWhyLove.Section'>
      <Grid className='inner-container'>
        <Grid className='free-content-wrap'>
          <Grid className='inner-heading'>
            <Typography variant='h2'>
              Discover <span>Why Players Love</span> The Money Factory
            </Typography>
            <Typography>
              Watch the video below to find out why The Money Factory is the top choice for social casino games. Learn
              how we provide a secure, risk-free, and fun gaming experience for everyone!
            </Typography>
          </Grid>
          <figure>
            <Suspense fallback={<div>Loading video...</div>}>
              <LazyVideoViewer videoId='1017329102' />
            </Suspense>
          </figure>
          <Grid className='cta-wrap'>
            <Grid className='btn-wrap'>
              <Button
                className='btn btn-secondary'
                onClick={handlePayForFree}
                data-tracking='Home.DiscoverWhyLove.PlayFree.Btn'
              >
                <ArrowCircleRightOutlinedIcon />
                Play For Free{' '}
              </Button>
              <Button
                className='btn btn-primary'
                onClick={handlePayForFree}
                data-tracking='Home.DiscoverWhyLove.WelcomeOffer.Btn'
              >
                <ArrowCircleRightOutlinedIcon />
                <span>Claim</span> Welcome Offer{' '}
              </Button>
            </Grid>
            <Grid className='banner-disclaimer'>
              {/* <img src={shieldWhite} alt='Secure' /> */}
              <LazyImage src={shieldWhite} alt='Secure' lazy={false} />
              <Typography>Trusted by countless players across the US</Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </section>
  )
}

export default DiscoverSection
