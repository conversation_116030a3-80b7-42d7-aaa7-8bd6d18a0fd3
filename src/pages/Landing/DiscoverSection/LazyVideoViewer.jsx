import { useState } from 'react'
import { Box } from '@mui/material'
import PlayCircleFilledWhiteIcon from '@mui/icons-material/PlayCircleFilledWhite'

const LazyVideoViewer = ({ videoId }) => {
  const [isPlaying, setIsPlaying] = useState(false)

  const thumbnailUrl = `https://vumbnail.com/${videoId}.jpg`
  const videoUrl = `https://player.vimeo.com/video/${videoId}?autoplay=1&title=0&portrait=0&byline=0`

  return (
    <Box
      className='iframe-wrap'
      sx={{
        position: 'relative',
        width: '100%',
        // paddingTop: '56.25%', // 16:9 aspect ratio
        overflow: 'hidden',
        borderRadius: 2,
        boxShadow: 3,
        cursor: 'pointer'
      }}
      onClick={() => setIsPlaying(true)}
    >
      {isPlaying
        ? (
          <Box
            component='iframe'
            src={videoUrl}
            title='Vimeo video'
            allow='autoplay; fullscreen'
            allowFullScreen
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              border: 0
            }}
          />)
        : (
          <>
            <Box
              component='img'
              src={thumbnailUrl}
              alt='Vimeo thumbnail'
              loading='lazy'
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                borderRadius: 2
              }}
            />
            <PlayCircleFilledWhiteIcon
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                fontSize: 80,
                color: 'white',
                opacity: 0,
                transition: 'opacity 0.3s',
                '&:hover': {
                  opacity: 0
                }
              }}
            />
          </>)}
    </Box>
  )
}

export default LazyVideoViewer
