import { makeStyles } from '@mui/styles'

import { socialCasinoBg2 } from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  newGamesWrap: {

    background: `url(${socialCasinoBg2})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    padding: theme.spacing(5, 1),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 1, 0)
    },
    '& .mob-img-wrap': {
      position: 'absolute',
      top: '2px',
      '& img': {
        width: theme.spacing(40.75)
      }
    },
    '& .free-content-wrap': {
      '& .iframe-wrap': {
        maxWidth: theme.spacing(50),
        margin: '0 auto',
        aspectRatio: '1.77777',
        borderRadius: theme.spacing(1.25),
        backgroundColor: theme.colors.textBlack,
        overflow: 'hidden',
        '& iframe': {
          border: 'none',
          height: '100%',
          width: '100%',
          display: 'flex',
          backgroundColor: theme.colors.textBlack
        }
      }

    },
    '& .cta-wrap': {
      padding: theme.spacing(2, 0),
      '& button': {
        '& span': {
          [theme.breakpoints.down('md')]: {
            display: 'none'
          }
        }
      }
    }
  }
}))
