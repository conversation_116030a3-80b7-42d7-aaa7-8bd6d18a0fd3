import Signup from '../../../components/Modal/Signup'
import shieldWhite from '../../../components/ui-kit/icons/webp/shield-white.webp'
import whyChooseGraphic from '../../../components/ui-kit/icons/webp/why-money-graphic.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import useStyles from './style'
import { Box, Button, Grid, Typography } from '@mui/material'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import LazyImage from '../../../utils/lazyImage'

const ChooseMoneyFactory = () => {
  const classes = useStyles()
  const portalStore = usePortalStore()
  const handlePayForFree = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  return (
    <section className={classes.whyChooseSection} id='about' data-tracking='Home.WhyChoose.Section'>
      <Grid className='why-choose-content'>
        <Grid className='inner-heading-why-section'>
          <Typography variant='h3'>Why Choose The Money Factory?</Typography>
        </Grid>
        <Grid className='why-choose-content-box'>
          <figure className='why-choose-top-group'>
            {/* <img src={whyChooseGraphic} alt='Group' /> */}
            <LazyImage src={whyChooseGraphic} alt='why-choose-moneyfactory' />
          </figure>
          <Typography variant='h3'>Experience the excitement of risk-free gaming with real rewards.</Typography>
          <Grid className='choose-listing-wrap'>
            <article>
              <ul>
                <li>
                  <strong>Safe and Secure:</strong> Enjoy a secure platform with top-level encryption, keeping your
                  information safe.
                </li>
                <li>
                  <strong>Mobile-Friendly:</strong> Play anywhere, anytime, on your smartphone or tablet.
                </li>
                <li>
                  <strong>1,000+ Exciting Games:</strong> Choose from hundreds of thrilling games updated regularly.
                </li>
                <li>
                  <strong>24/7 Customer Support:</strong> Get help when you need it with our around-the-clock support
                  team.
                </li>
              </ul>
            </article>
          </Grid>
        </Grid>
        <Grid className='cta-wrap'>
          <Grid className='btn-wrap'>
            <Button
              className='btn btn-secondary'
              onClick={handlePayForFree}
              data-tracking='Home.WhyChoose.PlayForFree.Btn'
            >
              <ArrowCircleRightOutlinedIcon />
              Play For Free{' '}
            </Button>
            <Button
              className='btn btn-primary'
              onClick={handlePayForFree}
              data-tracking='Home.WhyChoose.ClaimOffer.Btn'
            >
              <ArrowCircleRightOutlinedIcon />
              Welcome Offer{' '}
            </Button>
          </Grid>
          <Grid className='banner-disclaimer'>
            {/* <img src={shieldWhite} alt='Secure' /> */}
            <LazyImage src={shieldWhite} alt='Secure' />
            <Typography>Trusted by countless players across the US</Typography>
          </Grid>
        </Grid>
      </Grid>
    </section>
  )
}

export default ChooseMoneyFactory
