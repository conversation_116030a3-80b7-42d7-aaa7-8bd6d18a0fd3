import { makeStyles } from '@mui/styles'

import { checkMark, whyChooseBg } from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  whyChooseSection: {
    padding: theme.spacing(5, 1),
    backgroundImage: `url(${whyChooseBg})`,
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center, center',
    backgroundSize: 'cover',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 0)
    },
    '& .why-choose-content': {
      maxWidth: theme.spacing(71.25),
      margin: '0 auto',
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(0, 1)
      },
      '& h3': {
        textAlign: 'center',
        fontSize: theme.spacing(2.75),
        fontWeight: '900',
        [theme.breakpoints.down('md')]: {
          paddingTop: theme.spacing(2.25)
        },
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1.875),
          lineHeight: '2.7rem',
          paddingTop: 0
        }
      },
      '& .why-choose-content-box': {
        padding: theme.spacing(1.875),
        borderRadius: theme.spacing(1.25),
        border: `2px solid ${theme.colors.YellowishOrange}`,
        marginBottom: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(3, 1, 1)
        },
        '& .why-choose-top-group': {
          textAlign: 'center',
          display: 'flex',
          marginTop: theme.spacing(-7.5),
          '& img': {
            width: theme.spacing(25),
            margin: '0 auto',
            [theme.breakpoints.down('sm')]: {
              width: theme.spacing(15)
            }
          }
        },
        '& h3': {
          textAlign: 'center',
          fontSize: theme.spacing(1.8125),
          fontWeight: '700',
          [theme.breakpoints.down('md')]: {
            paddingTop: theme.spacing(1)
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1)
          }
        },
        '& .choose-listing-wrap': {
          margin: theme.spacing(2, 0),
          '& ul': {
            paddingLeft: theme.spacing(2),
            listStyle: 'none',
            display: 'flex',
            flexWrap: 'wrap',

            [theme.breakpoints.down('sm')]: {
              flexDirection: 'column'
            },
            '& li': {
              fontSize: theme.spacing(1.25),
              lineHeight: theme.spacing(2),
              marginBottom: theme.spacing(1),
              width: '50%',
              position: 'relative',
              paddingRight: theme.spacing(2),
              // fontSize: theme.spacing(0.875),
              [theme.breakpoints.down('sm')]: {
                width: '100%'
              },

              '&:last-child': {
                marginBottom: '0'
              },
              '&:before': {
                position: 'absolute',
                content: "''",
                background: `url(${checkMark})`,
                height: theme.spacing(1.25),
                width: theme.spacing(1.25),
                backgroundSize: theme.spacing(1.25),
                left: theme.spacing(-1.875),
                top: theme.spacing(0.1875)
              }
            }
          }
        }
      },
      '& .inner-heading': {
        marginBottom: theme.spacing(7)
      }
    }
  }
}))
