import { Typography, Box } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { maintenanceSocket } from '../../utils/socket'
import { useUserStore } from '../../store/useUserSlice'

import closeIcon from '../../components/ui-kit/icons/png/sidebar-cross.png'
import { getCookie, setCookie } from '../../utils/cookiesCollection'

const HeaderRibbon = () => {
  const MaintenanceSocketConnection = useUserStore((state) => state.MaintenanceSocketConnection)

  const [socketRibbonData, setSocketData] = useState(() => {
    const storedData = getCookie('socketRibbonData')
    return storedData ? JSON.parse(storedData) : null
  })

  const [countdown, setCountdown] = useState(() => {
    const storedTime = getCookie('remainingRibbonSeconds')
    const storedTimestamp = getCookie('lastUpdatedTimestamp')

    if (storedTime && storedTimestamp) {
      const elapsedSeconds = Math.floor((Date.now() - parseInt(storedTimestamp, 10)) / 1000)
      const adjustedCountdown = Math.max(parseInt(storedTime, 10) - elapsedSeconds, 0)
      return adjustedCountdown
    }
    return null
  })

  useEffect(() => {
    if (MaintenanceSocketConnection) {
      maintenanceSocket.on('USER_NOTIFICATION_UPDATE', (data) => {
        setSocketData(data?.data)

        setCookie('socketRibbonData', JSON.stringify(data?.data || {}), 1)
        const remainingSeconds = Math.floor((data?.data?.remainingTime || 0) * 60) // Convert minutes to seconds

        setCookie('remainingRibbonSeconds', remainingSeconds, 1)
        setCookie('lastUpdatedTimestamp', Date.now(), 1)

        setCountdown(remainingSeconds)
      })
    }
  }, [MaintenanceSocketConnection])

  useEffect(() => {
    if (countdown === null) return

    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev > 0) {
          setCookie('remainingRibbonSeconds', prev - 1, 1)
          setCookie('lastUpdatedTimestamp', Date.now(), 1)
          return prev - 1
        }
        return 0
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [countdown])

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`
  }

  const [closeRibbon, setCloseRibbon] = useState(true)
  const handleClose = () => setCloseRibbon(false)

  const style = {
    '& .close-icon': {
      position: 'absolute',
      top: '1rem',
      right: '1rem',
      cursor: 'pointer',
      width: '0.875rem'
    }
  }

  return (
    <>
      {socketRibbonData?.isRibbon && closeRibbon && (
        <Box className='info-header' sx={style}>
          <Typography variant='body1'>
            {socketRibbonData?.startMessage}{' '}
            {countdown ? <Typography component='span'>{formatTime(countdown)}</Typography> : ''}{' '}
            {socketRibbonData?.endMessage}
          </Typography>
          {socketRibbonData?.isCancelActive && <img className='close-icon' onClick={handleClose} src={closeIcon} />}
        </Box>
      )}
    </>
  )
}

export default HeaderRibbon
