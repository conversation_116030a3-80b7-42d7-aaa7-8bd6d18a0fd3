import React from 'react'
import useStyles from './../Lobby/Lobby.styles'
import { Box, Grid, Typography } from '@mui/material'
import './NotFound.scss'
import moneyDummy from '../../components/ui-kit/icons/opImages/money-dummy.webp'
import TagManager from 'react-gtm-module'

const NotFound = () => {
  const classes = useStyles()
  const isAllowedUserAccess = localStorage.getItem('allowedUserAccess')
  TagManager.dataLayer({
    dataLayer: {
      event: 'geo_block',
      access_allowed: isAllowedUserAccess
    }
  })
  return (
    <Grid className={classes.lobbyRight}>
      <Grid className={classes.wrapper}>
        <Grid container spacing={1}>
          <Grid item xs={12} sm={6} md={5}>
            <Box className='img-box'>
              <img src={moneyDummy} alt='Money dummy graphic' />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={7}>
            <Box className='text-box'>
              <Typography className='title-text'>Thank you for your interest</Typography>
              <Typography className='subtitle-text'>
                Unfortunately, The Money Factory is not available in this region. If you believe you should have access,
                please contact us.
              </Typography>
              <Typography className='pera-text'>Kind Regards, The Money Factory Team.</Typography>
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default NotFound
