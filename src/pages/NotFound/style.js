import { Box, styled } from '@mui/material'

import MaintananceBg from '../../components/ui-kit/icons/webp/maintance-bg.webp'
const MaintenanceWrapper = styled(Box)(({ theme }) => ({
  '& .maintenance-wrap': {
    background: '#0d0d0d',
    height: '100vh',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '0.75rem',
    '& .maintenance-box': {
      maxWidth: '42rem',
      borderRadius: '8px',
      width: '100%',
      backgroundImage: `url(${MaintananceBg})`,
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat',
      padding: '3.125rem 2.25rem',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column',
      textAlign: 'center',
      gap: '6px',
      [theme.breakpoints.down('sm')]: {
        padding: '1.25rem'
      },
      '& img': {
        maxWidth: '14rem',
        width: '100%',
        marginBottom: '0.75rem'
      },
      '& h3': {
        fontSize: '2rem',
        fontWeight: '700',
        color: theme.colors.YellowishOrange,
        [theme.breakpoints.down('sm')]: {
          fontSize: '1.25rem'
        }
      },
      '& p': {
        fontSize: '1.5rem',
        fontWeight: '600',
        [theme.breakpoints.down('sm')]: {
          fontSize: '0.875rem'
        }
      },
      '& .MuiButtonBase-root': {
        marginTop: '1rem',
        fontWeight: '700',
        fontSize: '1rem',
        [theme.breakpoints.down('sm')]: {
          padding: '0.25rem 1rem'
        }
      }
    }
  },
  '& .maintenancetimer': {
    display: 'flex',
    marginTop: '1rem',
    alignItems: 'center',
    // gap: theme.spacing(1),
    fontWeight: theme.typography.fontWeightBold,
    [theme.breakpoints.down(1300)]: {
      // gap: theme.spacing(0.5)
    },
    '& .counter-divider': {
      // display: 'none',
      fontSize: theme.spacing(2),
      padding: theme.spacing(0, 0.313),
      color: '#AAA9A9',
      marginTop: theme.spacing(-1.5)
    },
    '& .maintenanceCountdown': {
      textAlign: 'center',
      display: 'flex',
      flexDirection: 'column',
      background: 'linear-gradient(176.23deg, #cccccc 25.71%, #656464 96.95%)',
      borderRadius: '10px',
      padding: '1.5px',
      boxShadow: '0px 0px 8.19px 0px #CCCCCC',
      marginBottom: '0.25rem'

      // gap: theme.spacing(0.313),
      // gap: theme.spacing(0.313),
    },
    '& .MuiTypography-span': {
      fontSize: theme.spacing(0.875),
      padding: '2px 0',
      color: '#949494'
    },
    '& .maintenanceTime': {
      // background: theme.colors.counterBg,
      color: theme.colors.textWhite,
      fontSize: theme.spacing(1.75),
      fontWeight: '700',
      padding: '12px 16px',
      textAlign: 'center',
      // fontStyle: 'italic',
      // transform: 'skewX(-20deg)',
      // boxShadow: theme.shadows[15],
      minWidth: theme.spacing(4),
      borderRadius: '10px',
      // borderBottomRightRadius: '0',
      background: '#313131',
      // borderBottomLeftRadius: '0',
      // minHeight: theme.spacing(2.6969),

      // [theme.breakpoints.down('xl')]: {
      //   fontSize: theme.spacing(1.8),
      //   minWidth: theme.spacing(5.5)
      // },
      [theme.breakpoints.down(1300)]: {
        fontSize: theme.spacing(1.5),
        minWidth: theme.spacing(3.5),
        padding: '12px'
      },
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(1.25),
        minWidth: theme.spacing(3.25)
      }
    }
  }
}))

export default MaintenanceWrapper
