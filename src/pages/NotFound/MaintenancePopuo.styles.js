import { makeStyles } from '@mui/styles'

import { MaintenanceBg } from '../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
    purchaseModal: {
        backgroundImage: `url(${MaintenanceBg})`,
        "& .modal-close": {
            color: theme.colors.textWhite,
            marginLeft: "auto",
            position: "absolute",
            right: theme.spacing(1),
            top: theme.spacing(1),
            cursor: "pointer",
            zIndex: "5",
        },
        position: "relative",
        "& .maintenance-img-wrap": {
            textAlign: "center",
            "& .maintenance-img": {
                width: "70%",
                margin: "0 auto",
                [theme.breakpoints.down('md')]: {
                    display: "none",
                },
            },
        },
        "& .purchase-content": {
            position: "relative",
            padding: theme.spacing(3, 1),
            [theme.breakpoints.down('md')]: {
                padding: theme.spacing(2),
                textAlign: "center",
            },

            "& .MuiTypography-h4": {
                color: theme.colors.YellowishOrange,
                fontWeight: 700,
                fontSize: theme.spacing(1.625),
                marginBottom: theme.spacing(1),

            },
            '& .MuiTypography-h3': {
                color: theme.colors.YellowishOrange,
                fontWeight: 700,
                fontSize: theme.spacing(1.375),
                marginBottom: theme.spacing(2),
                "& span": {
                    color: theme.colors.greenActive
                }

            },
            "& p": {
                fontSize: theme.spacing(1),
                fontWeight: "600",
                lineHeight: theme.spacing(1.25),
                color: theme.colors.textWhite,
                margin: theme.spacing(0, 0, 2),
                "&.text-highlight": {
                    color: theme.colors.YellowishOrange,

                }
            },
            "&:before": {
                position: "absolute",
                left: theme.spacing(-1.875),
                top: "50%",
                transform: "translate(-50%, -50%)",
                background: theme.colors.sitemapDot,
                width: "2px",
                height: "80%",
                content: "''"
            }
        },
        "& .MuiGrid-container": {
            alignItems: "center",
        }

    }

}))
