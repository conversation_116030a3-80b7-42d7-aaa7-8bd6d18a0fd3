import React from 'react'
import { Grid, IconButton, Typography, DialogContent, Box } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import useStyles from './MaintenancePopuo.styles'
import { usePortalStore } from '../../store/userPortalSlice'
import Maintenance from '../../components/ui-kit/icons/webp/maintance.webp'

const MaintenancePopup = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <Grid>
      <DialogContent className={classes.purchaseModal}>
        <Box className={classes.redeemModalContainer}>
          <Grid display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
            <Grid className='modal-close'>
              <IconButton edge='start' color='inherit' className='close' onClick={handleClose} aria-label='close'>
                <CloseIcon />
              </IconButton>
            </Grid>
          </Grid>
          <Grid container spacing={1}>
            <Grid item xs={12} lg={5}>
              <Grid className='maintenance-img-wrap'>
                <img src={Maintenance} className='maintenance-img' alt='Maintenance' />
              </Grid>
            </Grid>
            <Grid item xs={12} lg={7}>
              <Grid className='purchase-content'>
                <Typography variant='h4'>Hi, we'll be going under maintenance in next 30 minutes</Typography>
                <Typography variant='h3'>October 17 2024 4:30 AM PST - October 17 2024 5:00 AM PST</Typography>
              </Grid>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
    </Grid>
  )
}

export default MaintenancePopup
