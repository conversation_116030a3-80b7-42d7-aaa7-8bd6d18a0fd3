import React, { useEffect } from 'react'
import useStyles from './style'
import { Box, Button, CircularProgress, Grid, Typography } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import BonusBadge from '../../components/ui-kit/icons/svg/bonus.svg'
import FreeTag from '../../components/ui-kit/icons/svg/free.svg'
import Specialbanner from '../../components/ui-kit/icons/svg/specialbanner.svg'
import offerGraphics from '../../components/ui-kit/icons/opImages/offer-graphics.webp'
import smallCoinGraphic from '../../components/ui-kit/icons/opImages/small-coin-graphic.webp'
import usePackages from '../Store/hooks/usePackages'
import { formatPriceWithCommas, formatValueWithB, formatAmount } from '../../utils/helpers'
import StepperForm from '../../components/StepperForm'
import BannerManagement from '../../components/BannerManagement'
import { useBannerStore } from '../../store/useBannerSlice'
import { useLocation, useParams } from 'react-router-dom'
import PaymentStatus from '../../components/PaymentStatus'
import usePaysafePayment from '../Store/PaymentModal/hooks/usePaysafe'
import PaymentLoader from '../../components/Loader/PaymentLoader'
import { usePaymentProcessStore } from '../../store/store'
import cardCoin2 from '../../components/ui-kit/icons/utils/card-coin2.webp'
import usdCash from '../../components/ui-kit/icons/utils/usd-cash.webp'
// import PaymentConnectionPopup from '../Store/PaymentModal/components/PaymentConnectionPopup'
// import JackpotBadge from '../Jackpot/JackpotBadge'

const Package = () => {
  const { status } = useParams()
  const { setPackageData } = usePaysafePayment()
  const portalStore = usePortalStore()
  const classes = useStyles()
  const { packagePage } = useBannerStore((state) => state)
  const location = useLocation()
  const { isPaymentScreenLoading, setIsPaymentScreenLoading } = usePaysafePayment()
  const cancelDepositOpen = usePaymentProcessStore((state) => state.cancelDepositOpen)

  const { packageData, isloading, refetch } = usePackages()
  const packageRow = packageData?.rows?.[0]

  const handleBuyNow = (item) => {
    ;(function () {
      window._conv_q = window._conv_q || []
      _conv_q.push(['pushRevenue', 'credit', item, '100466670'])
    })()
    setPackageData(item)
    portalStore.openPortal(
      () => (
        <>
          <Box className='stepper-outer-box'>
            <StepperForm stepperCalledFor={'purchase'} packageDetails={item} />
          </Box>
        </>
      ),
      'StepperModal'
    )
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const statuss = params.get('status')

    if (statuss === 'success' || statuss === 'failed' || statuss === 'cancelled') {
      const data = {
        transactionId: params.get('transactionId'),
        status: params.get('status'),
        paymentMethod: params.get('paymentMethod'),
        scCoin: params.get('scCoin'),
        gcCoin: params.get('gcCoin'),
        bonusSc: params.get('bonusSc'),
        bonusGc: params.get('bonusGc'),
        amount: params.get('amount')
      }
      handlePaymentSuccess(data)
    }
  }, [location])

  const handlePaymentSuccess = (data) => {
    refetch()
    portalStore.openPortal(() => <PaymentStatus paymentDetails={data} />, 'loginModal')
  }

  return (
    <>
      {status && <PaymentStatus status={status} isTrustly />}
      {(isPaymentScreenLoading || cancelDepositOpen) && <PaymentLoader />}
      <Grid className={classes.lobbyRight}>
        <Box className='package-page'>
          <Grid>
            <BannerManagement bannerData={packagePage} />
          </Grid>

          {isloading && (
            <div>
              <CircularProgress size={24} style={{ marginLeft: 8 }} />
              Loading...
            </div>
          )}
          {packageRow?.isSpecialPackage === true && (
            <Grid className='special-offer-section'>
              <Grid className='package-card'>
                <Grid container spacing={0.5}>
                  <Grid item xs={6} lg={3}>
                    <img
                      src={packageRow?.imageUrl ? packageRow?.imageUrl : offerGraphics}
                      alt='Offer'
                      className='offer-graphics'
                    />
                  </Grid>
                  <Grid item xs={12} lg={6} className='package-details-grid'>
                    <Box className={classes.container}>
                      <Grid className='package-details' spacing={0.5} justifyContent='center' alignItems='center'>
                        <Grid className='package-content'>
                          <Grid className='package-price'>
                            <img src={cardCoin2} alt='coins' />
                            {packageRow?.gcCoin} GC +
                          </Grid>
                          <Grid className='package-price'>
                            <img src={FreeTag} alt='free' className='free-tag' />
                          </Grid>
                          <Grid className='package-price'>
                            <img src={usdCash} alt='free' />
                            {packageRow?.scCoin} SC
                          </Grid>
                        </Grid>
                        <Button
                          className='btn btn-primary'
                          onClick={() => handleBuyNow(packageRow)}
                          data-tracking={`Store.${packageRow?.packageName}.Offer`}
                          data-tracking-item-id={packageRow?.packageId}
                          data-tracking-item-price={formatAmount(packageRow?.amount)}
                          data-tracking-item-name={`${packageRow?.packageName}`}
                          data-tracking-item-list={'User.Store.Main'}
                          data-tracking-item-catalog={'Special_Package'}
                        >
                          ${formatAmount(packageRow?.amount)}
                        </Button>
                      </Grid>
                    </Box>
                  </Grid>
                  <Grid item xs={6} lg={3} className='badge-grid'>
                    <Grid className='offer-badge'>
                      <img src={Specialbanner} alt='Offer' />
                      <Grid className='sp-offer-badge-content'>
                        {packageRow?.bonusPercentage > 0 ? (
                          <>
                            <Typography variant='h5'>{packageRow?.bonusPercentage}%</Typography>
                            <Typography>Bonus</Typography>
                          </>
                        ) : (
                          <Typography>Special Offer</Typography>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          )}
          <Grid className='purchase-section'>
            <Grid className='inner-heading'>
              <Typography variant='h4'>Purchase Package</Typography>
            </Grid>
            <Grid className='purchase-section-content'>
              {packageData?.rows?.length > 0
                ? packageData?.rows
                    ?.filter((packageRow) => !packageRow?.isSpecialPackage || packageRow?.isSpecialPackage === true)
                    ?.slice(packageRow?.isSpecialPackage ? 1 : 0)
                    .map((item, index) => {
                      return (
                        <Grid className='package-card' key={`package-${item?.packageId}`}>
                          <Grid container spacing={0.5}>
                            <Grid item xs={6} md={6} lg={3}>
                              <Grid className='package-img-wrap order-1'>
                                <>
                                  {item?.bonusPercentage > 0 && (
                                    <Grid className='package-img'>
                                      <img src={BonusBadge} alt='Badge' className='bonus-badge' />
                                      <Grid className='offer-badge-content-text'>
                                        <Typography variant='h5'>{item?.bonusPercentage}%</Typography>
                                        <Typography>Bonus</Typography>
                                      </Grid>
                                    </Grid>
                                  )}
                                </>

                                <img
                                  src={item?.imageUrl ? item?.imageUrl : smallCoinGraphic}
                                  alt='Bonus'
                                  className='bonus-graphics'
                                />
                              </Grid>
                            </Grid>
                            <Grid item xs={12} md={12} lg={6} className='order-3'>
                              <Grid className='package-details' spacing={1} justifyContent='center' alignItems='center'>
                                <Grid className='package-content'>
                                  <Grid className='package-price'>
                                    <img src={cardCoin2} alt='coins' />
                                    {item?.gcCoin > 0
                                      ? formatPriceWithCommas(formatValueWithB(Number(item?.gcCoin)))
                                      : item?.gcCoin}{' '}
                                    GC +
                                  </Grid>
                                  <Grid className='package-price'>
                                    <img src={FreeTag} alt='free' className='free-tag' />
                                  </Grid>
                                  <Grid className='package-price'>
                                    <img src={usdCash} alt='free' />
                                    {item?.scCoin > 0
                                      ? formatPriceWithCommas(formatValueWithB(Number(item?.scCoin)))
                                      : item?.scCoin}{' '}
                                    SC
                                  </Grid>
                                </Grid>
                              </Grid>
                            </Grid>
                            <Grid item xs={6} md={6} lg={3} className='order-2'>
                              <Grid className='package-btn-wrap'>
                                <Button
                                  className='btn btn-primary'
                                  onClick={() => handleBuyNow(item)}
                                  data-tracking={`Store.${item?.packageName}.Offer`}
                                  data-tracking-item-id={item?.packageId}
                                  data-tracking-item-price={formatAmount(item?.amount)}
                                  data-tracking-item-name={`${item?.packageName}`}
                                  data-tracking-item-list={'User.Store.Main'}
                                  data-tracking-item-catalog={'Basic_Package'}
                                >
                                  ${formatAmount(item?.amount)}
                                </Button>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                      )
                    })
                : 'No Active Packages '}
            </Grid>
          </Grid>
        </Box>
      </Grid>
    </>
  )
}

export default Package
