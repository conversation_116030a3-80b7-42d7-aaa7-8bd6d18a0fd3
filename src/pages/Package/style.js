import { makeStyles } from '@mui/styles'

import { PackageBanner } from '../../components/ui-kit/icons/banner'
import { InnerBanner, LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    minHeight: 'auto',

    '& .package-page': {
      maxWidth: theme.spacing(74.6875),
      margin: '0 auto',
      '& .free-tag': {
        width: '48px !important',
        [theme.breakpoints.down('sm')]: {
          width: '32px !important'
        }
      },
      '& .btn-primary': {
        color: theme.colors.textBlack,
        [theme.breakpoints.down('md')]: {
          maxWidth: theme.spacing(12.25),
          marginLeft: 'auto'
        },
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      },

      '& .special-offer-section': {
        margin: theme.spacing(4.3125, 0),
        [theme.breakpoints.down('lg')]: {
          margin: theme.spacing(2, 0, 1)
        },

        '& .offer-graphics': {
          width: '14rem',
          [theme.breakpoints.down('sm')]: {
            width: '70%'
          }
        },
        '& .package-details': {
          background: theme.colors.packageDetailsBg,
          border: `1px solid ${theme.colors.packageInnerCard}`,
          padding: theme.spacing(1.25),
          borderRadius: theme.spacing(1.875),
          display: 'flex',
          flexDirection: 'column',
          minHeight: theme.spacing(10.75),
          alignItems: 'center',
          [theme.breakpoints.down('md')]: {
            borderRadius: theme.spacing(0.9375),
            padding: theme.spacing(1, 0.313),
            minHeight: 'auto'
          },
          '& .package-content, & .package-price': {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(0.625),
            fontWeight: '500',
            [theme.breakpoints.down('sm')]: {
              gap: theme.spacing(0.3)
            },
            '& img': {
              width: '25px'
            }
          },
          '& .package-price': {
            fontSize: theme.spacing(1.5625),
            [theme.breakpoints.down('md')]: {
              fontSize: theme.spacing(0.875)
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(0.75)
            },
            '& img': {
              width: '25px'
            }
          },
          '& .MuiButtonBase-root': {
            marginTop: theme.spacing(1.6875),
            fontSize: theme.spacing(1.25),
            minWidth: theme.spacing(16.8125),
            minHeight: theme.spacing(0.125),
            fontWeight: '600',
            [theme.breakpoints.down('md')]: {
              minWidth: theme.spacing(8.4375),
              minHeight: theme.spacing(2.125),
              fontSize: theme.spacing(0.875),
              lineHeight: theme.spacing(1),
              margin: '1rem auto 0.625rem'
            }
          }
        },
        '& .MuiGrid-container': {
          alignItems: 'center'
        },
        '& .badge-grid': {
          position: 'absolute',
          top: theme.spacing(-1.875),
          right: theme.spacing(0.625),
          [theme.breakpoints.down(1199)]: {
            top: theme.spacing(-2.5)
          },
          [theme.breakpoints.down(983.99)]: {
            top: theme.spacing(-2.5),
            order: 2,
            right: theme.spacing(0.625)
          },
          [theme.breakpoints.down(899.99)]: {
            top: theme.spacing(-2.3125),
            order: 2,
            right: theme.spacing(0.625)
          },
          [theme.breakpoints.down(600)]: {
            top: theme.spacing(-1.875)
          },
          '& .offer-badge': {
            position: 'relative',
            top: '2px',
            zIndex: 2,
            [theme.breakpoints.down('lg')]: {
              top: '4px'
            },
            [theme.breakpoints.down(1400)]: {
              top: '7px'
            },
            [theme.breakpoints.down('md')]: {
              top: '6px'
            },
            // [theme.breakpoints.down('sm')]: {
            //     top:'2px',
            // },
            '& .sp-offer-badge-content': {
              position: 'absolute',
              top: '39%',
              left: '50%',
              transform: 'translate(-50%,-50%)',
              marginInline: 'auto',
              width: 'fit-content',
              '& h5': {
                fontWeight: 'bold',
                fontSize: theme.spacing(2),
                lineHeight: theme.spacing(2),

                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.875),
                  lineHeight: theme.spacing(0.875)
                }
              },
              '& p': {
                fontWeight: 'bold',
                fontSize: theme.spacing(1.25),
                textAlign: 'center',

                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.75),
                  lineHeight: theme.spacing(0.8)
                }
              }
            },
            [theme.breakpoints.down('sm')]: {
              '& .sp-offer-badge-content': {
                top: '3px',
                left: '50%',
                position: 'absolute',
                marginInline: 'auto',
                width: 'fit-content'
              }
            },
            [theme.breakpoints.down('sm')]: {
              textAlign: 'right',
              top: '2px'
            },

            '& img': {
              [theme.breakpoints.down(1400)]: {
                width: theme.spacing(10)
              },
              [theme.breakpoints.down('md')]: {
                width: theme.spacing(8)
              },
              [theme.breakpoints.down('sm')]: {
                width: theme.spacing(6.25)
              }
            }
          }
        },

        '& .package-details-grid': {
          [theme.breakpoints.down('md')]: {
            order: 3
          }
        }
      },
      '& .package-card': {
        background: theme.colors.coinBundle,
        border: `1px solid ${theme.colors.packageInnerCard}`,
        padding: theme.spacing(0.2, 1.25),
        borderRadius: theme.spacing(2.5),
        minHeight: theme.spacing(13.4375),
        position: 'relative',
        display: 'flex',
        [theme.breakpoints.down('lg')]: {
          padding: theme.spacing(1)
        },
        [theme.breakpoints.down('md')]: {
          borderRadius: theme.spacing(0.625),
          padding: theme.spacing(0.9375)
        }
      },
      '& .purchase-section': {
        '& .purchase-section-content': {
          background: theme.colors.coinBundle,
          border: `1px solid ${theme.colors.packageInnerCard}`,
          padding: theme.spacing(2.5, 2.0625),
          borderRadius: theme.spacing(2.5),
          [theme.breakpoints.down('lg')]: {
            borderRadius: theme.spacing(1)
          },
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(0.9375)
          },
          '& .package-card': {
            background: '#20242C',
            border: '1px solid #4D535F',
            minHeight: theme.spacing(6.1875),
            marginBottom: theme.spacing(2.125),
            borderRadius: theme.spacing(3.75),

            [theme.breakpoints.down('lg')]: {
              marginBottom: theme.spacing(1),
              borderRadius: theme.spacing(0.5625)
            },
            '&:last-child': {
              marginBottom: '0'
            },
            '& .MuiGrid-container': {
              alignItems: 'center',
              '& .bonus-badge': {
                position: 'relative',
                left: theme.spacing(-2.875),
                [theme.breakpoints.down(1500)]: {
                  left: theme.spacing(-2.625)
                  // width: theme.spacing(5.625),
                },
                [theme.breakpoints.down(1400)]: {
                  width: theme.spacing(5.625)
                },
                [theme.breakpoints.down('lg')]: {
                  width: theme.spacing(7),
                  left: theme.spacing(-2.75)
                },
                [theme.breakpoints.down('md')]: {
                  left: theme.spacing(-2.625)
                },
                [theme.breakpoints.down('sm')]: {
                  width: theme.spacing(4),
                  left: theme.spacing(-1.9375)
                }
              },
              '& .bonus-graphics': {
                // height: theme.spacing(6.25),
                width: theme.spacing(8.25),
                [theme.breakpoints.down(1300)]: {
                  width: theme.spacing(6.5)
                },
                [theme.breakpoints.down('md')]: {
                  width: theme.spacing(5.625)
                },
                [theme.breakpoints.down(360)]: {
                  width: theme.spacing(3.5)
                }
              }
            },
            '& .package-details': {
              background: theme.colors.Pastel,
              padding: theme.spacing(0.625),
              minHeight: theme.spacing(3.75),
              borderRadius: theme.spacing(1.875),
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              [theme.breakpoints.down('md')]: {
                borderRadius: theme.spacing(1.875),
                minHeight: theme.spacing(2.0625),
                marginTop: theme.spacing(0.313)
              },
              '& .package-content, & .package-price': {
                display: 'flex',
                alignItems: 'center',
                gap: theme.spacing(0.625)
              },
              '& .package-price': {
                fontSize: theme.spacing(1.5625),
                fontWeight: '600',
                [theme.breakpoints.down(1400)]: {
                  fontSize: theme.spacing(0.875)
                },
                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.75)
                },
                '& img': {
                  width: '25px',
                  [theme.breakpoints.down('sm')]: {
                    width: theme.spacing(1)
                  },
                  '&.free-tag': {
                    [theme.breakpoints.down('md')]: {
                      width: '48px !important'
                    },
                    [theme.breakpoints.down('sm')]: {
                      width: '32px !important'
                    }
                  }
                }
              }
            },
            '& .order-1': {
              [theme.breakpoints.down(1199)]: {
                justifyContent: 'flex-start',
                display: 'flex',
                alignItems: 'center'
              }
            },
            '& .order-2': {
              [theme.breakpoints.down(1199)]: {
                order: 2
              }
            },
            '& .order-3': {
              [theme.breakpoints.down(1199)]: {
                order: 3
              }
            },
            '& .package-img-wrap': {
              display: 'flex',
              alignItems: 'center',
              '& .package-img': {
                position: 'relative',
                [theme.breakpoints.down('sm')]: {
                  width: theme.spacing(3)
                },
                '& .offer-badge-content-text': {
                  top: '49%',
                  left: '14%',
                  position: 'absolute',
                  textAlign: 'center',
                  transform: 'translate(-50%, -50%)',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  [theme.breakpoints.down(1500)]: {
                    top: '48%',
                    left: '14%'
                  },

                  // [theme.breakpoints.down(1400)]: {

                  //     left: "0%",
                  // },
                  [theme.breakpoints.down(1199)]: {
                    left: '8%'
                  },

                  [theme.breakpoints.down(767)]: {
                    top: '44%',
                    left: '7%'
                  },

                  [theme.breakpoints.down(460)]: {
                    left: '0%'
                  },
                  '& h5': {
                    fontWeight: 'bold',
                    fontSize: theme.spacing(1.25),
                    lineHeight: theme.spacing(1.25),
                    [theme.breakpoints.down('sm')]: {
                      fontSize: theme.spacing(0.875),
                      lineHeight: theme.spacing(0.875)
                    }
                  },
                  '& p': {
                    fontWeight: 'bold',
                    fontSize: theme.spacing(0.875),
                    [theme.breakpoints.down('sm')]: {
                      fontSize: theme.spacing(0.75),
                      lineHeight: theme.spacing(0.625)
                    }
                  }
                }
              }
            },

            '&:first-child': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #1362BE99)'
              }
            },
            '&:nth-child(2)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #F8D54ECC)'
              }
            },
            '&:nth-child(3)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #3BBE13CC)'
              }
            },
            '&:nth-child(4)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #A613BECC)'
              }
            },
            '&:nth-child(5)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #A613BECC)'
              }
            },
            '&:nth-child(6)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #00A30BCC)'
              }
            },
            '&:nth-child(7)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #C4009DCC)'
              }
            },
            '&:nth-child(8)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #C44800CC)'
              }
            },
            '& .offer-badge-tag': {
              position: 'absolute',
              left: theme.spacing(-1.3125),
              top: 0,
              zIndex: 1,
              [theme.breakpoints.down('lg')]: {
                left: theme.spacing(-2)
              },
              [theme.breakpoints.down('md')]: {
                left: theme.spacing(-1.825)
              },
              [theme.breakpoints.down('sm')]: {
                left: theme.spacing(-1.875)
              },

              '& img': {
                width: theme.spacing(9.375),
                [theme.breakpoints.down('lg')]: {
                  width: theme.spacing(6.375)
                },
                [theme.breakpoints.down('md')]: {
                  width: theme.spacing(5.375)
                }
              },
              '& .offer-badge-content': {
                position: 'absolute',
                zIndex: 2,
                transform: 'translate(-50%, -50%)',
                left: '50%',
                top: '50%',
                [theme.breakpoints.down('lg')]: {
                  top: '47%'
                },
                '& h5, & p': {
                  fontWeight: theme.typography.fontWeightExtraBold,
                  [theme.breakpoints.down('lg')]: {
                    fontSize: theme.spacing(0.875),
                    textAlign: 'center'
                  },
                  [theme.breakpoints.down('md')]: {
                    fontSize: theme.spacing(0.75),
                    lineHeight: theme.spacing(1)
                  }
                }
              }
            }
          }
        },
        '& .package-btn-wrap': {
          textAlign: 'center',
          display: 'flex',
          alignItems: 'center',
          '& .MuiButtonBase-root': {
            fontSize: theme.spacing(1.25),
            padding: theme.spacing(0.4, 1.5),
            fontWeight: '600',
            minWidth: theme.spacing(14.5625),
            minHeight: theme.spacing(3.25),
            [theme.breakpoints.down(1499)]: {
              minWidth: theme.spacing(11.5625),
              width: '100%'
            },
            [theme.breakpoints.down(1080)]: {
              minWidth: 'auto',
              fontSize: theme.spacing(1),
              minHeight: theme.spacing(2.5)
            },

            [theme.breakpoints.down('md')]: {
              minHeight: theme.spacing(2.125),
              fontSize: theme.spacing(0.875),
              lineHeight: theme.spacing(1),
              padding: theme.spacing(0.4, 0.625)
            }
          }
        }
      },
      '& .inner-heading': {
        marginBottom: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(1.125)
        },
        '& h4': {
          fontSize: theme.spacing(1.875),
          fontWeight: '600',
          lineHeight: theme.spacing(2),

          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    }
  },
  InnerBanner: {
    ...InnerBanner(theme),
    background: `url(${PackageBanner})`
  },
  lobbySearchWrap: {
    marginBottom: theme.spacing(3.5),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      display: 'none'
      // marginTop: theme.spacing(6.25),
    },
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1.25)
    },
    '& .MuiTextField-root': {
      width: '100%',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.Pastel}`,
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        '&::placeholder': {
          color: `${theme.colors.placeHolderText} !important`,
          fontSize: `${theme.spacing(1)} !important`,
          fontWeight: '500 !important'
        },

        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(2.18)
        }
      }
    }
  }
}))
