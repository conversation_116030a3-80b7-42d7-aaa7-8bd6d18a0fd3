
import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({

  btnGradientWrap: {
    "& .btn-gradient": {
      "&.MuiButtonBase-root": {
        background: theme.colors.primaryGradient,
        boxShadow: theme.shadows[1],
        borderRadius: "30px",
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: "relative",
        overflow: "hidden",
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        "&:before": {
          position: "absolute",
          width: "700px",
          height: "100%",
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: "1rem 1rem",
        },
        "& .btn-span": {
          position: "relative",
          color: theme.colors.white,
          zIndex: "2",
          fontWeight: theme.typography.fontWeightSemiBold,
          display: "flex",
        },
        "&:hover": {
          backgroundColor: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
        },
      },
    },

  },
  lobbyRight: {
    width: "100%",
    marginLeft: "0",
    minHeight: "calc(100vh - 260px)",
    padding: theme.spacing(1, 1.5),
    position: "relative",
    marginTop: '4.5rem',
    "&:before": {
      position: "absolute",
      left: "0",
      top: "0",
      content: "''",
      height: "100%",
      width: "100%",
      backgroundSize: "100%",
    },
  },
  geoblockWrap: {
    padding: theme.spacing(5, 3),
    display: "flex",
    alignItems: "center",

    "& .geoblock-content": {
      backgroundColor: "#1B181E !important",
      backdropFilter: "blur(1px)",
      padding: theme.spacing(0.625),
      color: theme.colors.white,
      textAlign: "center",
      maxWidth: "600px",
      margin: "0 auto",
      "& .geoblock-img": {
        width: "300px",
        margin: "0 auto 2rem",
      },
      "& .MuiTypography-h2": {
        fontWeight: theme.typography.fontWeightExtraBold,
        background: theme.colors.textGradient,
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        textDecoration: "none",
        textTransform: "uppercase",
        fontSize: theme.spacing(3),
        textAlign: "center",
      },
      "& .MuiTypography-h4": {
        fontWeight: theme.typography.fontWeightBold,
        margin: theme.spacing(0.625, 0),
        fontSize: theme.spacing(1.5),
      },
      "& .MuiTypography-body1": {
        fontSize: theme.spacing(1),
        color: theme.colors.themeText,
      },
      "& .MuiButtonBase-root": {
        margin: theme.spacing(2, 0, 0),
      }
    }
  }



}))
