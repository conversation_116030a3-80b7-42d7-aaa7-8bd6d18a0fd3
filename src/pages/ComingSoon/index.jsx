import { Grid, Typography } from '@mui/material'
import React from 'react'
import useStyles from './ComingSoon.styles'
import BrandLogo from '../../components/ui-kit/icons/brand/brand-logo.webp'
import { useSiteLogoStore } from '../../store/store'

const ComingSoon = () => {
  const classes = useStyles()
  const logoData = useSiteLogoStore((state) => state)
  return (
    <Grid className={classes.lobbyRight}>
      <Grid className={classes.geoblockWrap}>
        <Grid className='geoblock-content'>
          <img src={logoData?.desktopLogo || BrandLogo} alt='Logo' className='Brand Logo' />
          <Typography variant='h2'>We are Coming Soon!</Typography>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default ComingSoon
