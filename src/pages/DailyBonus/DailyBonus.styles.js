import { makeStyles } from '@mui/styles'

import { WelcomeBgImage } from '../../components/ui-kit/icons/opImages'
import { BonusHeading, Bonuspara, ButtonPrimary } from '../../MainPage.styles'
export default makeStyles((theme) => ({
  dailyBonusBg: {
    '& .coinSection': {
      display: 'flex',
      alignItems: 'center',
      gap: '10px',
      '& p': {
        margin: '0',
        display: 'flex',
        alignItems: 'center',
        marginTop: '10px',
        marginBottom: '10px',
        fontWeight: 'bold',
        fontSize: theme.spacing(1.5),
        color: theme.colors.YellowishOrange,
        gap: '10px'
      }
    },
    backgroundImage: `url(${WelcomeBgImage})`,
    '& h2': {
      ...BonusHeading(theme)
    },
    '& p': {
      ...Bonuspara(theme)
    },
    '& .claimBtn': {
      ...ButtonPrimary(theme),
      '&:hover': {
        ...ButtonPrimary(theme)
      },
      marginTop: '15px'
    },
    '& .modal-close': {
      color: theme.colors.textWhite,
      textAlign: 'right'
    }
  }
}))
