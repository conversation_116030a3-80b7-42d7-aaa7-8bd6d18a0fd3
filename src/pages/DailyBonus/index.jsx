import React from 'react'
import { <PERSON>rid, I<PERSON><PERSON>utton, Dialog<PERSON>ontent, Typo<PERSON>, But<PERSON> } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import coinIcon from '../../components/ui-kit/icons/svg/coin.svg'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
const DailyBonusPopup = () => {
  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <>
      <Grid className='dailyBonusBg'>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
        <DialogContent>
          <Grid>
            <Typography variant='h2'>
              Get rewarded
              <br />
              daily
            </Typography>
            <Typography>
              Log in without fail for your
              <br />
              exclusive perks!
            </Typography>

            <Grid className='coinSection'>
              <Typography>
                <img src={usdIcon} />
                1000
              </Typography>

              <Typography>
                <img src={coinIcon} />
                10 Coins
              </Typography>
            </Grid>

            <Button className='claimBtn'>Redeem & Claim</Button>
          </Grid>
        </DialogContent>
      </Grid>
    </>
  )
}

export default DailyBonusPopup
