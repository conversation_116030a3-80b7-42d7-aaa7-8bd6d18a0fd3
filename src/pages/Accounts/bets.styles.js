import { makeStyles } from '@mui/styles'

import { LobbyRight, Container, leaderBoardContainer } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
  },

  wrapper: {
    ...Container(theme),
  },
  leaderBoardContainer: {
    ...leaderBoardContainer(theme),

  },

  inputError: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    margin: "0 !important",
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600',
  },
  transactionInputs: {
    marginTop: "2rem !important",
    [theme.breakpoints.down('md')]: {
      marginTop: "1rem !important",
    },
    "& select": {
      width: "100%",
      border: `1px solid ${theme.colors.YellowishOrange}`,
      background: "transparent",
      color: theme.colors.textWhite,
      height: "43px",
      borderRadius: "5px",
      padding: "0 14px",
      "& option": {
        color: theme.colors.textBlack
      },
      "&:focus-visible": {
        outline: "none",
        border: `1px solid ${theme.colors.YellowishOrange}`,
      }
    },
    "& button": {
      "& svg": {
        color: theme.colors.textWhite,
      }
    },
    "& .MuiTextField-root": {
      width: "100%",
      height: "43px",
      "& .MuiInputBase-root": {
        height: "100%",
        "& input": {
          height: "100%",
          padding: "0 0 0 14px",
          color: theme.colors.textWhite,
        }
      },
      "& fieldset": {
        borderColor: `${theme.colors.YellowishOrange} !important`,
        borderWidth: '1px',
      },
      "& .Mui-focused": {
        "& .MuiOutlinedInput-notchedOutline": {
          borderColor: theme.colors.YellowishOrange,
          borderWidth: '1px',
        }
      }
    }
  },
  tablePagination: {
    marginBottom: theme.spacing(1),
    "& ul": {
      justifyContent: 'center',
      "& li": {
        "& button": {
          color: '#fff',
        },
        "& .MuiPaginationItem-root.Mui-selected": {
          backgroundColor: theme.colors.YellowishOrange,
          color: theme.colors.textBlack

        }

      }
    }
  },

  tableCard: {
    backgroundColor: theme.colors.jungleGreen,
    borderRadius: theme.spacing(1),
    display: 'none',
    // [theme.breakpoints.down('sm')]: {
    //   display: 'block',
    // },
  },
  tableCardHead: {
    display: 'flex',
    justifyContent: 'space-between',
    // padding: theme.spacing(1),
    "& .tdate": {
      fontSize: theme.spacing(0.6),
      color: theme.colors.textWhite
    },
    "& .tstatus": {
      fontSize: theme.spacing(0.6),
      color: theme.colors.success
    }
  },
  tableCardBody: {
    display: "flex",
    justifyContent: "space-between",
    // padding: theme.spacing(1),
    backgroundColor: theme.colors.Pastel,
    borderRadius: theme.spacing(0, 0, 1, 1),
    "& p": {
      fontSize: theme.spacing(0.5),
    }
  },
  redeemModal: {
    // minWidth: "800px",
    "& .MuiFormControlLabel-label": {
      fontSize: '0.7rem',
    },
    "& .leftSection": {
      flexGrow: 1,
      [theme.breakpoints.down('sm')]: {
        width: '100%'
      }
    },
    "& .inputWrap": {
      marginBottom: theme.spacing(1),
      marginTop: theme.spacing(0.5),
      '& .textAmount': {
        // '&:hover':{
        color: 'yellow'
        // }
      },
    },
    "& .MuiTypography-heading": {
      color: theme.colors.textWhite,
      fontWeight: 'bold',
      fontSize: theme.spacing(1.5),
    },
    "& .MuiFormLabel-root": {
      color: theme.colors.textWhite,
    },
    "& .redeemImg": {
      width: '40%',
      textAlign: 'right',
      padding: '1rem',
      "& img": {
        width: '100%',
      },
      [theme.breakpoints.down('sm')]: {
        display: 'none',
      }
    },
    "& .MuiTextField-root, & .MuiOutlinedInput-root": {
      width: '100%',
      "& input": {
        color: "#fff !important",
        width: "-webkit-fill-available",
        border: "1px solid #293937",
        padding: "0px 14px",
        borderRadius: "0.25rem",
        backgroundColor: "transparent",
        height: '41px',

        '&.Mui-disabled': {
          background: theme.colors.disabledInput,
          textFillColor: theme.colors.textWhite,
        }
      },
    },
    '& .MuiSvgIcon-root': {
      color: theme.colors.textWhite,
    },
    "& .btn-wrap": {
      "& button": {
        color: theme.colors.textBlack,
        padding: "0.375rem 2rem",
        fontSize: "1rem",
        minWidth: "115px",
        background: "#FDB72E",
        fontWeight: 700,
        borderRadius: "7.8rem",
        textTransform: "uppercase",
        width: "100%",
      },
    },
    "& .leftText": {
      flexGrow: "1",
      [theme.breakpoints.down('sm')]: {
        width: "100%",
      },
      "& h4": {
        fontWeight: 'bold',
        fontSize: theme.spacing(1.8),
      }
    },
    "& .rightImage": {
      width: '100%',
      textAlign: 'right',
      [theme.breakpoints.down('sm')]: {
        display: 'none',
      },
      "& img": {
        width: '100%',
        paddingLeft: '1rem'
      },
    },
    '& .input-grp': {
      display: 'flex',
      "& .MuiTextField-root, & .MuiOutlinedInput-root": {
        '& input': {
          borderTopRightRadius: '0 !important',
          borderBottomRightRadius: '0 !important',
        }
      },
      '& .inner-btn': {
        minWidth: 'unset',
        color: theme.colors.textBlack,
        padding: "0.375rem 0.75rem",
        fontSize: "1rem",
        background: "#FDB72E",
        fontWeight: 700,
        borderRadius: "0 0.25rem 0.25rem 0",
        textTransform: "uppercase",
      }
    },
    '& .themeCheckBoxWrap': {
      '& label': {

        '& .MuiFormControlLabel-label': {
          fontSize: theme.spacing(0.9),
        }

      }
    }
  },

  reactCoinSelect: {

    '& .reactInnerCoinSelect__control': {
      borderRadius: theme.spacing(0.3125),
      backgroundColor: "transparent",
      border: `1px solid ${theme.colors.YellowishOrange}`,
      cursor: 'pointer',
      height: '43px',

      '& .reactInnerCoinSelect__value-container': {
        padding: theme.spacing(0.3125, 0.5),

        '& .reactInnerCoinSelect__single-value': {
          color: theme.colors.textWhite,
        },
      },

      '& .reactInnerCoinSelect__indicator-separator': {
        display: 'none',
      },

      '&.reactInnerCoinSelect__control--is-focused': {
        boxShadow: 'none',
        outline: 'none',
      },

      '&:hover': {
        boxShadow: 'none',
        outline: 'none',
        border: `1px solid ${theme.colors.YellowishOrange}`,
      },
    },

    '& .reactInnerCoinSelect__menu': {
      background: theme.colors.GreenishCyan,

      '& .reactInnerCoinSelect__option': {
        cursor: 'pointer',
        fontWeight: '500',

        '&.reactInnerCoinSelect__option--is-focused': {
          background: 'transparent',
          color: theme.colors.textWhite,
        },

        '&.reactInnerCoinSelect__option--is-selected': {
          background: theme.colors.YellowishOrange,
          color: theme.colors.textBlack,

          '&:hover': {
            background: theme.colors.YellowishOrange,
            // color: theme.colors.textBlack,
          },
        }

      }


    }
  }
}))
