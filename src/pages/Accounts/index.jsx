import React, { useEffect, useMemo } from 'react'
import useStyles from './Accounts.styles'
import '../../../src/App.css'
import VipPlayerFormCIcon from '../../components/ui-kit/icons/svg/vip-player-c.svg'
import gameblingAIcon from '../../components/ui-kit/icons/svg/gamebling-a.svg'
import gameblingCIcon from '../../components/ui-kit/icons/svg/gamebling-c.svg'
import multiColorProfile from '../../components/ui-kit/icons/svg/multiColorProfile.svg'
import darkProfile from '../../components/ui-kit/icons/svg/darkProfile.svg'
import VipPlayerFormAIcon from '../../components/ui-kit/icons/svg/vip-player-a.svg'
import { Link, Outlet, useLocation } from 'react-router-dom'
import { useUserStore } from '../../store/useUserSlice'
import { Box, Grid, Tabs, Typography, Tab, AppBar } from '@mui/material'
import { useVipQuetsions } from '../../store/store'
import { useQuery } from '@tanstack/react-query'
import { KeyTypes } from '../../reactQuery/KeyTypes'
import { getVipQuetsions, getVipUserAnswers } from '../../utils/apiCalls'
import Loader from '../../components/Loader'
/* eslint-disable multiline-ternary */

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  }
}

const Accounts = () => {
  const classes = useStyles()
  const { pathname } = useLocation()
  const [value, setValue] = React.useState(0)
  const { userDetails } = useUserStore((state) => state)
  const { setVipQuestions, setVipAnswers } = useVipQuetsions()
  const isUserDetails = userDetails !== null && userDetails !== undefined
  const showVipTab = isUserDetails && userDetails?.isVipApproved

  const tabRoutes = useMemo(() => {
    if (!isUserDetails) return []

    const routes = ['/user/account-details', '/user/account-details/responsible-gaming']
    if (userDetails?.isVipApproved) {
      routes.push('/user/account-details/vip-player-interests')
    }
    return routes
  }, [showVipTab, isUserDetails])

  useEffect(() => {
    if (!isUserDetails || tabRoutes.length === 0) return
    const index = tabRoutes.findIndex((route) => pathname === route || pathname === route + '/')
    setValue(index !== -1 ? index : 0)
  }, [pathname, tabRoutes, isUserDetails])

  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  useQuery({
    queryKey: [KeyTypes.VIP_QUESTIONS],
    queryFn: getVipQuetsions,
    select: (data) => data?.data,
    retry: false,
    // refetchOnMount: false,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      setVipQuestions(data?.questions?.rows)
    }
  })

  useQuery({
    queryKey: [KeyTypes.VIP_USER_ANSWERS],
    queryFn: getVipUserAnswers,
    select: (res) => res?.data?.data,
    retry: false,
    // refetchOnMount: false,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      setVipAnswers(data)
    }
  })

  return (
    <>
      {!isUserDetails || tabRoutes.length === 0 ? (
        <Loader />
      ) : (
        <Grid className={classes.lobbyRight}>
          <Grid className={`${classes.container}`}>
            <Box className={classes.profileTabsDesign}>
              <Grid className='headingLogout'>
                <Typography variant='h2'>My Profile</Typography>
              </Grid>

              <Box>
                <AppBar position='static'>
                  <Tabs
                    value={Math.min(value, tabRoutes.length - 1)}
                    onChange={handleChange}
                    aria-label='basic tabs example'
                    scrollButtons
                    allowScrollButtonsMobile
                    variant='scrollable'
                  >
                    <Tab
                      icon={
                        <div>
                          <img src={multiColorProfile} alt='editProfile' className='image1' />
                          <img src={darkProfile} alt='editProfileAIcon' className='image2' />
                        </div>
                      }
                      iconPosition='start'
                      label='Edit Profile'
                      component={Link}
                      to='.'
                      {...a11yProps(0)}
                    />
                    <Tab
                      icon={
                        <div>
                          <img src={gameblingCIcon} alt='editProfile' className='image1' />
                          <img src={gameblingAIcon} alt='editProfileAIcon' className='image2' />
                        </div>
                      }
                      iconPosition='start'
                      label='Responsible Gaming'
                      component={Link}
                      to='responsible-gaming'
                      {...a11yProps(1)}
                    />
                    {showVipTab && (
                      <Tab
                        icon={
                          <div>
                            <img src={VipPlayerFormCIcon} alt='vip-user' className='image1' />
                            <img src={VipPlayerFormAIcon} alt='vip-userIcon' className='image2' />
                          </div>
                        }
                        iconPosition='start'
                        label='VIP Player Interests'
                        component={Link}
                        to='vip-player-interests'
                        {...a11yProps(2)}
                      />
                    )}
                  </Tabs>
                </AppBar>
              </Box>
              <Box className='profile-section-detail'>
                <Outlet />
              </Box>
            </Box>
          </Grid>
        </Grid>
      )}
    </>
  )
}

export default Accounts
