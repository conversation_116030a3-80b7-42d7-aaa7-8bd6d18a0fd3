import { makeStyles } from '@mui/styles'

import { Container, ButtonSecondary, ButtonPrimary, LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  termsandcondition: {
    width: '100%',
    maxWidth: '869px !important'
  },
  gamblingInput: {
    '& .MuiOutlinedInput-root': {
      height: '100%',
      padding: '0 14px',
      '& input': {
        height: '100%',
        padding: '0',
        color: theme.colors.textWhite,
        '&.Mui-disabled': {
          WebkitTextFillColor: theme.colors.textWhite
        }
      }
    },
    '& .MuiIconButton-root': {
      '& svg': {
        color: theme.colors.textWhite
      }
    },
    '& fieldset': {
      border: 'none'
    }
  },
  dobWrap: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(0.313),
    '& select': {
      borderRadius: theme.spacing(0.625)
    },
    '& .dobInputSelect': {
      width: '100%'
    }
  },

  lobbyRight: {
    ...LobbyRight(theme)
  },
  profileTabsDesign: {
    display: 'inherit',
    position: 'inherit',

    '& .MuiAppBar-colorPrimary': {
      backgroundColor: '#0c0a0e !important',
      marginLeft: '0 !important'
    },
    '& .headingLogout': {
      display: 'flex',
      // marginTop: '65px',
      justifyContent: 'space-between',
      alignItems: 'center',
      [theme.breakpoints.down('md')]: {
        marginTop: '20px'
      }
    },
    '& h2': {
      fontSize: '25px',
      fontWeight: '500',
      [theme.breakpoints.down('md')]: {
        width: '100%',
        marginLeft: '0'
      }
    },

    '& .MuiTabs-root': {
      width: 'fit-content',
      borderRadius: theme.spacing(2.18),
      border: `2px solid ${theme.colors.GreenishCyan}`,
      padding: theme.spacing(0.31),
      margin: theme.spacing(1, 0),
      // [theme.breakpoints.down('md')]: {
      //   width: "100%",
      // },
      '& .MuiTabs-scroller': {
        maxWidth: '100%',
        display: 'flex',
        whiteSpace: 'nowrap',
        flexWrap: 'nowrap',
        overflowX: 'auto',
        // margin: theme.spacing(0.625, 0),
        // margin: '38px 0 68px 0 !important',
        // [theme.breakpoints.down('md')]: {
        //   margin: '20px 0 20px 0 !important',
        // },
        '& .MuiTab-labelIcon': {
          color: theme.colors.textWhite,
          opacity: 1,
          padding: theme.spacing(0.4, 1.2),
          position: 'relative',
          marginRight: theme.spacing(0.5),
          fontWeight: theme.typography.fontWeightExtraBold,
          cursor: 'pointer',
          fontSize: theme.spacing(1),
          transition: 'none',
          transform: 'none',
          minWidth: 'auto',
          zIndex: '1',
          // minHeight: 'auto',
          borderRadius: theme.spacing(1.875),
          minHeight: theme.spacing(2.5),
          [theme.breakpoints.down('md')]: {
            // padding: theme.spacing(0.4, 0.5),
            // fontSize: theme.spacing(0.75)

            padding: theme.spacing(0.25),
            marginRight: theme.spacing(0.25),
            fontSize: theme.spacing(0.675)
          },
          '&:hover .MuiTab-iconWrapper': {
            '& .image2': {
              display: 'block'
            },
            '& .image1': {
              display: 'none'
            }
          },
          '& .MuiTab-iconWrapper': {
            '& .image2': {
              display: 'none'
            },
            '& .image1': {
              display: 'block'
            }
          },
          '&.Mui-selected': {
            color: theme.colors.textBlack,
            background: theme.colors.YellowishOrange,
            '& .MuiTab-iconWrapper': {
              '& .image1': {
                display: 'none'
              },
              '& .image2': {
                display: 'block !important'
              }
            }
          },
          '&:hover': {
            background: theme.colors.YellowishOrange,
            transition: 'none',
            transform: 'none',
            color: theme.colors.textBlack,
            border: '0'
          },
          '& img': {
            marginRight: theme.spacing(0.62),
            width: theme.spacing(1.3),
            aspectRatio: '1',

            [theme.breakpoints.down('md')]: {
              marginRight: theme.spacing(0.25),
              width: theme.spacing(0.875)
            }
          }
        },
        '& .MuiTab-wrapper': {
          flexDirection: 'row'
          // alignItems: 'start'
        },
        '& .MuiTouchRipple-root': {
          display: 'none'
        },
        '& .MuiTabs-indicator': {
          // borderRadius: theme.spacing(4.1875),
          background: theme.colors.YellowishOrange,
          position: 'absolute',
          fontWeight: theme.typography.fontWeightExtraBold,
          // color: theme.colors.textBlack,
          // height: 'calc(100% - 0.62rem)',
          top: '0',
          height: theme.spacing(2.5),
          borderRadius: theme.spacing(1.875),
          color: theme.colors.textBlack
        },

        '& .MuiTab-iconWrapper': {
          margin: '0'
        },

        '& button': {
          color: theme.colors.textWhite,
          padding: theme.spacing(0.4, 1.2),
          position: 'relative',
          marginRight: theme.spacing(0.5),
          fontWeight: theme.typography.fontWeightExtraBold,
          cursor: 'pointer',
          fontSize: theme.spacing(1),
          transition: 'none',
          transform: 'none',
          minWidth: 'auto',
          zIndex: '1',
          borderRadius: theme.spacing(1.875),
          minHeight: theme.spacing(2.5),
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(0.4, 0.5),
            fontSize: theme.spacing(0.75)
          },

          '&:hover .MuiTab-iconWrapper': {
            '& .image2': {
              display: 'block'
            },
            '& .image1': {
              display: 'none'
            }
          },
          '& .MuiTab-iconWrapper': {
            '& .image2': {
              display: 'none'
            },
            '& .image1': {
              display: 'block'
            }
          },
          '&.Mui-selected': {
            color: theme.colors.textBlack,
            background: theme.colors.YellowishOrange,
            '& .MuiTab-iconWrapper': {
              '& .image1': {
                display: 'none'
              },
              '& .image2': {
                display: 'block !important'
              }
            }
          },

          '&:hover': {
            background: theme.colors.YellowishOrange,
            transition: 'none',
            transform: 'none',
            color: theme.colors.textBlack,
            border: '0'
          },
          '& img': {
            marginRight: theme.spacing(0.62),
            width: theme.spacing(1.3),
            aspectRatio: '1'
          }
        }
      },
      '& .MuiTabScrollButton-root': {
        '&.Mui-disabled': {
          display: 'none'
        }
      }
    },

    '& .profile-section-detail': {
      '& .profile': {
        display: 'flex',
        gap: '30px',
        flexWrap: 'wrap'
      },
      '& .profile-cms-terms': {
        margin: theme.spacing(1, 0),
        '& a': {
          color: theme.colors.YellowishOrange,
          fontWeight: theme.typography.fontWeightBold,
          textDecorationColor: theme.colors.YellowishOrange
          // textDecoration: "none",
        }
      },
      '& .MuiSelect-select, & .MuiTextField-root': {
        width: '100%',
        border: `1px solid ${theme.colors.inputBorder}`,
        borderRadius: '0.25rem',
        height: '43px',
        '&:focus': {
          border: `1px solid ${theme.colors.YellowishOrange}`
        }
      },
      '& .uppercaseInput': {
        textTransform: 'uppercase'
      },
      '& .inputSelect': {
        width: '-webkit-fill-available',
        border: `1px solid ${theme.colors.inputBorder}`,
        // borderColor: theme.colors.YellowishOrange,
        borderRadius: '0.25rem',
        color: `${theme.colors.textWhite}!important`,
        padding: '12px 14px',
        backgroundColor: 'transparent',
        '&:focus': {
          border: `1px solid ${theme.colors.YellowishOrange}`
        },
        '& option': {
          color: theme.colors.textBlack
        },
        '&:focus-visible': {
          outline: 'none'
        },
        '&:disabled': {
          // color: 'rgba(255, 255, 255, 0.6) !important'
          opacity: '0.4 !important',
          cursor: 'not-allowed'
        },
        '&.disabled': {
          opacity: '0.4 !important',
          cursor: 'not-allowed'
        },
        // '&:disabled': {
        // WebkitTextFillColor: theme.colors.textWhite
        // },
        '&::-webkit-inner-spin-button, &::-webkit-outer-spin-button': {
          '-webkit-appearance': 'none',
          margin: 0
        }
      },

      '& .datePicker': {
        '& .MuiStack-root': {
          padding: '0 !important',
          width: '100% !important'
        },
        '& .MuiInputBase-root': {
          padding: '0 !important',
          height: '100% !important',
          '& input': {
            padding: '0 0 0 15px !important',
            height: '100% !important',
            color: theme.colors.textWhite,
            '&:disabled': {
              WebkitTextFillColor: theme.colors.textWhite
            }
          }
        },
        '& .MuiInputAdornment-positionEnd': {
          marginLeft: '0 !important',
          '& button': {
            margin: '0 !important',
            '& svg': {
              color: theme.colors.textWhite
            }
          }
        },
        '& .Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            border: '0',
            borderColor: 'transparent'
          }
        }
      },
      '& .react-select__control': {
        background: 'transparent !important',
        border: '1px solid #293937',
        cursor: 'pointer',
        '&.css-t3ipsp-control ': {
          border: '1px solid #293937 !important',
          boxShadow: 'none'
        },
        '& .react-select__single-value': {
          color: '#fff'
        }
      },
      '& .react-select__menu ': {
        background: '#293937',

        '& .react-select__option': {
          cursor: 'pointer',
          background: '#293937 !important'
        }
      }
    },

    '& .saveBtn': {
      ...ButtonPrimary(theme),
      '&:hover': {
        ...ButtonPrimary(theme)
      }
    },

    '& .cancelBtn': {
      ...ButtonSecondary(theme),
      '&:hover': {
        ...ButtonSecondary(theme)
      }
    },

    '& .gridHeading': {
      marginBottom: '30px',
      '& h3': {
        fontSize: '25px',
        marginBottom: '15px',
        fontWeight: '500'
      }
    }
  },

  container: {
    ...Container(theme),
    [theme.breakpoints.down('md')]: {
      marginBottom: '20px'
    }
  },

  logOutBtn: {
    color: '#000 !important',
    fontSize: '1rem !important',
    minWidth: '130px !important',
    background: `${theme.colors.YellowishOrange} !important`,
    boxShadow:
      '0px 0.637px 1.401px 0px #EAB647, 0px 1.932px 4.25px 0px rgba(234, 182, 71, 0.04), 0px 5.106px 11.233px 0px rgba(234, 182, 71, 0.09), 0px 16px 35.2px 0px rgba(234, 182, 71, 0.30) !important',
    fontWeight: '700 !important',
    borderRadius: '7.8rem !important',
    '& img': {
      filter: 'invert(1)',
      paddingRight: '10px',
      transform: 'rotate(180deg)',
      marginRight: '10px'
    }
  },

  userImgWrap: {
    position: 'relative'
  },

  userImg: {
    width: '120px',
    height: '120px',
    [theme.breakpoints.down('sm')]: {
      width: '80px',
      height: '80px'
    },
    '& img': {
      width: '100%',
      borderRadius: '50%',
      objectFit: 'cover'
    }
  },

  uploadImgBtn: {
    top: '0',
    right: '0',
    position: 'absolute !important',
    borderRadius: '50% !important',
    minHeight: '30px !important',
    minWidth: '30px !important',
    width: '30px !important',
    background: `${theme.colors.YellowishOrange} !important`,
    '& img': {
      width: '13px'
    }
  },
  errorLabel: {
    // marginBottom: `${theme.spacing(1.5)} !important`,
    color: `${theme.colors.error} !important`,
    fontSize: `${theme.spacing(0.8)}!important`,
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600 !important'
  },

  changePWD: {
    '& .MuiOutlinedInput-root': {
      padding: '0',
      height: '43px',
      width: '100%'
    },
    '& input': {
      padding: '0',
      color: theme.colors.textWhite,
      height: '100%',
      paddingLeft: '15px',
      maxWidth: '350px',
      width: '100%',
      border: `1px solid ${theme.colors.inputBorder}`,
      borderRadius: theme.spacing(0.25),
      '&:focus': {
        border: `1px solid ${theme.colors.YellowishOrange}`
      }
    },
    '& .MuiInputAdornment-positionEnd': {
      marginLeft: '-60px',
      '& svg': {
        color: theme.colors.textWhite
      }
    },
    '& fieldset': {
      border: 'none'
    }
  },

  '& .Mui-focused': {
    '& .MuiOutlinedInput-notchedOutline': {
      borderWidth: '0px',
      borderColor: 'transparent !important'
    }
  },

  inputError: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    margin: '0 !important',
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600'
  },
  inputDetail: {
    [theme.breakpoints.down('xs')]: {
      flexDirection: 'column !important'
    }
  },
  dob: {
    //  marginTop: theme.spacing(2),
    //  marginLeft: theme.spacing(2),
    [theme.breakpoints.down('md')]: {
      gap: theme.spacing(1.875),
      justifyContent: 'space-between'
    },
    '& .dobInputSelect': {
      marginRight: theme.spacing(0.2)
    }
  },
  profileBottom: {
    display: 'flex',
    gap: '15px',
    marginTop: '38px',
    [theme.breakpoints.down('md')]: {
      marginTop: '34px',
      marginBottom: '0'
    },
    '& button': {
      [theme.breakpoints.down('md')]: {
        minHeight: '2.5rem'
      }
    }
  },

  userKyc: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
    [theme.breakpoints.down('lg')]: {
      flexDirection: 'column !important',
      gap: '20px'
    }
  },

  kycAndRefferal: {
    display: 'flex',
    gap: '20px',
    width: '100%',
    '& .MuiTouchRipple-root': {
      display: 'none'
    },
    '& .variant1,  & .variant2': {
      color: theme.colors.Pastel,
      position: 'relative',
      fontSize: '1rem',
      textAlign: 'center',
      border: `2px solid ${theme.colors.Pastel}`,
      padding: theme.spacing(0.8, 5),
      borderRadius: '50px',
      fontWeight: 'bold',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 2.5)
      },
      '&:hover': {
        color: theme.colors.Pastel,
        position: 'relative',
        fontSize: '1rem',
        textAlign: 'center',
        border: `2px solid ${theme.colors.Pastel}`,
        padding: theme.spacing(0.8, 5),
        borderRadius: '50px',
        fontWeight: 'bold'
      },
      '& span': {
        left: '0',
        color: theme.colors.textWhite,
        right: '0',
        bottom: '-18px',
        position: 'absolute',
        fontSize: '0.8rem',
        fontWeight: 500,
        backgroundColor: '#1B181E',
        borderRadius: '10px',
        width: 'fit-content',
        padding: '5px 14px',
        margin: '0 auto'
      }
    },
    '& .variant2': {
      border: '2px solid transparent',
      backgroundColor: theme.colors.YellowishOrange,
      color: '#05051C',
      '&:hover': {
        border: '2px solid transparent !important',
        backgroundColor: `${theme.colors.YellowishOrange} !important`,
        color: '#05051C'
      }
    },
    '& .variant3': {
      fontSize: '1rem',
      textAlign: 'center',
      border: 'none',
      backgroundColor: theme.colors.YellowishOrange,
      color: '#05051C',
      padding: theme.spacing(0.8, 2),
      borderRadius: '50px',
      fontWeight: 'bold',
      display: 'flex',
      gap: '10px',
      whiteSpace: 'nowrap',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 1)
      },
      '&:hover': {
        fontSize: '1rem',
        textAlign: 'center',
        border: 'none',
        backgroundColor: theme.colors.YellowishOrange,
        color: '#05051C',
        padding: '0.8rem 2rem',
        borderRadius: '50px',
        fontWeight: 'bold',
        display: 'flex',
        gap: '10px',
        whiteSpace: 'nowrap'
      }
    },
    '& .referalBtn': {
      fontSize: '1rem',
      textAlign: 'center',
      // border: 'none',
      border: `2px solid ${theme.colors.textWhite}`,
      color: theme.colors.textWhite,
      padding: theme.spacing(0.8, 2),
      borderRadius: '50px',
      fontWeight: 'bold',
      display: 'flex',
      gap: '10px',
      whiteSpace: 'nowrap',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 1)
      }
    }
  },

  userDetailsRight: {
    width: '100%',
    '& h4': {
      fontWeight: '900',
      [theme.breakpoints.down('md')]: {
        fontSize: `${theme.spacing(1.5)}!important`
      }
    },
    '& p': {
      [theme.breakpoints.down('md')]: {
        fontSize: `${theme.spacing(0.8)}!important`
      }
    }
  },
  termsAndConditionMainContainer: {
    display: 'flex',
    minWidth: '869px !important',
    width: '100%'
  },

  lobbySearchWrap: {
    position: 'relative',
    minWidth: theme.spacing(18.75),
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1)
    },
    '& .inputSearch': {
      width: '-webkit-fill-available',
      border: `1px solid ${theme.colors.inputBorder}`,
      borderRadius: '10rem',
      color: `${theme.colors.textWhite}!important`,
      padding: '12px 14px 15px 50px',
      backgroundColor: 'transparent'
    },
    '& .MuiTextField-root': {
      width: '100%',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        '&::placeholder': {
          color: 'red'
        },
        '&::-ms-input-placeholder': {
          color: 'red'
        },
        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(2.18)
        }
      }
    }
  }
}))
