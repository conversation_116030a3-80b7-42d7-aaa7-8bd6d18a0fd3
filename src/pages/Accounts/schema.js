import * as Yup from 'yup'

export const personalInfoSchema = Yup.object().shape({
  firstName: Yup.string()
    .min(2, 'First Name must be at-least 2 characters')
    .max(50, 'First Name must be at most 50 characters')
    .matches(/^[a-zA-Z]+$/, 'Only Alphabets Allowed')
    .required('Please provide first name'),
  lastName: Yup.string()
    .min(2, 'Last Name must be at-least 2 characters')
    .max(50, 'Last Name must be at most 50 characters')
    .matches(/^[a-zA-Z]+$/, 'Only Alphabets Allowed')
    .required('Please provide last name'),
  dateOfBirth: Yup.date()
    .min(new Date(1900, 0, 1), 'Date of birth cannot be before January 1, 1900')
    .max(new Date(), 'Date of birth cannot be in the future')
    .typeError('Enter valid date')
    .required('Date of birth is required'),

  addressLine_1: Yup.string().min(1, 'Address must be at-least 1 characters').required('Please provide Address Line '),
  // city: Yup.string().nullable().max(100, 'city must be at most 100 characters').required('City is a required field'),
  city: Yup.string()
    .nullable()
    .max(100, 'City must be at most 100 characters')
    .required('City is a required field')
    .matches(/^[A-Za-z\s'-]+$/, 'City must contain only letters, spaces, apostrophes, or hyphens'),

  // zipCode: Yup.string()
  //   .matches(/^\d[\d-]*$/, 'Must be digits and/or hyphens only')
  //   .min(5, 'Must be at least 5 characters')
  //   .required('Zip Code is required'),
  zipCode: Yup.string()
    .required('Zip Code is required')
    .min(5, 'Must be at least 5 characters')
    .matches(/^[\d-]+$/, 'Must be digits and/or hyphens only')
    .test('not-negative', 'Negative zip codes are not allowed', (value) => !value?.startsWith('-')),

  state: Yup.string().required('State is a required field'),
  gender: Yup.string().required('Gender is a required field')
})
export const userSSNSchema = Yup.object().shape({
  ssn: Yup.string()
    .required('Please provide SSN')
    .matches(/^[0-9]+$/, 'Must be only digits')
    .min(9, 'Must be exactly 09 digits')
    .max(9, 'Must be exactly 09 digits')
})
export const userPasswordChange = Yup.object().shape({
  password: Yup.string()
    .required('Password is required')
    .min(8, 'Password must contain at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~`!@#$%^&*()_\-+={[}\]|:;"'<>,.?/]).{8,20}$/,
      'Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character'
    ),
  newPassword: Yup.string()
    .required('New Password is required.')
    .min(8, 'Password must contain at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~`!@#$%^&*()_\-+={[}\]|:;"'<>,.?/]).{8,20}$/,
      'Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character'
    ),
  // .notOneOf([Yup.ref('password')], 'Old Password and New Password should not be the same'),
  repeatPassword: Yup.string().oneOf([Yup.ref('newPassword')], 'New Password and confirm  Password  not matching')
})

export const userPersonalBonus = Yup.object().shape({
  amount: Yup.number()
    .required()
    .positive()
    .transform((value) => (isNaN(value) || value === null || value === undefined ? 0 : value))
    .nullable()
    .required('Amount is required')
})

export const personalInfoSchemaPayment = Yup.object().shape({
  firstName: Yup.string()
    .min(2, 'First Name must be at-least 2 characters')
    .max(50, 'First Name must be at most 50 characters')
    .matches(/^[a-zA-Z]+(\s[a-zA-Z]+)?$/, 'Only Alphabets and Space Allowed and Must Start with Alphabet')
    .required('Please provide first name'),
  lastName: Yup.string()
    .min(2, 'Last Name must be at-least 2 characters')
    .max(50, 'Last Name must be at most 50 characters')
    .matches(
      /^[A-Za-z]+([ -][A-Za-z]+)*$/,
      'Only alphabets, spaces, and hyphens are allowed, and must start with an alphabet'
    )
    .required('Please provide last name'),
  addressLine_1: Yup.string().min(1, 'Address must be at-least 1 characters').required('Please provide Address Line '),
  // addressLine_2: Yup.string().min(3, 'Address line -2  must be at-least 3 characters')
  //   .max(200, "address line 2 must be at most 200 characters"),
  city: Yup.string().nullable().max(100, 'city must be at most 100 characters').required('City is a required field'),
  zipCode: Yup.string()
    .matches(/^[\d-]+$/, 'Must be digits and/or hyphens only')
    .min(5, 'Must be atleast 5 digits')
    .required('Zip Code is required'),
  state: Yup.string().required('State is a required field')
})
