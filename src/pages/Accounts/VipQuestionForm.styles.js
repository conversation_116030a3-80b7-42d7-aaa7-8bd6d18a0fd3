import { makeStyles } from '@mui/styles'

import { ButtonPrimary } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  vipFormBottom: {
    display: 'flex',
    gap: '15px',
    // marginTop: '38px',
    marginBottom: '38px',
    justifyContent: 'center',
    // marginTop: '20px',
    [theme.breakpoints.down('md')]: {
      // marginTop: '34px',
      marginBottom: '14px'
    },
    '& button': {
      [theme.breakpoints.down('md')]: {
        minHeight: '2.5rem'
      }
    },
    '& .saveBtn': {
      ...ButtonPrimary(theme),
      '&:hover': {
        ...ButtonPrimary(theme)
      }
    }
  },

  vipQuestionForm: {
    minHeight: 'auto',
    '& .affiliate-page': {
      '& .user-detail-label': {
        fontSize: theme.spacing(1),
        fontWeight: '500',
        lineHeight: '23.02px',
        textAlign: 'left',
        color: '#FFFFFF'
      },
      maxWidth: theme.spacing(74.6875),
      margin: '0 auto',
      '& .genral-tab': {
        border: `1px solid ${theme.colors.modalTabBtnActive}`,
        borderRadius: theme.spacing(0.625),
        margin: theme.spacing(1, 0),
        '& .setting-card-details': {
          padding: theme.spacing(2.375, 2.9375),
          [theme.breakpoints.down('lg')]: {
            padding: theme.spacing(1)
          },
          '& .MuiFormControl-root': {
            // minWidth: theme.spacing(37.5),
            [theme.breakpoints.down('lg')]: {
              minWidth: '100%'
            },
            '& .MuiInputBase-root': {
              paddingRight: 0,
              '& .MuiInputBase-input': {
                border: `2px solid ${theme.colors.modalTabBtnActive}`,
                color: theme.colors.textWhite,
                borderRadius: theme.spacing(0.625),

                padding: theme.spacing(0.625, 1),
                height: 'auto'
                // height:theme.spcing()
              },
              '&.Mui-focused': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.colors.YellowishOrange,
                  borderRadius: theme.spacing(0.625)
                }
              }
            },
            '& .MuiFormLabel-root': {
              color: theme.colors.textWhite,
              lineHeight: theme.spacing(1)
            }
          },
          '& p': {
            marginBottom: theme.spacing(1),
            [theme.breakpoints.up('md')]: {
              minHeight: '46px'
            }
          },
          '& .MuiFormGroup-root': {
            border: `2px solid ${theme.colors.modalTabBtnActive}`,
            color: theme.colors.textWhite,
            borderRadius: theme.spacing(0.625),

            padding: theme.spacing(0.625, 1)
          },

          '& .MuiIconButton-edgeEnd': {
            color: theme.colors.textWhite
          },
          '& .MuiInputAdornment-root': {
            position: 'absolute',
            right: theme.spacing(2)
          },
          '& .inputSelect': {
            width: '-webkit-fill-available',
            border: `2px solid ${theme.colors.modalTabBtnActive}`,
            // borderColor: theme.colors.YellowishOrange,
            borderRadius: '0.625rem',
            color: `${theme.colors.textWhite}!important`,
            padding: '12px 14px',
            backgroundColor: 'transparent',
            height: 'auto',

            '&:focus': {
              border: `1px solid ${theme.colors.YellowishOrange}`
            },
            '& option': {
              color: theme.colors.textWhite,
              background: theme.colors.textBlack
            },
            '&:focus-visible': {
              outline: 'none'
            },
            '&:disabled': {
              // color: 'rgba(255, 255, 255, 0.6) !important'
              opacity: '0.4 !important',
              cursor: 'not-allowed'
            },
            '&.disabled': {
              opacity: '0.4 !important',
              cursor: 'not-allowed'
            },
            // '&:disabled': {
            // WebkitTextFillColor: theme.colors.textWhite
            // },
            '&::-webkit-inner-spin-button, &::-webkit-outer-spin-button': {
              '-webkit-appearance': 'none',
              margin: 0
            }
          }
        }
      },
      '& .text-area': {
        '& .MuiInputBase-root': {
          padding: '0'
        }
      },
      '& .MuiFormControl-root': {
        width: '100%'
      }
    }
  },
  errorLabel: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    lineHeight: 'normal !important',
    fontWeight: '600 !important',
    marginTop: theme.spacing(0.5),
    position: 'absolute'
  }
}))
