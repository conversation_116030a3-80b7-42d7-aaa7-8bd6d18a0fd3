
import { makeStyles } from '@mui/styles'

import { ButtonPrimary } from '../../../../MainPage.styles'

export default makeStyles((theme) => ({
  updateProfileText: {
    margin: "20px 0px",
    fontFamily: "Raj<PERSON>ni,sans-serif",
    fontWeight: 300,
    fontSize: "2.125rem",
    lineHeight: "1.235",
  },
  KycContainer: {
    display: "flex",
    alignItems: "center",
    flexDirection: "column",

    "& .profileBtn": {
      ...ButtonPrimary(theme),
      "&:hover": {
        ...ButtonPrimary(theme),
      }
    }
  }

}))
