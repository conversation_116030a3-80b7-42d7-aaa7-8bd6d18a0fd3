import * as React from 'react'
import PropTypes from 'prop-types'
import { Grid, Box, Typography } from '@mui/material'
import SumSubKYCSection from './SumSubKYCSection'

function CustomTabPanel (props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

function KYCSection ({ handleClose, setInitializeKYC }) {
  return (
    <Grid>
      <SumSubKYCSection handleClose={handleClose} setInitializeKYC={setInitializeKYC} />
    </Grid>
  )
}

export default KYCSection
