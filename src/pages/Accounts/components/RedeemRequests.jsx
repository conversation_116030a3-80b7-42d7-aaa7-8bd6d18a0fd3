import React, { useState } from 'react'
import useStyles from '../bets.styles'
import '../../../../src/App.css'
import {
  Grid,
  Pagination,
  Typography,
  Tooltip,
  Button,
  CircularProgress,
  IconButton,
  TableRow,
  TableHead,
  TableContainer,
  TableCell,
  TableBody,
  Table,
  useTheme
} from '@mui/material'
import useTransaction from '../hooks/useRedeemRequests'
import { usePortalStore } from '../../../store/userPortalSlice'
import { commonDateTimeFormat, formatCommonNumber } from '../../../utils/helpers'
import moment from 'moment'
import { TransitionLeaderBoard } from '../../Lobby/components/LeaderBoard/TransitionLeaderBoard.style'
import { PaymentQuery, useGetProfileMutation } from '../../../reactQuery'
import SomethingWentWrong from '../../../components/SomethingWentWrong'
import EditIcon from '../../../components/ui-kit/icons/svg/editicon.svg'
import { toast } from 'react-hot-toast'
import EditCancelRedeem from './components/EditCancelRedeem'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { useUserStore } from '../../../store/useUserSlice'
import Delete from '@mui/icons-material/Delete'

const TransactionSection = ({ showHistory, setShowHistory }) => {
  const classes = useStyles()
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const portalStore = usePortalStore((state) => state)
  const theme = useTheme()
  const userDetails = useUserStore((state) => state.userDetails)

  const handleChange = (value) => {
    setPageNo(value)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const { transactionsData, isTransactionLoading, pageNo, setPageNo, limit, refetch } = useTransaction()

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      portalStore.closePortal()
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const successToggler = (res) => {
    toast.success(res?.message)
    refetch()
    getProfileMutation.mutate()
  }
  const [showEditRedeemData, setShowEditRedeemData] = useState(null)

  const errorToggler = () => {
    portalStore.openPortal(() => <SomethingWentWrong />, 'innerModal')
  }
  const cancelRedeemRequest = PaymentQuery.useCancelRedeemMutation({ successToggler, errorToggler })

  const handleCancelRedeem = (transactionId, amount, paymentProvider) => {
    cancelRedeemRequest.mutate({ transactionId })
    var parameters = {
      amount: amount,
      paymentMethod: paymentProvider
    }
    if (import.meta.env.VITE_NODE_ENV === 'production') {
      customEvent('redemption_reversed', parameters, userDetails?.userId)
    }
  }

  const confirmRedeemRequest = PaymentQuery.useConfirmRedeemMutation({ successToggler, errorToggler })

  return (
    <>
      {showEditRedeemData ? (
        <EditCancelRedeem
          item={showEditRedeemData}
          cancelRedeemRequest={cancelRedeemRequest}
          confirmRedeemRequest={confirmRedeemRequest}
          setShowEditRedeemData={setShowEditRedeemData}
          type='redeemPage'
        />
      ) : (
        <>
          <Grid className={classes.redeemModal}>
            <Grid display={'flex'} alignItems={'center'} justifyContent={''}>
              {showHistory && (
                <Grid>
                  <Tooltip title='Back to Redeem' arrow>
                    <IconButton edge='start' color='inherit' onClick={() => setShowHistory(false)} aria-label='close'>
                      <ArrowBackIcon />
                    </IconButton>
                  </Tooltip>
                </Grid>
              )}
              <Typography variant='heading' sx={{ color: '#FFFFFF' }}>
                Redeem Transaction List
              </Typography>
            </Grid>

            <TransitionLeaderBoard theme={theme}>
              <Grid>
                <TableContainer style={{ width: '100%', margin: '20px 0px' }}>
                  <Grid className={classes.leaderBoardContainer}>
                    <Grid className={classes.tableCard}>
                      {isTransactionLoading ? (
                        <Typography colSpan={8} style={{ textAlign: 'center' }}>
                          <CircularProgress size={10} />
                        </Typography>
                      ) : transactionsData?.myTransactions?.rows?.length ? (
                        transactionsData?.myTransactions?.rows?.map((item, index) => {
                          return (
                            <>
                              <Grid className={classes.tableCardHead}>
                                <Typography className='tdate'>
                                  {moment(new Date(item?.createdAt)).format(commonDateTimeFormat.date)}
                                </Typography>
                                <Typography className='tstatus'>
                                  {item?.status === 0
                                    ? 'Pending'
                                    : item?.status === 1
                                    ? 'Success'
                                    : item?.status === 7
                                    ? 'In-process'
                                    : item?.status === 3
                                    ? 'Failed'
                                    : 'Cancelled'}
                                </Typography>
                              </Grid>
                              <Grid className={classes.tableCardBody}>
                                <Grid>
                                  <Typography sx={{ fontSize: '14px', padding: '0' }}>Withdraw Request ID</Typography>
                                  <Typography sx={{ padding: '0' }}>
                                    <b>{item?.transactionId}</b>
                                  </Typography>
                                </Grid>
                                <Grid style={{ marginLeft: '5px', padding: '0' }}>
                                  <Typography>Amount</Typography>
                                  <Typography>
                                    <b>{formatCommonNumber(item?.amount)}</b>
                                  </Typography>
                                </Grid>

                                {item?.status === 0 ? (
                                  <TableCell style={{ textAlign: 'center', padding: '0px' }} scope='row'>
                                    <Tooltip title='Edit Request' arrow>
                                      <Button onClick={() => setShowEditRedeemData(item)}>
                                        <img src={EditIcon} />
                                      </Button>
                                    </Tooltip>
                                  </TableCell>
                                ) : (
                                  <TableCell style={{ flex: 1, textAlign: 'center' }} scope='row'>
                                    -
                                  </TableCell>
                                )}
                              </Grid>
                            </>
                          )
                        })
                      ) : (
                        <Typography colSpan={8} style={{ textAlign: 'center', color: 'red' }}>
                          <span>No Records Found</span>
                        </Typography>
                      )}
                    </Grid>
                    <Table className={classes.transactionTable} sx={{ whiteSpace: 'nowrap' }}>
                      <TableHead>
                        <TableRow>
                          <TableCell style={{ flex: 1 }}>Amount</TableCell>
                          <TableCell style={{ flex: 1 }}>Status</TableCell>
                          <TableCell style={{ flex: 1 }}>Date/Time</TableCell>
                          <TableCell style={{ flex: 1, paddingLeft: '1rem' }}>Withdraw Request ID</TableCell>
                          <TableCell
                            style={{
                              //  flex: 1,
                              textAlign: 'center'
                            }}
                          >
                            Edit
                          </TableCell>

                          <TableCell
                            style={{
                              textAlign: 'center'
                            }}
                          >
                            Cancel Request
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {isTransactionLoading ? (
                          <TableRow style={{ background: 'transparent' }}>
                            <TableCell colSpan={8} style={{ textAlign: 'center' }}>
                              <CircularProgress size={24} />
                            </TableCell>
                          </TableRow>
                        ) : transactionsData?.myTransactions?.rows?.length ? (
                          transactionsData?.myTransactions?.rows?.map((item, index) => {
                            return (
                              <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                                <TableCell style={{ flex: 1 }} scope='row'>
                                  <span>{formatCommonNumber(item?.amount)}</span>
                                </TableCell>

                                <TableCell style={{ flex: 1 }} scope='row'>
                                  <span>
                                    {item?.status === 0
                                      ? 'Pending'
                                      : item?.status === 1
                                      ? 'Success'
                                      : item?.status === 7
                                      ? 'Pending'
                                      : item?.status === 8
                                      ? 'Pending'
                                      : item?.status === 3
                                      ? 'Failed'
                                      : 'Cancelled'}
                                  </span>
                                </TableCell>

                                <TableCell style={{ flex: 1 }} scope='row'>
                                  <span>
                                    {moment(new Date(item?.createdAt)).format(commonDateTimeFormat.date)}/
                                    {new Date(item?.createdAt).toLocaleTimeString('en-US', { hour12: true })}
                                  </span>
                                </TableCell>

                                <TableCell style={{ flex: 1, paddingLeft: '1rem' }} scope='row'>
                                  <span>{item?.transactionId || '-'}</span>
                                </TableCell>

                                {item?.status === 0 ? (
                                  <TableCell style={{ flex: 1, textAlign: 'center' }} scope='row'>
                                    <Tooltip title='Edit Request' arrow>
                                      <Button onClick={() => setShowEditRedeemData(item)}>
                                        <img src={EditIcon} />
                                      </Button>
                                    </Tooltip>
                                  </TableCell>
                                ) : (
                                  <TableCell style={{ flex: 1, textAlign: 'center' }} scope='row'>
                                    -
                                  </TableCell>
                                )}

                                {item?.status === 0 ? (
                                  <TableCell style={{ textAlign: 'center' }} scope='row'>
                                    <Tooltip title='Cancel Request' arrow>
                                      <Button
                                        onClick={() =>
                                          handleCancelRedeem(item?.transactionId, item?.amount, item?.paymentProvider)
                                        }
                                        disabled={cancelRedeemRequest.isLoading}
                                      >
                                        <Delete />
                                      </Button>
                                    </Tooltip>
                                  </TableCell>
                                ) : (
                                  <TableCell style={{ textAlign: 'center' }} scope='row'>
                                    -
                                  </TableCell>
                                )}
                              </TableRow>
                            )
                          })
                        ) : (
                          <TableRow style={{ background: 'transparent' }}>
                            <TableCell colSpan={8} style={{ textAlign: 'center', color: 'red' }}>
                              <span>No Records Found</span>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </Grid>
                </TableContainer>
              </Grid>
            </TransitionLeaderBoard>

            {transactionsData?.myTransactions?.count > 0 && !isTransactionLoading && (
              <Pagination
                count={Math.ceil(transactionsData?.myTransactions?.count / limit)}
                page={pageNo}
                onChange={handleChange}
                defaultPage={3}
                siblingCount={1}
                className={classes.tablePagination}
              />
            )}
          </Grid>
        </>
      )}
    </>
  )
}

export default TransactionSection
