import React, { useState } from 'react'
import useStyles from '../bets.styles'
import '../../../../src/App.css'
import {
  Grid,
  Box,
  Pagination,
  Tab,
  Tabs,
  CircularProgress,
  TableRow,
  TableHead,
  TableContainer,
  TableCell,
  TableBody,
  Table,
  useTheme
} from '@mui/material'
import useBetHistorySection from '../hooks/useBetHistorySection'
import { commonDateTimeFormat, formatCommonNumber } from '../../../utils/helpers'
import moment from 'moment'
import { LeaderBoardContainer } from '../../Lobby/components/LeaderBoard/LeaderBoard.styles'
import PropTypes from 'prop-types'
import TransactionSection from './TransactionSection'
import Select from 'react-select'
import DatePicker from '../../../components/DatePicker'
import GiveawayHistorySection from './GiveawayHistorySection'
/* eslint-disable multiline-ternary */
function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <>{children}</>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

const BetHistorySection = () => {
  const classes = useStyles()
  const theme = useTheme()
  const [selectedOption, setSelectedOption] = useState({ value: 'GC', label: 'Gold Coin' })
  const options = [
    { value: 'GC', label: 'Gold Coin' },
    { value: 'SC', label: 'Sweep Coin' }
  ]
  const [value, setValue] = React.useState(0)

  const handleChange = (event, value) => {
    setPageNo(value)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const handleTabChange = (event, newValue) => {
    setValue(newValue)
  }

  const { betsData, isBetsLoading, pageNo, setPageNo, limit, setStartDate, setEndDate, setCoinType } =
    useBetHistorySection()

  const handleCoinChange = (event) => {
    setPageNo(1)
    setSelectedOption(event)
    setCoinType(event.value)
  }

  const customStyles = {
    input: (provided) => ({
      ...provided,
      pointerEvents: 'none'
    })
  }

  // Function to handle start date change
  const handleStartDateChange = (date) => {
    setStartDate(date)
  }

  // Function to handle end date change
  const handleEndDateChange = (date) => {
    setEndDate(date)
  }

  return (
    <>
      <Grid className={`${classes.lobbyRight}`}>
        <Grid className={classes.wrapper}>
          {/* <Grid className='theme-table'> */}
          <LeaderBoardContainer theme={theme}>
            <Grid className='parent'>
              <Grid display={'flex'} justifyContent='space-between'>
                <Box sx={{ width: '100%' }} className='tabsDesign'>
                  <Tabs
                    value={value}
                    onChange={handleTabChange}
                    aria-label='basic tabs example'
                    variant='scrollable'
                    scrollButtons
                    allowScrollButtonsMobile
                  >
                    <Tab label='My Plays' />
                    <Tab label='Transactions' />
                    <Tab label='Giveaway History' />
                  </Tabs>
                </Box>
              </Grid>
              <CustomTabPanel value={value} index={0}>
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={4} className={classes.transactionInputs}>
                    <Grid className={classes.authInputWrap}>
                      <DatePicker onStartDateChange={handleStartDateChange} onEndDateChange={handleEndDateChange} />
                    </Grid>
                  </Grid>

                  <Grid item xs={12} sm={4} className={classes.transactionInputs}>
                    <Grid className={classes.authInputWrap}>
                      <Select
                        value={selectedOption} // defaultMenuIsOpen
                        onChange={handleCoinChange}
                        options={options}
                        className={classes.reactCoinSelect}
                        classNamePrefix='reactInnerCoinSelect'
                        placeholder='CoinType'
                        styles={customStyles}
                        isSearchable={false}
                        components={{
                          Input: (props) => <components.Input {...props} readOnly />
                        }}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                <TableContainer style={{ margin: '20px 0px' }}>
                  <Grid className='leaderBoardContainer'>
                    <Table aria-label='a dense table'>
                      <TableHead>
                        <TableRow>
                          <TableCell style={{ flex: 1 }}>Game Id</TableCell>
                          <TableCell style={{ flex: 1 }}>Date & Time</TableCell>
                          <TableCell style={{ flex: 1 }}>Game Name</TableCell>
                          <TableCell style={{ flex: 1 }}>Stake</TableCell>
                          <TableCell style={{ flex: 1 }}>Win</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {isBetsLoading ? (
                          <TableRow style={{ background: 'transparent' }}>
                            <TableCell colSpan={10} style={{ textAlign: 'center' }}>
                              <CircularProgress size={24} />
                            </TableCell>
                          </TableRow>
                        ) : betsData?.rows?.length ? (
                          betsData?.rows?.map((item, index) => {
                            return (
                              <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                                <TableCell style={{ flex: 1 }} scope='row'>
                                  {item?.game_id}
                                </TableCell>
                                <TableCell style={{ flex: 1 }}>
                                  {moment(new Date(item?.created_at)).format(commonDateTimeFormat.dateWithTime)}
                                </TableCell>
                                <TableCell style={{ flex: 1 }}>{item?.game_identifier}</TableCell>
                                <TableCell style={{ flex: 1 }}>{formatCommonNumber(item?.bet_amount)}</TableCell>
                                <TableCell style={{ flex: 1 }}>{formatCommonNumber(item?.win_amount)}</TableCell>
                              </TableRow>
                            )
                          })
                        ) : (
                          <TableRow style={{ background: 'transparent' }}>
                            <TableCell colSpan={10} style={{ textAlign: 'center', color: 'red' }}>
                              <span>No Records Found</span>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </Grid>
                </TableContainer>

                {betsData?.count > 0 && !isBetsLoading && (
                  <Pagination
                    className={classes.tablePagination}
                    count={Math.ceil(betsData?.count / limit)}
                    page={pageNo}
                    onChange={handleChange}
                    defaultPage={3}
                    siblingCount={1}
                  />
                )}
              </CustomTabPanel>

              <CustomTabPanel value={value} index={1}>
                <TransactionSection />
              </CustomTabPanel>
              <CustomTabPanel value={value} index={2}>
                <GiveawayHistorySection />
              </CustomTabPanel>
            </Grid>
          </LeaderBoardContainer>
        </Grid>
      </Grid>
    </>
  )
}

export default BetHistorySection
