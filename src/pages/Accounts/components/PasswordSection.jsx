import React from 'react'
import useStyles from '../Accounts.styles'
import '../../../../src/App.css'
import { Button, Grid, IconButton, InputAdornment, OutlinedInput, CircularProgress, Box } from '@mui/material'
import Visibility from '@mui/icons-material/Visibility'
import VisibilityOff from '@mui/icons-material/VisibilityOff'
import useChangePassword from '../hooks/useChangePassword'
import PasswordErrorMsg from '../../../components/ReusableComponents/PasswordErrorMsg'

const PasswordSection = () => {
  const classes = useStyles()
  const {
    handleSubmit,
    register,
    handleOnSubmitPassword,
    errors,
    changePasswordMutation,
    reset,
    watch
  } = useChangePassword()

  const [showPassword, setShowPassword] = React.useState({
    password: false,
    newPassword: false,
    repeatPassword: false
  })

  const handleClickShowPassword = (field) => {
    setShowPassword((prevShowPassword) => ({
      ...prevShowPassword,
      [field]: !prevShowPassword[field]
    }))
  }
  const handleMouseDownPassword = (event) => {
    event.preventDefault()
  }
  const newPasswordValue = watch('newPassword')

  return (
    <>
      <form onSubmit={handleSubmit(handleOnSubmitPassword)}>
        <Box className='profile'>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4} className={classes.changePWD}>
              <OutlinedInput
                id='password'
                {...register('password')}
                placeholder='Enter Old Password'
                onKeyDown={(evt) => evt.keyCode === 32 && evt.preventDefault()}
                type={showPassword.password ? 'text' : 'password'}
                endAdornment={
                  <InputAdornment position='end'>
                    <IconButton
                      aria-label='toggle password visibility'
                      onClick={() => handleClickShowPassword('password')}
                      onMouseDown={handleMouseDownPassword}
                      edge='end'
                    >
                      {showPassword.password ? <Visibility /> : <VisibilityOff />}
                    </IconButton>
                  </InputAdornment>
                }
              />
              <p className={classes.inputError}>{errors?.password && errors?.password?.message}</p>
            </Grid>
            <Grid item xs={12} sm={6} md={4} className={classes.changePWD}>
              <OutlinedInput
                id='password'
                {...register('newPassword')}
                placeholder='Enter New Password'
                onKeyDown={(evt) => {
                  if (evt.keyCode === 32) evt.preventDefault()
                }}
                type={showPassword.newPassword ? 'text' : 'password'}
                endAdornment={
                  <InputAdornment position='end'>
                    <IconButton
                      aria-label='toggle password visibility'
                      onClick={() => handleClickShowPassword('newPassword')}
                      onMouseDown={handleMouseDownPassword}
                      edge='end'
                    >
                      {showPassword.newPassword ? <Visibility /> : <VisibilityOff />}
                    </IconButton>
                  </InputAdornment>
                }
              />
              <p className={classes.inputError}>{errors?.newPassword && errors?.newPassword?.message}</p>
              {newPasswordValue === undefined || newPasswordValue?.length === 0 ? (
                <></>
              ) : (
                <PasswordErrorMsg password={newPasswordValue} errors={errors} />
              )}
            </Grid>
            <Grid item xs={12} sm={6} md={4} className={classes.changePWD}>
              <OutlinedInput
                id='cpd_password'
                {...register('repeatPassword')}
                placeholder='Enter Confirm Password'
                onKeyDown={(evt) => {
                  if (evt.keyCode === 32) evt.preventDefault()
                }}
                type={showPassword.repeatPassword ? 'text' : 'password'}
                endAdornment={
                  <InputAdornment position='end'>
                    <IconButton
                      aria-label='toggle password visibility'
                      onClick={() => handleClickShowPassword('repeatPassword')}
                      onMouseDown={handleMouseDownPassword}
                      edge='end'
                    >
                      {showPassword.repeatPassword ? <Visibility /> : <VisibilityOff />}
                    </IconButton>
                  </InputAdornment>
                }
              />
              <p className={classes.inputError}>{errors?.repeatPassword && errors?.repeatPassword?.message}</p>
            </Grid>
          </Grid>
        </Box>
        <Grid sx={{ display: 'flex', gap: '15px', marginTop: '38px' }}>
          <Button variant='contained' className='saveBtn' type='submit' disabled={changePasswordMutation.isLoading}>
            {changePasswordMutation.isLoading ? (
              <CircularProgress size={24} style={{ marginRight: 8 }} />
            ) : (
              <span className='btn-span'>Save</span>
            )}
          </Button>

          <Button variant='contained' className='saveBtn' onClick={() => reset()}>
            <span className='btn-span'>Reset</span>
          </Button>
        </Grid>
      </form>
    </>
  )
}

export default PasswordSection
