import React from 'react'
import useStyles from '../Accounts.styles'
import { Button, Grid, IconButton, TextField, Typography, DialogContent, FormLabel } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { usePortalStore } from '../../../store/userPortalSlice'
import { userSSNSchema } from '../schema'
import { GeneralQuery, useGetProfileMutation } from '../../../reactQuery'
import { useUserStore } from '../../../store/useUserSlice'


const SsnSection = () => {
  const classes = useStyles()
  const user = useUserStore((state) => state)
  const portalStore = usePortalStore((state) => state)
  const {
    register,
    formState: { errors },
    handleSubmit,
  } = useForm({ resolver: yupResolver(userSSNSchema) })

  const handleClose = () => {
    portalStore.closePortal()
  }
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails({ ...res?.data?.data })
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const successToggler = (data) => {
    getProfileMutation.mutate()
    handleClose()
  }
  const errorToggler = () => {
  }
  const { mutate: submitPersonalMutation, isLoading: isSubmitFormLoading } = GeneralQuery.useSsnMutation({
    successToggler,
    errorToggler
  })

  const onSSNSubmit = (data) => {
    const tempData = {
      ssn: data?.ssn
    }
    submitPersonalMutation(tempData)
  }

  return (
    <>
      <Grid className='inner-modal-header'>
        <Typography variant='h4'>SSN Verification</Typography>
        {/* {isPhoneVerified ? */}
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
        {/* :
                        <></>
                    } */}
      </Grid>
      <DialogContent>
        <Grid className='user-name-content-wrap'>
          <form onSubmit={handleSubmit(onSSNSubmit)}>
            <Grid className='inner-modal-left'>
              <Grid className={classes.authInputWrap}>
                <Grid className={classes.authInputWrap}>
                  <FormLabel>Please enter your Social Security Number</FormLabel>
                  <TextField
                    id='outlined-basic'
                    label=''
                    variant='outlined'
                    placeholder='Enter SSN '
                    {...register('ssn')}
                  />
                  {errors?.ssn && <p className='input-error'>{errors?.ssn?.message}</p>}
                </Grid>
              </Grid>
              <Grid className='otp-btn-wrap'>
                <Grid className={classes.btnWhiteGradient}>
                  <Button variant='contained' className='btn-gradient' type='submit'>
                    <span>Continue</span>
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </form>
        </Grid>
      </DialogContent>
    </>
  )
}

export default SsnSection
