import * as Yup from 'yup'

export const redeemSchema = (maxRedeemableCoins) => {
  return Yup.object().shape({

    actionableEmail: Yup.string().test('is-email', 'Invalid email address', value => {
      if (!value) return true; // Let Yup.string().required() handle empty values
      const emailRegex = /^(([^<>()[\]\\.,;+:\s@"]+(\.[^<>()[\]\\.,;+:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return emailRegex.test(value);
    }).required('Please enter an email address'),

    amount: Yup.number().typeError('Must be a number').positive('Must be greater than 0').max(maxRedeemableCoins, `Amount must not exceed ${maxRedeemableCoins}`).required('Amount Required').nullable(),
    checkbox: Yup.boolean().oneOf([true], 'Please select the checkbox'),

  })
}

export const redeemCancelSchema = (isActionableEmail) => {
  if (isActionableEmail) {
    return Yup.object().shape({

      actionableEmail: Yup.string().test('is-email', 'Invalid email address', (value) => {
        if (!value) return false; // Let Yup.string().required() handle empty values
        const emailRegex =
        /^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}|(\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\]))$/;
      return emailRegex.test(value);
      }).required('Please enter an email address'),
    });
  } 
  return Yup.object().shape({

    bank: Yup.string().test('is-bank', 'Please select bank account', (value) => { 
      return !!value;
    }).required('Please select a bank account'),
  });
}



