import { makeStyles } from '@mui/styles'

import { MaintenanceBg } from '../../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
    cancelRedeemModal: {
        minWidth: theme.spacing(22.5),
        backgroundImage: `url(${MaintenanceBg})`,
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: "98% 100%",
        borderRadius: theme.spacing(0.625),
        [theme.breakpoints.down('sm')]: {
            minWidth: "100%",
        },
        "& .reedem-kyc-content": {
            padding: theme.spacing(1, 2),
            [theme.breakpoints.down('sm')]: {
                padding: theme.spacing(1),
            },
            "& .MuiInputBase-root": {
                height: theme.spacing(2.3125),
                borderRadius: theme.spacing(0.313)
            }
        },
        "& .MuiTypography-heading": {
            color: theme.colors.textWhite,
            fontSize: theme.spacing(1.5),
            fontWeight: "bold"
        }
    }


}))


