import { Controller, useForm } from 'react-hook-form'
import {useQueryClient } from '@tanstack/react-query'
import { yupResolver } from '@hookform/resolvers/yup'
import { KeyTypes } from '../../../../reactQuery/KeyTypes'
import { useEffect, useMemo } from 'react'
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  OutlinedInput,
  Radio,
  RadioGroup,
  Typography
} from '@mui/material'
import { usePortalStore, useVipQuetsions } from '../../../../store/store'
import useStyles from '../../VipQuestionForm.styles'
import { useVipUserAnswerMutation } from '../../../../reactQuery'
import toast from 'react-hot-toast'
import buildValidationSchema from './formSchema'
import VipCompletionPopUp from './VipCompletionPopUp'
import { deleteVipRoute } from '../../../../utils/cookiesCollection'

/* eslint-disable multiline-ternary */

const DynamicVipQuestionForm = () => {
  deleteVipRoute('vipRoute')

  const { vipQuestions, vipAnswers } = useVipQuetsions()
  const classes = useStyles()
  const queryClient = useQueryClient()
  const portalStore = usePortalStore((state) => state)

  const onVipAnswerSuccess = (data) => {
    toast.success(`${data?.data?.message}`)
    queryClient.invalidateQueries([KeyTypes.VIP_USER_ANSWERS])
    if (data?.data?.isClaimedVipBonus) portalStore.openPortal(() => <VipCompletionPopUp />, 'vipModal')
  }

  const onVipAnswerError = (err) => {
    console.log(err)
  }

  const vipUserAnswerMutation = useVipUserAnswerMutation({
    onSuccess: onVipAnswerSuccess,
    onError: onVipAnswerError
  })

  const mappedAnswers = useMemo(() => {
    const values = {}
    ;(vipAnswers || []).forEach((ans) => {
      const qId = String(ans?.questionId)
      if (typeof ans?.rawAnswer === 'string') {
        values[qId] = ans?.rawAnswer || ''
      } else if (ans?.rawAnswer?.selectedOptionId) {
        values[qId] = String(ans?.rawAnswer?.selectedOptionId) || ''
      } else if (ans?.rawAnswer?.selectedOptionIds) {
        values[qId] = ans?.rawAnswer?.selectedOptionIds.map(String) || []
      }
    })
    return values
  }, [vipAnswers])

  const initialValues = useMemo(() => {
    const values = {}
    ;(vipQuestions || [])?.forEach((question) => {
      const id = String(question?.questionnaireId)
      const type = question?.frontendQuestionType
      const existingAnswer = mappedAnswers[id]
      if (existingAnswer !== undefined) {
        values[id] = existingAnswer
      } else {
        if (type === 'checkbox') {
          values[id] = []
        } else {
          values[id] = ''
        }
      }
    })
    return values
  }, [vipQuestions, mappedAnswers])

  useEffect(() => {
    if (vipQuestions?.length > 0 && vipAnswers?.length > 0) {
      reset(initialValues)
    }
  }, [vipQuestions, vipAnswers, initialValues])

  const formSchema = buildValidationSchema(vipQuestions)
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    reset,
    control
  } = useForm({
    resolver: yupResolver(formSchema),
    defaultValues: initialValues,
    shouldUnregister: false,
    mode: 'onChange',
    criteriaMode: 'all',
    shouldFocusError: true
  })

  const transformPayload = (data, vipQuestions) => {
    const answers = Object.entries(data).map(([key, value]) => {
      const question = vipQuestions?.find((question) => String(question?.questionnaireId) === key)
      if (!question) return null
      const type = question?.frontendQuestionType
      let answer
      if (type === 'text') {
        answer = value.replace(/\s{2,}/g, ' ').trim()
      } else if (type === 'email' || type === 'textarea' || type === 'number') {
        answer = value?.trim()
      } else if (type === 'radio' || type === 'select') {
        answer = { selectedOptionId: Number(value) }
      } else if (type === 'checkbox') {
        answer = { selectedOptionIds: value.map(Number) }
      }
      return {
        questionnaireId: Number(key),
        answer
      }
    })
    return answers
  }

  const onSubmit = (data) => {
    if (isDirty) {
      const transformedPayload = transformPayload(data, vipQuestions)
      const finalPayload = { answers: transformedPayload.filter(Boolean) }
      vipUserAnswerMutation.mutate(finalPayload)
    }
  }

  return (
    <>
      <h2 style={{ marginTop: '12px' }}>Unlock Your VIP Identity</h2>
      <>
        {vipQuestions && vipQuestions?.length > 0 ? (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid className={classes.vipQuestionForm}>
              <Box className='affiliate-page'>
                <Box className='genral-tab'>
                  <Grid className='setting-card-details'>
                    <Grid container spacing={2}>
                      {vipQuestions &&
                        vipQuestions?.length > 0 &&
                        vipQuestions.map((question, index) => {
                          const isFullWidth =
                            question?.frontendQuestionType === 'checkbox' || question?.frontendQuestionType === 'radio'
                          return (
                            <Grid key={question?.questionnaireId} item xs={12} sm={12} lg={isFullWidth ? 12 : 6}>
                              <Grid className='input-wrap '>
                                <Typography className='user-detail-label'>
                                  {index + 1}
                                  {' . '}
                                  {question?.question} {question?.required && <span style={{ color: 'red' }}>*</span>}
                                </Typography>
                                {(question?.frontendQuestionType === 'text' ||
                                  question?.frontendQuestionType === 'email') && (
                                  <>
                                    <FormControl variant='outlined'>
                                      <OutlinedInput
                                        id={String(question?.questionnaireId)}
                                        variant='outlined'
                                        placeholder={question?.question}
                                        type={question?.frontendQuestionType}
                                        {...register(String(question?.questionnaireId))}
                                      />
                                    </FormControl>
                                    {errors[question?.questionnaireId] && (
                                      <p className={classes.errorLabel}> {errors[question?.questionnaireId].message}</p>
                                    )}
                                  </>
                                )}

                                {question?.frontendQuestionType === 'number' && (
                                  <>
                                    <FormControl variant='outlined'>
                                      <OutlinedInput
                                        {...register(String(question?.questionnaireId))}
                                        variant='outlined'
                                        placeholder={question?.question}
                                        type={question?.frontendQuestionType}
                                        onInput={(e) => {
                                          e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, 10)
                                        }}
                                        min={0}
                                        onKeyDown={(evt) =>
                                          ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()
                                        }
                                      />
                                    </FormControl>

                                    {errors[question?.questionnaireId] && (
                                      <p className={classes.errorLabel}> {errors[question?.questionnaireId].message}</p>
                                    )}
                                  </>
                                )}
                                {question?.frontendQuestionType === 'textarea' && (
                                  <>
                                    <Grid className='input-wrap text-area'>
                                      <FormControl variant='outlined'>
                                        <OutlinedInput
                                          multiline
                                          rows={8}
                                          placeholder={question?.question}
                                          type='text'
                                          key={question?.questionnaireId}
                                          {...register(String(question?.questionnaireId))}
                                        />
                                      </FormControl>
                                    </Grid>
                                    {errors[question?.questionnaireId] && (
                                      <p className={classes.errorLabel}> {errors[question?.questionnaireId].message}</p>
                                    )}
                                  </>
                                )}
                                {question?.frontendQuestionType === 'checkbox' && question?.options?.length > 0 && (
                                  <>
                                    <FormGroup className='user-details-input'>
                                      <Controller
                                        name={String(question?.questionnaireId)}
                                        control={control}
                                        render={({ field, fieldState }) => {
                                          const selectedValues = field.value || []
                                          const handleChange = (id) => {
                                            const newVal = selectedValues.includes(id)
                                              ? selectedValues.filter((v) => v !== id)
                                              : [...selectedValues, id]
                                            field.onChange(newVal)
                                          }

                                          return (
                                            <Box
                                              sx={{
                                                display: 'flex',
                                                flexDirection: {
                                                  xs: 'column', // vertical on small screens (mobile)
                                                  sm: 'row' // horizontal from small screens upwards
                                                },
                                                flexWrap: {
                                                  xs: 'nowrap',
                                                  sm: 'wrap'
                                                },
                                                gap: {
                                                  sx: 1,
                                                  sm: 2
                                                }
                                              }}
                                            >
                                              {question?.options?.map((opt) => (
                                                <FormControlLabel
                                                  key={opt?.id}
                                                  control={
                                                    <Checkbox
                                                      checked={selectedValues.includes(String(opt?.id))}
                                                      onChange={() => handleChange(String(opt?.id))}
                                                      sx={{
                                                        color: '#707070',
                                                        '&.Mui-checked': {
                                                          color: '#FDB72E'
                                                        }
                                                      }}
                                                    />
                                                  }
                                                  label={opt?.text}
                                                  sx={{
                                                    '& .Mui-checked + .MuiFormControlLabel-label': {
                                                      color: '#FDB72E'
                                                    }
                                                  }}
                                                />
                                              ))}
                                            </Box>
                                          )
                                        }}
                                      />
                                    </FormGroup>
                                    {errors[question?.questionnaireId] && (
                                      <p className={classes.errorLabel}> {errors[question?.questionnaireId].message}</p>
                                    )}
                                  </>
                                )}
                                {question?.frontendQuestionType === 'radio' && question?.options?.length > 0 && (
                                  <>
                                    <Controller
                                      name={String(question?.questionnaireId)}
                                      control={control}
                                      render={({ field }) => (
                                        <RadioGroup
                                          {...field}
                                          value={field.value || ''}
                                          row
                                          sx={{
                                            display: 'flex',
                                            flexDirection: {
                                              xs: 'column', // vertical on small screens (mobile)
                                              sm: 'row' // horizontal from small screens upwards
                                            },
                                            flexWrap: {
                                              xs: 'nowrap',
                                              sm: 'wrap'
                                            },
                                            gap: {
                                              sx: 1,
                                              sm: 2
                                            } // spacing between items
                                          }}
                                        >
                                          {question?.options?.map((opt) => (
                                            <FormControlLabel
                                              key={opt?.id}
                                              value={String(opt?.id)}
                                              control={
                                                <Radio
                                                  sx={{
                                                    color: '#707070',
                                                    '&.Mui-checked': {
                                                      color: '#FDB72E'
                                                    }
                                                  }}
                                                />
                                              }
                                              label={opt?.text}
                                              sx={{
                                                mr: 2,
                                                '& .Mui-checked + .MuiFormControlLabel-label': {
                                                  color: '#FDB72E'
                                                }
                                              }}
                                            />
                                          ))}
                                        </RadioGroup>
                                      )}
                                    />

                                    {errors[question?.questionnaireId] && (
                                      <p className={classes.errorLabel}> {errors[question?.questionnaireId].message}</p>
                                    )}
                                  </>
                                )}

                                {question?.frontendQuestionType === 'select' && question?.options?.length > 0 && (
                                  <>
                                    <select
                                      className='inputSelect'
                                      defaultValue=''
                                      {...register(String(question?.questionnaireId))}
                                    >
                                      <option value='' disabled>
                                        Select an option
                                      </option>
                                      {question?.options?.map((opt) => (
                                        <option key={opt.id} value={opt.id}>
                                          {opt.text}
                                        </option>
                                      ))}
                                    </select>
                                    {errors[question?.questionnaireId] && (
                                      <p className={classes.errorLabel}> {errors[question?.questionnaireId].message}</p>
                                    )}
                                  </>
                                )}
                              </Grid>
                            </Grid>
                          )
                        })}
                    </Grid>
                  </Grid>

                  <Grid className={classes.vipFormBottom}>
                    <Button
                      variant='contained'
                      className='saveBtn btn btn-primary'
                      type='submit'
                      disabled={!isValid || !isDirty}
                    >
                      Submit
                    </Button>
                  </Grid>
                </Box>
              </Box>
            </Grid>
          </form>
        ) : (
          <Typography>No Questions Found</Typography>
        )}
      </>
    </>
  )
}

export default DynamicVipQuestionForm
