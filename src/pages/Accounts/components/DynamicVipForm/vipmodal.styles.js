import { makeStyles } from '@mui/styles'

import vipBg from '../../../../components/ui-kit/icons/webp/vip-bg.webp'

export default makeStyles(() => ({
  vipCompletionPopUp: {
    '& .vipCompletion': {
      backgroundImage: `url(${vipBg})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
      maxWidth: '480px'
    },
    '& .modal-close': {
      top: '1rem',
      right: '1rem',
      width: '1.375rem',
      height: '1.375rem',
      display: 'flex',
      zIndex: 2,
      position: 'absolute',
      alignItems: 'center',
      borderRadius: '100%',
      justifyContent: 'center',
      '& .MuiButtonBase-root': {
        margin: 0,
        display: 'flex',
        padding: 0,
        position: 'relative',
        alignItems: 'center',
        justifyContent: 'center',
        '& svg': {
          top: '50%',
          left: '50%',
          width: '1.5rem',
          height: '1.5rem',
          position: 'absolute',
          transform: ' translate(-50%, -50%)',
          fill: '#fff',
          fontSize: '2rem'
        }
      }
    },

    '& .MuiTypography-root': {
      color: '#fff',
      fontSize: '1.25rem',
      fontWeight: '600',
      textAlign: 'center'
    },
    '& .thank-you': {
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      '& img': {
        margin: '0 auto',
        maxWidth: '250px'
      }
    },
    '& .vip-img': {
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      padding: '1rem 0',
      '& img': {
        margin: '0 auto',
        maxWidth: '200px'
      }
    }
  }
}))
