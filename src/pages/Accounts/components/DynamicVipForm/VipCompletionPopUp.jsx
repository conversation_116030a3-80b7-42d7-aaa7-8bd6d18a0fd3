import { Box, DialogContent, Grid, IconButton, Typography } from '@mui/material'
import { Close as CloseIcon } from '@mui/icons-material'
import { usePortalStore } from '../../../../store/store'
import VipImage from '../../../../components/ui-kit/icons/webp/vip-character.webp'
import Thankyou from '../../../../components/ui-kit/icons/webp/thank-you.webp'

const VipCompletionPopUp = () => {
  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }
  return (
    <>
      <Grid className='vipCompletion'>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
        <DialogContent>
          <Grid>
            <Grid>
              <Box className='thank-you'>
                <img src={Thankyou} alt='vip-completion-bg' />
              </Box>
              <Typography>VIP Questionnaire Completed Successfully!</Typography>
            </Grid>
            <Box className='vip-img'>
              <img src={VipImage} alt='vip-completion-bg' />
            </Box>
            <Typography>
              Check your account balance for a FREE Sweep Coin Gift from The Money Factory VIP team!
            </Typography>
          </Grid>
        </DialogContent>
      </Grid>
    </>
  )
}

export default VipCompletionPopUp
