import React, { useEffect, useState } from 'react'

import useStyles from './Withdrawl.styles'
import '../../../../src/App.css'
import { Button, FormControlLabel, FormLabel, Grid, IconButton, Typography, OutlinedInput } from '@mui/material'

import DialogContent from '@mui/material/DialogContent'
import CloseIcon from '@mui/icons-material/Close'

import Checkbox from '@mui/material/Checkbox'
import { usePortalStore } from '../../../store/userPortalSlice'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import { redeemSchema } from './schema'
import { PaymentQuery, useGetProfileMutation } from '../../../reactQuery'
import { useUserStore } from '../../../store/useUserSlice'
import PaymentInitiated from '../../../components/PaymentInitiated'
import ErrorPopup from '../../../components/ErrorPopup'
import ProfileSection from '../../Store/PaymentModal/ProfileSection'
import KYCSection from './KYCSections'
import successImage from '../../../components/ui-kit/icons/utils/affi-modal-img.webp'
import TransactionSection from './RedeemRequests'
import useTransaction from '../hooks/useRedeemRequests'
import { customEvent } from '../../../utils/optimoveHelper'
import useSeon from '../../../utils/useSeon'
import TagManager from 'react-gtm-module'

const WithdrawSection = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const [type, setType] = useState('redeem')
  const userDetails = useUserStore((state) => state.userDetails)
  const [isLoading, setIsLoading] = useState(false)
  const [isDisable, setIsDisable] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [redeemMethod, setRedeemMethod] = useState('')
  const [initializeKYC, setInitializeKYC] = useState(false)
  const [isMaxCoin, setIsMaxCoin] = useState(false)
  const [isRedeemPopupOpen, setIsRedeemPopupOpen] = useState(false)
  const sessionId = useSeon()

  const handleClose = () => {
    portalStore.closePortal()
  }

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  useEffect(() => {
    getProfileMutation.mutate()
  }, [])

  const schemaRedeem = redeemSchema(userDetails?.maxRedeemableCoins)
  const {
    handleSubmit,
    register,
    formState: { errors },
    getValues,
    watch,
    setValue,
    setError
  } = useForm({
    resolver: yupResolver(schemaRedeem),
    defaultValues: {
      amount: '',
      actionableEmail: ''
    }
  })
  watch('providerType')
  const { transactionsData } = useTransaction()

  const mutation = PaymentQuery.initPayRedeemMutation({
    onSuccess: (res) => {
      setIsLoading(false)
      setIsDisable(false)
      handleClose()
      getProfileMutation.mutate()
      portalStore.openPortal(() => <PaymentInitiated type={type} />, 'innerModal')
    },
    onError: (err) => {
      if (err?.response?.data?.errors.length > 0) {
        const { errors } = err.response.data
        errors.forEach((error) => {
          if (error?.description) {
            if (error?.errorCode === 3075) {
              if (redeemMethod === 'fiat') {
                portalStore.openPortal(() => <ErrorPopup message={error?.description} />, 'innerModal')
              } else {
                portalStore.openPortal(
                  () => <ErrorPopup message={'You need to purchase a package first, to create a redeem request'} />,
                  'innerModal'
                )
              }
            } else if (error?.errorCode === 3076) {
              portalStore.openPortal(
                () => (
                  <ErrorPopup
                    message={'Please wait to request a redemption until your previous request has been completed.'}
                  />
                ),
                'innerModal'
              )
            } else {
              handleClose()
            }
          }
        })
      }
    }
  })
  const handleIDVerifyClick = () => {
    setInitializeKYC(!initializeKYC)
  }
  const setAmountValue = () => {
    if (userDetails?.userWallet?.scCoin?.wsc > userDetails?.maxRedeemableCoins) {
      setValue('amount', userDetails?.maxRedeemableCoins)
      setIsMaxCoin(true)
    } else {
      setValue('amount', userDetails?.userWallet?.scCoin?.wsc || '0.00')
      setIsMaxCoin(false)
    }
  }

  const handleOnSubmit = (data) => {
    if (parseInt(userDetails?.minRedeemableCoins) > parseInt(data?.amount)) {
      setError(
        'amount',
        { type: 'focus', message: `You can redeem minimum SC ${userDetails?.minRedeemableCoins} coins` },
        { shouldFocus: true }
      )
      return
    }
    // if (parseInt(userDetails?.maxRedeemableCoins) < parseInt(data?.amount)) {
    //   setError('amount', { type: 'focus', message: 'Requested redemption amount is more then Redeemable SC coins' }, { shouldFocus: true })
    //   return
    // }
    if (parseInt(userDetails?.userWallet?.scCoin?.wsc) < parseInt(data?.amount)) {
      setError('amount', { type: 'focus', message: 'Insufficient Redeemable SC coins' }, { shouldFocus: true })
      return
    } else {
      setIsDisable(true)
      setIsLoading(true)
      mutation.mutate({
        ...data,
        paymentType: 'redeem',
        sessionKey: sessionId,
        rtyuioo: sessionId === ' ' ? true : false
      })
      TagManager.dataLayer({
        dataLayer: {
          event: "redeem",
          amount: data?.amount,
          actionable_email: data?.actionableEmail,
          user_id: userDetails?.userId,
          username: userDetails?.username,
          email: userDetails?.email
        }
      })
      var parameters = {
        amount: data?.amount,
        actionable_email: data?.actionableEmail,
        username: userDetails?.username,
        email: userDetails?.email
      }
      if (import.meta.env.VITE_NODE_ENV === 'production') {
        customEvent('redeem', parameters, userDetails?.userId)
      }
    }
  }
  // Function to prevent invalid characters while allowing backspace and delete
  const preventInvalidCharacters = (evt) => {
    const invalidChars = ['e', 'E', '+', '-', ' '] // Disallow these characters
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', 'Enter'] // Allow these keys
    const key = evt.key
    const isDecimal = key === '.'
    const isDigit = /^[0-9]$/.test(key) // Check if it's a digit

    const hasDecimal = evt.target.value.includes('.')

    if (
      invalidChars.includes(key) ||
      (!isDigit && !isDecimal && !allowedKeys.includes(key)) ||
      (isDecimal && hasDecimal)
    ) {
      evt.preventDefault()
    }
  }
  return (
    <>
      {userDetails?.kycStatus === 'K1' || userDetails?.kycStatus === 'K2' || userDetails?.kycStatus === 'K3' ? (
        <DialogContent className={classes.redeemModal}>
          {isLoading && <p className='modal-loader'> Loading ....</p>}
          <Grid display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
            <Typography className='modal-heading'>Redeem</Typography>
            <Grid className='modal-close'>
              <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
                <CloseIcon />
              </IconButton>
            </Grid>
          </Grid>
          {userDetails?.kycStatus === 'K1' && <ProfileSection handleClose={handleIDVerifyClick} redeemCalled={true} />}

          {(userDetails?.kycStatus === 'K2' || userDetails?.kycStatus === 'K3') &&
            (initializeKYC ? (
              portalStore.openPortal(() => <KYCSection handleClose={handleIDVerifyClick} />, 'withdrawModal')
            ) : (
              <>
                <Grid className='reedem-modal-content'>
                  <Grid container spacing={1}>
                    <Grid item xs={12} lg={6}>
                      <Grid className='leftText'>
                        <Typography variant='h4'>Complete Your KYC Verification</Typography>
                        <Typography>
                          You are about to start your Identity Verification. Please prepare a valid ID and have your
                          mobile device handy.{' '}
                        </Typography>
                        <Grid className='btn-wrap'>
                          <Button
                            variant='contained'
                            className='btn btn-primary'
                            disabled={isDisable}
                            onClick={handleIDVerifyClick}
                          >
                            <span>Start Verification</span>
                          </Button>
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={12} lg={6}>
                      <Grid className='rightImage'>
                        <img src={successImage} alt='successImage' />
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </>
            ))}
        </DialogContent>
      ) : (
        <>
          {(userDetails?.kycStatus === 'K4' || userDetails?.kycStatus === 'K5') && showHistory ? (
            <TransactionSection
              showHistory={showHistory}
              setShowHistory={setShowHistory}
              setIsRedeemPopupOpen={setIsRedeemPopupOpen}
            />
          ) : (
            <DialogContent className={classes.withdraModalConent}>
              {isLoading && <p className='modal-loader'> Loading ....</p>}
              <Grid display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
                <Typography variant='heading'>Redeem</Typography>
                <Grid className='modal-close'>
                  <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
                    <CloseIcon />
                  </IconButton>
                </Grid>
              </Grid>
              <form onSubmit={handleSubmit(handleOnSubmit)}>
                <Grid display={'flex'} justifyContent={'space-between'} className='reedem-kyc-content'>
                  <Grid className='leftSection'>
                    <Grid className='inputWrap'>
                      <FormLabel>
                        Redeem Your Amount{' '}
                        <span className='textAmount' style={{ cursor: 'pointer' }}>{` ( Max  Amount : ${
                          userDetails?.userWallet?.scCoin?.wsc || '0.00'
                        } )`}</span>
                      </FormLabel>
                      <br />
                      <Grid className='input-grp'>
                        <OutlinedInput
                          id='outlined-basic'
                          label=''
                          onKeyDown={preventInvalidCharacters}
                          min='1'
                          className='no-spinners'
                          variant='outlined'
                          placeholder='Amount'
                          {...register('amount')}
                          name='amount'
                          type='text'
                        />
                        <Button className='inner-btn' onClick={setAmountValue}>
                          Max
                        </Button>
                      </Grid>
                      {isMaxCoin && (
                        <p style={{ paddingTop: '3px', fontSize: '0.8rem' }}>*This is max redeem limit.*</p>
                      )}
                      {errors?.amount && (
                        <p style={{ paddingTop: '3px' }} className={classes.inputError}>
                          {errors?.amount?.message}
                        </p>
                      )}
                    </Grid>

                    <Grid className='inputWrap'>
                      <FormLabel>Skrill Email Address</FormLabel>
                      <br />
                      <OutlinedInput
                        variant='outlined'
                        id='Email_Address'
                        {...register('actionableEmail')}
                        placeholder='Skrill Email Address'
                        autoComplete='off'
                      />
                      {errors?.actionableEmail && (
                        <p style={{ paddingTop: '3px' }} className={classes.inputError}>
                          {errors?.actionableEmail?.message}
                        </p>
                      )}
                    </Grid>

                    <Grid>
                      <Grid className='themeCheckBoxWrap'>
                        <FormControlLabel
                          control={<Checkbox {...register('checkbox')} />}
                          label='I confirm that information provided is correct.'
                        />
                        {errors?.checkbox && (
                          <p style={{ paddingTop: '3px' }} className={classes.inputError}>
                            {errors?.checkbox?.message}
                          </p>
                        )}
                      </Grid>

                      <Grid className='btn-wrap' style={{ display: 'flex' }}>
                        <Button variant='contained' className='btn btn-primary' disabled={isDisable} type='submit'>
                          <span>Confirm</span>
                        </Button>
                        <Button
                          variant='contained'
                          className='btn btn-secondary'
                          style={{ color: isDisable || (!transactionsData?.rows?.length && 'rgba(0, 0, 0, 0.26)') }}
                          disabled={isDisable || !transactionsData?.rows?.length}
                          type='click'
                          onClick={() => setShowHistory(true)}
                        >
                          <span>Show Requests</span>
                        </Button>
                        {/* {transactionsData?.rows.length > 0 &&
                    <Button variant='contained' style={{ width: '100%' }} disabled={isDisable} type='click' onClick={() => setShowHistory(true)}>
                      <span>Show Requests</span>
                    </Button>
                    } */}
                      </Grid>
                    </Grid>
                  </Grid>

                  {/* <Grid className='redeemImg'>
                    <img src={redeemImage} alt='redeemImage' />
                  </Grid> */}
                </Grid>
              </form>
            </DialogContent>
          )}
        </>
      )}
    </>
  )
}

export default WithdrawSection
