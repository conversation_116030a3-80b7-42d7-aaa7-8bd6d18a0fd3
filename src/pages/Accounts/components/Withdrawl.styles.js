import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  withdrawModal: {
    background: theme.spacing.innerModal,
    backdropFilter: 'blur(20px) drop-shadow(2px 4px 6px black)',
    overflow: 'hidden',
    position: 'relative',
    '& .MuiPaper-root': {
      borderRadius: theme.spacing(0.625),
      backdropFilter: 'blur(14px)',
      backgroundColor: theme.colors.textBlack,
      color: theme.colors.textWhite,
      minWidth: '800px',
      maxWidth: '800px',
      [theme.breakpoints.down('sm')]: {
        minWidth: '95%'
      },
      '& input': {
        borderRadius: theme.spacing(0.625),
        color: theme.colors.textWhite,
        '&.Mui-disabled': {
          WebkitTextFillColor: theme.colors.textWhite
        }
      }
    },
    '& .kyc-content': {
      textAlign: 'center',
      padding: theme.spacing(2),
      '& .MuiTypography-h4': {
        fontWeight: theme.typography.fontWeightBold,
        marginBottom: theme.spacing(0.625)
      },
      '& .btn-wrap': {
        margin: theme.spacing(1, 0, 0)
      }
    },

    '& .leftSection': {
      width: '100%',
      margin: theme.spacing(1, 0)
    },
    '& .MuiFormLabel-root': {
      fontSize: theme.spacing(1),
      color: theme.colors.textWhite,
      marginBottom: theme.spacing(0.313),
      fontWeight: '500'
    },
    '& .MuiInputBase-root': {
      borderRadius: theme.spacing(0.625),
      width: '100%',
      '&:hover': {
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.colors.inputBorder
        }
      },
      '& .MuiOutlinedInput-notchedOutline': {
        borderRadius: theme.spacing(0.625),
        borderColor: `${theme.colors.inputBorder} !important`
      },
      '& input': {
        borderRadius: theme.spacing(0.625),
        '&.Mui-disabled': {
          WebkitTextFillColor: theme.colors.textWhite
        }
      }
    },
    '& .inputWrap': {
      marginBottom: theme.spacing(1),
      marginTop: theme.spacing(0.5),
      '& .textAmount': {
        // '&:hover':{
        color: theme.colors.YellowishOrange
        // cursor: auto,
        // }
      }
    },
    '& .MuiTypography-heading': {
      color: theme.colors.textWhite,
      fontWeight: 'bold',
      textAlign: 'center',
      width: '100%',
      fontSize: theme.spacing(1.5)
    },
    '& .btn-wrap': {
      gap: theme.spacing(0.625),
      display: 'flex',
      margin: theme.spacing(1.5, 0, 1),
      [theme.breakpoints.down('sm')]: {
        justifyContent: 'center'
      }
    },
    '& .reedem-modal-content': {
      '& p': {
        margin: theme.spacing(0.313, 0, 1)
      },
      '& h4': {
        fontWeight: '600'
      },
      '& .MuiGrid-container': {
        alignItems: 'center'
      }
    },
    '& .modal-heading': {
      color: theme.colors.textWhite,
      fontWeight: 'bold',
      fontSize: theme.spacing(1.5)
    },
    '& .leftText': {
      flexGrow: '1',
      [theme.breakpoints.down('sm')]: {
        width: '100%',
        textAlign: 'center'
      },
      '& h4': {
        fontWeight: 'bold',
        fontSize: theme.spacing(1.2)
      }
    },
    // "& .btn-wrap-fiat": {
    //     textAlign: "center",
    //     "& .pay-with-fiat-btn": {
    //         border: 'none',
    //         background: theme.colors.primaryGradient,
    //         color: theme.colors.white,
    //         fontWeight: theme.typography.fontWeightBold,
    //         fontSize: theme.spacing(1),
    //         margin: theme.spacing(0.625, 0),
    //         cursor: 'pointer',
    //         padding: theme.spacing(0.6, 1),
    //         borderRadius: "30px",

    //     }
    // },
    '& .inner-modal-header': {
      borderRadius: theme.spacing(0.625, 0.625, 0, 0),
      background: theme.colors.primaryGradient,
      padding: theme.spacing(0.625, 2),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      '& .MuiTypography-h4': {
        color: theme.colors.white,
        fontWeight: theme.typography.fontWeightExtraBold,
        fontSize: theme.spacing(1.375),
        textTransform: 'capitalize',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1)
        }
      },

      '& .modal-close': {
        '& .MuiButtonBase-root': {
          background: theme.colors.white,
          padding: '0.625rem',
          borderRadius: theme.spacing(0.625),
          height: theme.spacing(1.75),
          width: theme.spacing(1.75),
          [theme.breakpoints.down('sm')]: {
            marginRight: '0'
          },
          '& svg': {
            color: theme.colors.themeText,
            fontSize: theme.spacing(0.875)
          },
          '&:hover': {
            '& svg': {
              color: theme.colors.highlighColor
            }
          }
        }
      }
    }
  },

  withdraModalConent: {
    '& .MuiFormControlLabel-label': {
      fontSize: '0.7rem'
    },
    '& .MuiFormLabel-root': {
      color: theme.colors.textWhite,
      fontSize: theme.spacing(0.875)
    },
    '& .redeemImg': {
      width: '40%',
      textAlign: 'right',
      padding: '1rem',
      '& img': {
        width: '100%'
      },
      [theme.breakpoints.down('sm')]: {
        display: 'none'
      }
    },
    '& .MuiOutlinedInput-root input': {
      borderRadius: `${theme.spacing(0.625)} !important`
    },
    '& .MuiTextField-root, & .MuiOutlinedInput-root': {
      width: '100%',
      '& input': {
        color: '#fff !important',
        width: '-webkit-fill-available',
        border: '1px solid #293937',
        padding: '0px 14px',
        borderRadius: '0.25rem',
        backgroundColor: 'transparent',
        height: '41px',

        '&.Mui-disabled': {
          background: theme.colors.disabledInput,
          textFillColor: theme.colors.textWhite
        }
      }
    },
    '& .MuiSvgIcon-root': {
      color: theme.colors.textWhite
    },
    '& .btn-wrap': {
      gap: theme.spacing(0.625),
      display: 'flex',
      margin: theme.spacing(1.5, 0, 1)
    },
    '& .rightImage': {
      width: '100%',
      textAlign: 'right',
      [theme.breakpoints.down('sm')]: {
        display: 'none'
      },
      '& img': {
        width: '100%',
        paddingLeft: '1rem'
      }
    },
    '& .input-grp': {
      display: 'flex',
      '& .MuiTextField-root, & .MuiOutlinedInput-root': {
        // '& input': {
        //     borderTopRightRadius: '0 !important',
        //     borderBottomRightRadius: '0 !important',
        // }
      },
      '& .inner-btn': {
        minWidth: 'unset',
        color: theme.colors.textBlack,
        padding: theme.spacing(0.375, 1),
        fontSize: '1rem',
        background: '#FDB72E',
        fontWeight: 700,
        borderRadius: theme.spacing(0, 0.625, 0.625, 0),
        textTransform: 'uppercase',
        position: 'absolute',
        right: '0',
        minHeight: theme.spacing(2.6875)
      }
      // "& .MuiOutlinedInput-notchedOutline": {
      //     borderRadius: `${theme.spacing(0.625, 0, 0, 0.625)} !important`
      // }
    },
    '& .themeCheckBoxWrap': {
      '& label': {
        '& .MuiFormControlLabel-label': {
          fontSize: theme.spacing(0.9)
        }
      }
    },

    '& .reedem-kyc-content': {
      '& .btn-wrap': {
        margin: theme.spacing(1, 0),
        gap: theme.spacing(0.625),
        '& button': {
          width: 'auto'
        }
      }
    },
    '& .modal-heading': {
      fontWeight: '600',
      fontSize: theme.spacing(1.5)
    }
  },
  inputError: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    margin: '0 !important',
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600'
  }
}))
