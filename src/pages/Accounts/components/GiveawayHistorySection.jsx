import React, { useState } from 'react'
import useGiveawaySection from '../hooks/useGiveawaySection'
import {
  useTheme,
  Grid,
  Pagination,
  CircularProgress,
  IconButton,
  TableContainer,
  TableRow,
  TableHead,
  TableCell,
  TableBody,
  Table
} from '@mui/material'
import { TransitionLeaderBoard } from '../../Lobby/components/LeaderBoard/TransitionLeaderBoard.style'
import useStyles from '../bets.styles'
import { commonDateTimeFormat } from '../../../utils/helpers'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore' // For the expand icon
import ExpandLessIcon from '@mui/icons-material/ExpandLess'
import DatePicker from '../../../components/DatePicker'
import moment from 'moment'
/* eslint-disable multiline-ternary */

const GiveawayHistorySection = () => {
  const { giveawayData, isGiveawayHistoryLoading, pageNo, setPageNo, limit, setStartDate, setEndDate } =
    useGiveawaySection()

  const classes = useStyles()

  const handleChange = (event, value) => {
    setPageNo(value)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const handleStartDateChange = (date) => {
    setStartDate(date)
  }

  // Function to handle end date change
  const handleEndDateChange = (date) => {
    setEndDate(date)
  }

  const theme = useTheme()

  const ExpandableTable = ({ isGiveawayHistoryLoading, giveawayData }) => {
    const [expandedRows, setExpandedRows] = useState({})

    const handleToggleExpand = (index) => {
      setExpandedRows((prev) => ({
        ...prev,
        [index]: !prev[index]
      }))
    }

    return (
      <TableBody>
        {isGiveawayHistoryLoading ? (
          <TableRow style={{ background: 'transparent' }}>
            <TableCell colSpan={8} style={{ textAlign: 'center' }}>
              <CircularProgress size={24} />
            </TableCell>
          </TableRow>
        ) : giveawayData?.userTickets?.rows?.length ? (
          giveawayData.userTickets.rows?.map((item, index) => {
            const hasMoreThanFourEntries = item?.entries.length > 4
            const visibleEntries = hasMoreThanFourEntries ? item?.entries.slice(0, 4) : item?.entries

            return (
              <React.Fragment key={index}>
                <TableRow sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                  <TableCell style={{ flex: 1 }} scope='row'>
                    <span>{item?.title}</span>
                  </TableCell>
                  <TableCell style={{ flex: 1 }} scope='row'>
                    <span>{moment(new Date(item?.startDate)).format(commonDateTimeFormat.date)}</span>
                  </TableCell>
                  <TableCell style={{ flex: 1 }} scope='row'>
                    <span>{moment(new Date(item?.endDate)).format(commonDateTimeFormat.date)}</span>
                  </TableCell>
                  <TableCell style={{ flex: 1 }} scope='row'>
                    {hasMoreThanFourEntries ? (
                      <>
                        <span>{visibleEntries.join(', ')}</span>
                        <IconButton onClick={() => handleToggleExpand(index)} size='medium' sx={{ color: 'white' }}>
                          {expandedRows[index] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                        {expandedRows[index] && (
                          <span style={{ display: 'block' }}>{item?.entries.slice(4).join(', ')}</span>
                        )}
                      </>
                    ) : (
                      <span>{item?.entries.join(', ')}</span>
                    )}
                  </TableCell>
                </TableRow>
              </React.Fragment>
            )
          })
        ) : (
          <TableRow style={{ background: 'transparent' }}>
            <TableCell colSpan={8} style={{ textAlign: 'center', color: 'red' }}>
              <span>No Records Found</span>
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    )
  }
  return (
    <>
      <TransitionLeaderBoard theme={theme}>
        <Grid>
          <Grid container spacing={1}>
            <Grid item xs={12} sm={4} className={classes.transactionInputs}>
              <Grid className={classes.authInputWrap}>
                <DatePicker onStartDateChange={handleStartDateChange} onEndDateChange={handleEndDateChange} />
              </Grid>
            </Grid>
          </Grid>

          <TableContainer style={{ margin: '20px 0px' }}>
            <Grid className='leaderBoardContainer'>
              <Table className={classes.transactionTable}>
                <TableHead>
                  <TableRow>
                    <TableCell style={{ flex: 1 }}>Title</TableCell>
                    <TableCell style={{ flex: 1 }}>Start Date</TableCell>
                    <TableCell style={{ flex: 1 }}>End Date</TableCell>
                    <TableCell style={{ flex: 1, paddingLeft: '2rem' }}>Entry ID</TableCell>
                  </TableRow>
                </TableHead>

                <ExpandableTable isGiveawayHistoryLoading={isGiveawayHistoryLoading} giveawayData={giveawayData} />
              </Table>
            </Grid>
          </TableContainer>
        </Grid>
      </TransitionLeaderBoard>

      {giveawayData?.userTickets?.count > 0 && !isGiveawayHistoryLoading && (
        <Pagination
          count={Math.ceil(giveawayData?.userTickets?.count / limit)}
          page={pageNo}
          className={classes.tablePagination}
          onChange={handleChange}
          defaultPage={3}
          siblingCount={1}
        />
      )}
    </>
  )
}

export default GiveawayHistorySection
