import moment from 'moment'
import { useState } from 'react'

import { TransactionsQuery } from '../../../reactQuery'

const useTransaction = () => {
  const [startDate, setStartDate] = useState(moment().startOf('day').toDate())
  const [endDate, setEndDate] = useState(moment().endOf('day').toDate())
  const [actionType, setActionType] = useState('pending')
  const [limit, setLimit] = useState(10)
  const [pageNo, setPageNo] = useState(1)
  const [startError, setStartError] = useState(false)
  const [endError, setEndError] = useState(false)

  const {
    data: transactionsData,
    isFetching: isTransactionLoading,
    refetch
  } = TransactionsQuery.getRedeemTransactionsQuery({
    params: {
      // startDate: formatDateYMD(startDate),
      // endDate: formatDateYMD(endDate),
      status: actionType,
      limit,
      page: pageNo
    }
  })

  return {
    transactionsData,
    isTransactionLoading,
    pageNo,
    setPageNo,
    limit,
    setLimit,
    setStartDate,
    setEndDate,
    startDate,
    endDate,
    actionType,
    setActionType,
    startError,
    setStartError,
    endError,
    setEndError,
    refetch
  }
}

export default useTransaction
