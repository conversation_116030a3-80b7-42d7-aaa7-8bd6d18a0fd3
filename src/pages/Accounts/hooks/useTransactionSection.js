
import moment from "moment";
import { useState } from "react";

import { TransactionsQuery } from "../../../reactQuery";

const useTransaction = () => {
    const [startDate, setStartDate] = useState(moment().subtract(6, 'days').startOf('day').toDate());
    const [endDate, setEndDate] = useState(moment().endOf('day').toDate());
    const [actionType, setActionType] = useState('bonus')
    const [limit, setLimit] = useState(10)
    const [pageNo, setPageNo] = useState(1)
    const [startError, setStartError] = useState(false)
    const [endError, setEndError] = useState(false)
    const [selectedStatus, setSelectedStatus] = useState({ value: 'all', label: 'All' })

    const {
        data: transactionsData, isFetching: isTransactionLoading,refetch
    } = TransactionsQuery.getTransactionsQuery({
        params: {
            startDate: (startDate),
            endDate: (endDate),
            // startDate: formatDateToLocalISOString(startDate),
            // endDate: formatDateToLocalISOString(endDate),
            actionType,
            status:actionType === 'redeem' ? selectedStatus?.value : null,
            page:pageNo,
            limit
        }
    })

    return {
        transactionsData, isTransactionLoading , pageNo,setPageNo,limit,setLimit,
        setStartDate, setEndDate, startDate, endDate, actionType, setActionType, startError, setStartError, endError, setEndError,refetch,
        selectedStatus,
        setSelectedStatus
    }
}

export default useTransaction

