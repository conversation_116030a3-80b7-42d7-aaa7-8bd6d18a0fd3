
import { useEffect } from "react";

import { PaymentQuery } from "../../../reactQuery";

export const useGetPayByBankData = () => {

    const {
        mutate: fetchPayByBank, isLoading: isPayByBankDataLoading, data : payByBankData
    } = PaymentQuery.getPayByBankQuery({})

    useEffect(() => {
        fetchPayByBank()
    }, [])

    return {
        payByBankData,
        isPayByBankDataLoading,
        refetch: fetchPayByBank,
    }
}

export const useGetTrustlyData = () => {

    const {
        mutate: fetchTrustly, isLoading: isTrustlyDataLoading, data : trustlyData
    } = PaymentQuery.getTrustlyQuery({})

    useEffect(() => {
        fetchTrustly()
    }, [])

    return {
        trustlyData,
        isTrustlyDataLoading,
        refetch: fetchTrustly,
    }
}


export const useAddPayByBankData = () => {
    const {
        mutateAsync: addBankAccountMutate, data: addBankAccountData, isFetching: isAddBankAccountLoading, refetch: addBankAccountRefetch
    } = PaymentQuery.addBankAccountMutation({})

    return {
        addBankAccountMutate,
        addBankAccountData,
        isAddBankAccountLoading,
        addBankAccountRefetch
    }
}

export const usePaymentErrorData = () => {
    const {
        mutateAsync: addPaymentErrorMutate, data: addPaymentErrorData, isFetching: isAddPaymentErrorLoading, refetch: addPaymentErrorRefetch
    } = PaymentQuery.addPaymentErrorsMutation({})

    return {
        addPaymentErrorMutate,
        addPaymentErrorData,
        isAddPaymentErrorLoading,
        addPaymentErrorRefetch
    }
}


export const usePostalCodeData = () => {
    const {
        mutateAsync: postalCodeMutate } = PaymentQuery.getPostalCodeMutation({})
    return {
        postalCodeMutate
    }
}


