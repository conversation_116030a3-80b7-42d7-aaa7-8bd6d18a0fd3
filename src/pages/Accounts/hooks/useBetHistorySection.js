
import moment from "moment";
import { useState } from "react";

import { TransactionsQuery } from "../../../reactQuery";

const useBetHistory = () => {
    const [startDate, setStartDate] = useState(moment().subtract(6, 'days').startOf('day').toDate());
    const [endDate, setEndDate] = useState(moment().endOf('day').toDate());
    const [coinType, setCoinType] = useState('GC')
    const [startError, setStartError] = useState(false)
    const [endError, setEndError] = useState(false)
    const [limit, setLimit] = useState(10)
    const [pageNo, setPageNo] = useState(1)
    const [message, setMessage] = useState('')
    const [endDateMessage, setEndDateMessage] = useState('')
    const {
        data: betsData,
        isFetching: isBetsLoading
    } = TransactionsQuery.getBetTransactionsQuery({
        params: {
            limit, page: pageNo,
            startDate: (startDate),
            endDate: (endDate),
            // startDate: formatDateToLocalISOString(startDate),
            // endDate: formatDateToLocalISOString(endDate),
            coinType: coinType
        }
    })

    return {
        betsData, isBetsLoading, pageNo, setPageNo, limit, setLimit,
        setStartDate, setEndDate, startDate, endDate, setCoinType, coinType, startError, setStartError, endError, setEndError,
        setMessage, message, endDateMessage, setEndDateMessage
    }
}

export default useBetHistory

