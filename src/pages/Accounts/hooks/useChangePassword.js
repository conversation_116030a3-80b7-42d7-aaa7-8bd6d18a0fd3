import { yupResolver } from '@hookform/resolvers/yup'
import { useCallback, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'

import { useChangePasswordMutation } from '../../../reactQuery'
import { useUserStore } from '../../../store/useUserSlice'
import { userPasswordChange } from '../schema'

const useChangePassword = () => {
  const navigate = useNavigate()
  const [isFormSubmitting, setIsFormSubmitting] = useState(false)
  const {
    register,
    formState: { errors, isValid, isDirty },
    reset,
    handleSubmit,
    watch
  } = useForm({
    resolver: yupResolver(userPasswordChange),
    mode: 'onChange'
  })
  const user = useUserStore((state) => state)

  const changePasswordMutation = useChangePasswordMutation({
    onSuccess: (res) => {
      setIsFormSubmitting(false)

      toast.success(res?.data?.message)
      reset({
        password: '',
        newPassword: '',
        repeatPassword: ''
      })
      // handleClose()
      user.logout()
      navigate('/')
    },
    onError: (err) => {
      setIsFormSubmitting(false)
      if (err?.response?.data?.errors.length > 0) {
        const { errors } = err.response.data
        console.log(errors, 'errors')
        errors.forEach((error) => {
          if (error?.description) {
            // toast.error(error?.description)
          }
        })
      }
    }
  })

  const handleOnSubmitPassword = useCallback(async (data) => {
    setIsFormSubmitting(true)
    changePasswordMutation.mutate({
      oldPassword: window.btoa(data.password),
      newPassword: window.btoa(data.newPassword)
    })
  }, [])

  return {
    handleSubmit,
    register,
    handleOnSubmitPassword,
    errors,
    changePasswordMutation,
    reset,
    watch,
    isValid,
    isFormSubmitting,
    isDirty
  }
}

export default useChangePassword
