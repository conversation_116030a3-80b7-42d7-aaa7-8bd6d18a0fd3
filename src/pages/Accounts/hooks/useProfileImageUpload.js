import { useQueryClient } from '@tanstack/react-query'
import { serialize } from 'object-to-formdata'
import { toast } from 'react-hot-toast'

import { useGetProfileMutation, useProfileImageMutation } from "../../../reactQuery"
import { useUserStore } from "../../../store/useUserSlice"

const useProfileImageUpload = () => {
  const user = useUserStore((state) => state)

  const queryClient = useQueryClient()

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails({ ...res?.data?.data })
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  const { mutate: userProfileImageUpload, isLoading, data } = useProfileImageMutation({
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['uploadImage'] })
      queryClient.setQueriesData({ queryKey: ['uploadImage'], response })
      toast.success('Image uploaded successfully.')
      getProfileMutation.mutate()
    }
  })

  const userProfileImage = (data) => {
    userProfileImageUpload(serialize({ document: data }))
  }

  return {
    userProfileImage,
    data,
    isLoading
  }

}

export default useProfileImageUpload