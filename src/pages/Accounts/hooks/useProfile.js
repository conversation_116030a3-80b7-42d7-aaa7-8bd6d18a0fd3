import { yupResolver } from '@hookform/resolvers/yup'
import { useQueryClient } from '@tanstack/react-query'
import { serialize } from 'object-to-formdata'
import { useCallback, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'

import { PlayerRoutes } from '../../../../src/routes'
import { GeneralQuery, useGetProfileMutation, useProfileImageMutation } from '../../../reactQuery'
import { useStateStore } from '../../../store/store'
import { usePortalStore } from '../../../store/userPortalSlice'
import useStepperStore from '../../../store/useStepperStore'
import { useUserStore } from '../../../store/useUserSlice'
import { personalInfoSchema } from '../schema'

const usePersonalInfo = (stepperCalledFor = '') => {
  const portalStore = usePortalStore((state) => state)
  const userDetails = useUserStore((state) => state.userDetails)
  const user = useUserStore((state) => state)
  const stateList = useStateStore((state) => state.stateList)
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' }
  ]
  let stateId = ''
  if (userDetails?.state) {
    stateId = stateList?.find((x) => x?.state_id === Number(userDetails.state))?.stateCode
  }

  const [selectedGender, setSelectedGender] = useState('')
  const [selectedState, setSelectedState] = useState(stateId)
  const [selectedDate, setSelectedDate] = useState(userDetails?.dateOfBirth ? userDetails?.dateOfBirth : '')
  const initialState = {
    firstName: userDetails?.firstName || '',
    lastName: userDetails?.lastName || '',
    middleName: userDetails?.middleName === 'undefined' ? '' : userDetails?.middleName || '',
    email: userDetails?.email,
    dateOfBirth: userDetails?.dateOfBirth || '',
    gender: userDetails?.gender || '',
    addressLine_1: userDetails?.addressLine_1 || '',
    addressLine_2: userDetails?.addressLine_2 === 'undefined' ? '' : userDetails?.addressLine_2,
    city: userDetails?.city || '',
    state: stateId || selectedState,
    country: +199,
    zipCode: userDetails?.zipCode || ''
  }

  const [isReset, setIsReset] = useState(false)
  const { handleNext } = useStepperStore((state) => state)

  useEffect(() => {
    if (userDetails) {
      setSelectedState(stateId)
      setSelectedGender(userDetails?.gender)
      reset({
        ...initialState,
        firstName: userDetails?.firstName || '',
        lastName: userDetails?.lastName || '',
        middleName: userDetails?.middleName === 'undefined' ? '' : userDetails?.middleName || '',
        dateOfBirth: userDetails?.dateOfBirth || '',
        gender: userDetails?.gender || selectedGender,
        addressLine_1: userDetails?.addressLine_1 || '',
        addressLine_2: userDetails?.addressLine_2 === 'undefined' ? '' : userDetails?.addressLine_2,
        city: userDetails?.city || '',
        state: stateId || selectedState,
        zipCode: userDetails?.zipCode || ''
      })
    }
  }, [userDetails])

  const {
    handleSubmit,
    control,
    register,
    formState: { errors, isSubmitted },
    getValues,
    setValue,
    reset,
    watch,
    clearErrors
  } = useForm({
    resolver: yupResolver(personalInfoSchema),
    mode: 'onChange',
    defaultValues: initialState
  })

  const handelReset = () => {
    const address1Field = document.querySelector('#addressLine_1')
    let address1 = ''
    if (userDetails?.addressLine_1 && address1Field) {
      address1Field.value = userDetails?.addressLine_1
      address1 = userDetails?.addressLine_1
    }
    setValue('addressLine_1', '', { shouldValidate: true })

    reset({
      ...initialState,
      firstName: userDetails?.firstName || '',
      lastName: userDetails?.lastName || '',
      middleName: userDetails?.middleName === 'undefined' ? '' : userDetails?.middleName || '',
      dateOfBirth: userDetails?.dateOfBirth || '',
      gender: userDetails?.gender || null,
      addressLine_1: address1,
      addressLine_2: userDetails?.addressLine_2 === 'undefined' ? '' : userDetails?.addressLine_2,
      city: userDetails?.city || '',
      state: stateId || null,
      zipCode: userDetails?.zipCode || ''
    })
  }

  const [userField, setUserField] = useState(getValues())

  const onDobChange = (value) => {
    setSelectedDate(value)
    setValue('dateOfBirth', value)
    clearErrors('dateOfBirth')
  }

  const onStateChangeHandler = (event) => {
    setSelectedState(event.target.value)
  }

  const onGenderChangeHandler = (event) => {
    setSelectedGender(event.target.value)
  }

  const handleClose = () => {
    portalStore.closePortal()
  }
  const successToggler = (data) => {
    setUserField({ ...userDetails, ...data })
    user.setUserDetails({ ...userDetails, ...data })
    toast.success('Profile updated successfully.')
    handleNext()
    if (stepperCalledFor === '') navigate(PlayerRoutes.Lobby)
    // handleClose();
  }
  const errorToggler = () => {
  }

  const { mutate: submitPersonalMutation, isLoading: isSubmitFormLoading } = GeneralQuery.usePersonalFormMutation({
    successToggler,
    errorToggler
  })

  const handleOnFormSubmit = useCallback(async (data, date) => {
    let stateId = 0
    if (data?.state) {
      stateId = stateList?.find((x) => x?.stateCode === data.state)?.state_id
    }
    // let newDate = (profileDataFormate(data?.dateOfBirth) === '01-01-1970') ? '' : profileDataFormate(data?.dateOfBirth)
    const tempData = {
      firstName: data?.firstName,
      lastName: data?.lastName,
      middleName: data?.middleName === 'undefined' ? '' : data?.middleName,
      dateOfBirth: date,
      gender: data?.gender || '',
      addressLine_1: data?.addressLine_1,
      addressLine_2: data?.addressLine_2 === 'undefined' ? '' : data?.addressLine_2,
      city: data?.city,
      state: stateId,
      country: +199,
      zipCode: data?.zipCode
    }
    submitPersonalMutation(tempData)
  }, [])

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails({ ...userDetails, ...res?.data?.data })
      toast.success('Image uploaded successfully.')
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const {
    mutate: userProfileImageUpload
  } = useProfileImageMutation({
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['uploadImage'] })
      queryClient.setQueriesData({ queryKey: ['uploadImage'], response })

      getProfileMutation.mutate()
    }
  })

  const userProfileImage = (data) => {
    userProfileImageUpload(serialize({ document: data }))
  }

  return {
    handleOnFormSubmit,
    handleSubmit,
    register,
    selectedState,
    isSubmitFormLoading,
    control,
    errors,
    genderOptions,
    stateList,
    setValue,
    onStateChangeHandler,
    initialState,
    getValues,
    userDetails,
    isSubmitted,
    selectedGender,
    handleClose,
    reset,
    handelReset,
    onGenderChangeHandler,
    selectedDate,
    onDobChange,
    userField,
    setSelectedState,
    userProfileImage,
    setSelectedDate,
    isReset,
    setIsReset,
    watch
  }
}

export default usePersonalInfo
