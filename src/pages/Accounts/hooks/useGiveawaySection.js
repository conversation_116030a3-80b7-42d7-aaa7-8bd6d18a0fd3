import moment from 'moment'
import { useState } from 'react'

import giveAwayQuery from '../../../reactQuery/GiveawayQuery'

const useGiveawaySection = () => {
  const [startDate, setStartDate] = useState(moment().subtract(6, 'days').startOf('day').toDate())
  const [endDate, setEndDate] = useState(moment().endOf('day').toDate())
  const [limit, setLimit] = useState(10)
  const [pageNo, setPageNo] = useState(1)
  const [startError, setStartError] = useState(false)
  const [endError, setEndError] = useState(false)
  const selectedStatus = { value: 'all', label: 'All' }
  const [actionType, setActionType] = useState('bonus')
  const {
    data: giveawayData,
    isFetching: isGiveawayHistoryLoading,
    refetch
  } = giveAwayQuery.getGiveawayHistoryQuery({
    params: {
      startDate: (startDate),
      endDate: (endDate),
      status: actionType === 'redeem' ? selectedStatus?.value : null,
      page: pageNo,
      limit

    }
  })
  return {
    giveawayData,
    isGiveawayHistoryLoading,
    pageNo,
    setPageNo,
    limit,
    setLimit,
    setStartDate,
    setEndDate,
    startDate,
    endDate,
    actionType,
    setActionType,
    startError,
    setStartError,
    endError,
    setEndError,
    refetch
  }
}

export default useGiveawaySection
