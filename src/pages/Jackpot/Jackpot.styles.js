import { makeStyles } from '@mui/styles'

import modalBg from '../../components/ui-kit/icons/png/background-jackpot.png'
import mainborder from '../../components/ui-kit/icons/png/jackpot-bg.png'
import { ButtonPrimary, LobbyRight } from '../../MainPage.styles'

const DIGIT_HEIGHT = 32
const MOBILE_DIGIT_HEIGHT = 22

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    '& .jackpot-page-wrap': {
      maxWidth: theme.spacing(71),
      backgroundImage: `url(${modalBg})`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column',
      margin: '0 auto',
      background: theme.colors.settingsBg,
      borderRadius: theme.spacing(1.25),
      padding: theme.spacing(2.25, 2.8125),

      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(2.25, 0)
      },
      '& .MuiTypography-root, & button': {},
      '& h5': {
        color: '#B9B7B7',
        fontSize: '1.25rem',
        fontWeight: '600',
        maxWidth: '320px',
        margin: '0 auto',
        marginTop: '1rem',
        textAlign: 'center',
        [theme.breakpoints.down('md')]: {
          fontSize: '0.675rem',
          marginTop: '0.25rem',
          maxWidth: '200px'
        }
      },
      '& .MuiAccordion-root': {
        marginTop: '2rem !important',
        '& .MuiAccordionSummary-root': {
          background: '#313131',
          borderRadius: '0.5rem',
          padding: '0 1rem'
        }
      }
    },

    inputError: {
      color: theme.colors.error,
      fontSize: `${theme.spacing(0.8)}!important`,
      margin: '0 !important',
      lineHeight: 'normal !important',
      minHeight: '16px',
      fontWeight: '600'
    },
    '& .jackpot-badge': {
      // position: 'fixed',
      background: `url(${mainborder})`,
      backgroundSize: '100% 100%',
      maxWidth: '270px',
      width: '100%',
      // top: '100px',
      zIndex: '3',
      height: '100%',
      maxHeight: '75px',
      borderRadius: '5rem',
      display: 'flex',
      justifyContent: 'center',
      gap: '0.5rem',
      alignItems: 'center',
      padding: '1rem 2rem 0.5rem 2rem',
      // left: '50%',
      // transform: 'translate(-50% , 0%)',
      [theme.breakpoints.down('md')]: {
        padding: '0.875rem 1rem 1rem 1rem',
        maxWidth: '240px',
        maxHeight: '60px'
        // bottom: '60px',
        // top: 'auto'
      },

      '& .cash-icon': {
        width: '2rem',
        [theme.breakpoints.down('md')]: {
          width: '1.75rem'
        }
      },
      '& .MuiTypography-root': {
        fontSize: '2rem',
        fontWeight: '700',
        background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        color: 'transparent',
        whiteSpace: 'nowrap',
        WebkitTextStroke: '0.5px #BA5C25',
        textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
        lineWeight: '1.2',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.5rem',
          paddingTop: '4px'
        }
      },
      '& .tmf-jackpot': {
        position: 'absolute',
        top: '-13px',
        left: '50%',
        transform: 'translate(-52% , 0%) scale(1)',
        width: '155px',
        [theme.breakpoints.down('md')]: {
          width: '130px',
          top: '-10px'
        }
      }
    },
    '& .jackpot-mode': {
      display: 'flex',
      justifyContent: 'center',
      marginTop: '1rem',
      '& .jackpot-box': {
        maxWidth: '300px',
        display: 'flex',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: '2rem',
        padding: '0.15rem 1rem',
        '&.jackpot-off': {
          border: '3px solid #7c0003',
          '& .MuiSwitch-thumb': {
            border: '1px solid #7c0003',
            background: '#7c0003'
          },
          '& .MuiSwitch-track': {
            border: '2px solid #7c0003',
            background: 'transparent'
          }
        },
        '&.jackpot-on': {
          border: '3px solid rgb(0, 255, 21)',
          '& .MuiSwitch-thumb': {
            border: '1px solid rgb(0, 255, 21)',
            background: 'rgb(0, 255, 21)'
          },
          '& .MuiSwitch-track': {
            border: '1px solid rgb(0, 255, 21)',
            background: 'transparent'
          }
        },
        '& .MuiTypography-root': {
          fontSize: '1rem',
          fontWeight: '600',
          textTransform: 'uppercase'
        }
      }
    },
    '& h5': {
      color: '#B9B7B7',
      fontSize: '1.25rem',
      fontWeight: '600',
      maxWidth: '320px',
      margin: '0 auto',
      marginTop: '1rem',
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        fontSize: '1rem'
      }
    },
    '& .steps-section': {
      marginBottom: '1rem',
      maxWidth: '60rem',
      width: '100%',
      '& h4': {
        fontSize: '2rem',
        textTransform: 'capitalize',
        fontWeight: '600',
        marginBottom: '2rem',
        textAlign: 'center',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.25rem',
          marginBottom: '0.25rem'
        }
      },
      '& .steps-box': {
        padding: '1px',
        background: 'linear-gradient(180deg, #6E6E6E 0%, #232323 100%)',
        '& .box-content': {
          display: 'flex',
          justifyContent: 'space-between',
          background: 'linear-gradient(180deg, #0E0E0E 0%, #2D2D2D 100%)',
          padding: '1rem 0rem',
          [theme.breakpoints.down('md')]: {
            padding: '0.5rem'
          },
          '& .main-box': {
            display: 'flex',
            alignItems: 'center',
            gap: '1rem',
            position: 'relative',
            width: '100%',
            borderRight: '1px solid #6A6969',
            padding: '1rem',
            justifyContent: 'space-between',
            flexDirection: 'column',
            [theme.breakpoints.down('md')]: {
              padding: '0.5rem',
              gap: '0.5rem'
            },
            '& img': {
              maxWidth: '3.75rem',
              width: '100%',
              [theme.breakpoints.down('md')]: {
                maxWidth: '2.5rem'
              }
            },
            '& p': {
              fontSize: '1.25rem',
              fontWeight: '500',
              textAlign: 'center',
              [theme.breakpoints.down('md')]: {
                fontSize: '0.75rem'
              }
            },
            '&:last-child': {
              borderRight: 'none'
            },
            '& span': {
              color: '#767676',
              textAlign: 'center',
              fontSize: '0.875rem',
              fontWeight: '600',
              [theme.breakpoints.down('md')]: {
                fontSize: '0.675rem'
              }
            },
            '& .box-detail': {
              textAlign: 'center'
            }
          },
          '&::after': {
            content: "''",
            background: 'linear-gradient(90.04deg, #6b6b6b 10.53%, #ffffff 51.77%, #686868 89.98%)',
            position: 'absolute',
            bottom: '-10px',
            left: '0',
            zIndex: '-1',
            height: '20px',
            width: '100%',
            borderRadius: '100%'
          }
        }
      }
    },
    '& h4': {
      fontSize: '2rem',
      textTransform: 'capitalize',
      fontWeight: '600',
      margin: '2rem 0',
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        fontSize: '1.25rem',
        margin: '1rem 0'
      }
    },
    '& .winners-table-container': {
      backgroundColor: '#1C1C1C !important',
      borderRadius: '12px',
      maxWidth: '60rem',
      overflow: 'auto',
      width: '100%',
      border: '2px solid #293937'
    },
    '& .winners-table': {
      borderCollapse: 'separate',
      minWidth: '59.5rem',
      borderSpacing: 0,
      [theme.breakpoints.down('md')]: {
        minWidth: '30.5rem'
      }
    },
    '& .winners-table-header': {
      backgroundColor: '#1f1f1f',
      '& th:nth-of-type(1)': {
        textAlign: 'left'
      },
      '& th:nth-of-type(2), & th:nth-of-type(3)': {
        textAlign: 'center'
      },
      '& th:nth-of-type(4)': {
        textAlign: 'right'
      }
    },
    '& .header-cell': {
      color: '#ffb800',
      fontWeight: 'bold',
      padding: '16px',
      fontSize: '14px'
    },
    '& .body-cell': {
      color: '#ffffff',
      fontSize: '14px',
      fontWeight: '500',
      padding: '16px',
      borderBottom: '1px solid #2a2a2a',
      [theme.breakpoints.down('md')]: {
        padding: '6px'
      }
    },
    '& .winners-table-row': {
      '& td:nth-of-type(1)': {
        textAlign: 'left'
      },
      '& td:nth-of-type(2), & td:nth-of-type(3)': {
        textAlign: 'center'
      },
      '& td:nth-of-type(4)': {
        textAlign: 'right'
      },
      '&:nth-of-type(even)': {
        backgroundColor: '#262626'
      },
      '&:hover': {
        backgroundColor: '#333333'
      }
    },
    '& .jackpot-games-section': {
      '& h4': {
        fontSize: '2rem',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.25rem'
        }
      },
      '& .jackpot-games-wrap': {
        display: 'grid',
        gridTemplateColumns: 'repeat(7, 1fr)',
        rowGap: '2rem',
        columnGap: '1rem',
        '& .casino-card': {
          position: 'relative',
          transition: 'all 200ms ease-in-out',
          lineHeight: '0',
          // maxWidth:'119px',
          // maxHeight: '168px',
          '& .casinoGame-img': {
            width: '100%',
            aspectRatio: '2/3',
            borderRadius: '8px',
            height: '100%',
            '&:hover': {
              backgroundColor: theme.colors.textWhite,
              cursor: 'pointer'
            }
          },
          '& .casino-img': {
            width: '100%',
            aspectRatio: '1'
          },
          '&:hover': {
            transform: 'translateY(-0.25rem)',
            '& .casino-overlay': {
              display: 'flex',
              opacity: '1',
              transition: 'all 300ms ease-in-out'
            }
          },
          '& .casino-overlay': {
            position: 'absolute',
            opacity: '0',
            display: 'flex',
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            margin: '0 auto',
            inset: '0',
            flexDirection: 'column',
            background: 'linear-gradient(180deg, rgba(255,84,37,0.9) 0%, rgba(251,162,83,0.9) 100%)',
            cursor: 'pointer',
            transition: 'all 200ms ease-in-out',
            borderRadius: '8px',
            color: theme.colors.textWhite,
            '& a': {
              color: theme.colors.textWhite,
              textDecoration: 'none'
            },
            '& h6': {
              color: theme.colors.textWhite
              // wordBreak:'break-all'
            }
          },
          '& .tournamentLogo': {
            position: 'absolute',
            left: '2px',
            top: '5px',
            width: '30px',
            height: '30px'
          },
          '& .prgamatic-jackpot-amount-wrapper': {
            position: 'absolute',
            top: '12px',
            left: '47%',
            display: 'flex',
            justifyContent: 'center',
            gap: '4px',
            alignItems: 'center',
            background: '#000000B2',
            borderRadius: '17px',
            whiteSpace: 'nowrap',
            transform: 'translate(-50%, 0)',
            padding: '1px 5px'
          }
        },
        [theme.breakpoints.down('md')]: {
          gridTemplateColumns: 'repeat(5, 1fr)'
        },
        [theme.breakpoints.down('sm')]: {
          gridTemplateColumns: 'repeat(3, 1fr)'
        }
      }
    }
  },

  playImg: {
    width: '60px',
    margin: '20px 0',
    height: 'auto',
    [theme.breakpoints.down('xl')]: {
      width: '40px',
      margin: '5px 0'
    }
  },

  digitContainer: {
    overflow: 'hidden',
    height: `${DIGIT_HEIGHT}px`,
    width: '14px',
    display: 'inline-block',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      height: `${MOBILE_DIGIT_HEIGHT}px`,
      width: '8px'
    }
  },

  digitStrip: {
    transition: 'transform 0.3s ease-out',
    willChange: 'transform'
  },

  digit: {
    height: `${DIGIT_HEIGHT}px`,
    lineHeight: `${DIGIT_HEIGHT}px`,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: 700,
    fontSize: '28px',
    background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    color: 'transparent',
    WebkitTextStroke: '0.5px #BA5C25',
    textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
    cursor: 'pointer',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    [theme.breakpoints.down('md')]: {
      height: `${MOBILE_DIGIT_HEIGHT}px`,
      lineHeight: `${MOBILE_DIGIT_HEIGHT}px`,
      fontSize: '20px',
      paddingTop: '0px'
    }
  },

  digitsWrapper: {
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    gap: '2px'
  },

  scText: {
    fontWeight: 700,
    fontSize: '28px',
    background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    color: 'transparent',
    WebkitTextStroke: '0.5px #BA5C25',
    textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
    [theme.breakpoints.down('md')]: {
      fontSize: '20px'
      // paddingTop: '2px'
    }
  },
  loadMore: {
    width: '100%',
    textAlign: 'center',
    margin: theme.spacing(2.5, 0, 1, 0),
    '& button': {
      ...ButtonPrimary(theme),
      '&:hover': {
        ...ButtonPrimary(theme)
      }
    }
  }
}))
