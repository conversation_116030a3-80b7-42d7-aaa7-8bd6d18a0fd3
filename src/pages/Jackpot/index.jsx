import React, { useEffect, useRef, useState } from 'react'
import useStyles from './Jackpot.styles'
import usdCash from '../../components/ui-kit/icons/utils/usd-cash.webp'
import tmfjackpot from '../../components/ui-kit/icons/png/tmf-jackpot.png'
import CustomSwitch from '../../components/CustomSwitch'
import enableJackpot from '../../components/ui-kit/icons/webp/enable-jackpot.webp'
import spinWheel from '../../components/ui-kit/icons/webp/lucky-wheel.webp'
import entryFee from '../../components/ui-kit/icons/webp/entry-fee.webp'
import winBig from '../../components/ui-kit/icons/webp/win-big.webp'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
  Typography,
  useMediaQuery,
  useTheme
} from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import jackpotQuery from '../../reactQuery/jackpotQuery'
import { useJackpotStore } from '../../store/useJackpotStore'
import { FooterQuerys } from '../../reactQuery'
import parse from 'html-react-parser'
import { useCoinStore, usePortalStore, useUserStore } from '../../store/store'
import ConfirmationPopup from './components/ConfirmationPopup'
import { getLoginToken } from '../../utils/storageUtils'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { PlayerRoutes } from '../../routes'
import GamesList from './components/GamesList'
import { formatToReadableDateTime } from '../../utils/helpers'
import useGetDeviceType from '../../utils/useGetDeviceType'

const DIGIT_HEIGHT = 32
const MOBILE_DIGIT_HEIGHT = 22

const RollingDigit = ({ digit, isMobile, animate }) => {
  const classes = useStyles()
  const height = isMobile ? MOBILE_DIGIT_HEIGHT : DIGIT_HEIGHT

  return (
    <div className={classes.digitContainer} style={{ height }}>
      <div
        className={classes.digitStrip}
        style={{
          transform: `translateY(-${digit * height}px)`,
          transition: animate ? 'transform 0.6s ease-out' : 'none'
        }}
      >
        {Array.from({ length: 10 }, (_, i) => (
          <div key={i} className={classes.digit} style={{ height }}>
            {i}
          </div>
        ))}
      </div>
    </div>
  )
}

const JackpotPage = () => {
  const classes = useStyles()
  const { isMobile } = useGetDeviceType()
  const navigate = useNavigate()
  const portalStore = usePortalStore()
  const auth = useUserStore((state) => state)
  const coinType = useCoinStore((state) => {
    return state.coinType
  })

  // JACKPOT - STORE
  const { setJackpotOn, setJackpotData, setJackpotMultiplier, jackpotOn, jackpotMultiplier } = useJackpotStore()

  const jackpotPoolAmount = useJackpotStore((state) => Number(state.jackpotData.jackpotPoolAmount) || 0)
  const isFirstRender = useRef(true)

  // GET JACKPOT DATA
  const { data: jackpotData } = jackpotQuery.getJackpotDataQuery({
    params: {
      jackpotPage: true
    },
    successToggler: (data) => {
      setJackpotData('jackpotPoolAmount', Number(data?.jackpotPoolAmount))
      setJackpotData('entryAmount', data?.entryAmount)
      setJackpotData('recentJackpotWinners', data?.recentJackpotWinners)
    }
  })

  const [count, setCount] = useState(Number(jackpotData?.jackpotPoolAmount) || 0)

  // OPT JACKPOT
  const toggleJackpotSuccess = (data, variables) => {
    setJackpotOn(variables?.status)
    setJackpotMultiplier(variables?.multiplier)
  }

  const toggleJackpotError = (err) => {
    console.log('$$$ToggleJACKPOT_ERR', err)
  }

  const toggleJackpot = jackpotQuery.useJackpotOptInMutation({
    onSuccess: toggleJackpotSuccess,
    onError: toggleJackpotError
  })

  const handleToggle = () => {
    if (jackpotOn === true) {
      portalStore.openPortal(() => <ConfirmationPopup />, 'jackpotPopup')
    } else {
      toggleJackpot.mutate({
        status: !jackpotOn,
        multiplier: jackpotMultiplier
      })
    }
  }

  // CMS - JACKPOT TERMS SECTION
  const { data: cmsContent } = FooterQuerys.getCmsContentQuery({ pageSlug: 'jackpot' })

  const htmlRenderer = (htmlContent) => {
    return parse(htmlContent)
  }

  const handlePlayNow = (masterCasinoGameId, name, gameType) => {
    if (!!getLoginToken() || auth.isAuthenticate) {
      if (auth.userDetails?.username) {
        if (coinType === 'SC' && auth?.userDetails?.userWallet?.totalScCoin > 0) {
          navigate(`/game-play/${masterCasinoGameId}`, { state: { name, gameType } })
        } else if (coinType === 'GC' && auth?.userDetails?.userWallet?.gcCoin > 0) {
          navigate(`/game-play/${masterCasinoGameId}`, { state: { name, gameType } })
        } else {
          toast.error('Please make a purchase!')
          navigate(PlayerRoutes.Store)
        }
      } else {
        navigate('/')
      }
    } else {
      navigate('/')
    }
  }

  // JACKPOT ANIMATION
  const duration = 2000 // in ms

  useEffect(() => {
    const newAmount = Number(jackpotPoolAmount)
    if (newAmount == null || newAmount === count) return

    if (isFirstRender.current) {
      setCount(newAmount)
      isFirstRender.current = false
      return
    }

    const startAmount = typeof count === 'number' && !isNaN(count) ? count : 0
    const difference = newAmount - startAmount
    const startTime = performance.now()

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      const eased = easeOutQuad(progress)
      const currentCount = +(startAmount + difference * eased)

      setCount(currentCount)

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        setCount(newAmount)
      }
    }

    requestAnimationFrame(animate)

    function easeOutQuad(t) {
      return t * (2 - t)
    }
  }, [jackpotPoolAmount])

  const countStr = Number(count).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })

  return (
    <Grid className={classes.lobbyRight}>
      <Box className='jackpot-page-wrap'>
        {jackpotData?.jackpotPoolAmount > 0 ? (
          <>
            <div className='jackpot-badge'>
              <img src={usdCash} className='cash-icon' alt='cash' />
              <Typography component='div' className={classes.digitsWrapper}>
                {countStr.split('').map((char, idx) =>
                  /\d/.test(char) ? (
                    <RollingDigit
                      key={`digit-${idx}`}
                      digit={parseInt(char, 10)}
                      isMobile={isMobile}
                      animate={!isFirstRender.current}
                    />
                  ) : (
                    <span key={`char-${idx}`} className={classes.digit}>
                      {char}
                    </span>
                  )
                )}
                <span className={classes.scText}>SC</span>
              </Typography>
              <img src={tmfjackpot} className='tmf-jackpot' alt='jackpot crown' />
            </div>

            <div className='jackpot-mode'>
              <div className={`jackpot-box ${jackpotOn ? 'jackpot-on' : 'jackpot-off'}`}>
                <Typography>Jackpot MODE</Typography>
                <CustomSwitch checked={jackpotOn} onChange={handleToggle} />
              </div>
            </div>
            <Typography variant='h5'>
              {parseFloat((jackpotData?.entryAmount * jackpotMultiplier).toFixed(2))} SC per spin will be deducted when Jackpot Mode is enabled.
            </Typography>
            <Box className='steps-section'>
              <Typography variant='h4'>How To Enter The TMF jackpot</Typography>
              <Box className='steps-box'>
                <Box className='box-content'>
                  <Box className='main-box'>
                    <img src={enableJackpot} />
                    <Typography variant='body1'>Enable Jackpot Mode</Typography>
                  </Box>
                  <Box className='main-box'>
                    <img src={spinWheel} />
                    <Typography variant='body1'>Spin Any Game</Typography>
                  </Box>
                  <Box className='main-box'>
                    <img src={entryFee} />
                    <div className='box-detail'>
                      <Typography variant='body1'>Entry Fee</Typography>
                      <Typography variant='span'>{parseFloat((jackpotData?.entryAmount * jackpotMultiplier).toFixed(2))} SC per spin</Typography>
                    </div>
                  </Box>
                  <Box className='main-box'>
                    <img src={winBig} />
                    <Typography variant='body1'>Win Big</Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </>
        ) : (
          <Typography variant='h4'>OOPS!, No jackpot available at this moment</Typography>
        )}
        {jackpotData?.recentJackpotWinners && jackpotData?.recentJackpotWinners?.length > 0 && (
          <>
            <Typography variant='h4'>Recent TMF Jackpot Winners</Typography>

            <TableContainer component={Paper} className='winners-table-container'>
              <Table className='winners-table'>
                <TableHead>
                  <TableRow className='winners-table-header'>
                    <TableCell className='header-cell'>Winner Username</TableCell>
                    <TableCell className='header-cell'>Prize Amount</TableCell>
                    <TableCell className='header-cell'>Game</TableCell>
                    <TableCell className='header-cell'>Date & Time</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {jackpotData?.recentJackpotWinners?.map((row, index) => (
                    <TableRow key={index} className='winners-table-row'>
                      <TableCell className='body-cell'>{row.username}</TableCell>
                      <TableCell className='body-cell' style={{ textAlign: 'center' }}>
                        {row.poolAmount}
                      </TableCell>
                      <TableCell className='body-cell'>{row.gameName}</TableCell>
                      <TableCell className='body-cell'>{formatToReadableDateTime(row.winningTime)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        <Box className='jackpot-terms'>
          <Accordion style={{ color: 'white', marginTop: '15px' }}>
            <AccordionSummary
              expandIcon={<ArrowDropDownIcon style={{ color: 'white' }} />}
              aria-controls='panel2-content'
              id='jackpot-terms-accordion'
            >
              <Typography variant='h4' style={{ margin: 0 }}>
                TMF Jackpot Terms
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography>{htmlRenderer(cmsContent?.content?.EN || '')}</Typography>
            </AccordionDetails>
          </Accordion>
        </Box>

        <Box className='jackpot-games-section'>
          <Typography variant='h4'>Opt-in, Play any Game, Win Big.</Typography>
          <Box>
            <GamesList loading={false} handlePlayNow={handlePlayNow} />
          </Box>
        </Box>
      </Box>
    </Grid>
  )
}

export default JackpotPage
