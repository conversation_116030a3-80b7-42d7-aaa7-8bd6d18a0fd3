import React from 'react'
import { <PERSON>, But<PERSON>, Typo<PERSON>, Modal } from '@mui/material'
import useStyles from './DisableJackpotModal.styles'
import jackpotDisable from '../../../components/ui-kit/icons/webp/disable-jackpot.png'
import jackpotQuery from '../../../reactQuery/jackpotQuery'
import { useJackpotStore } from '../../../store/useJackpotStore'
import { usePortalStore } from '../../../store/store'

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  maxWidth: 380,
  width: '100%',
  boxShadow: 24,
  p: 2
}

const ConfirmationPopup = () => {
  const portalStore = usePortalStore((state) => state)
  // Always open the modal
  const open = true

  // Dummy close handler (no-op)
  const handleClose = () => {
    portalStore.closePortal()
  }

  const classes = useStyles()
  // SETTERS - JACKPOT
  const { setJackpotOn, jackpotMultiplier } = useJackpotStore()

  // OPT JACKPOT
  const toggleJackpotSuccess = (data) => {
    setJackpotOn(false)
    portalStore.closePortal()
  }

  const toggleJackpotError = (err) => {
    console.log('$$$ToggleJACKPOT_ERR', err)
  }

  const toggleJackpot = jackpotQuery.useJackpotOptInMutation({
    onSuccess: toggleJackpotSuccess,
    onError: toggleJackpotError
  })

  const handleYesBtn = () => {
    toggleJackpot.mutate({
      status: false,
      multiplier: jackpotMultiplier
    })
  }
  return (
    <Modal
      open={open}
      onClose={handleClose}
      className={classes.jackpotDisable}
      aria-labelledby='confirmation-modal-title'
      aria-describedby='confirmation-modal-description'
    >
      <Box sx={style} className='jackpot-content'>
        <Typography variant='h4' id='confirmation-modal-description'>
          Disable Jackpot
        </Typography>
        <img src={jackpotDisable} />
        <Typography variant='body1'>
          Are you sure you want to disable the Jackpot feature? You won't be able to access special rewards or bonuses.
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-evenly', alignItems: 'center', mt: 1 }}>
          <Button variant='contained' className='btn btn-primary' onClick={() => handleYesBtn()}>
            Disable
          </Button>
          <Button variant='contained' className='btn btn-primary' onClick={() => portalStore.closePortal()}>
            Not Now
          </Button>
        </Box>
      </Box>
    </Modal>
  )
}

export default ConfirmationPopup
