import { Box, Slider, Tooltip } from '@mui/material'
import useGetDeviceType from '../../../utils/useGetDeviceType'

export default function JackpotMultiplier({ mode, onChange, value }) {
  const { isMobile } = useGetDeviceType()

  const handleChange = (event, newValue) => {
    onChange?.(newValue)
  }

  const percentFilled = ((value - 1) / 9) * 100 // from 1 to 10

  return (
    <Box
      className='multiplier-bar'
      sx={{
        width: isMobile ? '220px' : '350px',
        display: 'flex',
        alignItems: 'center',
        marginTop: '-30px',
        position: 'relative'
      }}
    >
      {/* Progress bar wrapper */}
      <Box
        className='progress-bar-wrapper'
        sx={{
          flexGrow: 1,
          position: 'relative',
          height: '40px',
          marginLeft: '8px'
        }}
      >
        {/* Main filled bar */}
        <Box
          className='progress-bar-filled'
          sx={{
            width: '100%',
            height: '16px',
            padding: '4px',
            borderRadius: '10px',
            background: '#BA5C25',
            boxShadow: '0px 7.89px 6.32px 0px #00000066 inset',
            overflow: 'hidden',
            position: 'absolute',
            top: '14px'
          }}
        >
          {/* Green fill inside the bar */}
          <Box
            sx={{
              width: `${percentFilled}%`,
              height: '100%',
              borderRadius: '10px',
              backgroundColor: '#00FF00'
            }}
          />
        </Box>

        {/* Multiplier Thumb acting as Slider Thumb */}

        <Box
          className='multiplier-thumb pulse'
          sx={{
            width: 38,
            height: 38,
            borderRadius: '50%',
            background: 'linear-gradient(180deg, #FDB72E 0%, #BC6F06 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            color: '#000',
            position: 'absolute',
            // left: `calc(${percentFilled}% - 2px)`,
            left: `calc(${percentFilled}% - ${percentFilled === 100 ? 9 : 2}px)`,
            top: '0px',
            cursor: 'pointer',
            zIndex: 9,
            userSelect: 'none',
            border: '2px solid white',
            fontSize: '16px',
            transition: 'left 0.1 s ease-out',
            pointerEvents: 'none' // allows slider to still capture pointer events
          }}
        >
          {value}x
        </Box>

        {/* Transparent Slider for interaction */}
        <Slider
          value={value}
          onChange={handleChange}
          min={1}
          max={10}
          step={1}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            color: 'transparent',
            zIndex: 4,
            '& .MuiSlider-thumb': {
              display: 'none'
            },
            '& .MuiSlider-track': {
              display: 'none'
            },
            '& .MuiSlider-rail': {
              opacity: 0
            }
          }}
        />
      </Box>
    </Box>
  )
}
