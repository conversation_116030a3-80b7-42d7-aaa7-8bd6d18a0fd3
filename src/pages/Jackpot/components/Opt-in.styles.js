import { makeStyles } from '@mui/styles'

import mainborder from '../../../components/ui-kit/icons/png/jackpot-bg.png'
import JackpotOptInBg from '../../../components/ui-kit/icons/webp/jackpotOptInBg.webp'

const DIGIT_HEIGHT = 32
const MOBILE_DIGIT_HEIGHT = 22

export default makeStyles((theme) => ({
  jackpotOptIn: {
    maxWidth: '500px',
    margin: '0 auto',
    '& .close-btn': {
      color: theme.colors.textWhite,
      marginLeft: 'auto',
      position: 'absolute',
      top: theme.spacing(0),
      right: theme.spacing(0),
      cursor: 'pointer',
      zIndex: '5',
      '& svg': {
        fontSize: theme.spacing(1.2)
      }
    },
    '& .jackpot-content': {
      backgroundImage: `url(${JackpotOptInBg})`,
      backgroundRepeat: 'no-repeat',
      outline: 'none',
      backgroundSize: 'cover',
      display: 'flex',
      padding: '1rem',
      maxWidth: '456px',
      flexDirection: 'column',
      alignItems: 'center',
      backgroundPosition: 'center',
      boxShadow: 'none',
      // borderRadius: '1rem',
      // height: '625px',
      [theme.breakpoints.down('md')]: {
        padding: '0.75rem 0.75rem 1.5rem 0.75rem',
        height: 'auto'
      },
      '& .jackpot-img': {
        width: '100%',
        [theme.breakpoints.down('md')]: {
          maxHeight: '250px'
        },
        [theme.breakpoints.down('sm')]: {}
      },
      '& .join-jackpot': {
        top: '-40px',
        fontWeight: '700',
        fontSize: '51.63px',
        background: 'linear-gradient(171.6deg, #FFC538 6.97%, #E37A34 68.29%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        color: 'transparent',
        [theme.breakpoints.down('md')]: {
          fontSize: '32.38px'
        }
      },
      '& .opt-in-content': {
        top: '-40px',
        fontSize: '1.25rem',
        fontWeight: '500',
        textAlign: 'center',
        marginBottom: '0.75rem',
        width: '330px',
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          fontSize: '12.45px',
          width: '207px'
        }
      },
      '& .current-jackpot': {
        top: '-40px',
        fontWeight: '700',
        fontSize: '22.97px',
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          fontSize: '14.4px'
        }
      },
      '& .jackpot-amount-block': {
        top: '-30px',
        position: 'relative',
        display: 'inline-block',
        [theme.breakpoints.down('md')]: {
          width: '184px'
        },

        '& .jackpot-amount': {
          position: 'absolute',
          whiteSpace: 'nowrap',
          color: theme.colors.textWhite,
          fontWeight: '700',
          fontSize: '1.125rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '1rem',
          top: '40%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.75rem'
          },
          '& img': {
            width: '2rem',
            [theme.breakpoints.down('md')]: {
              width: '1.25rem'
            }
          }
        }
      },
      '& .jackpot-multiplier': {
        color: theme.colors.textWhite,
        fontWeight: '600',
        [theme.breakpoints.down('md')]: {
          fontSize: '0.75rem'
        }
      },
      '& .jackpot-spin-amount': {
        background: 'linear-gradient(180deg, #FDB72E 0%, #BC6F06 100%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        display: 'inline-block',
        fontWeight: '700',
        fontSize: '20px',
        [theme.breakpoints.down('md')]: {
          fontSize: '14.4px'
        }
      },
      '& .jackpot-btn': {
        marginTop: '1rem',
        // display: 'flex',
        // justifyContent: 'space-between',
        // width: '100%',
        gap: '1rem',
        '& .btn-primary': {
          backgroundColor: '#00A30B',
          maxWidth: '170px',
          width: '100%',
          whiteSpace: 'nowrap',
          [theme.breakpoints.down('md')]: {
            padding: '0.5rem 1rem'
          },
          '&:hover': {
            borderColor: '#00A30B',
            color: theme.colors.textBlack
          }
        }
      },
      '& .jackpot-badge': {
        top: '-30px',
        // position: 'fixed',
        background: `url(${mainborder})`,
        backgroundSize: '100% 100%',
        width: '100%',
        // top: '100px',
        zIndex: '3',
        height: '100%',
        borderRadius: '5rem',
        display: 'flex',
        gap: '0.5rem',
        alignItems: 'center',
        padding: '0.875rem 1rem 0.875rem 1rem',
        maxWidth: '190px',
        maxHeight: '45px',
        // left: '50%',
        // transform: 'translate(-50% , 0%)',
        // [theme.breakpoints.down('md')]: {
        //   padding: '0.875rem 0.65rem 0.65rem 0.65rem',
        //   maxWidth: '140px',
        //   maxHeight: '40px',
        //   gap: '0.25rem'
        // },

        '& .cash-icon': {
          width: '1.5rem',
          [theme.breakpoints.down('md')]: {
            width: '1.125rem'
          }
        },
        '& .MuiTypography-root': {
          fontSize: '1.25rem',
          fontWeight: '700',
          background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          color: 'transparent',
          whiteSpace: 'nowrap',
          WebkitTextStroke: '0.5px #BA5C25',
          textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
          lineWeight: '1.2',
          minWidth: '100px',
          paddingTop: '3px',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem',
            minWidth: '90px',
            gap: '1px'
          }
        },
        '& .tmf-jackpot': {
          position: 'absolute',
          width: '100px',
          top: '-10px',
          left: '50%',
          transform: 'translate(-52% , 0%) scale(1)',
          [theme.breakpoints.down('md')]: {
            width: '75px',
            top: '-5px'
          }
        }
      }
    },
    '& .progress-bar-wrapper': {
      margin: '0.75rem 0'
    }
  },
  digitsWrapper: {
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    gap: '2px'
  },
  scText: {
    fontWeight: 700,
    fontSize: '20px',
    background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    color: 'transparent',
    WebkitTextStroke: '0.5px #BA5C25',
    textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
    [theme.breakpoints.down('md')]: {
      fontSize: '20px'
      // paddingTop: '2px'
    }
  },
  digit: {
    height: `${DIGIT_HEIGHT}px`,
    lineHeight: `${DIGIT_HEIGHT}px`,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: 700,
    fontSize: '20px',
    background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    color: 'transparent',
    WebkitTextStroke: '0.5px #BA5C25',
    textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
    cursor: 'pointer',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    [theme.breakpoints.down('md')]: {
      height: `${MOBILE_DIGIT_HEIGHT}px`,
      lineHeight: `${MOBILE_DIGIT_HEIGHT}px`,
      fontSize: '20px',
      paddingTop: '0px'
    }
  },
  digitContainer: {
    overflow: 'hidden',
    height: `${DIGIT_HEIGHT}px`,
    width: '14px',
    display: 'inline-block',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      height: `${MOBILE_DIGIT_HEIGHT}px`,
      width: '8px'
    }
  },

  digitStrip: {
    transition: 'transform 0.3s ease-out',
    willChange: 'transform'
  }
}))
