import React from 'react'
import useStyles from '../JackpotBadge.styles'


const DIGIT_HEIGHT = 32
const MO<PERSON>LE_DIGIT_HEIGHT = 22

const RollingDigit = ({ digit, isMobile }) => {
  const classes = useStyles()
  const height = isMobile ? MOBILE_DIGIT_HEIGHT : DIGIT_HEIGHT

  return (
    <div className={classes.digitContainer}>
      <div className={classes.digitStrip} style={{ transform: `translateY(-${digit * height}px)` }}>
        {Array.from({ length: 10 }, (_, i) => (
          <div key={i} className={classes.digit}>
            {i}
          </div>
        ))}
      </div>
    </div>
  )
}

export default RollingDigit
