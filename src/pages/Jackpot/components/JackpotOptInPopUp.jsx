import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>onte<PERSON>, <PERSON>rid, <PERSON><PERSON><PERSON><PERSON><PERSON>, Typography } from '@mui/material'
import jackpotOptInBadge from '../../../components/ui-kit/icons/webp/jackpot-opt-in.webp'
// import jackpotAmount from '../../../components/ui-kit/icons/svg/jackpotAmount.svg'
import JackpotMultiplier from './JackpotMultiplier'
import { useJackpotStore, usePortalStore } from '../../../store/store'
// import cashIcon from '../../../components/ui-kit/icons/utils/usd-cash.webp'
import { useEffect, useState } from 'react'
import useStyles from './Opt-in.styles'
// import { formatPriceWithCommas } from '../../../utils/helpers'
import CloseIcon from '@mui/icons-material/Close'
// import JackpotBadge from '../JackpotBadge'
import usdCash from '../../../components/ui-kit/icons/utils/usd-cash.webp'
import useGetDeviceType from '../../../utils/useGetDeviceType'
import tmfjackpot from '../../../components/ui-kit/icons/png/tmf-jackpot.png'
import jackpotQuery from '../../../reactQuery/jackpotQuery'

/* eslint-disable multiline-ternary */

const DIGIT_HEIGHT = 32
const MOBILE_DIGIT_HEIGHT = 22

const RollingDigit = ({ digit, isMobile, animate }) => {
  const classes = useStyles()
  const height = isMobile ? MOBILE_DIGIT_HEIGHT : DIGIT_HEIGHT

  return (
    <div className={classes.digitContainer} style={{ height }}>
      <div
        className={classes.digitStrip}
        style={{
          transform: `translateY(-${digit * height}px)`,
          transition: animate ? 'transform 0.6s ease-out' : 'none'
        }}
      >
        {Array.from({ length: 10 }, (_, i) => (
          <div key={i} className={classes.digit} style={{ height }}>
            {i}
          </div>
        ))}
      </div>
    </div>
  )
}

const JackpotOptInPopUp = ({ toggleJackpot, entryAmount }) => {
  const { jackpotMultiplier, setJackpotMultiplier, setJackpotData } = useJackpotStore()
  const [localMultiplier, setLocalMultiplier] = useState(jackpotMultiplier)
  const portalStore = usePortalStore()
  const { isMobile } = useGetDeviceType()

  const { data: jackpotInitData } = jackpotQuery.getJackpotDataQuery({
    params: {
      jackpotPage: false
    },
    successToggler: (data) => {
      setJackpotData('jackpotPoolAmount', Number(data?.jackpotPoolAmount))
      setJackpotData('entryAmount', data?.entryAmount)
      setJackpotData('recentJackpotWinners', data?.recentJackpotWinners)
    }
  })
  const { jackpotPoolAmount } = useJackpotStore((state) => ({
    jackpotPoolAmount: state.jackpotData.jackpotPoolAmount
  }))
  console.log(jackpotPoolAmount)
  const [count, setCount] = useState(Number(jackpotPoolAmount) || 0)
  const [hasAnimated, setHasAnimated] = useState(false)

  // const isFirstRender = useRef(true)
  const handleClose = () => {
    portalStore.closePortal()
  }
  const handleOptInJackpot = () => {
    setJackpotMultiplier(localMultiplier)
    console.log(localMultiplier)
    toggleJackpot.mutate({
      status: true,
      multiplier: localMultiplier
    })
    handleClose()
  }

  const duration = 2000 // in ms
  const frameRate = 30 // frames per second

  useEffect(() => {
    const newAmount = jackpotPoolAmount
    if (newAmount == null || newAmount === count) return

    // If this is the first time (initial render), skip animation
    if (!hasAnimated) {
      setCount(newAmount)
      setHasAnimated(true)
      return
    }

    const startAmount = count
    const difference = newAmount - startAmount
    const totalFrames = duration / (1000 / frameRate)
    let currentFrame = 0

    const interval = setInterval(() => {
      currentFrame++
      const progress = currentFrame / totalFrames
      const eased = easeOutQuad(progress)
      const currentCount = +(startAmount + difference * eased)

      setCount(currentCount)
      if (currentFrame >= totalFrames) {
        clearInterval(interval)
        setCount(newAmount)
      }
    }, 1000 / frameRate)

    function easeOutQuad(t) {
      return t * (2 - t)
    }

    return () => clearInterval(interval)
  }, [jackpotPoolAmount])

  const countStr = Number(count).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })

  const classes = useStyles()
  return (
    <Dialog open={true} onClose={handleClose} className={classes.jackpotOptIn}>
      <DialogContent sx={{ padding: '0' }}>
        <IconButton onClick={handleClose} className='close-btn'>
          <CloseIcon />
        </IconButton>
        <Grid className='jackpot-content'>
          <img src={jackpotOptInBadge} className='jackpot-img' />
          <Typography className='join-jackpot'>Join the Jackpot !</Typography>
          <Typography className='opt-in-content'>
            Don't miss your chance to win big. Opt in now and be part of the jackpot draw!
          </Typography>
          <Typography className='current-jackpot'>Current Jackpot</Typography>
          {jackpotInitData?.jackpotPoolAmount > 0 && (
            <div className='jackpot-badge'>
              <img src={usdCash} className='cash-icon' alt='cash' />
              <Typography component='div' className={classes.digitsWrapper}>
                {countStr.split('').map((char, idx) =>
                  /\d/.test(char) ? (
                    hasAnimated ? (
                      <RollingDigit key={idx} digit={parseInt(char, 10)} isMobile={isMobile} />
                    ) : (
                      <span key={idx} className={classes.digit}>
                        {char}
                      </span>
                    )
                  ) : (
                    <span key={idx} className={classes.digit}>
                      {char}
                    </span>
                  )
                )}
                <span className={classes.scText}>SC</span>
              </Typography>
              <img src={tmfjackpot} className='tmf-jackpot' alt='jackpot crown' />
            </div>
          )}
          <JackpotMultiplier mode='popup' value={localMultiplier} onChange={setLocalMultiplier} />
          <Typography className='jackpot-spin-amount'> Move slider to increase jackpot entries</Typography>
          <Typography className='jackpot-multiplier'>
            {parseFloat((entryAmount * localMultiplier).toFixed(2)) || 0} SC per spin will be deducted.
          </Typography>

          <Grid className='jackpot-btn'>
            <Button className='btn btn-primary' onClick={handleOptInJackpot}>
              Opt in Jackpot
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default JackpotOptInPopUp
