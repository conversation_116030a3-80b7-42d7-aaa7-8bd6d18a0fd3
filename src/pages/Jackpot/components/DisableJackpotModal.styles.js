import { makeStyles } from '@mui/styles'

import jackpotBg from '../../../components/ui-kit/icons/webp/background-jackpot.webp'
export default makeStyles(() => ({
  jackpotDisable: {
    '& .jackpot-content': {
      backgroundImage: `url(${jackpotBg})`,
      backgroundRepeat: 'no-repeat',
      outline: 'none',
      background: '#202020',
      backgroundSize: 'cover',
      position: 'relative',
      borderRadius: '12px',
      boxShadow: 'none',
      '& img': {
        maxWidth: '300px'
      },
      '& .close-icon': {
        position: 'absolute',
        top: '1rem',
        width: '1rem',
        right: '1rem',
        cursor: 'pointer'
      },
      '& h4': {
        textAlign: 'center',
        fontSize: '2rem',
        fontWeight: '700',
        background: 'linear-gradient(171.6deg, #FFC538 6.97%, #E37A34 68.29%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        color: 'transparent'
      },
      '& p': {
        fontSize: '1.15rem',
        fontWeight: '600',
        textAlign: 'center'
      }
    }
  }
}))
