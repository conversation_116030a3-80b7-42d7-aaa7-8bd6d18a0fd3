import React, { useEffect } from 'react'
import { Box, Button, Modal, Typography } from '@mui/material'
import useStyles from './ConfirmationModal.styles'
import winBadge from '../../../components/ui-kit/icons/webp/win-badge.webp'
import jackpotShield from '../../../components/ui-kit/icons/webp/jackpot-shield.webp'
import coinsGroup from '../../../components/ui-kit/icons/utils/usd-cash.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import { loadLottieScript } from '../../../utils/loadLottieScript'

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  maxWidth: 380,
  width: '100%',
  boxShadow: 24,
  p: 2
}

const JackpotWinPopup = ({ jackpotWinAmount }) => {
  const portalStore = usePortalStore((state) => state)
  // Always open the modal
  const open = true

  // Dummy close handler (no-op)
  const handleClose = () => {
    portalStore.closePortal()
  }

  const classes = useStyles()

  useEffect(() => {
    loadLottieScript()
  }, [])

  return (
    <>
      <Modal
        open={open}
        onClose={handleClose}
        className={classes.jackpotConfirmation}
        aria-labelledby='confirmation-modal-title'
        aria-describedby='confirmation-modal-description'
      >
        <Box sx={style} className='jackpot-content'>
          <img src={winBadge} className='win-badge' />
          <img src={jackpotShield} className='jackpot-shield' />
          <Typography variant='h4' id='confirmation-modal-description'>
            CONGRATULATIONS!
          </Typography>
          <Typography variant='body1'>Great!, You’ve hit the jackpot and made history!</Typography>
          <Box className='value-box'>
            <Box className='value-content'>
              <Typography variant='h4' id='confirmation-modal-description'>
                <img src={coinsGroup} /> {jackpotWinAmount} SC
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
            <Button variant='contained' className='btn btn-primary' onClick={() => portalStore.closePortal()}>
              Continue Playing
            </Button>
          </Box>
          <Box className='payment-gif'>
            <lottie-player
              src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/applied.json`}
              background='transparent'
              speed='1'
              loop
              autoplay
              style={{ width: '500px', height: '500px' }}
            />
          </Box>
        </Box>
      </Modal>
    </>
  )
}

export default JackpotWinPopup
