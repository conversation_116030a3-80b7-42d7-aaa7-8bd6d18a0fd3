import React, { useEffect, useState } from 'react'
import useStyles from '../Jackpot.styles'
import { Box, Button, CircularProgress, Grid, Tooltip, Typography } from '@mui/material'
// import { motion } from 'framer-motion'
import CasinoCard from '../../../components/ui-kit/icons/utils/casinoGames.webp'
import whitePlay from '../../../components/ui-kit/icons/svg/white-play-button.svg'
import TournamentLogo from '../../../components/ui-kit/icons/png/tournament-logo.png'
import { usePortalStore, useSubCategoryOnLoadStore, useUserStore } from '../../../store/store'
import { getItem, getLoginToken } from '../../../utils/storageUtils'
import { CasinoQuery, useGetProfileMutation } from '../../../reactQuery'
import MobileVerification from '../../MobileVerification'
import { usePragmaticJackpotStore } from '../../../store/usePragmaticJackpot'
import LazyImage from '../../../utils/lazyImage'
import { formatPriceWithCommas, dynamicMerge } from '../../../utils/helpers'
import PragmaticJackpotSCLogo from '../../../components/ui-kit/icons/svg/pragmatic-sc.svg'
import PragmaticJackpotGCLogo from '../../../components/ui-kit/icons/svg/pragmatic-gc.svg'

const GamesList = ({ loading, handlePlayNow }) => {
  const classes = useStyles()
  const coinType = getItem('coin')
  const auth = useUserStore((state) => state)
  const portalStore = usePortalStore((state) => state)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const [gameId, setGameId] = useState(null)
  const [name, setName] = useState('')
  const [allGames, setAllGames] = useState([])
  const [subCategoryName, setSubCategoryName] = useState('Lobby')
  const { pragmaticJackpotSc, pragmaticJackpotGc } = usePragmaticJackpotStore()
  const [gamesLoading, setGamesLoading] = useState(false)
  const [gameDataCount, setGameDataCount] = useState()
  const [pageNo, setPageNo] = useState(1)

  const successToggler = (res) => {
    const games = res?.data?.data?.[0]?.subCategoryGames || []
    const name = res?.data?.data?.[0]?.name || 'Lobby'
    const count = res?.data?.data?.[0]?.totalGames || 0
    const updatedGameData = dynamicMerge(allGames || [], games || [], 'masterCasinoGameId')
    setAllGames(updatedGameData)
    setSubCategoryName(name)
    setGameDataCount(count)
    setGamesLoading(false)
  }
  const errorToggler = (error) => {
    console.log('$$ERROR', error)
    // toast.error(error?.response?.data?.errors?.[0]?.description)
  }

  /* subcategoryListMutation api hit  */
  const subcategoryListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (!res?.data?.data?.phoneVerified) {
        portalStore.openPortal(
          () => (
            <MobileVerification
              calledFor='gamePlay'
              handlePlayNow={() => handlePlayNow(gameId, name, subCategoryName)}
            />
          ),
          'innerModal'
        )
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const handlePhoneVerification = (gameId, name, subCategoryName) => {
    if (coinType === 'SC' && !auth?.userDetails?.phoneVerified && (!!getLoginToken() || auth.isAuthenticate)) {
      setGameId(gameId)
      setName(name)
      getProfileMutation.mutate()
    } else {
      handlePlayNow(gameId, name, subCategoryName)
    }
  }

  const handleLoadMore = () => {
    if (Math.ceil(gameDataCount / 21) > pageNo) {
      setPageNo(pageNo + 1)
      subcategoryListMutation.mutate({
        subCategorySlug: 'jackpot',
        limit: 21,
        page: pageNo + 1
      })
    }
  }

  useEffect(() => {
    subcategoryListMutation.mutate({
      subCategorySlug: 'jackpot',
      limit: 21,
      page: pageNo
    })
  }, [])

  return (
    <>
      <Box className={!loading ? 'jackpot-games-wrap' : ''}>
        <>
          {allGames?.map((game, key) => {
            const gameId = String(game?.masterCasinoGameId)

            const isScPragmatic = coinType === 'SC' && pragmaticJackpotSc?.hasOwnProperty(gameId)
            const isGcPragmatic = coinType === 'GC' && pragmaticJackpotGc?.hasOwnProperty(gameId)
            const jackpotValue = isScPragmatic
              ? pragmaticJackpotSc?.[gameId]
              : isGcPragmatic
                ? pragmaticJackpotGc?.[gameId]
                : null
            return (
              <div className='custom-col-2' key={`${game.masterCasinoGameId}_${key}`}>
                <Grid key={`${game.masterCasinoGameId}_1_${key}`}>
                  <Grid>
                    <Tooltip
                      title={game?.gameInTournament ? 'TOURNAMENT' : ''}
                      arrow
                      disableHoverListener={!game?.gameInTournament}
                      placement='top-start' // You can adjust this if needed
                      componentsProps={{
                        tooltip: {
                          sx: {
                            backgroundColor: '#FF3000 ',
                            color: 'white',
                            textAlign: 'center',
                            fontWeight: '800',
                            fontSize: '14px'
                          },
                          style: {
                            maxWidth: 'none' // optional: ensures full text is visible
                          }
                        },
                        arrow: {
                          sx: {
                            color: '#FF3000',
                            position: 'absolute',
                            left: '16px !important',
                            transform: 'translate(0px, 0px) !important',
                            marginTop: '-8px !important'
                          }
                        }
                      }}
                    >
                      <Grid className='casino-card'>
                        <Grid className='casino-card'>
                          <img
                            src={game?.imageUrl || CasinoCard}
                            alt='Casino'
                            className='casinoGame-img'
                            loading='lazy'
                          />
                          <Grid
                            className='casino-overlay'
                            onClick={() => {
                              handlePhoneVerification(game?.masterCasinoGameId, game?.name, subCategoryName)
                            }}
                          >
                            <Typography
                              variant='h6'
                              sx={{ lineHeight: '20px', textAlign: 'center', padding: '0 10px' }}
                            >
                              <b>{game.name}</b>
                            </Typography>

                            <img src={whitePlay} alt='Play' className={classes.playImg} />

                            <b>Play Now </b>
                          </Grid>
                          {jackpotValue !== null && (
                            <div className='prgamatic-jackpot-amount-wrapper'>
                              <LazyImage
                                src={coinType === 'SC' ? PragmaticJackpotSCLogo : PragmaticJackpotGCLogo}
                                alt='prgamatic-jakcpot-logo'
                              />
                              <Typography
                                style={{
                                  color: ` ${coinType === 'SC' ? '#00C80E' : '#FDB72E'}`,
                                  fontWeight: '700',
                                  fontSize: '10px'
                                }}
                              >
                                {formatPriceWithCommas(jackpotValue)} {coinType}
                              </Typography>
                            </div>
                          )}
                          {game?.gameInTournament && (
                            <img src={TournamentLogo} alt='tournament-logo' className='tournamentLogo' />
                          )}
                        </Grid>
                      </Grid>
                    </Tooltip>
                  </Grid>
                </Grid>
              </div>
            )
          })}
        </>
      </Box>
      {gameDataCount === 0 && (
        <Grid className='no-data-content'>
          <Typography sx={{ textAlign: 'center' }}> No Games Found</Typography>
        </Grid>
      )}
      {gamesLoading === true && (
        <Grid className={classes.loadMore}>
          <CircularProgress size={25} />
        </Grid>
      )}
      {Math.ceil(gameDataCount / 21) > pageNo && (
        <Grid className={classes.loadMore}>
          <Button
            variant='contained'
            className='btn-gradient'
            onClick={() => {
              handleLoadMore()
              setGamesLoading(true)
            }}
          >
            <span>Load More</span>
          </Button>
        </Grid>
      )}
    </>
  )
}

export default GamesList
