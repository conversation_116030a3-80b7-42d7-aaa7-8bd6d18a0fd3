import { makeStyles } from '@mui/styles'

import jackpotBg from '../../../components/ui-kit/icons/webp/jackpot-modal-bg.webp'

export default makeStyles((theme) => ({
  jackpotConfirmation: {
    '& .jackpot-content': {
      backgroundImage: `url(${jackpotBg})`,
      backgroundRepeat: 'no-repeat',
      outline: 'none',
      backgroundSize: 'cover',
      position: 'relative',
      display: 'flex',
      maxWidth: '460px',
      flexDirection: 'column',
      alignItems: 'center',
      backgroundPosition: 'center',
      boxShadow: 'none',
      borderRadius: '1rem',
      [theme.breakpoints.down('md')]: {
        padding: '0.5rem'
      },
      '& .win-badge': {
        position: 'absolute',
        top: '-105px',
        left: '50%',
        width: '70%',
        transform: 'translate(-50%,0)',
        [theme.breakpoints.down('md')]: {
          top: '-115px'
        },
        [theme.breakpoints.down('sm')]: {
          top: '-95px'
        }
      },
      '& .jackpot-shield': {
        maxWidth: '140px',
        marginTop: '20px'
      },
      '& h4': {
        textAlign: 'center',
        fontSize: '2rem',
        fontWeight: '700',
        background: 'linear-gradient(171.6deg, #FFC538 6.97%, #E37A34 68.29%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        color: 'transparent'
      },
      '& p': {
        fontSize: '1.25rem',
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: '0.75rem'
      },
      '& .value-box': {
        background: 'linear-gradient(180deg, #FFA538 4.26%, #76411E 100%)',
        borderRadius: '12px',
        padding: '2px',
        maxWidth: '200px',
        margin: '0 auto',
        '& .value-content': {
          background: '#000',
          borderRadius: '12px',
          padding: '12px',
          '& .sub-text': {
            display: 'flex',
            justifyContent: 'space-between',
            '& p': {
              fontSize: '13px'
            }
          },
          '& h4': {
            display: 'flex',
            justifyContent: 'center',
            gap: '0.75rem',
            alignItems: 'center',
            '& img': {
              width: '1.5rem',
              height: '1.5rem'
            }
          }
        }
      },
      '& .payment-gif': {
        position: 'absolute',
        top: 0,
        width: '100%',
        height: '100%',
        '& .popper-animation': {
          position: 'absolute',
          width: '60vh',
          height: '100%'
        }
      },
      '& .spins-text': {
        display: 'flex',
        marginTop: '1rem',
        gap: '1rem',
        alignItems: 'center',
        '& .MuiTypography-h5': {
          fontSize: '1.875rem',
          fontWeight: '700',
          background: 'linear-gradient(180deg, #1751C2 22.5%, #5E94FF 66.42%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          color: 'transparent',
          display: 'flex',
          gap: '0.25rem',
          alignItems: 'center',
          '& span': {
            color: '#1D55C5 !important',
            fontSize: '13px',
            WebkitTextFillColor: '#1D55C5'
          },
          '&.green': {
            background: 'linear-gradient(180deg, #00A30B 21.05%, #80F488 81.58%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            color: 'transparent',
            '& span': {
              color: '#00A30B !important',
              WebkitTextFillColor: '#00A30B',
              fontSize: '13px'
            }
          },
          '&.line': {
            WebkitTextFillColor: '#787777'
          }
        }
      },
      '& button': {
        zIndex: '99'
      }
    }
  }
}))
