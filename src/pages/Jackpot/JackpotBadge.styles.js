import { makeStyles } from '@mui/styles'

import mainborder from '../../components/ui-kit/icons/png/jackpot-bg.png'

const DIGIT_HEIGHT = 32
const MOBILE_DIGIT_HEIGHT = 22

export default makeStyles((theme) => ({
  jackpotBadge: {
    position: 'fixed',
    background: `url(${mainborder})`,
    backgroundSize: '100% 100%',
    maxWidth: '270px',
    width: '100%',
    top: '100px',
    zIndex: '10',
    height: '100%',
    maxHeight: '75px',
    borderRadius: '5rem',
    display: 'flex',
    justifyContent: 'center',
    gap: '0.5rem',
    alignItems: 'center',
    padding: '1rem 2rem 0.5rem 2rem',
    left: '56%',
    transform: 'translate(-50%, 0%)',
    cursor: 'pointer',
    [theme.breakpoints.down('lg')]: {
      left: '50%'
    },
    [theme.breakpoints.down('md')]: {
      padding: '0.875rem 1rem 0.75rem 1rem',
      maxWidth: '170px',
      maxHeight: '45px',
      bottom: '60px',
      top: 'auto',
      gap: '0.125rem'
    },

    '& .cash-icon': {
      width: '2rem',
      [theme.breakpoints.down('md')]: {
        width: '1.5rem'
      }
    },
    '& .tmf-jackpot': {
      position: 'absolute',
      top: '-13px',
      left: '50%',
      transform: 'translate(-52%, 0%) scale(1)',
      width: '155px',
      [theme.breakpoints.down('md')]: {
        width: '100px',
        top: '-10px'
      }
    }
  },

  landingBadge: {
    left: '50% !important',
    top: '105px',
    [theme.breakpoints.down('md')]: {
      bottom: '10px',
      top: '105px',
      maxWidth: '200px',
      maxHeight: '56px'
    },
    digit: {
      [theme.breakpoints.down('md')]: {
        fontSize: '20px'
      }
    }
  },

  digitContainer: {
    overflow: 'hidden',
    height: `${DIGIT_HEIGHT}px`,
    width: '14px',
    display: 'inline-block',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      height: `${MOBILE_DIGIT_HEIGHT}px`,
      width: '8px'
    }
  },

  digitStrip: {
    transition: theme.transitions.create('transform', {
      duration: theme.transitions.duration.shortest,
      easing: theme.transitions.easing.easeOut
    }),
    willChange: 'transform'
  },

  digit: {
    height: `${DIGIT_HEIGHT}px`,
    lineHeight: `${DIGIT_HEIGHT}px`,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: 700,
    fontSize: '28px',
    background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    color: 'transparent',
    WebkitTextStroke: '0.5px #BA5C25',
    textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    cursor: 'pointer',

    [theme.breakpoints.down('md')]: {
      height: `${MOBILE_DIGIT_HEIGHT}px`,
      lineHeight: `${MOBILE_DIGIT_HEIGHT}px`,
      fontSize: '20px',
      WebkitTextStroke: '0.6px #BA5C25'
    }
  },

  digitsWrapper: {
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    gap: '2px'
  },

  scText: {
    fontWeight: 700,
    fontSize: '28px',
    background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    color: 'transparent',
    WebkitTextStroke: '0.5px #BA5C25',
    textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
    [theme.breakpoints.down('md')]: {
      fontSize: '20px'
    }
  }
}))
