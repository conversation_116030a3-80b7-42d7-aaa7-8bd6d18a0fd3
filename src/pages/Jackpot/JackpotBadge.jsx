import React, { useEffect, useState, useRef, lazy, Suspense } from 'react'
import useStyles from './JackpotBadge.styles'
import usdCash from '../../components/ui-kit/icons/utils/usd-cash.webp'
import tmfjackpot from '../../components/ui-kit/icons/png/tmf-jackpot.png'
import { Typography, useMediaQuery, useTheme } from '@mui/material'
import { useLocation, useNavigate } from 'react-router-dom'
import { useUserStore } from '../../store/useUserSlice'
import { getLoginToken } from '../../utils/storageUtils'
import jackpotQuery from '../../reactQuery/jackpotQuery'
import { useJackpotStore } from '../../store/useJackpotStore'
import LazyImage from '../../utils/lazyImage'
import { usePortalStore } from '../../store/userPortalSlice'

// Lazy load Signin component
const Signin = lazy(() => import('../../components/Modal/Signin'))

const DIGIT_HEIGHT = 32
const MOBILE_DIGIT_HEIGHT = 22

const RollingDigit = ({ digit, isMobile, animate }) => {
  const classes = useStyles()
  const height = isMobile ? MOBILE_DIGIT_HEIGHT : DIGIT_HEIGHT

  return (
    <div className={classes.digitContainer} style={{ height }}>
      <div
        className={classes.digitStrip}
        style={{
          transform: `translateY(-${digit * height}px)`,
          transition: animate ? 'transform 0.6s ease-out' : 'none'
        }}
      >
        {Array.from({ length: 10 }, (_, i) => (
          <div key={i} className={classes.digit} style={{ height }}>
            {i}
          </div>
        ))}
      </div>
    </div>
  )
}

const JackpotBadge = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const auth = useUserStore((state) => state)
  const navigate = useNavigate()
  const location = useLocation()
  const pathname = location.pathname
  const isLandingPage = pathname.includes('/online-social-casino-games')
  const { setJackpotData } = useJackpotStore()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))

  const isFirstRender = useRef(true)

  const { data: jackpotData } = jackpotQuery.getJackpotDataQuery({
    params: { jackpotPage: false },
    successToggler: (data) => {
      setJackpotData('jackpotPoolAmount', Number(data?.jackpotPoolAmount))
      setJackpotData('entryAmount', data?.entryAmount)
    }
  })

  const jackpotPoolAmount = useJackpotStore((state) => Number(state.jackpotData.jackpotPoolAmount) || 0)

  const [count, setCount] = useState(Number(jackpotData?.jackpotPoolAmount) || 0)

  const duration = 2000

  useEffect(() => {
    if (isLandingPage) {
      // Directly set the jackpot value without animation
      setCount(jackpotPoolAmount)
      return
    }
    const newAmount = Number(jackpotPoolAmount)
    if (!Number.isFinite(newAmount) || newAmount === count) return

    if (isFirstRender.current) {
      setCount(newAmount)
      isFirstRender.current = false
      return
    }

    const startAmount = typeof count === 'number' && !isNaN(count) ? count : 0
    const difference = newAmount - startAmount
    const startTime = performance.now()

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      const eased = easeOutQuad(progress)
      const currentCount = +(startAmount + difference * eased)
      setCount(currentCount)

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        setCount(newAmount)
      }
    }

    requestAnimationFrame(animate)

    function easeOutQuad (t) {
      return t * (2 - t)
    }
  }, [jackpotPoolAmount])

  const countStr = Number(count).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })

  const handleNavigate = () => {
    if (getLoginToken() || auth.isAuthenticate) {
      navigate('/jackpot')
    } else {
      portalStore.openPortal(
        () => (
          <Suspense fallback={null}>
            <Signin />
          </Suspense>
        ),
        'loginModal'
      )
    }
  }

  return jackpotData?.jackpotPoolAmount > 0
    ? (
      <div
        className={`${classes.jackpotBadge} ${isLandingPage ? classes.landingBadge : ''}`}
        onClick={handleNavigate}
        role='button'
      >
        {/* <img src={usdCash} className='cash-icon' alt='cash' /> */}
        <LazyImage src={usdCash} alt='cash' className='cash-icon' lazy={false} />
        <Typography component='div' className={classes.digitsWrapper}>
          {countStr.split('').map((char, idx) =>
            /\d/.test(char)
              ? (
                <RollingDigit
                  key={`digit-${idx}`}
                  digit={parseInt(char, 10)}
                  isMobile={isMobile}
                  animate={!isFirstRender.current}
                />)
              : (
                <span key={`char-${idx}`} className={classes.digit}>
                  {char}
                </span>)
          )}
          <span className={classes.scText}>SC</span>
        </Typography>
        {/* <img src={tmfjackpot} className='tmf-jackpot' alt='jackpot crown' /> */}
        <LazyImage src={tmfjackpot} alt='jackpot crown' className='tmf-jackpot' lazy={false} />
      </div>)
    : null
}

export default JackpotBadge
