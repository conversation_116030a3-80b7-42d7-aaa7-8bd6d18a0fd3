import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({

  verifyModal: {
    background: theme.colors.valutModal,
    minWidth: theme.spacing(32.25),
    borderRadius: theme.spacing(.5625),
    overflowX: "hidden",
    position: "relative",
    padding: theme.spacing(2),
    [theme.breakpoints.down('sm')]: {
      minWidth: "100%",
    },
    "& .MuiTypography-root": {

    },

    "& .modal-close": {
      color: theme.colors.textWhite,
      marginLeft: "auto",
      position: "absolute",
      right: theme.spacing(-1),
      top: theme.spacing(-1),
      cursor: "pointer",
      zIndex: "5",
    },
    "& .modal-title": {
      color: theme.colors.textWhite,
      fontWeight: "700",
      fontSize: theme.spacing(1.4375),
      marginBottom: theme.spacing(2),
    },

    "& .modal-content": {
      "& .text-ui": {
        color: '#fff'
      },
      "& .input-wrap": {
        marginBottom: theme.spacing(1),
        "& .MuiInputBase-root": {
          paddingRight: 0,
          width: "100%",
          "& .MuiInputBase-input": {
            border: `2px solid ${theme.colors.modalTabBtnActive}`,
            background: theme.colors.verifyInputBg,
            color: theme.colors.textWhite,
            borderRadius: theme.spacing(0.625),

            padding: theme.spacing(0.625, 1),
            minHeight: theme.spacing(2.75),
          },
          "&.Mui-focused": {
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: theme.colors.YellowishOrange,
              borderRadius: theme.spacing(0.625),
            }
          },
        },

      },
      "& .modal-btn": {
        display: "flex",
        justifyContent: "flex-end",
        marginTop: theme.spacing(2.5)
      },
    },
    "& p": {
      color: theme.colors.modalText,
      fontSize: theme.spacing(1.125),
      marginBottom: theme.spacing(1),
    }
  }

}))
