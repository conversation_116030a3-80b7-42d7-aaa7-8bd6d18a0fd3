import React, { useState } from 'react'
import { Button, Grid, IconButton, Typography, DialogContent, Box, OutlinedInput } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import PropTypes from 'prop-types'
import useStyles from './style'
import { usePortalStore } from '../../store/userPortalSlice'
import { useLogOutMutation, useVerifyOtp2FAMutation } from '../../reactQuery'
import { toast } from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'
import { useUserStore } from '../../store/useUserSlice'
import useLogout from '../../hooks/useLogout'

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

const Popup2Fa = ({ res, afterLogin }) => {
  const [code2FA, setCode2FA] = useState('') // State for 2FA code
  const [password, setPassword] = useState(null) // State for 2FA code
  const classes = useStyles()
  const user = useUserStore((state) => state)
  const portalStore = usePortalStore((state) => state)
  const navigate = useNavigate()
  const { logoutHandler } = useLogout()
  const logoutMutation = useLogOutMutation({
    onSuccess: (res) => {
      logoutHandler()
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const handleClose = () => {
    user.logout()
    logoutMutation.mutate()
    portalStore.closePortal()
  }

  const { mutate: verifyOtp2FA } = useVerifyOtp2FAMutation({
    onSuccess: (data) => {
      navigate('/') // Redirect or perform another action upon successful verification
      portalStore.closePortal()
      toast.success('OTP verified successfully!')
      afterLogin(res)
    },
    onError: () => {
    }
  })

  const handleCodeChange = (event) => {
    setCode2FA(event.target.value)
  }

  const handleSubmit = () => {
    if (code2FA.trim() === '') {
      toast.error('Please enter the 2FA code.')
      return
    }
    verifyOtp2FA({ token: code2FA, password })
  }

  return (
    <Grid>
      <DialogContent className={classes.verifyModal}>
        <Grid display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
          <Typography variant='h4' className='modal-title'>
            Verify Code
          </Typography>
          <Grid className='modal-close'>
            <IconButton edge='start' color='inherit' className='close' onClick={handleClose} aria-label='close'>
              <CloseIcon />
            </IconButton>
          </Grid>
        </Grid>
        <Grid className='modal-content'>
          <Typography className='text-ui'>Before SignIn, please verify the authentication code:</Typography>
          <Grid className='input-wrap'>
            <OutlinedInput
              id='Add-code'
              placeholder='Add Code here'
              value={code2FA}
              onChange={handleCodeChange}
              fullWidth
            />
          </Grid>
          <Grid className='modal-btn'>
            <Button type='button' className='btn btn-primary' onClick={handleSubmit}>
              Submit
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </Grid>
  )
}

export default Popup2Fa
