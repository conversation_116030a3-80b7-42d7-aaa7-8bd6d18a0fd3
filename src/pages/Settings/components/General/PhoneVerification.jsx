import React from 'react'
import { <PERSON><PERSON>, <PERSON>rid, TextField, Typography } from '@mui/material'
import Box from '@mui/material/Box'
import MobileVerification from '../../../MobileVerification'
import { usePortalStore } from '../../../../store/userPortalSlice'
import { useUserStore } from '../../../../store/useUserSlice'
import EmailVerified from '../../../../components/ui-kit/icons/svg/email-verified.svg'

const PhoneVerification = () => {
  const portalStore = usePortalStore((state) => state)
  const userDetails = useUserStore((state) => state.userDetails)

  const handleMobileVerificationOpen = () => {
    portalStore.openPortal(() => <MobileVerification />, 'innerModal')
  }

  return (
    <Box className='genral-tab'>
      <Grid className='setting-card-header'>
        <Typography variant='h4'>
          Phone Number {userDetails?.phoneVerified && <img src={EmailVerified} alt='Verified' />}
        </Typography>
      </Grid>
      <Grid className='setting-card-details'>
        {userDetails?.phoneVerified ? (
          <Typography>Your phone number is verified</Typography>
        ) : (
          <Typography>Verify your phone number to start playing with Sweep Coins (SC).</Typography>
        )}
        {userDetails?.phoneVerified && (
          <Grid container spacing={1}>
            <Grid item xs={12} sm={5} lg={4}>
              <TextField
                id='outlined-basic'
                value={`+${userDetails?.phoneCode} ${userDetails?.phone}`}
                variant='outlined'
                InputProps={{
                  readOnly: true,
                  style: { cursor: 'default' }
                }}
              />
            </Grid>
          </Grid>
        )}
      </Grid>
      <Grid className='setting-card-footer'>
        {userDetails?.phoneVerified ? (
          <Button type='button' className='btn btn-primary' disabled>
            Verified
          </Button>
        ) : (
          <Button type='button' className='btn btn-primary' onClick={handleMobileVerificationOpen}>
            Verify
          </Button>
        )}
      </Grid>
    </Box>
  )
}

export default PhoneVerification
