import React, { useEffect } from 'react'
import { Box, Grid, Typography, Button } from '@mui/material'
import useMarketingHook from '../../hooks/useMarketingHook'
import CustomSwitch from '../../../../components/CustomSwitch'

const MarketingSettingsForm = () => {
  const { getProfileMutation, emailMarketing, smsMarketing, handleSwitchSmsChange, handleSwitchEmailChange, handleSave } = useMarketingHook()

  useEffect(() => {
    getProfileMutation.mutate()
  }, [])

  return (
    <Box className='genral-tab'>
      <Grid className='setting-card-header'>
        <Typography variant='h4'>Marketing</Typography>
      </Grid>
      <Grid className='setting-card-details'>
        <Grid className='privacy-card'>
          <CustomSwitch checked={emailMarketing} onChange={handleSwitchEmailChange} />
          <Grid className='privacy-card-left'>
            <Typography variant='h4'>Receive Email offers from us</Typography>
            {/* <Typography>Choose if you wish to hear from us via email and SMS</Typography> */}
          </Grid>
        </Grid>

        <Grid className='privacy-card'>
          <CustomSwitch checked={smsMarketing} onChange={handleSwitchSmsChange} />
          <Grid className='privacy-card-left'>
            <Typography variant='h4'>Receive SMS offers from us</Typography>
          </Grid>
        </Grid>

        <Grid className='setting-card-footer privacy-footer'>
          <Typography>Please allow up to 30 seconds for the update to complete.</Typography>
          <Button
            type='button'
            className='btn btn-primary'
            onClick={handleSave}
          >
            Save
          </Button>

        </Grid>
      </Grid>
    </Box>
  )
}

export default MarketingSettingsForm
