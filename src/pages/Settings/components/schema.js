import * as Yup from 'yup'

export const userPasswordChange = Yup.object().shape({
  password: Yup.string()
    .required('Old Password is required')
    .min(8, 'Password must contain at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~`!@#$%^&*()_\-+={}[\]|:;"'<>,.?/]).{8,20}$/,
      'Password must be 8–20 characters and include at least one uppercase letter, one lowercase letter, one number, and one special character.'
    ),
  newPassword: Yup.string()
    .required('New Password is required.')
    .min(8, 'Password must contain at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~`!@#$%^&*()_\-+={}[\]|:;"'<>,.?/]).{8,20}$/,
      'Password must be 8–20 characters and include at least one uppercase letter, one lowercase letter, one number, and one special character.'
    ),
  repeatPassword: Yup.string().oneOf([Yup.ref('newPassword')], 'New Password and confirm  Password  not matching')
})

export const twoFactorSchema = Yup.object().shape({
  code: Yup.string().required('2FA Code is required'),
  password: Yup.string().required('Password is required')
})

export const twoFactorSchemaForSocial = Yup.object().shape({
  code: Yup.string().required('2FA Code is required')
})
