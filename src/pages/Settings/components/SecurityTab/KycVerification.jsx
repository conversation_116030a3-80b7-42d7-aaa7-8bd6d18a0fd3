import React from 'react'
import { <PERSON><PERSON>, Grid, Typography, Box } from '@mui/material'
import EmailVerified from '../../../../components/ui-kit/icons/svg/email-verified.svg'
import { useUserStore } from '../../../../store/useUserSlice'
import StepperForm from '../../../../components/StepperForm/index'
import { usePortalStore } from '../../../../store/userPortalSlice'
/* eslint-disable multiline-ternary */
const KycVerification = () => {
  const userDetails = useUserStore((state) => state.userDetails)
  const portalStore = usePortalStore()

  const handleIDVerifyClick = () => {
    portalStore.openPortal(() => <StepperForm stepperCalledFor={'kycSettings'} />, 'StepperModal')
  }

  return (
    <Grid>
      {userDetails.kycStatus === 'K1' || userDetails.kycStatus === 'K2' || userDetails.kycStatus === 'K3' ? (
        <Box className='genral-tab'>
          <Grid className='setting-card-header'>
            <Typography variant='h4'>KYC</Typography>
          </Grid>
          <Grid className='setting-card-details'>
            <Grid className='privacy-card'>
              <Grid className='kyc-content'>
                <Typography variant='h3'>Complete Your KYC Verification</Typography>
                <Typography>Choose if you wish to hear from us via email and SMS</Typography>
              </Grid>
            </Grid>
            <Grid className='setting-card-footer'>
              <Button type='button' className='btn btn-primary' onClick={handleIDVerifyClick}>
                Start Verification
              </Button>
            </Grid>
          </Grid>
        </Box>
      ) : (
        <Box className='genral-tab'>
          <Grid className='setting-card-header'>
            <Typography variant='h4'>KYC</Typography>
          </Grid>
          <Grid className='setting-card-details'>
            <Grid className='privacy-card'>
              <Grid className='kyc-content kyc-success'>
                <img src={EmailVerified} alt='Verified' className='verified-img' />
                <Typography variant='h3'>Your KYC Verification Done!</Typography>
              </Grid>
            </Grid>
          </Grid>
        </Box>
      )}
    </Grid>
  )
}

export default KycVerification
