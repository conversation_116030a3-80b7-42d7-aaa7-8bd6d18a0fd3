import React from 'react'
import { Box, Grid, Button, Typography, FormControl, OutlinedInput, InputAdornment, IconButton, CircularProgress } from '@mui/material'
import Visibility from '@mui/icons-material/Visibility'
import VisibilityOff from '@mui/icons-material/VisibilityOff'
import useChangePassword from '../../hooks/useChangePassword'
import useStyles from '../../Settings.styles'
const ChangePassword = () => {
  const {
    handleSubmit,
    register,
    handleOnSubmitPassword,
    errors,
    changePasswordMutation,
    reset,
  } = useChangePassword();

  const [showPassword, setShowPassword] = React.useState({
    password: false,
    newPassword: false,
    repeatPassword: false
  });

  const handleClickShowPassword = (field) => {
    setShowPassword((prevShowPassword) => ({
      ...prevShowPassword,
      [field]: !prevShowPassword[field]
    }));
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const classes = useStyles()
  return (
    <Box className="genral-tab">
      <Grid className='setting-card-header'>
        <Typography variant='h4'>Password</Typography>
      </Grid>
      <Grid className='setting-card-details'>
        <form onSubmit={handleSubmit(handleOnSubmitPassword)}>
          <Grid container spacing={1}>
            <Grid item xs={12} lg={8}>
              <Grid className='input-wrap'>
                <FormControl variant="outlined">
                  {/* <InputLabel htmlFor="old-password"  style={{marginTop:50}}>Old Password</InputLabel> */}
                  <OutlinedInput
                    id="old-password"
                    variant="outlined"
                    placeholder='Old Password'
                    type={showPassword.password ? 'text' : 'password'}
                    {...register("password")}
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => handleClickShowPassword("password")}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                        >
                          {showPassword.password ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    }
                  />
                </FormControl>
                <p className={classes.inputError}>{errors?.password?.message}</p>
              </Grid>
            </Grid>
            <Grid item xs={12} lg={8}>
              <Grid className='input-wrap'>
                <FormControl variant="outlined">
                  {/* <InputLabel htmlFor="new-password">New Password</InputLabel> */}
                  <OutlinedInput
                    id="new-password"
                    variant="outlined"
                    placeholder='New Password'
                    type={showPassword.newPassword ? 'text' : 'password'}
                    {...register("newPassword")}
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => handleClickShowPassword("newPassword")}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                        >
                          {showPassword.newPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    }
                  />
                </FormControl>
                <p className={classes.inputError}>{errors?.newPassword?.message}</p>
                {/* {newPasswordValue && <PasswordErrorMsg password={newPasswordValue} errors={errors} />} */}
              </Grid>
            </Grid>
            <Grid item xs={12} lg={8}>
              <Grid className='input-wrap'>
                <FormControl variant="outlined">
                  {/* <InputLabel htmlFor="confirm-password">Confirm Password</InputLabel> */}
                  <OutlinedInput
                    id="confirm-password"
                    variant="outlined"
                    placeholder='Confirm Password'
                    type={showPassword.repeatPassword ? 'text' : 'password'}
                    {...register("repeatPassword")}
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => handleClickShowPassword("repeatPassword")}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                        >
                          {showPassword.repeatPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    }
                  />
                </FormControl>
                <p className={classes.inputError}>{errors?.repeatPassword?.message}</p>
              </Grid>
            </Grid>
          </Grid>
          <Grid className='setting-card-footer' sx={{ display: "flex", gap: "15px", marginTop: "38px" }}>
            <Button
              variant="contained"
              type="submit"
              disabled={changePasswordMutation.isLoading}
              className="btn btn-primary"
            >
              {changePasswordMutation.isLoading ? (
                <CircularProgress size={24} style={{ marginRight: 8 }} />
              ) : (
                <span className="btn-span">Save</span>
              )}
            </Button>
            <Button
              variant="contained"
              type="button"
              onClick={() => reset()}
              className="btn btn-secondary"
            >
              <span className="btn-span">Reset</span>
            </Button>
          </Grid>
        </form>
      </Grid>
    </Box>
  );
};

export default ChangePassword;
