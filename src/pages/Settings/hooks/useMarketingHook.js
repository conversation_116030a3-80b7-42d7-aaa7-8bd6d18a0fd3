import { useState } from 'react'
import { toast } from 'react-hot-toast'

import { useGetProfileMutation, useMarketingMutation } from '../../../reactQuery'
import { useUserStore } from '../../../store/store'

const useMarketingHook = () => {
  const user = useUserStore((state) => state)
  const [emailMarketing, setEmailMarketing] = useState(user?.userDetails?.emailMarketing || false)
  const [smsMarketing, setSmsMarketing] = useState(user?.userDetails?.smsMarketing || false)

  const marketingSettingMutation = useMarketingMutation({
    onSuccess: (res) => {
      toast.success(res?.data?.message)
      setEmailMarketing(res?.data?.data.emailMarketing)
      setSmsMarketing(res?.data?.data.smsMarketing)
    },
    onError: (err) => {
      if (err?.response?.data?.errors.length > 0) {
        const { errors } = err.response.data
        console.log(errors, 'errors')
        errors.forEach((error) => {
          if (error?.description) {
            // toast.error(error?.description)
          }
        })
      }
    }
  })

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setEmailMarketing(res?.data?.data?.emailMarketing)
      setSmsMarketing(res?.data?.data?.smsMarketing)
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })


  const handleSave = () => {
    marketingSettingMutation.mutate({ emailMarketing, smsMarketing })
  }

  const handleSwitchEmailChange = () => {
    setEmailMarketing(!emailMarketing)
  }
  const handleSwitchSmsChange = () => {
    setSmsMarketing(!smsMarketing)
  }


  return {
    emailMarketing,
    smsMarketing,
    handleSwitchEmailChange,
    handleSave,
    handleSwitchSmsChange,
    getProfileMutation
  }
}

export default useMarketingHook
