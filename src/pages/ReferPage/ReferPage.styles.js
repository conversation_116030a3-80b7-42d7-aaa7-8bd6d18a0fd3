import { makeStyles } from '@mui/styles'

import { RefferBanner } from '../../components/ui-kit/icons/banner'
import { InnerBanner, LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({

  lobbyRight: {
    ...LobbyRight(theme),
    "& .reffer-friend-page": {
      "& .MuiTypography-root, & span, & li": {

      },
      "& .btn-primary": {
        color: theme.colors.textBlack,
        [theme.breakpoints.down('md')]: {
          maxWidth: theme.spacing(12.25),
          margin: "0 auto",
        },
        "&:hover": {
          color: theme.colors.YellowishOrange,
        }
      },
      maxWidth: theme.spacing(74.6875),
      margin: "0 auto",
      "& .gc-coin-text": {
        color: `${theme.colors.YellowishOrange} !important`,
      },
      "& .sc-coin-text": {
        color: `${theme.colors.Promosuccess} !important`,
      },
      "& .shewwp-coins-section": {
        padding: theme.spacing(2, 0),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1, 0),
        },
        "& h4": {
          fontSize: theme.spacing(1.5625),
          fontWeight: "700",
          marginBottom: theme.spacing(1),
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1),
            marginBottom: theme.spacing(0.625),
          },
        },
        "& p": {
          fontSize: theme.spacing(1.25),
          fontWeight: "500",
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(0.875),
          },

        }
      },
      "& .inner-heading": {
        marginBottom: theme.spacing(1.5),
        "& h4": {
          fontSize: theme.spacing(1.5625),
          fontWeight: "700",
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.2),
          },
        }
      },
      "& .share-reffer-section": {
        padding: theme.spacing(2, 0),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.2),
          padding: theme.spacing(1, 0),
        },

        "& .share-reffer-left": {
          "& .share-reffer-heading": {
            display: "flex",
            alignItems: "center",
            gap: theme.spacing(1.5),
            marginBottom: theme.spacing(2),
            [theme.breakpoints.down('md')]: {
              flexDirection: "column",
              gap: 0,
            },
            "& h4": {
              color: theme.colors.textWhite,
              fontSize: theme.spacing(1.25),
              fontWeight: "700",
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(1),
              },
              "&.green-text": {
                color: theme.colors.Promosuccess,
              }
            }
          }
        },

        "& .social-listing-wrap": {
          display: "flex",
          justifyContent: "space-between",
          [theme.breakpoints.down('md')]: {
            flexDirection: "column",
          },
          "& .social-listing": {
            display: "flex",
            gap: theme.spacing(2),
            [theme.breakpoints.down('md')]: {
              gap: theme.spacing(0.875),
              marginBottom: theme.spacing(1),
              justifyContent: "center",
            },
            "& a": {
              background: theme.colors.Promosuccess,
              height: theme.spacing(2.75),
              width: theme.spacing(2.75),
              minWidth: theme.spacing(2.75),
              borderRadius: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              [theme.breakpoints.down('md')]: {
                height: theme.spacing(2),
                width: theme.spacing(2),
                minWidth: theme.spacing(2),
              },
            },
            "& .btn-primary": {
              display: "flex",
              gap: theme.spacing(0.625),

            }
          }
        }
      },
      "& .how-section": {
        padding: theme.spacing(2, 0),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1, 0),
        },
        "& .how-it-works-listing": {
          "& ol": {
            paddingLeft: theme.spacing(3),
            counterReset: "item",
            listStyle: "none",
            [theme.breakpoints.down('md')]: {
              paddingLeft: 0,
            },
            "& li": {
              margin: theme.spacing(3.625, 0),
              counterIncrement: "item",
              fontSize: theme.spacing(1.25),
              fontWeight: "700",
              display: "flex",
              [theme.breakpoints.down('md')]: {
                margin: theme.spacing(2, 0),
              },
              "&:before": {
                marginRight: theme.spacing(2),
                content: "counter(item)",
                background: theme.colors.Promosuccess,
                height: theme.spacing(2.5),
                width: theme.spacing(2.5),
                minWidth: theme.spacing(2.5),
                borderRadius: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                zIndex: "3",
                [theme.breakpoints.down('md')]: {
                  marginRight: theme.spacing(2),
                },
              },
              "&:after": {
                marginRight: theme.spacing(2),
                height: theme.spacing(3),
                width: theme.spacing(3),
                minWidth: theme.spacing(3),
                borderRadius: "100%",
                border: `1px dashed ${theme.colors.refferStepBorder}`,
                content: "''",
                left: theme.spacing(-0.313),
                top: theme.spacing(-0.313),
                position: "absolute",
                background: theme.colors.inputBg,
                zIndex: "1"
              },
              "& .how-it-works-list-card": {
                position: "relative",
                fontSize: theme.spacing(1.25),
                fontWeight: "600",
                [theme.breakpoints.down('md')]: {
                  fontSize: theme.spacing(1),
                },
                "&:before": {
                  position: "absolute",
                  left: theme.spacing(-3.25),
                  top: theme.spacing(3.125),
                  height: "100%",
                  width: "2px",
                  borderLeft: `1px dashed ${theme.colors.refferStepBorder}`,
                  content: "''",
                  // zIndex:"2",
                }
              },
              "&:last-child": {
                "& .how-it-works-list-card": {
                  "&:before": {
                    display: "none"
                  }
                }
              }
            }
          }
        }
      },
      "& .friend-section": {
        padding: theme.spacing(2, 0),
        "& .statistics-table-wrap": {
          overflow: "hidden",
          background: theme.colors.tableBg,
          borderRadius: theme.spacing(.5625),
          border: `1px solid ${theme.colors.refferCardBorder}`,
          "& .MuiPaper-root": {
            backgroundColor: `${theme.colors.tableBg} !important`,
            boxShadow: "none",
            "& .MuiAccordionSummary-root": {
              padding: theme.spacing(1.3, 2),
              minHeight: theme.spacing(7.4375),
              background: theme.colors.inputBg,
              borderRadius: theme.spacing(0, 0, 0.625, 0.625),
              [theme.breakpoints.down('lg')]: {
                padding: theme.spacing(1, 0.625),
              },
            },
            "& .MuiAccordionSummary-content": {

              margin: "0",

            },
            "& .MuiAccordionDetails-root ": {
              padding: "0",
            },
            "& .MuiAccordionDetails-root": {
              [theme.breakpoints.down('lg')]: {
                padding: theme.spacing(0.625),
              },
            },
            "& .MuiCollapse-root": {
              background: theme.colors.tableBg,
            },
            "& .accordian-grid": {
              display: "grid",
              gridTemplateColumns: 'repeat(4, 1fr)',
              columnGap: theme.spacing(1.875),
              width: "100%",
              [theme.breakpoints.down('md')]: {
                display: "none",
              },
              "& .accordian-card ": {
                display: "flex",
                alignItems: "center",
                gap: theme.spacing(1),
                position: "relative",
                borderRight: `1px solid ${theme.colors.refferCardBorder}`,
                [theme.breakpoints.down('md')]: {
                  display: "none",
                },


                "& img": {
                  width: theme.spacing(3.125),
                  [theme.breakpoints.down('lg')]: {
                    width: theme.spacing(2),
                  },
                },

                "& h4": {
                  fontWeight: "700",
                  fontSize: theme.spacing(1.5625),
                  color: theme.colors.textWhite,
                  [theme.breakpoints.down('lg')]: {
                    fontSize: theme.spacing(1),
                  },
                },
                "& p": {
                  fontSize: theme.spacing(1.1),
                  color: theme.colors.textWhite,
                  fontWeight: "600",
                  [theme.breakpoints.down('lg')]: {
                    fontSize: theme.spacing(0.875),
                  },

                },

              },

            },
            "& .mob-accordian": {
              display: "none",
              [theme.breakpoints.down('md')]: {
                display: "grid",
                gridTemplateColumns: 'repeat(4, 1fr)',
                columnGap: theme.spacing(0.75),
                width: "100%",
              },
              [theme.breakpoints.down('sm')]: {
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: theme.spacing(0.75),
              },
              "& .accordian-card ": {
                display: "flex",
                alignItems: "center",
                gap: theme.spacing(0.625),
                position: "relative",
                flexDirection: "column",


                "& img": {
                  width: theme.spacing(3.125),
                  [theme.breakpoints.down('md')]: {
                    width: theme.spacing(1.3),
                  },
                },

                "& h4": {
                  fontWeight: "700",
                  fontSize: theme.spacing(1),
                  color: theme.colors.textWhite,
                },
                "& p": {
                  fontSize: theme.spacing(0.625),
                  color: theme.colors.textWhite,
                  fontWeight: "600",
                },
                "& .accordian-card-content": {
                  display: "flex",
                  gap: theme.spacing(0.5),
                  width: "100%",
                },
                "& .accordian-card-right ": {
                  width: "100%",
                  position: "relative",

                },

              },
            },
            "& .MuiAccordionSummary-expandIconWrapper": {
              color: theme.colors.textWhite,
            }
          },
          "& .MuiTableContainer-root": {
            padding: 0,
            "& table": {

              "& th": {
                color: theme.colors.YellowishOrange,
                fontSize: theme.spacing(1.25),
                border: "none",
                fontWeight: "600",
                padding: theme.spacing(1.2, 1.75),
                [theme.breakpoints.down('lg')]: {
                  padding: theme.spacing(0.625),
                  fontSize: theme.spacing(0.875),
                },
                [theme.breakpoints.down('md')]: {
                  fontSize: theme.spacing(0.75),
                },
              },
              "& td": {
                color: theme.colors.textWhite,
                border: "none",
                fontSize: theme.spacing(1.25),
                padding: theme.spacing(1.2, 1.75),
                fontWeight: "600",
                [theme.breakpoints.down('lg')]: {
                  padding: theme.spacing(0.625),
                  fontSize: theme.spacing(0.875),
                },
                [theme.breakpoints.down('md')]: {
                  fontSize: theme.spacing(0.75),
                },
                "& .user-details, & .table-dllor": {
                  display: "flex",
                  alignItems: "center",
                  gap: theme.spacing(1),

                  fontSize: theme.spacing(1.125),
                  [theme.breakpoints.down('lg')]: {
                    fontSize: theme.spacing(0.875),
                  },
                  [theme.breakpoints.down('md')]: {
                    fontSize: theme.spacing(0.75),
                  },
                  "& span": {
                    fontSize: theme.spacing(1.125),
                    [theme.breakpoints.down('lg')]: {
                      fontSize: theme.spacing(0.875),
                    },
                    [theme.breakpoints.down('md')]: {
                      fontSize: theme.spacing(0.75),
                    },
                  }
                },
                "&  .user-details": {
                  "& img": {
                    width: theme.spacing(2),
                    [theme.breakpoints.down('lg')]: {
                      width: theme.spacing(1),
                    },
                  }
                }
              },
              "& .MuiTableBody-root": {
                "& tr": {

                  "&:nth-child(odd)": {
                    background: theme.colors.tableRowBG,
                  }
                }

              }
            },
          }
        },
        "& .last-card": {
          borderRight: "none !important",
        },
      },
      "& .reffer-theme-card": {
        background: theme.colors.inputBg,
        borderRadius: theme.spacing(.5625),
        border: `1px solid ${theme.colors.refferCardBorder}`,
        padding: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1),
        },

      },
    },


  },
  tablePagination: {
    "& ul": {
      justifyContent: 'center',
      "& li": {
        "& button": {
          color: '#fff',
        },
        "& .MuiPaginationItem-root.Mui-selected": {
          backgroundColor: theme.colors.YellowishOrange,
          color: theme.colors.textBlack

        }

      }
    }
  },
  InnerBanner: {
    ...InnerBanner(theme),
    background: `url(${RefferBanner})`,
  },

}))
