import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, FormControlLabel, Checkbox, CircularProgress } from '@mui/material'
import React, { useEffect, useState } from 'react'
import useStyles from './Cms.styles'
import parse from 'html-react-parser'
import { useCMSStore, usePortalStore, useUserStore } from '../../store/store'
import { useParams, useNavigate } from 'react-router-dom'
import ScrollToTop from '../../components/ScrollToTop'
import TermAndCondition from '../../components/Modal/TermAndCondition/TermAndCondition'
import { getLoginToken } from '../../utils/storageUtils'
import { FooterQuerys } from '../../reactQuery'
import PostalCodeModal from '../../components/PostalCodeModal/PostalCodeModal'
import { usePostalCodeData } from '../Accounts/hooks/usePayByBankData'
import { getCookie } from '../../utils/cookiesCollection'
/* eslint-dsiable multiline-ternary */

const Cms = (props) => {
  const classes = useStyles()
  const { cmsPage } = useParams()
  const navigate = useNavigate()
  const auth = useUserStore((state) => state)
  const portalStore = usePortalStore((state) => state)
  const userDetails = useUserStore((state) => state?.userDetails)
  const cmsData = useCMSStore((state) => state.cmsData)
  const popdata = props?.path?.replace('/cms/', '')
  const [dataCMSLinks, setDataCMSLink] = useState({})
  const [showContent, setShowContent] = useState(false)
  const [isChecked, setIsChecked] = useState(false)
  const [loading, setLoading] = useState(false)
  const { handleConfirm, fromLanding } = props
  const pathCookie = getCookie('path') ? true : false

  useEffect(() => {
    if (popdata) {
      setDataCMSLink(cmsData?.find((x) => x.slug === popdata))
    } else {
      setDataCMSLink(cmsData?.find((x) => x.slug === cmsPage))
    }
  }, [popdata, cmsPage])

  useEffect(() => {
    setTimeout(() => {
      setShowContent(true)
    }, 1000)
  }, [])

  const { postalCodeMutate } = usePostalCodeData()

  const { data: cmsContent } = FooterQuerys.getCmsContentQuery({ pageSlug: cmsPage || popdata })

  const htmlRenderer = (htmlContent) => {
    return parse(htmlContent)
  }
  const handletNcPopup = () => {
    navigate('/')
    portalStore.openPortal(() => <TermAndCondition />, 'termsNConditionModal')
  }
  if (!showContent && !popdata) return null

  const handleCheckboxChange = (event) => {
    setIsChecked(event.target.checked)
  }

  const handleSubmit = async () => {
    if (isChecked) {
      setLoading(true)
      try {
        if (popdata === 'postal-code') {
          const response = await postalCodeMutate()
          const code = response?.data?.data

          portalStore.openPortal(
            () => <PostalCodeModal code={code} onClose={() => portalStore.closePortal()} />,
            'innerModal'
          )
        } else {
          handleConfirm('submit')
        }
      } catch (e) {
        console.error('API Error:', e)
      } finally {
        setLoading(false)
      }
    }
  }

  return (
    <>
      {!fromLanding && <ScrollToTop />}
      {popdata ? (
        <>
          <Grid className='cms-wrap  cms-inner-scroll' sx={{ color: '#fff' }}>
            <Typography variant='h3'>POLICIES</Typography>
            <Typography variant='h4'>{dataCMSLinks?.title?.EN}</Typography>
            <Typography>{htmlRenderer(cmsContent?.content?.EN || '')}</Typography>
          </Grid>
          <Grid className='cms-modal-footer'>
            {(popdata === 'postal-code' || popdata === 'tournament-gc-terms' || popdata === 'tournament-sc-terms') && pathCookie === true ? (
              <Grid className={classes.checkboxSection}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isChecked}
                      onChange={handleCheckboxChange}
                      sx={{
                        color: 'white',
                        '&.Mui-checked': {
                          color: '#ff4081'
                        }
                      }}
                    />
                  }
                  label='I accept the Terms and Conditions.'
                  sx={{
                    color: 'white',
                    '& .MuiTypography-root': {
                      fontSize: '1rem',
                      fontWeight: 'bold'
                    }
                  }}
                />
                <Button variant='contained' className='btn btn-secondary' onClick={handleSubmit} disabled={!isChecked}>
                  {loading ? <CircularProgress size={20} /> : 'Submit'}
                </Button>
              </Grid>
            ) : null}
          </Grid>
        </>
      ) : (
        <Grid className={classes.lobbyRight}>
          <Grid className='cms-wrap'>
            {(!!getLoginToken() || auth.isAuthenticate) && !userDetails?.isTermsAccepted ? (
              <Button className='accept-cms' onClick={() => handletNcPopup()}>
                Back to accept updated T&C and Policies.
              </Button>
            ) : (
              ''
            )}
            <Typography variant='h3'>POLICIES</Typography>
            <Typography variant='h4'>{dataCMSLinks?.title?.EN}</Typography>
            <Typography>{htmlRenderer(cmsContent?.content?.EN || '')}</Typography>
          </Grid>
        </Grid>
      )}
    </>
  )
}

export default Cms
