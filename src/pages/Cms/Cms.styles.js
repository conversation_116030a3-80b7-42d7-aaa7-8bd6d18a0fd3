import { makeStyles } from '@mui/styles'

import { LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    padding: theme.spacing(1, 1.5),
    '& .cms-wrap': {
      padding: theme.spacing(2),
      color: theme.colors.white,
      [theme.breakpoints.down('md')]: {
        padding: 0
      },
      '& p': {
        textAlign: 'justify',
        [theme.breakpoints.down('md')]: {
          textAlign: 'start !important',
        },
      },
      '& .accept-cms': {
        fontSize: theme.spacing(1),
        fontWeight: theme.typography.fontWeightBold
      },
      '& .MuiTypography-h3': {
        fontSize: theme.spacing(2),
        fontWeight: theme.typography.fontWeightBold,
        marginBottom: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(0.5)
        }
      },
      '& .MuiTypography-h4': {
        fontSize: theme.spacing(1.2),
        fontWeight: theme.typography.fontWeightBold,
        marginBottom: theme.spacing(1),
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(0.25)
        }
      },
      '& .MuiTypography-body1': {
        color: theme.colors.themeText,
        marginBottom: theme.spacing(1)
      }
    }
  }
}))
