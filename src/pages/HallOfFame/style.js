import { makeStyles } from '@mui/styles'

import { AffilateBanner } from '../../components/ui-kit/icons/banner'
import { fameTopCard, playerRankCard } from '../../components/ui-kit/icons/webp'
import { InnerBanner, LobbyRight, switchTabWrap } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    minHeight: 'auto',

    '& .fame-page': {
      maxWidth: theme.spacing(74.6875),
      margin: '0 auto',
      '& .fame-card-section': {
        margin: theme.spacing(3, 0, 4.375),
        [theme.breakpoints.down(767)]: {
          margin: theme.spacing(1.25, 0, 4.375)
        },
        '& .fame-card': {
          background: `url(${fameTopCard})`,
          backgroundSize: '100% 100%',
          minHeight: theme.spacing(14.375),
          padding: theme.spacing(1, 2),
          backgroundRepeat: 'no-repeat',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',

          [theme.breakpoints.down(767)]: {
            minHeight: theme.spacing(13.8125)
          },
          [theme.breakpoints.down(500)]: {
            minHeight: theme.spacing(11.8125)
          },

          '& .fame-card-content': {
            display: 'flex',
            justifyContent: 'center',
            gap: theme.spacing(0.625),
            marginTop: theme.spacing(0.1875),
            // marginBottom: theme.spacing(1),
            [theme.breakpoints.down(767)]: {
              gap: theme.spacing(1),
              alignItems: 'center'
            }
          },

          '& .instructions-text-web': {
            [theme.breakpoints.down('md')]: {
              display: 'none'
            }
          },
          '& .instructions-text-mob': {
            display: 'none',
            [theme.breakpoints.down('md')]: {
              display: 'block',
              marginTop: '0',
              textAlign: 'left',
              fontSize: theme.spacing(0.625),
              lineHeight: theme.spacing(0.7),
              marginBottom: theme.spacing(0)
            }
          },

          '& .mirror-text': {
            fontSize: theme.spacing(2.0625)
          }
        },
        '& .player-position-card': {
          marginTop: theme.spacing(4),
          [theme.breakpoints.down(1400)]: {
            marginTop: '0'
          }
        },
        '& .rank-img, & .first-rank-img': {
          textAlign: 'center',
          '& img': {
            [theme.breakpoints.down('xl')]: {
              width: theme.spacing(10)
            }
          }
        },
        '& .first-rank-img': {
          [theme.breakpoints.down('lg')]: {
            position: 'relative',
            top: theme.spacing(1.875),
            zIndex: 1,
            textAlign: 'center'
          },
          [theme.breakpoints.down('md')]: {
            top: theme.spacing(1)
          }
        },
        '& .rank-img': {
          position: 'relative',
          top: theme.spacing(1.875),
          zIndex: 1,
          textAlign: 'center',
          [theme.breakpoints.down('sm')]: {
            top: theme.spacing(1)
          }
        },

        '& .fame-card-grid': {
          '&  > .MuiGrid-root': {
            [theme.breakpoints.down('sm')]: {
              paddiing: '0'
            }
          },
          '&  .player-position-card, &  .position-first': {
            maxWidth: '100%',
            padding: theme.spacing(0, 0.625),
            [theme.breakpoints.down('sm')]: {
              width: '100%',
              margin: '0 auto'
            }
          },
          '& .swiper-horizontal': {
            width: '100%',
            [theme.breakpoints.down(767)]: {
              width: '80%',
              overflow: 'visible'
            },
            [theme.breakpoints.down(360)]: {
              width: '100%',
              overflow: 'visible'
            }
          }
        }
      },
      '& .fame-runner-section': {
        margin: theme.spacing(2, 0, 0),
        '& .fame-card': {
          background: `url(${playerRankCard})`,
          position: 'relative',
          backgroundSize: '100% 100%',
          padding: theme.spacing(1, 2, 1, 4.375),
          marginBottom: theme.spacing(1),
          minHeight: theme.spacing(14),
          [theme.breakpoints.down(1400)]: {
            paddingLeft: theme.spacing(3.2)
          },
          [theme.breakpoints.down('lg')]: {
            paddingLeft: theme.spacing(7)
          },
          [theme.breakpoints.down(1024)]: {
            paddingLeft: theme.spacing(5)
          },
          [theme.breakpoints.down(767)]: {
            minHeight: theme.spacing(14.8125),
            paddingLeft: theme.spacing(3.8),
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            justifyContent: 'center'
          },
          [theme.breakpoints.down(599)]: {
            paddingLeft: theme.spacing(4)
          },
          [theme.breakpoints.down(360)]: {
            paddingLeft: theme.spacing(3.5)
          },
          '&:before': {
            position: 'absolute',
            left: '0',
            top: '0',
            background: theme.colors.runnerCardGLow,
            content: "''",
            width: '98%',
            height: '98%',
            zIndex: '-1',
            filter: 'blur(10px)'
          },

          '& .rank-number': {
            position: 'absolute',
            left: theme.spacing(1.2),
            top: theme.spacing(0.625),
            fontSize: theme.spacing(1.875),
            fontWeight: '700',
            [theme.breakpoints.down(767)]: {
              top: theme.spacing(0.5),
              left: theme.spacing(1)
            },
            [theme.breakpoints.down(599)]: {
              top: theme.spacing(0.2),
              left: theme.spacing(1.5)
            },
            [theme.breakpoints.down(360)]: {
              top: theme.spacing(0.2),
              left: theme.spacing(1)
            }
          },

          '& .instructions-text-web': {
            display: 'block !important',
            textAlign: 'left !important'
          },
          '& .fame-card-content': {
            display: 'flex',
            gap: theme.spacing(2.6875),
            margin: theme.spacing(0.625, 0),
            width: '100%',
            [theme.breakpoints.down(1399)]: {
              gap: theme.spacing(1)
            },
            [theme.breakpoints.down(767)]: {
              alignItems: 'center'
            }
          }
        }
      },
      '& .fame-card-player': {
        maxHeight: theme.spacing(7.875),
        maxWidth: theme.spacing(5.75),
        minWidth: theme.spacing(5.75),
        borderRadius: theme.spacing(0.625),
        overflow: 'hidden',
        [theme.breakpoints.down(1399)]: {
          maxHeight: theme.spacing(6.3),
          maxWidth: theme.spacing(4),
          minWidth: theme.spacing(4)
        },
        [theme.breakpoints.down('md')]: {
          maxHeight: theme.spacing(6.3),
          maxWidth: theme.spacing(4.75),
          minWidth: theme.spacing(4.75)
        },
        '& img': {
          height: '100%',
          width: '100%',
          objectFit: 'cover'
        }
      },
      '& .fame-card-right': {
        display: 'flex',
        justifyContent: 'center',
        flexDirection: 'column',
        gap: theme.spacing(0.2),

        '& .inActive-btn': {
          backgroundColor: '#BDBDBD',
          color: '#FFFFFF',
          borderRadius: '30px',
          width: 'auto',
          maxWidth: 'fit-content',
          minWidth: '6.875rem',
          margin: '0.313rem 0rem 0rem',
          minHeight: '1.6875rem',
          lineHeight: '0.5625rem',
          fontWeight: '700'
        }
      },
      '& .fame-user-balance': {
        display: 'flex',
        gap: theme.spacing(0.5),
        padding: '0',
        alignItems: 'center',
        '& .fame-user-balance-coin': {
          height: theme.spacing(1.625),
          borderRadius: '100%',
          width: theme.spacing(1.625),
          minWidth: theme.spacing(1.625),
          margin: '0',
          border: `1px solid ${theme.colors.coinBorder}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          '& img': {
            width: theme.spacing(1),
            [theme.breakpoints.down('sm')]: {
              width: theme.spacing(0.8)
            }
          },
          [theme.breakpoints.down('md')]: {
            height: theme.spacing(1.2),
            width: theme.spacing(1.2),
            minWidth: theme.spacing(1.2)
          }
        },
        '& h4': {
          fontSize: theme.spacing(1.125),
          fontWeight: '700',
          color: theme.colors.YellowishOrange,
          textTransform: 'uppercase',
          [theme.breakpoints.down('md')]: {
            fontSize: `${theme.spacing(0.875)} !important`,
            width: theme.spacing(7),
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          },
          [theme.breakpoints.down(360)]: {
            fontSize: `${theme.spacing(0.75)} !important`
          }
        }
      },
      '& .position-first': {
        '& .fame-user-balance': {
          margin: '0',
          '& h4': {
            fontWeight: '700',
            textShadow: theme.shadows[18],
            WebkitBackgroundClip: 'text !important',
            WebkitTextFillColor: 'transparent',
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(0.75)
            }
          }
        }
      },
      '& .btn-primary': {
        width: '100%',
        minWidth: theme.spacing(9.5625),
        minHeight: theme.spacing(1.6875),
        fontSize: theme.spacing(0.75),
        lineHeight: theme.spacing(0.5625),
        margin: theme.spacing(0.313, 0, 0),
        [theme.breakpoints.down(1399)]: {
          minWidth: theme.spacing(6.875),
          maxWidth: 'fit-content',
          width: 'auto'
        }
        // [theme.breakpoints.down('md')]: {
        //   minWidth: theme.spacing(6.875),
        //   maxWidth: "fit-content",
        //   width: "auto"
        // },
      },
      '& .instructions-text-web, & .instructions-text-mob': {
        fontSize: theme.spacing(0.75),
        color: theme.colors.textWhite,
        textAlign: 'center',
        fontWeight: '500',
        marginTop: theme.spacing(0.8125),
        textTransform: 'capitalize',
        '& span': {
          fontWeight: '700',
          color: theme.colors.YellowishOrange
        }
      },
      '& .fame-user': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(0.5),
        padding: '0',
        '& img': {
          width: theme.spacing(1.9375),
          height: theme.spacing(1.9375),
          objectFit: 'cover',
          [theme.breakpoints.down('md')]: {
            height: theme.spacing(1.2),
            width: theme.spacing(1.2)
          }
        },
        '& h4': {
          fontSize: theme.spacing(1.125),
          fontWeight: '700',
          color: theme.colors.textWhite,
          textTransform: 'uppercase',
          width: theme.spacing(6.25),
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.75),
            whiteSpace: 'nowrap'
          }
        }
      }
    }
  },
  InnerBanner: {
    ...InnerBanner(theme),
    background: `url(${AffilateBanner})`,
    minHeight: theme.spacing(23.5625),
    [theme.breakpoints.down('md')]: {
      minHeight: theme.spacing(11.875),
      backgroundPosition: '70%',
      padding: theme.spacing(1),
      marginBottom: theme.spacing(3)
    },
    '& h4': {
      fontSize: theme.spacing(1.375),
      fontWeight: 700
    },
    '& p': {
      fontSize: `${theme.spacing(1)}!important`,
      textTransform: 'capitalize !important',
      maxWidth: '36rem !important',
      fontWeight: '600 !important'
    },
    '& h2': {
      '& span': {
        display: 'inline-block !important',
        paddingLeft: theme.spacing(0.5)
      }
    }
  },
  switchTabWrap: {
    ...switchTabWrap(theme)
  },
  lobbySearchWrap: {
    marginBottom: theme.spacing(3.5),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      display: 'none'
      // marginTop: theme.spacing(6.25),
    },
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1.25)
    },
    '& .MuiTextField-root': {
      width: '100%',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.Pastel}`,
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        '&::placeholder': {
          color: `${theme.colors.placeHolderText}!important`,
          fontSize: `${theme.spacing(1)}!important`,
          fontWeight: '500 !important'
        },

        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(2.18)
        }
      }
    }
  }
}))
