import { Box, Button, Grid, Tab, Tabs, Typography } from '@mui/material'
import React, { useState } from 'react'
import Signup from '../../components/Modal/Signup'
import userHeaderIcon from '../../components/ui-kit/icons/svg/userHeader.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './style'
import { useBannerStore, useCoinStore, useUserStore } from '../../store/store'
import win1 from '../../components/ui-kit/icons/webp/win1.webp'
import win2 from '../../components/ui-kit/icons/webp/win2.webp'
import win3 from '../../components/ui-kit/icons/webp/win3.webp'
import CasinoCard from '../../components/ui-kit/icons/utils/casinoGames.webp'
import { GeneralQuery, useGetProfileMutation } from '../../reactQuery'
import { getLoginToken } from '../../utils/storageUtils'
import MobileVerification from '../MobileVerification'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import { formatPriceWithCommas } from '../../utils/helpers'
import BannerManagement from '../../components/BannerManagement'
import { PlayerRoutes } from '../../routes'

const HallOfFame = () => {
  const portalStore = usePortalStore()
  const classes = useStyles()
  const userDetails = useUserStore((state) => state.userDetails)
  const [value, setValue] = useState(0)
  const [coinType, setCoinType] = useState('GC')
  const navigate = useNavigate()
  const auth = useUserStore((state) => state)
  const gameType = 'All Games'
  const { hallofFame } = useBannerStore((state) => state)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const [gameId, setGameId] = useState(null)
  const [name, setName] = useState('')
  const coin = useCoinStore((state) => {
    return state.coinType
  })

  const setCoin = useCoinStore((state) => {
    return state.setCoinType
  })

  const { data: hallOfFameData } = GeneralQuery.getHallOfFameQuery({
    params: { coinType: coinType }
  })

  const topThreeWinners = hallOfFameData?.records?.rows?.slice(0, 3) || []
  const lastSevenwinners = hallOfFameData?.records?.rows?.slice(3, 9) || []

  const handleChangeCoinType = (event, newValue) => {
    setValue(newValue)
    if (newValue === 0) {
      setCoinType('GC')
    } else {
      setCoinType('SC')
    }
  }

  const updateCoinType = () => {
    if (coinType !== coin) {
      setCoin(coinType)
      toast(`You've successfully switched to ${coinType} coin!`)
    }
  }

  const handlePlayNow = (masterCasinoGameId, name) => {
    if (!!getLoginToken() || auth.isAuthenticate) {
      if (coin === 'SC' && userDetails?.userWallet?.totalScCoin > 0) {
        updateCoinType()
        navigate(`/game-play/${masterCasinoGameId}`, { state: { name, gameType } })
      } else if (coin === 'GC' && userDetails?.userWallet?.gcCoin > 0) {
        updateCoinType()
        navigate(`/game-play/${masterCasinoGameId}`, { state: { name, gameType } })
      } else {
        toast.error('Please make a purchase!')
        navigate(PlayerRoutes.Store)
      }
    } else {
      portalStore.openPortal(() => <Signup />, 'loginModal')
    }
  }

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (!res?.data?.data?.phoneVerified) {
        portalStore.openPortal(
          () => <MobileVerification calledFor='gamePlay' handlePlayNow={() => handlePlayNow(gameId, name)} />,
          'innerModal'
        )
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })
  const handlePhoneVerification = (casinoGameId, gameName) => {
    if (coin === 'SC' && !auth?.userDetails?.phoneVerified && (!!getLoginToken() || auth.isAuthenticate)) {
      setGameId(casinoGameId)
      setName(gameName)
      getProfileMutation.mutate()
      return
    } else {
      handlePlayNow(casinoGameId, gameName)
    }
  }

  const handleGameUnavailable = () => {
    toast.error('This game is currently not available')
  }

  const handlePlayNowClick = (isActive, casinoGameId, gameName) => {
    if (isActive) handlePhoneVerification(casinoGameId, gameName)
    else handleGameUnavailable()
  }

  return (
    <Grid className={classes.lobbyRight}>
      <Box className='fame-page'>
        <Grid>
          <BannerManagement bannerData={hallofFame} />
        </Grid>
        <Grid className={classes.switchTabWrap}>
          <Tabs value={value} onChange={handleChangeCoinType} aria-label='basic tabs example'>
            <Tab
              icon={
                <div>
                  <img src={usdchipIcon} alt='coinIcon' className='image1' />
                </div>
              }
              iconPosition='start'
              label='Gold Coin'
              className='gold-coin-tab'
            />

            <Tab
              icon={
                <div>
                  <img src={usdIcon} alt='walletIcon' className='image1' />
                </div>
              }
              iconPosition='start'
              className='shweep-coin-tab'
              label='Sweep Coin'
            />
          </Tabs>
        </Grid>

        <Box className='fame-card-section'>
          <Grid container spacing={{ md: 0, lg: 1 }} className='fame-card-grid'>
            <Swiper
              effect={'coverflow'}
              grabCursor={true}
              centeredSlides={true}
              loop={true}
              slidesPerView={'3'}
              slideshadows={'true'}
              autoplay={false}
              spaceBetween={100}
              className='mySwiper'
              breakpoints={{
                // when window width is >= 320px
                320: {
                  slidesPerView: 1,
                  spaceBetween: 20,
                  initialSlide: 1,
                  loop: false
                  // autoplay: true,
                },
                // when window width is >= 640px
                768: {
                  slidesPerView: 2,
                  spaceBetween: 20,
                  loop: false, // Disable loop for larger screens
                  allowTouchMove: true,
                  initialSlide: 1,
                  centeredSlides: false
                },
                991: {
                  slidesPerView: 2,
                  spaceBetween: 20,
                  loop: false, // Disable loop for larger screens
                  allowTouchMove: true
                },

                1024: {
                  slidesPerView: 3, // Show all slides without slider
                  spaceBetween: 0,
                  centeredSlides: false, // Disable centering on larger screens
                  loop: false, // Disable loop for larger screens
                  allowTouchMove: false
                }
              }}
            >
              <SwiperSlide className=''>
                <Grid item xs={12} lg={4} className='player-position-card'>
                  <Grid className='rank-img'>
                    <img src={win3} alt='Player Card' />
                  </Grid>
                  <Grid className='fame-card'>
                    <Grid className='fame-card-content'>
                      <Box className='fame-card-player'>
                        <img src={topThreeWinners[2]?.gameImage || CasinoCard} alt='Player Card' />
                      </Box>
                      <Grid className='fame-card-right'>
                        <Grid className='fame-user'>
                          <img src={topThreeWinners[2]?.profileImage || userHeaderIcon} alt='User' />
                          <Typography variant='h4'>{topThreeWinners[2]?.userName}</Typography>
                        </Grid>
                        <Grid className='fame-user-balance'>
                          <Grid className='fame-user-balance-coin'>
                            <img src={coinType === 'GC' ? usdchipIcon : usdIcon} alt='User' />
                          </Grid>
                          <Typography variant='h4' style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {formatPriceWithCommas(topThreeWinners[2]?.amount)}
                          </Typography>
                        </Grid>
                        <Typography className='instructions-text-mob'>
                          User {topThreeWinners[2]?.userName?.toUpperCase()} won a massive{' '}
                          <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {formatPriceWithCommas(topThreeWinners[2]?.amount)}
                          </span>{' '}
                          playing on{' '}
                          <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {topThreeWinners[2]?.gameName}
                          </span>{' '}
                          from supplier {topThreeWinners[2]?.providerName}.
                        </Typography>

                        <Button
                          type='button'
                          className={`${topThreeWinners[2]?.isActive ? 'btn btn-primary' : 'inActive-btn'}`}
                          onClick={() => {
                            handlePlayNowClick(
                              topThreeWinners[2]?.isActive,
                              topThreeWinners[2]?.masterCasinoGameId,
                              topThreeWinners[2]?.gameName
                            )
                          }}
                        >
                          Play Now
                        </Button>
                      </Grid>
                    </Grid>
                    <Typography className='instructions-text-web'>
                      User {topThreeWinners[2]?.userName?.toUpperCase()} won a massive{' '}
                      <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                        {formatPriceWithCommas(topThreeWinners[2]?.amount)}
                      </span>{' '}
                      playing on{' '}
                      <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                        {topThreeWinners[2]?.gameName}
                      </span>{' '}
                      from supplier {topThreeWinners[2]?.providerName}.
                    </Typography>
                  </Grid>
                </Grid>
              </SwiperSlide>

              <SwiperSlide className=''>
                <Grid item xs={12} lg={4} className='position-first'>
                  <Grid className='first-rank-img'>
                    <img src={win1} alt='Player Card' />
                  </Grid>
                  <Grid className='fame-card'>
                    {/* <Grid className='mirror-text'>
                    <Typography variant='h4'>Winner Jackpot</Typography>
                  </Grid> */}
                    <Grid className='fame-card-content'>
                      <Box className='fame-card-player'>
                        <img src={topThreeWinners[0]?.gameImage || CasinoCard} alt='Player Card' />
                      </Box>
                      <Grid className='fame-card-right'>
                        <Grid className='fame-user-balance'>
                          {/* <Grid className='fame-user-balance-coin'>
                              <img src={usdchipIcon} alt="User" />
                            </Grid> */}
                          <Typography
                            variant='h4'
                            style={{
                              background:
                                coinType === 'GC'
                                  ? 'linear-gradient(180deg, #FDB72E 26.34%, #E37A34 72.57%)'
                                  : 'linear-gradient(180deg, #219653 26.34%, #185833 72.57%)'
                            }}
                          >
                            {formatPriceWithCommas(topThreeWinners[0]?.amount)} {coinType === 'GC' ? 'GC' : 'SC'}
                          </Typography>
                        </Grid>
                        <Grid className='fame-user'>
                          <img src={topThreeWinners[0]?.profileImage || userHeaderIcon} alt='User' />
                          <Typography variant='h4'>{topThreeWinners[0]?.userName}</Typography>
                        </Grid>
                        <Typography className='instructions-text-mob'>
                          User {topThreeWinners[0]?.userName?.toUpperCase()} won a massive{' '}
                          <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {formatPriceWithCommas(topThreeWinners[0]?.amount)}
                          </span>{' '}
                          playing on{' '}
                          <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {topThreeWinners[0]?.gameName}
                          </span>{' '}
                          from supplier {topThreeWinners[0]?.providerName}.
                        </Typography>

                        <Button
                          type='button'
                          className={`${topThreeWinners[0]?.isActive ? 'btn btn-primary' : 'inActive-btn'}`}
                          style={{ marginTop: '5px' }}
                          onClick={() => {
                            handlePlayNowClick(
                              topThreeWinners[0]?.isActive,
                              topThreeWinners[0]?.masterCasinoGameId,
                              topThreeWinners[0]?.gameName
                            )
                          }}
                        >
                          Play Now
                        </Button>
                      </Grid>
                    </Grid>
                    <Typography className='instructions-text-web'>
                      User {topThreeWinners[0]?.userName?.toUpperCase()} won a massive{' '}
                      <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                        {formatPriceWithCommas(topThreeWinners[0]?.amount)}
                      </span>{' '}
                      playing on{' '}
                      <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                        {topThreeWinners[0]?.gameName}
                      </span>{' '}
                      from supplier {topThreeWinners[0]?.providerName}.
                    </Typography>
                  </Grid>
                </Grid>
              </SwiperSlide>
              <SwiperSlide className=''>
                <Grid item xs={12} lg={4} className='player-position-card'>
                  <Grid className='rank-img'>
                    <img src={win2} alt='Player Card' />
                  </Grid>
                  <Grid className='fame-card'>
                    <Grid className='fame-card-content'>
                      <Box className='fame-card-player'>
                        <img src={topThreeWinners[1]?.gameImage || CasinoCard} alt='Player Card' />
                      </Box>
                      <Grid className='fame-card-right'>
                        <Grid className='fame-user'>
                          <img src={topThreeWinners[1]?.profileImage || userHeaderIcon} alt='User' />
                          <Typography variant='h4'>{topThreeWinners[1]?.userName}</Typography>
                        </Grid>
                        <Grid className='fame-user-balance'>
                          <Grid className='fame-user-balance-coin'>
                            <img src={coinType === 'GC' ? usdchipIcon : usdIcon} alt='User' />
                          </Grid>
                          <Typography variant='h4' style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {formatPriceWithCommas(topThreeWinners[1]?.amount)}
                          </Typography>
                        </Grid>
                        <Typography className='instructions-text-mob'>
                          User {topThreeWinners[1]?.userName?.toUpperCase()} won a massive{' '}
                          <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {formatPriceWithCommas(topThreeWinners[1]?.amount)}
                          </span>{' '}
                          playing on{' '}
                          <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {topThreeWinners[1]?.gameName}
                          </span>{' '}
                          from supplier {topThreeWinners[1]?.providerName}.
                        </Typography>

                        <Button
                          type='button'
                          className={`${topThreeWinners[1]?.isActive ? 'btn btn-primary' : 'inActive-btn'}`}
                          onClick={() => {
                            handlePlayNowClick(
                              topThreeWinners[1]?.isActive,
                              topThreeWinners[1]?.masterCasinoGameId,
                              topThreeWinners[1]?.gameName
                            )
                          }}
                        >
                          Play Now
                        </Button>
                      </Grid>
                    </Grid>
                    <Typography className='instructions-text-web'>
                      User {topThreeWinners[1]?.userName?.toUpperCase()} won a massive{' '}
                      <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                        {formatPriceWithCommas(topThreeWinners[1]?.amount)}
                      </span>{' '}
                      playing on{' '}
                      <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                        {topThreeWinners[1]?.gameName}
                      </span>{' '}
                      from supplier {topThreeWinners[1]?.providerName}.
                    </Typography>
                  </Grid>
                </Grid>
              </SwiperSlide>
            </Swiper>
          </Grid>
        </Box>

        <Box className='fame-runner-section'>
          <Grid container spacing={1}>
            {lastSevenwinners?.map((item, index) => {
              return (
                <Grid item xs={12} sm={6} lg={4}>
                  <Grid className='fame-card'>
                    <Grid className='rank-number'>{index + 4}</Grid>
                    <Grid className='fame-card-content'>
                      <Grid className='fame-card-player'>
                        <img src={item?.gameImage || CasinoCard} alt='Player Card' />
                      </Grid>
                      <Grid className='fame-card-right'>
                        <Grid className='fame-user'>
                          <img src={item?.profileImage || userHeaderIcon} alt='User' />
                          <Typography variant='h4'>{item?.userName}</Typography>
                        </Grid>
                        <Grid className='fame-user-balance'>
                          <Grid className='fame-user-balance-coin'>
                            <img src={coinType === 'GC' ? usdchipIcon : usdIcon} alt='User' />
                          </Grid>
                          <Typography variant='h4' style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                            {formatPriceWithCommas(item?.amount)}
                          </Typography>
                        </Grid>
                        {/* <Typography className='instructions-text-mob'>User {item?.userName?.toUpperCase()} won a massive <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>{item?.amount.toLocaleString()}</span> playing on <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>{item?.gameName}</span> from supplier {item?.providerName}.</Typography> */}

                        <Button
                          type='button'
                          className={`${item?.isActive ? 'btn btn-primary' : 'inActive-btn'}`}
                          onClick={() => {
                            handlePlayNowClick(item?.isActive, item?.masterCasinoGameId, item?.gameName)
                          }}
                        >
                          Play Now
                        </Button>
                      </Grid>
                    </Grid>
                    <Typography className='instructions-text-web'>
                      User {item?.userName?.toUpperCase()} won a massive{' '}
                      <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>
                        {formatPriceWithCommas(item?.amount)}
                      </span>{' '}
                      playing on{' '}
                      <span style={{ color: coinType === 'GC' ? '#FDB72E' : '#219653' }}>{item?.gameName}</span> from
                      supplier {item?.providerName}.
                    </Typography>
                  </Grid>
                </Grid>
              )
            })}
          </Grid>
        </Box>
      </Box>
    </Grid>
  )
}

export default HallOfFame
