import * as React from 'react'
import { useNavigate } from 'react-router-dom'
import { ButtonPrimary } from '../../MainPage.styles'
import { useEffect } from 'react'
import { useUserStore } from '../../store/useUserSlice'
import TagManager from 'react-gtm-module'
import { Box, Button, Typography } from '@mui/material'

const NotFoundPage = () => {
  const navigate = useNavigate()
  const userDetails = useUserStore((state) => state.userDetails)

  useEffect(() => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'error_404',
        user_id: userDetails?.userId
      }
    })
  }, [])

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        width: '100vw'
      }}
    >
      <Typography variant='h1' component='h1' sx={{ m: 0, alignItems: 'center', display: 'flex' }}>
        4
        <Box
          component='span'
          sx={{
            display: 'flex',
            bgcolor: 'red',
            borderRadius: '50%',
            color: 'white',
            width: '70px',
            height: '70px',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Typography variant='h2' fontWeight='bold'>
            !
          </Typography>
        </Box>
        4
      </Typography>
      <Typography variant='h4' component='h4' sx={{ mt: 3 }}>
        Oops! You're lost.
      </Typography>
      <Typography variant='body1' sx={{ mt: 1 }}>
        The page you are looking for was not found.
      </Typography>
      <Button
        variant='contained'
        sx={(theme) => ({
          mt: 3,
          ...ButtonPrimary(theme),
          '&:hover': { backgroundColor: theme.colors.YellowishOrange, opacity: 0.8 }
        })}
        onClick={() => navigate('/')}
      >
        Back to Home
      </Button>
    </Box>
  )
}

export default NotFoundPage
