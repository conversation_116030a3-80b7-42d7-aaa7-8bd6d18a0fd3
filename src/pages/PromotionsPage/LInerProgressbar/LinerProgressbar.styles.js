
import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
    linerProgressWrap: {
        position: "relative",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
        width: "100%",
        margin: theme.spacing(1, 0),
        "& .value": {
            position: "absolute",
            left: "50%",
            top: "10%",
            transform: "translate(-50%,-10%)",
            fontSize: "calc(5vmax + 44px)",
            color: "darkred"
        },
        "& .range-input": {
            background: theme.colors.promoProgressGradient,
            appearance: "none",
            width: "100%",
            height: theme.spacing(.75),
            padding: "0px",
            borderRadius: "5.5px",

        },
        '& input[type="range" i]::-webkit-slider-thumb': {
            WebkitAppearance: "none",
            height: "20px",
            width: "20px",
            border: "4px solid #FFF",
            borderRadius: "50%",
            background: theme.colors.YellowishOrange,
            marginTop: theme.spacing(-.125),
            cursor: "pointer",
            boxShadow: "0 2px 4px 0 rgba(0, 0, 44, 0.5)"
        },










        "input[type=range]": {
            WebkitAppearance: "none",
            width: "90%",
            height: "9px",
            padding: "0",
            borderRadius: "5.5px",
            border: "solid 1px #dfe4ec"
        },
        "input[type=range]::-webkit-slider-runnable-track": {
            width: "100%",
            height: "9px",
            borderRadius: "5.5px",
            border: "0"
        },
        "input[type=range]::-webkit-slider-thumb": {
            WebkitAppearance: "none",
            height: "20px",
            width: "20px",
            border: "4px solid #FFF",
            borderRadius: "50%",
            background: "darkred",
            marginTop: "-6px",
            cursor: "pointer",
            boxShadow: "0 2px 4px 0 rgba(0, 0, 44, .5)"
        },
        "input[type=range]:focus": { outline: "none" },
        "input[type=range]:focus::-webkit-slider-runnable-track": {
            "//backgroundColor": "#fa4359",
            cursor: "pointer"
        },
        "input[type=range]::-moz-range-track": {
            width: "100%",
            height: "9px",
            borderRadius: "5.5px",
            "//backgroundColor": "#fa4359",
            backgroundColor: "transparent",
            border: "none",
            cursor: "pointer"
        },
        "input[type=range]::-moz-range-thumb": {
            height: "12px",
            width: "12px",
            border: "4px solid #FFF",
            borderRadius: "50%",
            background: "darkred",
            cursor: "pointer",
            boxShadow: "0 2px 4px 0 rgba(0, 0, 44, .5)"
        },
        "input[type=range]:-moz-focusring": { outline: "none" },
        "input[type=range]::-moz-focus-outer": { border: "0" },
        "input[type=range]::-ms-track": {
            width: "100%",
            height: "5px",
            background: "transparent",
            borderColor: "transparent",
            borderWidth: "6px 0",
            color: "transparent",
            cursor: "pointer"
        },
        "input[type=range]::-ms-fill-lower": {
            background: "#777",
            borderRadius: "10px"
        },
        "input[type=range]::-ms-fill-upper": {
            background: "#ddd",
            borderRadius: "10px"
        },
        "input[type=range]::-ms-thumb": {
            height: "20px",
            width: "20px",
            border: "4px solid #FFF",
            borderRadius: "50%",
            background: "darkred",
            cursor: "pointer",
            boxShadow: "0 2px 4px 0 rgba(0, 0, 44, .5)"
        },
        "input[type=range]:focus::-ms-fill-lower": { background: "#888" },
        "input[type=range]:focus::-ms-fill-upper": { background: "#ccc" }
    },



}))
