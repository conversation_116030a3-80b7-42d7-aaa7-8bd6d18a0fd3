import React from 'react'
import { <PERSON>rid, IconButton, <PERSON><PERSON>graphy, DialogContent, TableContainer, Table, TableHead, TableRow, TableCell, TableBody } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import useStyles from './Promotions.styles'
import { usePortalStore } from '../../store/userPortalSlice'
import { formatDateYMD } from '../../utils/dateFormatter'
/* eslint-disable multiline-ternary */

const ViewRaffleTicketDetail = (props) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <Grid className={classes.bonusModalWrap}>
      <Typography variant='h4' className='title'>
        {' '}
        Ticket Entry{' '}
      </Typography>
      <Grid className='modal-close'>
        <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
          <CloseIcon />
        </IconButton>
      </Grid>

      <DialogContent>
        <Grid>
          <TableContainer style={{ margin: '20px 0px' }}>
            <Grid className='leaderBoardContainer-wrap'>
              <Table aria-label='a dense table'>
                <TableHead>
                  <TableRow>
                    <TableCell className='table-headings'>Entry Id</TableCell>
                    <TableCell className='table-headings'>Awarded Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {props?.ticketData?.length ? (
                    props?.ticketData?.map((item, index) => {
                      return (
                        <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                          <TableCell style={{ flex: 1, color: '#fff', fontSize: '20px' }} scope='row'>
                            {item?.entryId}
                          </TableCell>
                          <TableCell style={{ flex: 1, color: '#fff', fontSize: '20px' }} scope='row'>
                            {formatDateYMD(item?.createdAt)}
                          </TableCell>
                        </TableRow>
                      )
                    })
                  ) : (
                    <TableRow style={{ background: 'transparent' }}>
                      <TableCell colSpan={10} style={{ textAlign: 'center', color: 'red' }}>
                        <span>No Tickets Found</span>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Grid>
          </TableContainer>
        </Grid>
      </DialogContent>
    </Grid>
  )
}
export default ViewRaffleTicketDetail
