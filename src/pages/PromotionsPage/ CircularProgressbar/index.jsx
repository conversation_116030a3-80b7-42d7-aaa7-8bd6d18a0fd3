import React from 'react'

const CircularProgressbar = ({ daysLeft = '00', percentage = 50 }) => {
  const radius = 35 // Updated radius to fit the smaller container
  const circumference = 2 * Math.PI * radius // Circumference of the circle
  const cx = 40 // Updated X-coordinate of the circle center
  const cy = 40 // Updated Y-coordinate of the circle center

  // Calculate stroke-dashoffset for the progress bar
  const offset = circumference - (percentage / 100) * circumference

  // Calculate pointer position
  const angle = (percentage / 100) * 360 // Angle in degrees
  const radians = (angle - 90) * (Math.PI / 180) // Convert angle to radians
  const pointerX = cx + radius * Math.cos(radians) // X position of the pointer
  const pointerY = cy + radius * Math.sin(radians) // Y position of the pointer

  // Precompute left and top values
  const left = `${pointerX}px`
  const top = `${pointerY}px`

  // Define styles
  const styles = {
    container: {
      position: 'relative',
      width: '81px', // Updated width
      height: '82px', // Updated height
      filter: 'drop-shadow( 1px 1px 4px #00000040)'
    },
    svg: {
      transform: 'rotate(-90deg)' // Start progress at the top
    },
    track: {
      fill: 'none',
      stroke: '#0E0E0E',
      strokeWidth: '10'
    },
    progress: {
      fill: 'none',
      stroke: '#FDB72E',
      strokeWidth: '10',
      strokeLinecap: 'round',
      transition: 'stroke-dashoffset 0.5s ease',
      strokeDasharray: circumference,
      strokeDashoffset: offset
    },
    pointer: {
      position: 'absolute',
      width: '16px',
      height: '16px',
      backgroundColor: '#FDB72E',
      borderRadius: '50%',
      transform: 'translate(-50%, -50%)',
      left, // Use precomputed value
      top // Use precomputed value
    },
    progressValue: {
      position: 'absolute',
      left: '50%',
      top: '50%',
      fontWeight: '700',
      fontSize: '2rem',
      transform: 'translate(-50%, -50%)'
    }
  }

  return (
    <div style={styles.container}>
      <svg width='80' height='80' style={styles.svg}>
        <circle cx={cx} cy={cy} r={radius} style={styles.track} />
        {percentage > 0 && <circle cx={cx} cy={cy} r={radius} style={styles.progress} />}
      </svg>
      {percentage > 0 && <div style={styles.pointer} />}
      <div style={styles.progressValue}>{daysLeft}</div>
    </div>
  )
}

export default CircularProgressbar
