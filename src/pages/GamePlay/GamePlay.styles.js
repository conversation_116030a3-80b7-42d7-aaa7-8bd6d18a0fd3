import { makeStyles } from '@mui/styles'

import mainborder from '../../components/ui-kit/icons/png/jackpot-bg.png'
import { LobbyRight } from '../../MainPage.styles'

const DIGIT_HEIGHT = 32
const MOBILE_DIGIT_HEIGHT = 22

export default makeStyles((theme) => ({
  lobbyWrap: {
    height: 'calc(100% - 90px)'
  },
  '& .main-page': {
    height: '100vh'
  },
  lobbyRight: {
    ...LobbyRight(theme),
    marginLeft: '0',
    width: '100%',

    '& .game-play-wrap': {
      height: 'calc(100dvh - 161px)',
      // [theme.breakpoints.down('sm')]: {
      //   height: 'calc(100dvh - 220px)'
      // },
      '& .game-frame': {
        boxShadow: theme.shadows[10],
        height: '100%',
        '& .payment-gif': {
          position: 'absolute',
          width: '100%',
          height: '100%',
          '& .popper-animation': {
            position: 'absolute',
            width: '100%',
            height: '100%'
          }
        },
        '& iframe': {
          border: 'none'
        }
      }
    },
    '& .fullscreen-mode': {
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      zIndex: 9999,
      background: 'black'
    },
    '& .game-play-cta-wrap': {
      background: theme.colors.gamePlayBg,
      borderRadius: ' 0px 0px 20px 20px',
      padding: theme.spacing(0.25, 1),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(0.5, 0)
      },
      '& .game-play-cta-left': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(0.4),
        [theme.breakpoints.down('md')]: {
          display: 'none'
        },
        '& a': {
          display: 'flex',
          margin: theme.spacing(0, 0.313),
          cursor: 'pointer',
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(0, 0.15),
            margin: theme.spacing(0)
          },
          '& img': {
            width: theme.spacing(1.2),
            height: theme.spacing(1.2),
            [theme.breakpoints.down('md')]: {
              width: theme.spacing(1),
              height: theme.spacing(1)
            }
          }
        }
      },
      '& .mobile-menu-wrapper': {
        display: 'none',
        [theme.breakpoints.down('md')]: {
          display: 'block'
        },
        '& .hamburger-toggle': {
          paddingLeft: '10px',
          '& svg': {
            fill: '#FDB72E'
          }
        },
        '& .mobile-floating-menu': {
          position: 'absolute',
          bottom: '35px',
          left: '0px',
          padding: '10px',
          background: '#161616bd',
          transform: 'translateX(-100px)',
          transition: '0.5s all ease-in-out',
          borderRadius: '0 5px 5px 0',
          display: 'flex',
          flexDirection: 'column',
          gap: '12px',
          '& a': {
            display: 'flex',
            margin: theme.spacing(0, 0.313),
            cursor: 'pointer',

            '& img': {
              width: theme.spacing(1.2),
              height: theme.spacing(1.2)
            }
          },
          '&.open': {
            transform: 'translateX(0%)'
          }
        }
      },
      '& .game-play-logo': {
        [theme.breakpoints.down('md')]: {
          display: 'none'
        },
        '& img': {
          filter: 'grayscale(1)',
          width: '80px',
          [theme.breakpoints.down('md')]: {
            width: '40px'
          }
        }
      },
      '& .MuiTypography-body1': {
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.75)
        }
      },
      '& .jackpot-grid': {
        display: 'flex',
        alignItems: 'center',
        gap: '1.5rem',
        [theme.breakpoints.down('md')]: {
          gap: '1rem'
        },
        '& .mode-wrap': {
          display: 'flex',
          alignItems: 'center',
          gap: '1.5rem',
          [theme.breakpoints.down('md')]: {
            gap: '0.5rem',
            flexDirection: 'column'
          },
          '&.jackpot-enabled': {
            '& .multiplier-bar ': {
              width: '250px',
              [theme.breakpoints.down('md')]: {
                width: '130px',
                height: '25px'
              }
            },
            '& .multiplier-thumb': {
              [theme.breakpoints.down('md')]: {
                width: '25px',
                height: '25px',
                fontSize: '12px'
              }
            },
            '& .progress-bar-wrapper': {
              [theme.breakpoints.down('md')]: {
                height: '25px',
                marginLeft: '3px'
              },
              '& .progress-background-bar': {
                [theme.breakpoints.down('md')]: {
                  top: '7px',
                  height: '12px'
                }
              },
              '& .progress-bar-filled': {
                [theme.breakpoints.down('md')]: {
                  top: '7px',
                  height: '12px'
                }
              },
              '& .progress-bolt': {
                [theme.breakpoints.down('md')]: {
                  top: 0
                }
              },
              '& .MuiSlider-root': {
                padding: '13px 10px'
              }
            }
          }
        },
        '& .jackpot-badge': {
          // position: 'fixed',
          background: `url(${mainborder})`,
          backgroundSize: '100% 100%',
          width: '100%',
          // top: '100px',
          zIndex: '3',
          height: '100%',
          borderRadius: '5rem',
          display: 'flex',
          gap: '0.5rem',
          alignItems: 'center',
          padding: '0.875rem 1rem 0.875rem 1rem',
          maxWidth: '190px',
          maxHeight: '45px',
          // left: '50%',
          // transform: 'translate(-50% , 0%)',
          [theme.breakpoints.down('md')]: {
            padding: '0.875rem 0.65rem 0.65rem 0.65rem',
            maxWidth: '140px',
            maxHeight: '40px',
            gap: '0.25rem'
            // bottom: '60px',
            // top: 'auto'
          },

          '& .cash-icon': {
            width: '1.5rem',
            [theme.breakpoints.down('md')]: {
              width: '1.125rem'
            }
          },
          '& .MuiTypography-root': {
            fontSize: '1.25rem',
            fontWeight: '700',
            background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            color: 'transparent',
            whiteSpace: 'nowrap',
            WebkitTextStroke: '0.5px #BA5C25',
            textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
            lineWeight: '1.2',
            minWidth: '100px',
            paddingTop: '3px',
            [theme.breakpoints.down('md')]: {
              fontSize: '0.875rem',
              minWidth: '90px',
              gap: '1px'
            }
          },
          '& .tmf-jackpot': {
            position: 'absolute',
            width: '100px',
            top: '-10px',
            left: '50%',
            transform: 'translate(-52% , 0%) scale(1)',
            [theme.breakpoints.down('md')]: {
              width: '75px',
              top: '-5px'
            }
          }
        },
        '& .jackpot-mode': {
          display: 'flex',
          justifyContent: 'center',
          // marginTop: '1rem',
          '& .jackpot-box': {
            maxWidth: '350px',
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderRadius: '2rem',
            padding: '0rem 1rem',
            [theme.breakpoints.down('md')]: {
              padding: '0rem 0.5rem',
              maxHeight: '35px',
              maxWidth: '125px'
            },
            '& .MuiSwitch-root ': {
              [theme.breakpoints.down('md')]: {
                transform: 'scale(0.7)',
                height: '40px',
                left: '-12px'
              }
            },
            '&.jackpot-off': {
              border: '3px solid #7c0003',
              '& .MuiSwitch-thumb': {
                border: '1px solid #7c0003',
                background: '#7c0003'
              },
              '& .MuiSwitch-track': {
                border: '2px solid #7c0003',
                background: 'transparent'
              }
            },
            '&.jackpot-on': {
              border: '3px solid rgb(0, 255, 21)',
              '& .MuiSwitch-thumb': {
                border: '1px solid rgb(0, 255, 21)',
                background: 'rgb(0, 255, 21)'
              },
              '& .MuiSwitch-track': {
                border: '1px solid rgb(0, 255, 21)',
                background: 'transparent'
              }
            },
            '& .MuiTypography-root': {
              fontSize: '1rem',
              fontWeight: '600',
              textTransform: 'uppercase',
              whiteSpace: 'nowrap',
              [theme.breakpoints.down('md')]: {
                fontSize: '0.675rem',
                lineHeight: '1',
                marginTop: '0.125rem'
              }
            }
          }
        },
        '& h5': {
          color: '#B9B7B7',
          fontSize: '0.75rem',
          fontWeight: '600',
          maxWidth: '280px',
          margin: '0 auto',
          lineHeight: '0',
          // marginTop: '1rem',
          textAlign: 'center',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.425rem',
            maxWidth: '120px',
            lineHeight: '0'
          }
        }
      }
    },
    '& .modal-loader': {
      position: 'absolute',
      width: '100%',
      height: '100%',
      top: '0',
      left: '0',
      background: theme.colors.modalLoaderBg,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: '2',
      backdropFilter: 'blur(20px)',
      fontSize: theme.spacing(2),
      '& p': {
        color: theme.colors.white
      },
      '& .daily-bonus-modal-wrap': {
        minWidth: '900px'
      }
    },
    '& .fullscreen': {
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      background: 'white',
      zIndex: 9999,
      overflow: 'auto',
      padding: 0
    }
  },

  digitContainer: {
    overflow: 'hidden',
    height: `${DIGIT_HEIGHT}px`,
    width: '10px',
    display: 'inline-block',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      height: `${MOBILE_DIGIT_HEIGHT}px`,
      width: '6px'
    }
  },

  digitStrip: {
    transition: 'transform 0.3s ease-out',
    willChange: 'transform'
  },

  digit: {
    height: `${DIGIT_HEIGHT}px`,
    lineHeight: `${DIGIT_HEIGHT}px`,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: 700,
    fontSize: '22px',
    background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    color: 'transparent',
    WebkitTextStroke: '0.5px #BA5C25',
    textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
    cursor: 'pointer',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    [theme.breakpoints.down('md')]: {
      height: `${MOBILE_DIGIT_HEIGHT}px`,
      lineHeight: `${MOBILE_DIGIT_HEIGHT}px`,
      fontSize: '14px',
      paddingTop: '0px'
    }
  },

  digitsWrapper: {
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    gap: '2px'
  },

  scText: {
    fontWeight: 700,
    fontSize: '22px',
    background: 'linear-gradient(178deg, #FCFF36 26.61%, #F7E041 44.99%, #ffae58 51.62%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    color: 'transparent',
    WebkitTextStroke: '0.5px #BA5C25',
    textShadow: '0px 4.66px 4.66px rgba(0, 0, 0, 0.3)',
    [theme.breakpoints.down('md')]: {
      fontSize: '14px',
      paddingTop: 0
    }
  }
}))
