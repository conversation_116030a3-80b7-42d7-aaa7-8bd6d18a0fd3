import React, { useState } from 'react'
import useStyles from './insufficient.style'
import { Box, Button, Grid, Typography, DialogContent, IconButton, CircularProgress } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import BonusBadge from '../../components/ui-kit/icons/svg/bonus.svg'
import FreeTag from '../../components/ui-kit/icons/svg/free.svg'
import Specialbanner from '../../components/ui-kit/icons/svg/specialbanner.svg'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import offerGraphics from '../../components/ui-kit/icons/opImages/offer-graphics.webp'
import smallCoinGraphic from '../../components/ui-kit/icons/opImages/small-coin-graphic.webp'
import usePackages from '../Store/hooks/usePackages'
import { formatPriceWithCommas, formatValueWithB, formatAmount } from '../../utils/helpers'
import { useGetProfileMutation } from '../../reactQuery'
import StepperForm from '../../components/StepperForm'
import usePaysafePayment from '../Store/PaymentModal/hooks/usePaysafe'
import PaymentLoader from '../../components/Loader/PaymentLoader'
import { usePaymentProcessStore } from '../../store/store'

const InsufficientBalancePopup = () => {
  const portalStore = usePortalStore()
  const classes = useStyles()
  const [paymentData, setPaymentData] = useState(null)
  const [packageDetails, setPackageDetails] = useState()
  const { isPaymentScreenLoading, setIsPaymentScreenLoading } = usePaysafePayment()
  const cancelDepositOpen = usePaymentProcessStore((state) => state.cancelDepositOpen)

  const { packageData, isloading } = usePackages()
  const packageRow = packageData?.rows?.[0]

  const setPaymentHandle = (res) => {
    setPaymentData(res)
  }
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setIsPaymentScreenLoading(false)
      portalStore.openPortal(
        () => (
          <>
            <Box className='stepper-outer-box'>
              <StepperForm
                stepperCalledFor={'purchase'}
                packageDetails={packageDetails}
                setPaymentHandle={setPaymentHandle}
              />
            </Box>
          </>
        ),
        'StepperModal'
      )
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const handleBuyNow = (item) => {
    setIsPaymentScreenLoading(true)
    ;(function () {
      window._conv_q = window._conv_q || []
      _conv_q.push(['pushRevenue', 'credit', item, '100466670'])
    })()
    setPackageDetails(item)

    getProfileMutation.mutate()
  }
  const handleClose = () => {
    setPaymentData(null)
    portalStore.closePortal()
  }

  return (
    <DialogContent>
      <IconButton onClick={handleClose} className='close-btn'>
        <CloseIcon />
      </IconButton>
      {(isPaymentScreenLoading || cancelDepositOpen) && <PaymentLoader />}
      <Grid>
        <Typography variant='h4' className='modal-header'>
          Exclusive Offers Await !
        </Typography>
        <Typography variant='h6' className='modal-subheader'>
          Enjoy immediate benefits with your purchase
        </Typography>
      </Grid>
      <Grid className={classes.lobbyRight}>
        <Box className='package-page'>
          {isloading && (
            <div>
              <CircularProgress size={24} style={{ marginLeft: 8 }} />
              Loading...
            </div>
          )}
          {packageRow?.isSpecialPackage === true && (
            <Grid className='special-offer-section'>
              <Grid className='package-card'>
                <Grid container spacing={0.5}>
                  <Grid item xs={6} lg={3}>
                    <img
                      src={packageRow?.imageUrl ? packageRow?.imageUrl : offerGraphics}
                      alt='Offer'
                      className='offer-graphics'
                    />
                  </Grid>
                  <Grid item xs={12} lg={6} className='package-details-grid'>
                    <Box className={classes.container}>
                      <Grid className='package-details' spacing={0.5} justifyContent='center' alignItems='center'>
                        <Grid className='package-content'>
                          <Grid className='package-price'>
                            <img src={usdchipIcon} alt='coins' />
                            {packageRow?.gcCoin} GC +
                          </Grid>
                          <Grid className='package-price'>
                            <img src={FreeTag} alt='free' className='free-tag' />
                          </Grid>
                          <Grid className='package-price'>
                            <img src={usdIcon} alt='free' />
                            {packageRow?.scCoin} SC
                          </Grid>
                        </Grid>
                        <Button
                          className='btn btn-primary'
                          onClick={() => handleBuyNow(packageRow)}
                          data-tracking={`Store.${packageRow?.packageName}.Offer`}
                          data-tracking-item-id={packageRow?.packageId}
                          data-tracking-item-price={formatAmount(packageRow?.amount)}
                          data-tracking-item-name={`${packageRow?.packageName}`}
                          data-tracking-item-list={'User.Store.Main'}
                          data-tracking-item-catalog={'Special_Package'}
                        >
                          ${formatAmount(packageRow?.amount)}
                        </Button>
                      </Grid>
                    </Box>
                  </Grid>
                  <Grid item xs={6} lg={3} className='badge-grid'>
                    <Grid className='offer-badge'>
                      <img src={Specialbanner} alt='Offer' />
                      <Grid className='sp-offer-badge-content'>
                        {packageRow?.bonusPercentage > 0 ? (
                          <>
                            <Typography variant='h5'>{packageRow?.bonusPercentage}%</Typography>
                            <Typography>Bonus</Typography>
                          </>
                        ) : (
                          <Typography>SPECIAL OFFER</Typography>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          )}
          <Grid className='purchase-section'>
            <Grid className='purchase-section-content'>
              {packageData?.rows?.length > 0
                ? packageData?.rows
                    ?.filter((packageRow) => !packageRow?.isSpecialPackage || packageRow?.isSpecialPackage === true)
                    ?.slice(packageRow?.isSpecialPackage ? 1 : 0)
                    .map((item, index) => {
                      return (
                        <Grid className='package-card' key={`package-${item?.packageId}`}>
                          <Grid container spacing={0.5}>
                            <Grid item xs={6} md={6} lg={3}>
                              <Grid className='package-img-wrap order-1'>
                                <>
                                  {item?.bonusPercentage > 0 && (
                                    <Grid className='package-img'>
                                      <img src={BonusBadge} alt='Badge' className='bonus-badge' />
                                      <Grid className='offer-badge-content-text'>
                                        <Typography variant='h5'>{item?.bonusPercentage}%</Typography>
                                        <Typography>Bonus</Typography>
                                      </Grid>
                                    </Grid>
                                  )}
                                </>

                                <img
                                  src={item?.imageUrl ? item?.imageUrl : smallCoinGraphic}
                                  alt='Bonus'
                                  className='bonus-graphics'
                                />
                              </Grid>
                            </Grid>
                            <Grid item xs={12} md={12} lg={6} className='order-3'>
                              <Grid className='package-details' spacing={1} justifyContent='center' alignItems='center'>
                                <Grid className='package-content'>
                                  <Grid className='package-price'>
                                    <img src={usdchipIcon} alt='coins' />
                                    {item?.gcCoin > 0
                                      ? formatPriceWithCommas(formatValueWithB(Number(item?.gcCoin)))
                                      : item?.gcCoin}{' '}
                                    GC +
                                  </Grid>
                                  <Grid className='package-price'>
                                    <img src={FreeTag} alt='free' className='free-tag' />
                                  </Grid>
                                  <Grid className='package-price'>
                                    <img src={usdIcon} alt='free' />
                                    {item?.scCoin > 0
                                      ? formatPriceWithCommas(formatValueWithB(Number(item?.scCoin)))
                                      : item?.scCoin}{' '}
                                    SC
                                  </Grid>
                                </Grid>
                              </Grid>
                            </Grid>
                            <Grid item xs={6} md={6} lg={3} className='order-2'>
                              <Grid className='package-btn-wrap'>
                                <Button
                                  className='btn btn-primary'
                                  onClick={() => handleBuyNow(item)}
                                  data-tracking={`Store.${item?.packageName}.Offer`}
                                  data-tracking-item-id={item?.packageId}
                                  data-tracking-item-price={formatAmount(item?.amount)}
                                  data-tracking-item-name={`${item?.packageName}`}
                                  data-tracking-item-list={'User.Store.Main'}
                                  data-tracking-item-catalog={'Basic_Package'}
                                >
                                  ${formatAmount(item?.amount)}
                                </Button>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                      )
                    })
                : 'No Active Packages '}
            </Grid>
          </Grid>
        </Box>
      </Grid>
    </DialogContent>
  )
}

export default InsufficientBalancePopup
