import React from 'react'
import { Dialog, DialogContent, Typography, Button, IconButton, Box, Grid } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { Swiper, SwiperSlide } from 'swiper/react'
import useStyles from '../TournamentsPage/Tournaments.styles'
import { Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import priceBadge from '../../components/ui-kit/icons/webp/card-badge.webp'
import { formatPriceWithCommas } from '../../utils/helpers'
import vipLock from '../../components/ui-kit/icons/svg/vip-lock.svg'
import defaultVipTournamentImage from '../../components/ui-kit/icons/webp/vip-card.webp'
import defaultTournamentImage from '../../components/ui-kit/icons/webp/tournament-4.webp'

const TournamentPopup = ({
  tournamentData,
  open,
  onClose,
  hanlePlayWithoutTournament = () => { },
  handlePlayInTournament = () => { },
  handleJoinTournament = () => { }
}) => {
  const classes = useStyles()

  return (
    <Dialog open={open} onClose={hanlePlayWithoutTournament} maxWidth='md' fullWidth className={classes.tournamentModal}>
      <DialogContent>
        <IconButton onClick={hanlePlayWithoutTournament} className='close-btn'>
          <CloseIcon />
        </IconButton>
        <Grid className='modal-header'>
          <Typography variant='h4'>HURRY UP!</Typography>
          <Typography>
            The clock is ticking!
            <br /> Make your move before it’s too late.
          </Typography>
        </Grid>

        <Box className='modal-slider-wrap'>
          <Swiper
            spaceBetween={0}
            slidesPerView={1}
            modules={[Navigation]}
            navigation={true}
            breakpoints={{
              0: {
                slidesPerView: 1,
                spaceBetween: 0
              },
              768: {
                slidesPerView: 1,
                spaceBetween: 0
              }
            }}
          >
            {tournamentData?.map((tournament, index) => {
              const isVIP = tournament?.vipTournament // `isVIP` indicates whether it's a VIP tournament
              const isUserAllowedForVIP = tournament?.isUserAllowedInVipTournament && isVIP // Check if the user is allowed for VIP tournaments
              return (
                <SwiperSlide key={index}>
                  <Box
                    className={`${classes.tournamentItems} modal-card ${isVIP ? 'vip-tournament' : ''}`}
                    style={{
                      backgroundImage: isVIP
                        ? tournament?.imageUrl
                          ? `url(${tournament?.imageUrl})`
                          : `url(${defaultVipTournamentImage})`
                        : tournament?.imageUrl
                          ? `url(${tournament?.imageUrl})`
                          : `url(${defaultTournamentImage})`
                    }}
                  >
                    {isVIP && !isUserAllowedForVIP && (
                      <Box className='vip-overlay'>
                        <Box className='vip-icon'>
                          <img src={vipLock} alt='lock' />
                        </Box>
                        {tournament?.vipTournamentTitle ? tournament?.vipTournamentTitle : 'Only VIP user can access.'}
                        <Button className='btn tournament-btn-popup' onClick={hanlePlayWithoutTournament}>
                          Not now
                        </Button>
                      </Box>
                    )}
                    <h3>{tournament?.title}</h3>
                    <Grid className='underline' />

                    <h4 className='pool-prize'>POOL PRIZE</h4>
                    {tournament?.entryCoin === 'SC'
                      ? tournament?.winSC > 0 && (
                        <Grid className='sc-badge'>
                          <img src={priceBadge} alt='price-badge' />
                          <span> {formatPriceWithCommas(tournament?.winSC)} SC</span>
                        </Grid>
                      )
                      : tournament?.winGc > 0 && (
                        <Grid className='sc-badge'>
                          <img src={priceBadge} alt='price-badge' />
                          <span> {formatPriceWithCommas(tournament?.winGc)} GC</span>
                        </Grid>
                      )}

                    <h3 className='entry-fee'>
                      {tournament?.isJoined
                        ? 'Tournament Joined'
                        : tournament?.entryAmount === 0
                          ? 'Free Entry'
                          : `Entry Fee :  ${formatPriceWithCommas(tournament?.entryAmount)} ${tournament?.entryCoin}`}
                    </h3>

                    <Box className='preview'>
                      <Button
                        className='btn btn-primary'
                        onClick={() =>
                          !tournament?.userInTournament
                            ? handleJoinTournament(
                              tournament?.entryCoin === 'SC'
                                ? `/cms/tournament-sc-terms`
                                : `/cms/tournament-gc-terms`,
                              tournament?.tournamentId, tournament?.entryCoin
                            )
                            : handlePlayInTournament(tournament?.tournamentId, tournamentData)
                        }
                      >
                        {!tournament?.userInTournament ? 'Join Tournament' : 'Tournament Play'}
                      </Button>
                    </Box>

                    <Button className='btn tournament-btn' onClick={hanlePlayWithoutTournament}>
                      Not now
                    </Button>
                  </Box>
                </SwiperSlide>
              )
            })}
          </Swiper>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default TournamentPopup
