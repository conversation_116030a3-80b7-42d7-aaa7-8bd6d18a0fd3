import { Box, Grid } from '@mui/material'
import React, { useEffect } from 'react'
import useStyles from './TournamentDetail.styles'
import { useParams } from 'react-router-dom'
import tournamentQuery from '../../../reactQuery/tournamentQuery'
import { useUserStore } from '../../../store/useUserSlice'
import { toast } from 'react-hot-toast'
import { useBannerStore, usePortalStore } from '../../../store/store'
import CmsModal from '../../../components/CmsModal/CmsModal'
import BannerManagement from '../../../components/BannerManagement'
import { formatTopTenData } from './tournamentUtils'
import TournamentDetailsCard from './TournamentDetailsCard'
import TournamentPrizeCard from './TournamentPrizeCard'
import TournamentGuidelineCard from './TournamentGuidelineCard'
import TournamentGamesCard from './TournamentGamesCard'
import TournamentLeaderboard from './TournamentLeaderboard'
import { useTournamentStore } from '../../../store/useTournamentStore'
import JackpotBadge from '../../Jackpot/JackpotBadge'

const TournamentDetail = () => {
  const classes = useStyles()
  const { tournamentId: Id } = useParams()
  const [tournamentId, setTournamentId] = React.useState(0)
  const [isEnable, setIsEnable] = React.useState(false)
  const portalStore = usePortalStore((state) => state)

  const { userDetails, setUserDetails } = useUserStore((state) => ({
    userDetails: state?.userDetails,
    TournamentLeaderboardSocketConnection: state?.walletSocketConnection,
    setUserDetails: state?.setUserDetails
  }))

  const { setTournamentData, setLeaderboardData, tournamentData } = useTournamentStore((state) => ({
    setTournamentData: state?.setTournamentData,
    setLeaderboardData: state?.setLeaderboardData,
    tournamentData: state?.tournamentData,
    leaderboardData: state?.leaderboardData
  }))

  const { tournamentDetailPage } = useBannerStore((state) => state)

  const { isScTournamentTermsAccepted, isGcTournamentTermsAccepted } = userDetails || {}

  useEffect(() => {
    setTournamentId(parseInt(Id))
    setIsEnable(true)
  }, [Id])

  const successToggler = (data) => {
    setTournamentData(data?.tournamentDetails)

    const formattedData = formatTopTenData(data?.leaderboard, userDetails?.userId)
    setLeaderboardData(formattedData)
    setIsEnable(false)
  }
  const errorToggler = () => {
    setIsEnable(false)
  }
  const { refetch } = tournamentQuery.getTournamentDetailQuery({
    enabled: isEnable,
    params: { tournamentId },
    successToggler,
    errorToggler
  })

  const handleJoinTournament = (pathname, entryCoin) => {
    const shouldOpenModal =
      (entryCoin === 'SC' && !isScTournamentTermsAccepted) || (entryCoin === 'GC' && !isGcTournamentTermsAccepted)

    if (shouldOpenModal) {
      portalStore.openPortal(() => <CmsModal path={pathname} handleConfirm={handleConfirm} />, 'cmsModal')
    } else {
      handleConfirm('submit') // Directly confirm if already accepted
    }
  }

  const handleConfirm = (data) => {
    if (data === 'submit' && tournamentId != 0) {
      const payload = {
        tournamentId: tournamentId,
        isTournamentTermsAccepted: true
      }
      mutationJoinTournament.mutate(payload)

      const isScTournamentTermsAccepted = tournamentData?.entryCoin === 'SC'
      const isGcTournamentTermsAccepted = tournamentData?.entryCoin === 'GC'

      setUserDetails({
        ...userDetails,
        isScTournamentTermsAccepted,
        isGcTournamentTermsAccepted
      })
      portalStore.closePortal()
    }
  }
  const successJoinToggler = (data) => {
    toast.success('Tournament Joined successfully.')
    refetch()
  }
  const errorJoinToggler = () => {}
  const mutationJoinTournament = tournamentQuery.useJoinTournamentMutation({
    successJoinToggler,
    errorJoinToggler
  })

  return (
    <Grid className={classes.lobbyRight}>
      <Grid
        className={[classes.wrapper, 'promotions-wrap'].join(' ')}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '2rem'
        }}
      >
        <BannerManagement bannerData={tournamentDetailPage} />
        <Box className={classes.tournamentDetail}>
          <TournamentDetailsCard handleJoinTournament={handleJoinTournament} />

          <TournamentPrizeCard />

          <TournamentGuidelineCard />

          <TournamentGamesCard tournamentId={tournamentId} />

          <TournamentLeaderboard />
        </Box>
      </Grid>
      <JackpotBadge />
    </Grid>
  )
}

export default TournamentDetail
