
const sortLeaderBoardDataDesc = (data = []) => data.sort((a, b) => b.score - a.score)

export const formatTopTenData = (data = [], userId) => {
  // sorting the data on the basis of score.
  const sortedData = sortLeaderBoardDataDesc(data)
  // Now getting the top 10 data
  const topTenList = sortedData?.slice(0, 10)

  // Now checking if the current user is in the leader board list or not.
  const userLeaderBoardData = sortedData?.find((boardData) => boardData?.userId === userId)

  if (userLeaderBoardData) {
    // Now checking if user exist in top 10 list
    const isUserInTopTen = topTenList?.find((topData) => topData?.userId === userId)
    if (!isUserInTopTen) {
      // this condition means user exist in the leader board list but not in top ten.
      // Now replaceing the last record with the user leader data.

      topTenList[topTenList?.length] = userLeaderBoardData // Replace the last object with the new object
    }
  }
  return topTenList
}