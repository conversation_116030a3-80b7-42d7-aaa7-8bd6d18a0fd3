import { makeStyles } from '@mui/styles'

import { TournamentGlow } from '../../../components/ui-kit/icons/webp'
import { leaderBoardContainer, LobbyRight } from '../../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),

    '& .counter-divider': {
      padding: theme.spacing(0, 0.625)
      // marginTop: theme.spacing(0.625)
    },

    '& .MuiTabs-root': {
      width: 'fit-content',
      [theme.breakpoints.down('sm')]: {
        width: '100%'
      },
      '& .MuiTabs-scroller': {
        maxWidth: '100%',
        display: 'flex',
        whiteSpace: 'nowrap',
        flexWrap: 'nowrap',
        overflowX: 'auto',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        padding: theme.spacing(0.31),
        margin: '0px 0 46px 0 !important',

        [theme.breakpoints.down('md')]: {
          margin: '20px 0 20px 0 !important'
        },
        '& .MuiTabs-indicator': {
          borderRadius: theme.spacing(4.1875),
          background: theme.colors.YellowishOrange,
          fontWeight: theme.typography.fontWeightExtraBold,
          color: theme.colors.textBlack,
          height: 'calc(100% - 0.62rem)',
          top: '0.31rem'
        },

        '& .MuiTab-iconWrapper': {
          margin: '0'
        },

        '& button': {
          color: theme.colors.textWhite,
          position: 'relative',
          marginRight: theme.spacing(0.5),
          fontWeight: theme.typography.fontWeightExtraBold,
          cursor: 'pointer',
          fontSize: theme.spacing(1),
          transition: 'none',
          transform: 'none',
          zIndex: '1',
          minHeight: 'auto',
          padding: '13px 20px',
          minWidth: '140px',

          '&:last-child': {
            marginRight: '0'
          },

          '&:hover .MuiTab-iconWrapper': {
            '& .image2': {
              display: 'block'
            },
            '& .image1': {
              display: 'none'
            }
          },
          '& .MuiTab-iconWrapper': {
            '& .image2': {
              display: 'none'
            },
            '& .image1': {
              display: 'block'
            }
          },
          '&.Mui-selected': {
            color: theme.colors.textBlack,
            '& .MuiTab-iconWrapper': {
              '& .image1': {
                display: 'none'
              },
              '& .image2': {
                display: 'block !important'
              }
            }
          },

          '&:hover': {
            borderRadius: theme.spacing(4.1875),
            background: theme.colors.YellowishOrange,
            transition: 'none',
            transform: 'none',
            color: theme.colors.textBlack,
            border: '0'
          },
          '& img': {
            marginRight: theme.spacing(0.62),
            width: '25px',
            aspectRatio: '1'
          }
        }
      }
    }
  },

  wrapper: {
    maxWidth: '1200px',
    margin: '0 auto',
    width: '100%',

    [theme.breakpoints.down(576)]: {
      gap: '1rem !important'
    }
  },
  tournamentDetail: {
    [theme.breakpoints.down(1200)]: {
      padding: theme.spacing(0, 1.25)
    },
    [theme.breakpoints.down(576)]: {
      padding: theme.spacing(0)
    },
    '& .border-box': {
      background: 'linear-gradient(180deg, #CCCCCC 0%, #293937 100%)',
      padding: '1px',
      borderRadius: '10px',
      height: '100%'
    },
    '& .tournament-details-wrap': {
      background: theme.colors.tournamentCardGradient,
      borderRadius: theme.spacing(0.5625),
      padding: theme.spacing(1.5, 3),
      boxShadow: theme.shadows[23],
      height: '100%',
      minHeight: theme.spacing(9.5625),
      [theme.breakpoints.down('lg')]: {
        padding: theme.spacing(1.5)
      },
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(1),
        minHeight: 'auto'
      },

      '& .tournament-start': {
        fontSize: '20px',
        fontWeight: '700',
        color: '#FFA538'
      },

      '& h2': {
        fontSize: theme.spacing(2.25),
        fontWeight: theme.typography.fontWeightExtraBold,
        textTransform: 'capitalize',
        marginBottom: theme.spacing(1),
        [theme.breakpoints.down('sm')]: {
          // textAlign: 'center',
          fontSize: theme.spacing(1.25)
        }
      },
      '& .tournament-details-grid': {
        // display: "grid",
        // gridTemplateColumns: "repeat(4, 1fr)",
        // gap: theme.spacing(1),
        alignItems: 'center',
        display: 'flex',
        justifyContent: 'space-between'
        // maxWidth: '25rem'
      }
    },
    '& .tournament-details-card': {
      position: 'relative',
      height: '100%',
      // width: '100%',
      alignItems: 'start',
      display: 'flex',
      gap: '12px',
      flexDirection: 'column',
      justifyContent: 'center',
      [theme.breakpoints.down(1250)]: {
        gap: '32px'
      },
      [theme.breakpoints.down('md')]: {
        // marginBottom: 'auto',
        gap: '20px'
      },
      '& .tournament-price': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(2),
        justifyContent: 'start',
        position: 'relative',
        [theme.breakpoints.down(1250)]: {
          gap: theme.spacing(1)
        },
        [theme.breakpoints.down('sm')]: {
          justifyContent: 'end'
        },
        '& .tournament-price-content': {
          display: 'flex',
          alignItems: 'center',
          gap: '1rem',
          [theme.breakpoints.down('md')]: {
            gap: '0.35rem'
          },
          '& p': {
            fontSize: theme.spacing(1),
            fontWeight: theme.typography.fontWeightBold,
            color: theme.colors.YellowishOrange,
            whiteSpace: 'nowrap',

            [theme.breakpoints.down('lg')]: {
              // maxWidth: '70px'
            },
            [theme.breakpoints.down('md')]: {
              // maxWidth: '100px'
              fontSize: '0.75rem'
            }
            // '&:before': {
            //   position: 'absolute',
            //   content: "''",
            //   width: '1px',
            //   height: theme.spacing(2.45),
            //   background: theme.colors.tournamentBorder,
            //   left: '-1rem',
            //   top: '50%',
            //   transform: 'translate(50%, -40%)',
            //   [theme.breakpoints.down('lg')]: {
            //     left: '-0.65rem',
            //     height: theme.spacing(1)
            //   }
            // }
          },
          '& h4': {
            fontSize: theme.spacing(1.125),
            fontWeight: theme.typography.fontWeightExtraBold,
            color: theme.colors.textWhite,
            whiteSpace: 'nowrap',
            [theme.breakpoints.down('md')]: {
              // maxWidth: '100px'
              fontSize: theme.spacing(0.75)
            },
            [theme.breakpoints.down('sm')]: {
              // maxWidth: '100px'
              fontSize: theme.spacing(0.65)
            }
          },
          '& .entry-fee-text': {
            display: 'flex',
            alignItems: 'center',
            gap: '5px'
          }
        },
        '& img': {
          width: theme.spacing(2),
          [theme.breakpoints.down(1250)]: {
            width: theme.spacing(1.25)
          }
        }
      },

      '& .coin-wrap': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(1),
        flexDirection: 'column',
        [theme.breakpoints.down('md')]: {
          alignItems: 'start'
        },
        '& .MuiButtonBase-root': {
          background: theme.colors.tournamentPrizeGradient,
          borderRadius: theme.spacing(0.375),
          padding: theme.spacing(0.2, 1),
          minHeight: theme.spacing(2.1875),
          width: '100%',
          maxWidth: '15.625rem',
          minWidth: '12rem',
          justifyContent: 'start',
          lineHeight: theme.spacing(0.625),
          [theme.breakpoints.down('md')]: {
            minHeight: theme.spacing(1.75),
            padding: theme.spacing(0.2, 0.5),
            width: 'auto',
            minWidth: '9rem'
          },
          '& span': {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(1),
            fontSize: theme.spacing(1.25),
            color: theme.colors.textColor,
            fontWeight: theme.typography.fontWeightExtraBold,
            '& img': {
              width: theme.spacing(1.25)
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(1)
            }
          }
        }
      },
      '&.first-card': {
        paddingRight: '3rem',
        borderRight: '1px solid #5B5B5B',
        [theme.breakpoints.down(1250)]: {
          paddingRight: '1.5rem'
        },
        [theme.breakpoints.down('sm')]: {
          paddingRight: '0',
          borderRight: 'none'
        }
      },
      // '&.first-card, &.last-card': {
      //   '&:before': {
      //     position: 'absolute',
      //     content: "''",
      //     width: '1px',
      //     height: theme.spacing(6.25),
      //     background: theme.colors.tournamentBorder,
      //     right: theme.spacing(-3.75),
      //     top: '50%',
      //     transform: 'translate(0, -50%)',
      //     [theme.breakpoints.down('md')]: {
      //       display: 'none'
      //     }
      //   }
      // },
      '&.last-card': {
        // minHeight: theme.spacing(5.5625),
        textAlign: 'center',
        alignItems: 'center',

        '&:before': {
          right: 'auto',
          left: theme.spacing(-1.25)
        },
        // display: "flex",
        // alignItems: "center",
        // justifyContent: "center",
        [theme.breakpoints.down('md')]: {
          justifyContent: 'flex-start',
          alignItems: 'center'
          // marginTop: '0.5rem',
          // paddingTop: '0.5rem',
          // borderTop: '1px solid #393939'
        },
        [theme.breakpoints.down('sm')]: {
          justifyContent: 'center'
        },
        '& .started-btn': {
          '&.MuiButtonBase-root': {
            // background: theme.colors.sidebarBg,
            filter: 'drop-shadow(0 0 10px rgb(120, 214, 82))',
            borderRadius: theme.spacing(1.875),
            color: theme.colors.success,
            minWidth: theme.spacing(10),
            padding: theme.spacing(0.5, 1.5),
            fontSize: '1.25rem',
            fontWeight: '600',
            marginTop: '0.25rem',
            [theme.breakpoints.down('md')]: {
              minWidth: 'unset',
              fontSize: '1rem'
            }
          }
        },
        '& button': {
          fontSize: `${theme.spacing(1.25)} !important`,
          fontWeight: '700 !important',
          maxHeight: '40px',
          [theme.breakpoints.down('md')]: {
            padding: '0.5rem 1rem !important',
            maxHeight: '2rem',
            whiteSpace: 'nowrap',
            minWidth: '90px'
          },
          '&.join-btn': {
            background: 'transparent !important',
            border: '1px solid #fdb72e',
            color: theme.colors.textWhite,
            marginTop: '0.75rem',
            minWidth: '14rem',
            margin: '0.75rem auto 0 auto',
            maxWidth: 'fit-content',
            '&:hover': {
              background: '#fdb72e !important',
              color: '#000 !important'
            }
          }
        }
      }
      // "&:before": {
      // position: "absolute",
      // content: "''",
      // width: "1px",
      // height: theme.spacing(6.25),
      // background: theme.colors.tournamentBorder,
      // right: theme.spacing(-1.25),
      // top: "0"
      // },
    },
    '& .swiper-button-prev, & .swiper-button-next': {
      top: 'auto',
      bottom: '0',
      borderRadius: '100%',
      background: theme.colors.modalArrows,
      height: theme.spacing(2),
      width: theme.spacing(2),
      left: '54%',
      '&:after': {
        color: theme.colors.YellowishOrange,
        fontSize: theme.spacing(1.25),
        fontWeight: theme.typography.fontWeightExtraBold,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.875)
        }
      }
    },
    // '& .swiper-button-next': {
    //   left: '54% !important'
    // },
    '& .swiper-button-prev': {
      left: '42% !important'
    },
    '& .swiper-wrapper': {
      paddingBottom: '40px'
    },
    '& .prize-section': {
      margin: theme.spacing(2.5, 0),
      // display: "grid",
      // gridTemplateColumns: "repeat(5, 1fr)",
      // gap: theme.spacing(1.25),
      [theme.breakpoints.down(768)]: {
        margin: theme.spacing(1, 0)
      },
      '& .prize-card-wrap': {
        background: theme.colors.prizeGradient,
        borderRadius: theme.spacing(0.5625),
        padding: '1px',
        '& .prize-card': {
          background: theme.colors.tournamentCardGradient,
          borderRadius: theme.spacing(0.5625),
          padding: theme.spacing(1),
          textAlign: 'center',
          '& .prize-icon': {
            marginBottom: theme.spacing(1.25),
            position: 'relative',
            '& img': {
              width: theme.spacing(10.1875),
              margin: '0 auto',
              [theme.breakpoints.down(1250)]: {
                width: theme.spacing(9.6875)
              },
              [theme.breakpoints.down(768)]: {
                width: theme.spacing(4.6875)
              }
            },
            '& .highlight': {
              position: 'absolute',
              top: '0',
              left: '50%',
              // height: '100%',
              // width: '100%',
              transform: 'translate(-50%, -1%) scale(1.2)'
            },
            [theme.breakpoints.down(768)]: {
              marginBottom: theme.spacing(0.25)
            }
          },
          [theme.breakpoints.down(768)]: {
            padding: theme.spacing(0.5)
          }
        },
        '&.active-position': {
          '& .prize-icon': {
            position: 'relative',
            '&:before': {
              backgroundImage: `url(${TournamentGlow})`,
              backgroundRepeat: 'no-repeat',
              height: '100%',
              width: '90%',
              content: "''",
              position: 'absolute',
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%) scale(1.1)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }
          }
        }
      },
      '& .prize-ribbon': {
        display: 'inline-block',
        marginBottom: theme.spacing(1.25),
        position: 'relative',
        height: theme.spacing(1.75),
        lineHeight: theme.spacing(1.75),
        textAlign: 'center',
        padding: '0 32px',
        fontSize: '18px',
        background: theme.colors.ribbonGradient,
        color: theme.colors.textWhite,
        fontWeight: theme.typography.fontWeightExtraBold,
        whiteSpace: 'nowrap',
        [theme.breakpoints.down(768)]: {
          fontSize: '10px',
          padding: '0 20px',
          height: theme.spacing(0.875),
          lineHeight: theme.spacing(0.975),
          marginBottom: '0'
        },
        '&::before, &::after': {
          position: 'absolute',
          content: '""',
          width: '0px',
          height: '0px',
          zIndex: 1
        },
        '&::before': {
          top: '0',
          left: '-1px',
          borderWidth: '14px 0px 14px 15px',
          borderColor: 'transparent transparent transparent #242323',
          borderStyle: 'solid',
          [theme.breakpoints.down(768)]: {
            borderColor: 'transparent transparent transparent #2f2f2f',
            borderWidth: '7px 0px 7px 7.5px'
          }
        },
        '&::after': {
          top: '0',
          right: '-1px',
          borderWidth: '14px 15px 14px 0px',
          borderColor: 'transparent #242323 transparent transparent',
          borderStyle: 'solid',
          [theme.breakpoints.down(768)]: {
            borderColor: 'transparent #2f2f2f transparent transparent',
            borderWidth: '7px 7.5px 7px 0px'
          }
        }
      },
      '& .prize-details-wrap': {
        margin: theme.spacing(0.625, 0),
        '& .prize-details-card': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: theme.spacing(0.313),
          fontWeight: theme.typography.fontWeightExtraBold,
          fontSize: theme.spacing(0.9375),
          gap: theme.spacing(0.625),
          '& img': {
            width: theme.spacing(1.25),
            [theme.breakpoints.down(768)]: {
              width: theme.spacing(1)
            }
          },
          [theme.breakpoints.down(768)]: {
            fontSize: theme.spacing(0.65)
          },
          '&.prize': {
            [theme.breakpoints.down(768)]: {
              fontSize: theme.spacing(0.75)
            }
          }
        }
      }
    }
  },

  tournamentBox: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    // paddingBottom: theme.spacing(1),

    [theme.breakpoints.down('lg')]: {
      flexWrap: 'wrap',
      paddingBottom: '0'
      // gap: theme.spacing(1),
      // justifyContent: 'start'
    }
  },

  tournamentInnerText: {
    '& .MuiTypography-h2': {
      fontSize: theme.spacing(2),
      fontWeight: theme.typography.fontWeightBold,
      textTransform: 'capitalize',
      marginBottom: theme.spacing(0.25),

      [theme.breakpoints.down(1200)]: {
        fontSize: theme.spacing(1.5)
      }
    }
  },

  priceBtn: {
    color: `${theme.colors.textBlack} !important`,
    fontSize: '1.5rem !important',
    minWidth: '120px !important',
    background: `${theme.colors.YellowishOrange} !important`,
    fontWeight: '700 !important',
    borderRadius: '0.5rem !important',
    lineHeight: '1.2 !important',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing(0.5),

    '& .MuiTypography-span': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: theme.spacing(0.25)
    },

    [theme.breakpoints.down(1200)]: {
      fontSize: `${theme.spacing(1)} !important`
    }
  },

  tournamentTimer: {
    display: 'flex',
    alignItems: 'center',
    // gap: theme.spacing(1),
    fontWeight: theme.typography.fontWeightBold,
    [theme.breakpoints.down(1300)]: {
      // gap: theme.spacing(0.5)
    },
    '& .counter-divider': {
      // display: 'none',
      fontSize: theme.spacing(2),
      padding: theme.spacing(0, 0.313),
      color: '#AAA9A9'
      // marginTop: theme.spacing(0.75)
    },
    '& .tournamentCountdown': {
      textAlign: 'center',
      display: 'flex',
      flexDirection: 'column',
      background: 'linear-gradient(176.23deg, #cccccc 25.71%, #656464 96.95%)',
      borderRadius: '10px',
      padding: '2px',
      boxShadow: '0px 0px 8.19px 0px #CCCCCC',

      // gap: theme.spacing(0.313),
      // gap: theme.spacing(0.313),
      '& .MuiTypography-span': {
        fontSize: theme.spacing(0.875),
        padding: '2px 0'
      }
    },
    '& .tournamentTime': {
      // background: theme.colors.counterBg,
      color: theme.colors.textWhite,
      fontSize: theme.spacing(1.5),
      fontWeight: '700',
      padding: '12px 16px',
      textAlign: 'center',
      // fontStyle: 'italic',
      // transform: 'skewX(-20deg)',
      // boxShadow: theme.shadows[15],
      minWidth: theme.spacing(4),
      borderRadius: '10px',
      borderBottomRightRadius: '0',
      background: '#313131',
      borderBottomLeftRadius: '0',
      // minHeight: theme.spacing(2.6969),

      // [theme.breakpoints.down('xl')]: {
      //   fontSize: theme.spacing(1.8),
      //   minWidth: theme.spacing(5.5)
      // },
      [theme.breakpoints.down(1300)]: {
        fontSize: theme.spacing(1),
        minWidth: theme.spacing(3.25),
        padding: '12px'
      },
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(1.5),
        minWidth: theme.spacing(3.5)
      }
    }
  },
  dailyBonusTimer: {
    display: 'flex',
    marginTop: '5px',
    fontWeight: theme.typography.fontWeightBold,
    '& .counter-divider': {
      fontSize: theme.spacing(1),
      padding: theme.spacing(0, 0.313)
      // marginTop: theme.spacing(0.313)
    },
    '& .tournamentCountdown': {
      textAlign: 'center',
      display: 'flex',
      flexDirection: 'column',
      padding: theme.spacing(0.4, 0.125),
      fontWeight: 900,
      '& .MuiTypography-span': {
        fontSize: theme.spacing(0.875),
        fontWeight: '600'
      }
    },
    '& .tournamentTime': {
      fontSize: theme.spacing(1),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontWeight: theme.typography.fontWeightBold,
      color: theme.colors.textBlack,
      // background: theme.colors.timerBg,
      padding: theme.spacing(0.3, 0.125),
      borderRadius: theme.spacing(0.5),
      width: theme.spacing(2.5),
      [theme.breakpoints.down(1200)]: {
        fontSize: theme.spacing(1),

        width: '2.3125rem'
      }
    }
  },

  tournamentPriceBox: {
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1.5),
    flexWrap: 'wrap',
    justifyContent: 'center',
    [theme.breakpoints.down(768)]: {
      flexWrap: 'wrap',
      gap: theme.spacing(1)
    }
  },

  tournamentPrice: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(0.75)
  },

  tournamentPriceHeading: {
    '& .MuiTypography-h5': {
      fontSize: theme.spacing(1),
      fontWeight: theme.typography.fontWeightBold,
      color: theme.colors.YellowishOrange
    },

    '& .MuiBox-root': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: theme.spacing(0.5),
      fontWeight: theme.typography.fontWeightBold,

      '& .MuiTypography-p': {
        fontSize: theme.spacing(1),
        fontWeight: theme.typography.fontWeightBold,
        textTransform: 'uppercase',
        // borderRight: '1px solid #fff',
        paddingRight: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing(0.125)
      }
    }
  },

  // joinTournamentBox: {
  //     display: 'flex',
  //     alignItems: 'center',
  //     justifyContent: 'space-between',
  //     paddingBottom: theme.spacing(2.5),

  //     [theme.breakpoints.down(768)]: {
  //         flexWrap: 'wrap',
  //         gap: theme.spacing(1),
  //     }
  // },

  registrationPrice: {
    '& .MuiTypography-h6': {
      backgroundColor: theme.colors.lightBlack,
      padding: theme.spacing(0, 1.5, 0, 0.3125),
      fontSize: theme.spacing(0.875)
    },

    '& .MuiTypography-p': {
      display: 'flex',
      alignItems: 'center',
      color: theme.colors.lightYellow,
      fontSize: theme.spacing(1.25),
      fontWeight: theme.typography.fontWeightBold
    }
  },

  joinRegistration: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(2.5)
  },

  joinTournamentBtn: {
    color: `${theme.colors.textBlack} !important`,
    fontSize: `${theme.spacing(1)} !important`,
    minWidth: '120px !important',
    background: `${theme.colors.YellowishOrange} !important`,
    fontWeight: '700 !important',
    borderRadius: '5rem !important',
    lineHeight: '1.2 !important',
    padding: `${theme.spacing(0.625, 1.5)} !important`
  },

  tournamentPara: {
    padding: theme.spacing(1.875, 3),
    background: theme.colors.tournamentGuidelineGradient,
    marginBottom: theme.spacing(2.5),
    borderRadius: theme.spacing(0.5625),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1),
      marginBottom: theme.spacing(1)
    },
    '& .MuiTypography-h4': {
      fontSize: theme.spacing(1.4375),
      fontWeight: theme.typography.fontWeightExtraBold,
      textTransform: 'capitalize',
      color: theme.colors.YellowishOrange,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(0.75)
      }
    },

    '& .MuiTypography-p': {
      fontSize: theme.spacing(1),
      fontWeight: theme.typography.fontWeightSemiBold,
      paddingTop: theme.spacing(1)
    }
  },

  leaderBoardContainer: {
    margin: theme.spacing(2.5, 0),
    overflowX: 'auto',
    ...leaderBoardContainer(theme),
    '& .MuiTableBody-root': {
      '& .MuiTableRow-root': {
        '&.currunt-user': {
          // position: 'relative',
          border: '1px solid #FDB72E50',
          borderRadius: '0.625rem',
          '& td': {
            '&:first-child': {
              borderTopLeftRadius: theme.spacing(0.313),
              borderBottomLeftRadius: theme.spacing(0.313)
            },
            '&:last-child': {
              borderTopRightRadius: theme.spacing(0.313),
              borderBottomRightRadius: theme.spacing(0.313)
            }
          }
        },
        '&.active-user': {
          border: '2px solid rgb(0, 65, 16)',
          borderRadius: '0.625rem'
        }

        // "& .table-prize" :{
        //         color
        // }
      }
    }
  },

  gameSlider: {
    '& .MuiTypography-h4': {
      fontSize: theme.spacing(1.25),
      fontWeight: theme.typography.fontWeightBold,
      textTransform: 'capitalize',
      paddingBottom: theme.spacing(0.75)
    },
    '& .swiper-wrapper': {
      marginTop: theme.spacing(0.5),
      '& .swiper-slide': {
        transition: 'all 200ms ease-in-out',
        lineHeight: '0',
        '& .casino-card': {
          textAlign: 'center',
          position: 'relative',
          transition: 'all 200ms ease-in-out',
          '&:hover': {
            transform: 'translateY(-0.5rem)',
            transition: 'all 0.5s ease-in-out',
            '& .overlayPlay': {
              display: 'flex',
              borderRadius: '8px'
            }
          },
          '& .fav-icon': {
            width: '20px',
            height: '20px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            top: '10px',
            right: '10px',
            zIndex: '8',
            '& img': {
              width: '20px',
              height: '20px',
              objectFit: 'contain',
              objectPosition: 'center'
            },
            '&:hover': {
              backgroundColor: theme.colors.textWhite,
              cursor: 'pointer'
            }
          },
          '& .casinoGame-img': {
            width: '100%',
            borderRadius: '8px',
            '&:hover': {
              backgroundColor: theme.colors.textWhite,
              cursor: 'pointer'
            }
          },
          '& .tournamentLogo': {
            position: 'absolute',
            left: '2px',
            top: '5px',
            width: '30px',
            height: '30px'
          },
          '& .prgamatic-jackpot-amount-wrapper': {
            position: 'absolute',
            top: '12px',
            left: '47%',
            display: 'flex',
            justifyContent: 'center',
            gap: '4px',
            alignItems: 'center',
            background: '#000000B2',
            borderRadius: '17px',
            whiteSpace: 'nowrap',
            transform: 'translate(-50%, 0)',
            padding: '1px 5px'
          }
        }
      }
    },
    '& .overlayPlay': {
      position: 'absolute',
      display: 'none',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      margin: '0 auto',
      inset: '0',
      flexDirection: 'column',
      background: 'linear-gradient(180deg, rgba(255,84,37,0.7) 0%, rgba(251,162,83,0.7) 100%)',
      cursor: 'pointer',
      transition: 'all 0.5s ease-in-out',
      borderRadius: '8px',
      '& a': {
        color: theme.colors.textWhite,
        textDecoration: 'none'
      }
    }
  },

  transactionTable: {
    overflowX: 'auto',
    '& .MuiTableRow-head': {
      '& .MuiTableCell-root': {
        '& .table-head-card': {
          display: 'flex',
          justifyContent: 'center',
          gap: theme.spacing(1),
          fontSize: theme.spacing(1),
          fontWeight: theme.typography.fontWeightExtraBold,
          color: theme.colors.YellowishOrange,
          '& img': {
            width: theme.spacing(1.5),
            [theme.breakpoints.down('md')]: {
              width: theme.spacing(1),
              height: theme.spacing(1)
            }
          },
          '&.table-prize': {
            color: theme.colors.Promosuccess
          }
        }
      }
    },
    '& .MuiTableHead-root': {
      background: theme.colors.sidebarBg
    },
    '& .table-prize-details': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: theme.spacing(1),
      color: theme.colors.Promosuccess,
      '& img': {
        width: theme.spacing(1.5)
      }
    }
  }
}))
