import React from 'react'
import { Box, Grid, Typography } from '@mui/material'
import usdIcon from '../../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../../components/ui-kit/icons/opImages/usd-chip.svg'
import { formatPriceWithCommas } from '../../../utils/helpers'
import { Swiper, SwiperSlide } from 'swiper/react'
import { FreeMode, Navigation } from 'swiper/modules'
import TournamentPosition1 from '../../../components/ui-kit/icons/webp/tournament-position.webp'
import TournamentWinner2 from '../../../components/ui-kit/icons/webp/Tournament_winner_02.webp'
import TournamentWinner3 from '../../../components/ui-kit/icons/webp/Tournament_winner_03.webp'
import TournamentWinner4 from '../../../components/ui-kit/icons/webp/Tournament_winner_04.webp'
import TournamentWinner5 from '../../../components/ui-kit/icons/webp/Tournament_winner_05.webp'
import TournamentWinner6 from '../../../components/ui-kit/icons/webp/Tournament_winner_06.webp'
import TournamentWinner7 from '../../../components/ui-kit/icons/webp/Tournament_winner_07.webp'
import TournamentWinner8 from '../../../components/ui-kit/icons/webp/Tournament_winner_08.webp'
import highlightImg from '../../../components/ui-kit/icons/webp/tournament-glow.webp'
import { useTournamentStore } from '../../../store/useTournamentStore'
/* eslint-disable multiline-ternary */
const TournamentPrizeCard = () => {
  const tournamentData = useTournamentStore((state) => state?.tournamentData)

  const prizesImage = [
    TournamentPosition1,
    TournamentWinner2,
    TournamentWinner3,
    TournamentWinner4,
    TournamentWinner5,
    TournamentWinner6,
    TournamentWinner7,
    TournamentWinner8
  ]

  const getPrizeImage = (rank) => {
    // If the rank is greater than 5, use the 5th prize image.
    if (rank >= 8) {
      return prizesImage[7] // The 8th prize image (index 7) for rank > 8.
    }
    return prizesImage[rank - 1] // Otherwise, use the image based on the rank (1-8).
  }

  // Helper function to get the rank suffix
  const getRankSuffix = (rank) => {
    if (rank === '1') return `${rank}st Prize`
    if (rank === '2') return `${rank}nd Prize`
    if (rank === '3') return `${rank}rd Prize`
    return `${rank}th Prize`
  }

  return (
    <Box className='prize-section'>
      {tournamentData?.winnerPrizes && Object.keys(tournamentData?.winnerPrizes).length > 0 ? (
        <Swiper
          spaceBetween={10}
          freeMode={true}
          navigation={{
            nextEl: `#swiper-button-next-${tournamentData?.gameId?.masterCasinoGameId}`,
            prevEl: `#swiper-button-prev-${tournamentData?.gameId?.masterCasinoGameId}`
          }}
          modules={[FreeMode, Navigation]}
          className='mySwiper'
          breakpoints={{
            0: { slidesPerView: 3 },
            768: { slidesPerView: 3 },
            1024: { slidesPerView: 5 }
          }}
        >
          {Object.keys(tournamentData?.winnerPrizes)?.map((rankKey, index) => {
            const prize = tournamentData.winnerPrizes[rankKey]
            const prizeImage = getPrizeImage(Number(rankKey))
            return (
              <SwiperSlide key={rankKey}>
                <Grid className='prize-card-wrap'>
                  <Grid className='prize-card'>
                    <Grid className='prize-icon'>
                      {index === 0 && <img className='highlight' src={highlightImg} />}
                      <img src={prizeImage} alt={`Prize ${index + 1}`} />
                    </Grid>
                    <span className='prize-ribbon'>{getRankSuffix(rankKey)}</span>
                    <Grid className='prize-details-wrap'>
                      <Grid className='prize-details-card prize'>{prize?.users && `Prize Spots: ${prize?.users}`}</Grid>
                      <Grid className='prize-details-card'>
                        <img src={usdIcon} alt={usdIcon} /> {formatPriceWithCommas(prize?.scCoin)} SC
                      </Grid>
                      <Grid className='prize-details-card'>
                        <img src={usdchipIcon} alt={usdchipIcon} /> {formatPriceWithCommas(prize?.gcCoin)} GC
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </SwiperSlide>
            )
          })}

          {/* Custom navigation buttons */}
          <div
            id={`swiper-button-next-${tournamentData?.gameId?.masterCasinoGameId}`}
            className='swiper-button-next'
          />
          <div
            id={`swiper-button-prev-${tournamentData?.gameId?.masterCasinoGameId}`}
            className='swiper-button-prev'
          />
        </Swiper>
      ) : (
        <Typography variant='h6' component='h6'>
          No prizes available for this tournament.
        </Typography>
      )}
    </Box>
  )
}

export default TournamentPrizeCard
