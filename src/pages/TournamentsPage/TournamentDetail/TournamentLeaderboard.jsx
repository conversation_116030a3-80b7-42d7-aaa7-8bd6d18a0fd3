import React from 'react'
import { Box, Grid, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material'
import useStyles from './TournamentDetail.styles'
import usdIcon from '../../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../../components/ui-kit/icons/opImages/usd-chip.svg'
import TablePosition from '../../../components/ui-kit/icons/webp/table-position.webp'
import TableScore from '../../../components/ui-kit/icons/webp/table-score.webp'
import TableUserName from '../../../components/ui-kit/icons/webp/table-user.webp'
import { useTournamentStore } from '../../../store/useTournamentStore'
import { useUserStore } from '../../../store/useUserSlice'

const TournamentLeaderboard = () => {
  const classes = useStyles()
  const userDetails = useUserStore((state) => state?.userDetails)
  const leaderboardData = useTournamentStore((state) => state?.leaderboardData)

  return (
    <Box className={classes?.leaderBoardContainer}>
      <Table className={classes?.transactionTable}>
        <TableHead>
          <TableRow>
            <TableCell style={{ flex: 1, paddingLeft: '2rem' }}>
              <Grid className='table-head-card'>
                <img src={TablePosition} alt='Rank' />
                Rank
              </Grid>
            </TableCell>
            <TableCell style={{ flex: 1 }}>
              <Grid className='table-head-card'>
                <img src={TableUserName} alt='User' />
                Username
              </Grid>
            </TableCell>
            <TableCell style={{ flex: 1, whiteSpace: 'nowrap' }}>
              <Grid className='table-head-card'>
                <img src={TableScore} alt='Score' />
                Score
              </Grid>
            </TableCell>
            <TableCell style={{ flex: 1, whiteSpace: 'nowrap' }}>
              <Grid className='table-head-card table-prize'>
                <img src={usdIcon} alt='walletIcon' />
                Sc Prize
              </Grid>
            </TableCell>

            <TableCell style={{ flex: 1, whiteSpace: 'nowrap' }}>
              <Grid className='table-head-card table-prize'>
                <img src={usdchipIcon} alt='walletIcon' />
                Gc Prize
              </Grid>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {leaderboardData &&
            leaderboardData?.length > 0 &&
            leaderboardData?.map((data, index) => {
              const isCurrentUser = data?.userId === userDetails?.userId
              return (
                <TableRow
                  sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                  key={index}
                  className={isCurrentUser ? 'active-user' : 'currunt-user'}
                >
                  <TableCell style={{ flex: 1, paddingLeft: '2rem' }} align='center' scope='row'>
                    {data?.rank}
                  </TableCell>
                  <TableCell style={{ flex: 1 }} align='center' scope='row'>
                    {data?.['User.username']}
                  </TableCell>
                  <TableCell style={{ flex: 1 }} align='center' scope='row'>
                    {data?.score?.toFixed(2)}
                  </TableCell>
                  <TableCell style={{ flex: 1 }} align='center' scope='row'>
                    <Grid className='table-prize-details'>
                      <img
                        src={usdIcon}
                        alt='SC Icon'
                        style={{ width: '20px', marginRight: '5px', verticalAlign: 'middle' }}
                      />
                      {data?.priceAmount?.scCoin}
                    </Grid>
                  </TableCell>
                  <TableCell style={{ flex: 1 }} scope='row'>
                    <Grid className='table-prize-details'>
                      <img
                        src={usdchipIcon}
                        alt='GC Icon'
                        style={{ width: '20px', marginRight: '5px', verticalAlign: 'middle' }}
                      />
                      {data?.priceAmount?.gcCoin}
                    </Grid>
                  </TableCell>
                </TableRow>
              )
            })}
        </TableBody>
      </Table>
    </Box>
  )
}

export default TournamentLeaderboard
