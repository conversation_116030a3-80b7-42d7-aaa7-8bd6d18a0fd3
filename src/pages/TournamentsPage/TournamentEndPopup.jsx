import React, { useState, useEffect } from 'react'
import { Dialog, DialogContent, Typography, Button, IconButton, Box, Grid } from '@mui/material'
import useStyles from '../TournamentsPage/Tournaments.styles'
import CloseIcon from '@mui/icons-material/Close'

const TournamentEndPopup = ({ open, onClose = () => {}, onPlayOutside = () => {}, onClickBackToLobby = () => {} }) => {
  const [countdown, setCountdown] = useState(10)
  const classes = useStyles()

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      onClickBackToLobby()
    }
  }, [countdown])

  return (
    <Dialog open={true} onClose={onClose} className={classes.tournamentModal}>
      <DialogContent>
        <IconButton onClick={onClose} className='close-btn'>
          <CloseIcon />
        </IconButton>
        <Grid className='modal-header timer-header'>
          <Typography>Tournament is ended, Do you want to play game outside the tournament</Typography>
        </Grid>
        <Box className='timer-modal-wrap'>
          <Grid className='timer-card'>
            <Grid className='end-tag'>ends in</Grid>
            <Typography variant='h4'>{`00:${countdown.toString().padStart(2, '0')}`}</Typography>
            <Typography>Seconds</Typography>
          </Grid>
          <Grid className='btn-wrap'>
            <Button className='btn btn-secondary' onClick={onPlayOutside}>
              Yes
            </Button>
            <Button className='btn btn-primary' onClick={onClickBackToLobby}>
              Back to lobby
            </Button>
          </Grid>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default TournamentEndPopup
