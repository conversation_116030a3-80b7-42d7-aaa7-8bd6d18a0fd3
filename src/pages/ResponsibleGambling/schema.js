import * as Yup from 'yup'

export const useResponsibleGamblingSchema = Yup.object().shape({
    dailyBetLimit: Yup.mixed().test(
        'is-valid-number',
        'daily play limit must be a valid number',
        function (value) {
            value = Number(value)
          return typeof value === 'number' && !isNaN(value);
        }
      ),
    weeklyBetLimit: Yup.mixed().test(
        'is-valid-number',
        'weekly play limit must be a valid number',
        function (value) {
            value = Number(value)
          return typeof value === 'number' && !isNaN(value);
        }
      ),
    monthlyBetLimit: Yup.mixed().test(
        'is-valid-number',
        'monthly play limit must be a valid number',
        function (value) {
            value = Number(value)
          return typeof value === 'number' && !isNaN(value);
        }
      ),
    weeklyDepositLimit: Yup.mixed().test(
        'is-valid-number',
        'weekly purchase limit must be a valid number',
        function (value) {
            value = Number(value)
          return typeof value === 'number' && !isNaN(value);
        }
      ),
    monthlyDepositLimit: Yup.mixed().test(
        'is-valid-number',
        'monthly purchase limit must be a valid number',
        function (value) {
            value = Number(value)
          return typeof value === 'number' && !isNaN(value);
        }
      ),
    dailyDepositLimit: Yup.mixed().test(
        'is-valid-number',
        'daily purchase limit must be a valid number',
        function (value) {
            value = Number(value)
          return typeof value === 'number' && !isNaN(value);
        }
      ),
    timeBreak: Yup.mixed().test(
        'is-valid-number',
        'time break must be a valid number',
        function (value) {
            value = Number(value)
          return typeof value === 'number' && !isNaN(value);
        }
      ),
})