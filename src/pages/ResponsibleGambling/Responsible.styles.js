
import { makeStyles } from '@mui/styles'

import { LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    padding: theme.spacing(1, 1.5),
    "& .responsible-wrap": {
      padding: theme.spacing(3),
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(1),
      },
      "& .responsible-card-header": {
        backgroundSize: "100% 100%",
        borderRadius: theme.spacing(1, 1, 0, 0),
        padding: theme.spacing(2.3, 2),
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        position: "relative",
        "& .MuiTypography-body1": {
          color: theme.colors.white,
          fontWeight: theme.typography.fontWeightBold,
          fontSize: theme.spacing(1.875),
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.1),
          },
        },
        "& .responsible-header-icon": {
          position: "absolute",
          right: "0",
        }
      },
      "& .responsible-card": {
        background: theme.colors.gamePlayBg,
        '& .responsible-card-form-wrap': {
          padding: theme.spacing(2, 4),
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(2),
          },
          "& .responsible-heading": {
            margin: theme.spacing(1, 0),
            "& .MuiTypography-h4": {
              fontWeight: theme.typography.fontWeightExtraBold,
              background: theme.colors.textGradient,
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontSize: theme.spacing(1.375),
            },

          }
        },
        "& .responsible-form-btn": {
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          "& .MuiButtonBase-root": {
            margin: theme.spacing(0, 0.313),
          }
        }

      }
    }
  },
  authInputWrap: {
    position: "relative",
    marginBottom: theme.spacing(1),
    "& .MuiFormControl-root": {
      width: "100%",
      "& .MuiInputBase-root": {
        background: theme.colors.gamblingInput,
        borderRadius: "10px",
        height: "46px",
        "& .MuiInputBase-input": {
          height: "46px",
          padding: theme.spacing(0, 1),
          color: theme.colors.white
        },

      },
      "& .MuiFormLabel-root": {
        top: "-5px",
        color: theme.colors.white
      },
      "& .MuiOutlinedInput-root": {
        "&.Mui-focused": {
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.colors.secondryBtnBorder,
          }
        }
      }
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderColor: "transparent",
      height: "46px",
      top: "0",
      borderRadius: "10px",
    },
    "& .input-error": {
      color: theme.colors.error,
      fontSize: `${theme.spacing(0.8)}!important`,
      marginBottom: `${theme.spacing(1.5)} !important`,
      lineHeight: 'normal !important',
      minHeight: '16px',
    },
    "& .input-inline": {
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      "& .MuiFormControl-root": {
        width: "calc(50% - 10px)",
      },
    },
    "& .input-inline-row": {
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      "& .MuiFormControl-root": {
        width: "calc(33% - 10px)",
      },
    },
    "& svg": {
      color: theme.colors.white
    },
    "& .MuiTypography-body1": {
      marginBottom: theme.spacing(0.313),
      color: theme.colors.white,
      fontSize: theme.spacing(0.75)
    },
    "& .MuiFormLabel-root": {
      fontSize: theme.spacing(0.875),
      marginBottom: theme.spacing(0.313),
      display: "block",
      color: theme.colors.white,
    },

    "& .MuiInputBase-root": {
      width: '100%',
      height: "46px",
      "& legend": {
        display: "none"
      }
    },
    "& .MuiSelect-select": {
      padding: theme.spacing(0, 0.625),
      minHeight: "46px !important",
      background: theme.colors.gamblingInput,
      borderRadius: "10px",
      display: "flex",
      alignItems: "center",
      color: theme.colors.white,


    },
    "& .MuiOutlinedInput-root": {
      "&.Mui-focused": {
        "& .MuiOutlinedInput-notchedOutline": {
          borderColor: theme.colors.secondryBtnBorder,
          height: "46px",
          borderRadius: "10px",
        }
      }

    },

  },
  innerModal: {
    background: theme.spacing.innerModal,
    backdropFilter: "blur(20px)",
    "& .MuiPaper-elevation": {
      backdropFilter: "blur(20px) !important",
      backgroundColor: "#1B181E !important",
      boxShadow: "none !important",
      color: theme.colors.white,
      borderRadius: '10px',
    },

    "& .MuiPaper-root": {
      borderRadius: theme.spacing(0.625),
      backdropFilter: "blur(20px)",
      color: theme.colors.white,
      minWidth: "600px",
      maxWidth: "600px",
      background: theme.colors.black,
      [theme.breakpoints.down('sm')]: {
        minWidth: "95%",
      },

    },
    "& .inner-modal-header": {
      borderRadius: theme.spacing(0.625, 0.625, 0, 0),
      background: theme.colors.primaryGradient,
      padding: theme.spacing(0.625, 2),
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      "& .MuiTypography-h4": {
        color: theme.colors.white,
        fontWeight: theme.typography.fontWeightExtraBold,
        fontSize: theme.spacing(1.375),
        textTransform: "capitalize",
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1),
        },
      },

      "& .modal-close": {
        "& .MuiButtonBase-root": {
          background: theme.colors.white,
          padding: "0.625rem",
          borderRadius: theme.spacing(0.625),
          height: theme.spacing(1.75),
          width: theme.spacing(1.75),
          [theme.breakpoints.down('sm')]: {
            marginRight: "0",
          },
          "& svg": {
            color: theme.colors.themeText,
            fontSize: theme.spacing(0.875),
          },
          "&:hover": {
            "& svg": {
              color: theme.colors.highlighColor
            },
          }
        }
      },
    },
    "& .kyc-content": {
      textAlign: "center",
      "& .MuiTypography-h4": {
        fontWeight: theme.typography.fontWeightBold,
        marginBottom: theme.spacing(0.625),
        fontSize: theme.spacing(1.2)
      },
    },
    "& .btn-wrap": {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      width: "100%",
      margin: theme.spacing(0, 0, 1),
      "& .MuiButtonBase-root": {
        margin: theme.spacing(0, 0.313),
      }
    },
  },

  gamblingInput: {
    "& .MuiOutlinedInput-root": {
      height: "100%",
      padding: '0 14px',
      "& input": {
        height: "100%",
        padding: '0',
        color: theme.colors.textWhite
      }
    },
    "& .MuiIconButton-root": {
      "& svg": {
        color: theme.colors.textWhite
      }
    },
    "& fieldset": {
      border: 'none',
    }
  },

  gamblingSelect: {
    "& .MuiSelect-outlined": {
      padding: '0',
    },
    "& svg": {
      color: theme.colors.textWhite
    }
  }
}))
