import React, { useCallback, useState } from 'react'
import Button from '@mui/material/Button'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import IconButton from '@mui/material/IconButton'
import CloseIcon from '@mui/icons-material/Close'
import Typography from '@mui/material/Typography'
import PropTypes from 'prop-types'
import { Grid, Box, TextField, CircularProgress } from '@mui/material'
import useStyles from '../../components/Modal/Signin/Signin.styles'
import { usePortalStore } from '../../store/userPortalSlice'
import { userForgotPasswordMobileSchema, userForgotPasswordSchema } from './schema'
import { useForgetPasswordMutation } from '../../reactQuery'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import OtpPhoneForgetVerification from './OtpPhoneForgetVerification'
import { handlePasswordPaste } from '../../utils/helpers'

function CustomTabPanel (props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

export default function Signin () {
  const classes = useStyles()
  const [value, setValue] = useState(0)
  const [phoneCode, setPhoneCode] = useState('1')
  const [phoneInfo, setPhoneInfo] = useState('')
  const portalStore = usePortalStore((state) => state)
  const [isFormSubmitting, setIsFormSubmitting] = useState(false)

  const {
    handleSubmit,
    register,
    formState: { errors, isValid },
    setError
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(value === 0 ? userForgotPasswordSchema : userForgotPasswordMobileSchema)
  })
  const handleClose = () => {
    portalStore.closePortal()
  }

  const handleOtpOpen = () => {
    portalStore.openPortal(
      () => <OtpPhoneForgetVerification phone={phoneInfo} code={phoneCode} forgetPassword={true} />,
      'loginModal'
    )
  }

  const mutation = useForgetPasswordMutation({
    onSuccess: (res) => {
      setIsFormSubmitting(false)
      if (value === 1) {
        toast.success('Verification code sent successfully')
        handleOtpOpen()
      } else {
        toast.success('Forgot password link has been sent to mail id.')
        handleClose()
      }
    },
    onError: (err) => {
      setIsFormSubmitting(false)
      if (err?.response?.data?.errors.length > 0) {
        const { errors } = err.response.data
        errors.forEach((error) => {
          if (error?.description) {
            if (error.errorCode === 3053) {
              setError(
                'email',
                { type: 'focus', message: 'Please enter your registered email.' },
                { shouldFocus: true }
              )
            }
            // else toast.error(error?.description)
          }
        })
      }
    }
  })

  const handleOnSubmit = useCallback(async (data) => {
    setIsFormSubmitting(true)
    mutation.mutate({ email: data.email })
  }, [])

  return (
    <Grid>
      <Grid>
        <DialogContent sx={{ padding: '0' }}>
          <Grid className='modal-section'>
            <Grid sx={{ width: '100%' }}>
              <DialogTitle sx={{ m: 0, p: 2 }} id='customized-dialog-title' style={{ marginBottom: '20px' }}>
                Forgot Password
              </DialogTitle>
              <IconButton
                aria-label='close'
                onClick={handleClose}
                sx={{
                  position: 'absolute',
                  right: -32,
                  top: -32,
                  color: (theme) => theme.palette.grey[500]
                }}
              >
                <CloseIcon />
              </IconButton>
              <form onSubmit={handleSubmit(handleOnSubmit)}>
                <Box sx={{ width: '100%' }} style={{ padding: '0' }} className={classes.modalWrapper}>
                  <CustomTabPanel value={0} index={0} style={{ padding: '0' }}>
                    <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                      <TextField
                        variant='outlined'
                        onPaste={handlePasswordPaste}
                        autoComplete='off'
                        placeholder='Email Address'
                        {...register('email')}
                        inputProps={{ 'data-tracking': 'ForgotPassword.EnterEmail.Fld' }}
                      />
                      <Typography className={classes.errorLabel}>
                        {errors?.email && <p className={classes.errorLabel}>{errors?.email?.message}</p>}
                      </Typography>
                    </Grid>
                  </CustomTabPanel>
                </Box>

                <Box className={classes.bottomSection}>
                  <Grid className={classes.submitBtn}>
                    <Button
                      variant='contained'
                      type='submit'
                      disabled={isFormSubmitting || !isValid}
                      style={{ opacity: isFormSubmitting || !isValid ? 0.5 : 1 }}
                      data-tracking='ForgotPassword.Continue.Btn'
                    >
                      <span>Continue</span>
                      {isFormSubmitting && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
                    </Button>
                  </Grid>
                </Box>
              </form>
            </Grid>
          </Grid>
        </DialogContent>
      </Grid>
    </Grid>
  )
}
