import React, { useState } from 'react'
import useStyles from '../../components/Modal/Signin/Signin.styles'
import { Button, Grid, IconButton, Box, TextField, Typography, DialogContent, CircularProgress, InputAdornment, OutlinedInput } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-hot-toast'
import { getResendfogetOtpMutation, usePhoneForgetVerifyMutation } from '../../reactQuery'
import { usePortalStore } from '../../store/userPortalSlice'
import { useUserStore } from '../../store/useUserSlice'
import DialogTitle from '@mui/material/DialogTitle'
import PropTypes from 'prop-types'
import { verifyPhoneForgotPasswordSchema } from './schema'
import Visibility from '@mui/icons-material/Visibility'
import VisibilityOff from '@mui/icons-material/VisibilityOff'


function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};


const OtpPhoneForgetVerification = ({ phone, code }) => {
  const classes = useStyles()

  const [showPassword, setShowPassword] = useState({
    password: false
  })


  const portalStore = usePortalStore((state) => state)
  const user = useUserStore((state) => state)
  const {
    register,
    formState: { errors },
    handleSubmit: handleOtpSubmit,
    setError,
    setValue
  } = useForm({ resolver: yupResolver(verifyPhoneForgotPasswordSchema) })

  const handleClickShowPassword = (field) => {
    setShowPassword((prevShowPassword) => ({
      ...prevShowPassword,
      [field]: !prevShowPassword[field]
    }))
  }
  const handleMouseDownPassword = (event) => {
    event.preventDefault()
  }


  const mutation = usePhoneForgetVerifyMutation({

    onSuccess: (res) => {
      toast.success('Phone number verified successfully')
      portalStore.closePortal()
    },
    onError: (error) => {
      if (error.response.status === 429) {
        // toast.error(error.response.statusText)
      }
      else if (error.response?.data?.errors?.[0]?.errorCode === 3025) {
        setError('otp', { type: 'focus', message: 'Invalid OTP. Please enter again.' }, { shouldFocus: true })
      }
      // else {
      // toast.error(error.response?.data?.errors?.[0]?.description)
      // }
    }
  })
  const onOtpSubmit = (data) => {

    const newData = {
      confirmPassword: btoa(data.confirmPassword),
      password: btoa(data.password),
      otp: String(data.otp),
      phoneCode: String(code),
      phone: phone
    }
    mutation.mutate({ ...newData })
  }

  const resetMutation = getResendfogetOtpMutation({
    onSuccess: (res) => {
      const tempOtpTimer = { ...otpTimer }
      tempOtpTimer.minutes = 2
      tempOtpTimer.seconds = 59
      setOtpTimer(tempOtpTimer)
    },
    onError: (error) => {
      // if (error.response.status === 429) {
      // toast.error(error.response.statusText)
      // } else {
      // toast.error(error.response?.data?.errors?.[0]?.description)
      // }
    }
  })
  const resetOtpFun = () => {
    resetMutation.mutate({
      phoneCode: String(code),
      phone: phone
    })
  }

  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <Grid>
      <Grid>
        <DialogContent sx={{ padding: "0" }}>
          <Grid className='modal-section'>
            <Grid sx={{ width: "100%" }}>
              <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
                forget password
              </DialogTitle>
              <IconButton
                aria-label="close"
                onClick={handleClose}
                sx={{
                  position: 'absolute',
                  right: -20,
                  top: -20,
                  color: (theme) => theme.palette.grey[500],
                }}
              >
                <CloseIcon />
              </IconButton>

              <form onSubmit={handleOtpSubmit(onOtpSubmit)}>
                <Box sx={{ width: '100%' }} style={{ marginTop: '15px' }} className={classes.modalWrapper}>

                  <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                    <TextField
                      id='outlined-basic'
                      label=''
                      variant='outlined'
                      placeholder='Enter OTP '
                      onChange={(e) => {
                        setValue("otp", e.target.value)
                      }}
                    />
                    <Typography className={classes.errorLabel}>{errors?.otp && errors?.otp?.message}</Typography>

                  </Grid>


                  <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                    <OutlinedInput variant='outlined'
                      id='password'
                      {...register('password')}
                      placeholder='Password'
                      onKeyDown={(evt) => (evt.keyCode === 32) && evt.preventDefault()}
                      type={showPassword.password ? 'text' : 'password'}
                      endAdornment={
                        <InputAdornment position='end'>
                          <IconButton
                            style={{ color: "#fff", marginRight: '0px' }}
                            aria-label='toggle password visibility' onClick={() => handleClickShowPassword('password')}
                            onMouseDown={handleMouseDownPassword}
                            edge='end'
                          >
                            {showPassword.password ? <Visibility /> : <VisibilityOff />}
                          </IconButton>
                        </InputAdornment>
                      }
                    />
                    <Typography className={classes.errorLabel}>{errors?.password && errors?.password?.message}</Typography>
                  </Grid>

                  <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                    <OutlinedInput variant='outlined'
                      onKeyDown={(evt) => {
                        if
                          (evt.keyCode === 32)
                          evt.preventDefault()
                      }}
                      placeholder='Confirm Password' id='confirmPassword'
                      {...register('confirmPassword')}
                      type={showPassword.confirmPassword ? 'text' : 'password'}
                      endAdornment={
                        <InputAdornment position='end'>
                          <IconButton
                            style={{ color: "#fff", marginRight: '0px' }}
                            aria-label='toggle password visibility' onClick={() => handleClickShowPassword('confirmPassword')}
                            onMouseDown={handleMouseDownPassword}
                            edge='end'
                          >
                            {showPassword.confirmPassword ? <Visibility /> : <VisibilityOff />}
                          </IconButton>
                        </InputAdornment>
                      } />
                    <Typography className={classes.errorLabel}>{errors?.confirmPassword && errors?.confirmPassword?.message}</Typography>

                  </Grid>
                </Box>

                <Box className={classes.bottomSection}>
                  <Grid className={classes.submitBtn}>
                    <Button variant="contained" type='submit' disabled={mutation.isLoading}>
                      {mutation.isLoading ?
                        <CircularProgress size={24} style={{ marginRight: 8 }} /> :
                        <span>Change Password</span>}
                    </Button>
                  </Grid>

                </Box>
                <Box className={classes.bottomSection} style={{ marginTop: '15px' }}>

                  <Grid className={classes.submitBtn}>
                    <Button variant="contained" type='button' disabled={resetMutation.isLoading} onClick={resetOtpFun}>
                      {resetMutation.isLoading ?
                        <CircularProgress size={24} style={{ marginRight: 8 }} /> :
                        <span>Resend Otp</span>}
                    </Button>
                  </Grid>
                </Box>
              </form>
            </Grid>
          </Grid>
        </DialogContent>
      </Grid>
    </Grid>
  )
}

export default OtpPhoneForgetVerification
