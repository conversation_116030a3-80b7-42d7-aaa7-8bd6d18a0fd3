import React, { useCallback, useState } from 'react'
import useStyles from '../../components/Modal/Signin/Signin.styles'
import { Button, Grid, IconButton, Box, Typography, DialogContent, CircularProgress, InputAdornment, OutlinedInput } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-hot-toast'
import { useVerifyPasswordMutation } from '../../reactQuery'
import DialogTitle from '@mui/material/DialogTitle'
import PropTypes from 'prop-types'
import { useNavigate } from 'react-router-dom'
import { verifyForgotPasswordSchema } from './schema'
import Visibility from '@mui/icons-material/Visibility'
import VisibilityOff from '@mui/icons-material/VisibilityOff'
function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

const OtpPhoneForgetVerification = ({ handleClose }) => {
  const classes = useStyles()


  const [showPassword, setShowPassword] = useState({
    password: false
  })

  const navigate = useNavigate()

  //const searchParams = new URLSearchParams(document.location.search)
  const searchParams = localStorage.getItem('forgotPassword')

  const {
    register,
    handleSubmit,
    control,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(verifyForgotPasswordSchema)
  })

  const handleClickShowPassword = (field) => {
    setShowPassword((prevShowPassword) => ({
      ...prevShowPassword,
      [field]: !prevShowPassword[field]
    }))
  }
  const handleMouseDownPassword = (event) => {
    event.preventDefault()
  }
  const mutation = useVerifyPasswordMutation({
    onSuccess: (res) => {
      toast.success('Your Password is changed')
      handleClose()
      localStorage.setItem('allowedUserAccess', true)
      navigate('/')
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        errors.forEach(({ errorCode, description }) => {
          // if (errorCode===3022) {
          // toast.error('Password reset link is no longer valid. Kindly request a new link.')
          // } else if (description) {
          // toast.error(description)

          // }
        })
      }
    }
  })

  const handleOnSubmit = useCallback(async (data) => {
    mutation.mutate({ newPasswordKey: searchParams, password: btoa(data.password), confirmPassword: btoa(data.confirmPassword) })
  }, [])



  return (
    <Grid>
      <Grid>
        <DialogContent sx={{ padding: "0" }}>
          <Grid className='modal-section'>
            <Grid sx={{ width: "100%" }}>
              <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
                forgot password
              </DialogTitle>
              <IconButton
                aria-label="close"
                onClick={handleClose}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: (theme) => theme.palette.grey[500],
                }}
              >
                <CloseIcon />
              </IconButton>

              <form onSubmit={handleSubmit(handleOnSubmit)}>
                <Box sx={{ width: '100%' }} style={{ marginTop: '15px' }} className={classes.modalWrapper}>

                  <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                    <OutlinedInput variant='outlined'
                      id='password'
                      {...register('password')}
                      placeholder='New Password'
                      onKeyDown={(evt) => (evt.keyCode === 32) && evt.preventDefault()}
                      type={showPassword.password ? 'text' : 'password'}
                      endAdornment={
                        <InputAdornment position='end'>
                          <IconButton
                            style={{ color: "#fff", marginRight: '0px' }}
                            aria-label='toggle password visibility'
                            onClick={() => handleClickShowPassword('password')}
                            onMouseDown={handleMouseDownPassword}
                            edge='end'
                          >
                            {showPassword.password ? <Visibility /> : <VisibilityOff />}
                          </IconButton>
                        </InputAdornment>
                      }
                    />
                    <Typography className={classes.errorLabel}>{errors?.password?.message}</Typography>
                  </Grid>

                  <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                    <OutlinedInput variant='outlined'
                      onKeyDown={(evt) => {
                        if
                          (evt.keyCode === 32)
                          evt.preventDefault()
                      }} placeholder='Confirm Password' id='confirmPassword'
                      {...register('confirmPassword')}
                      type={showPassword.confirmPassword ? 'text' : 'password'}
                      endAdornment={
                        <InputAdornment position='end'>
                          <IconButton
                            style={{ color: "#fff", marginRight: '0px' }}
                            aria-label='toggle password visibility' onClick={() => handleClickShowPassword('confirmPassword')}
                            onMouseDown={handleMouseDownPassword}
                            edge='end'
                          >
                            {showPassword.confirmPassword ? <Visibility /> : <VisibilityOff />}
                          </IconButton>
                        </InputAdornment>
                      } />
                    <Typography className={classes.errorLabel}>{errors?.confirmPassword?.message}</Typography>

                  </Grid>
                </Box>

                <Box className={classes.bottomSection}>
                  <Grid className={classes.submitBtn}>
                    <Button variant="contained" type='submit' disabled={mutation.isLoading}>
                      {mutation.isLoading ?
                        <CircularProgress size={24} style={{ marginRight: 8 }} /> :
                        <span>Change Password</span>}
                    </Button>
                  </Grid>

                </Box>
              </form>
            </Grid>
          </Grid>
        </DialogContent>
      </Grid>
    </Grid>
  )
}

export default OtpPhoneForgetVerification
