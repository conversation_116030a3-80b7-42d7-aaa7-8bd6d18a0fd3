import * as Yup from 'yup'
const numberPattern = /^-?\d+(\.\d+)?$/

export const userForgotPasswordSchema = Yup.object().shape({
  email: Yup.string()
    .test('is-email', 'Invalid email address', (value) => {
      if (!value) return true
      const emailRegex =
        /^(([^<>()[\]\\.,+;:\s@"]+(\.[^<>()[\]\\.,+;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return emailRegex.test(value)
    })
    .required('Please enter an email address')
})

const onlySpacesRegex = /^\s*$/
const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/
export const userForgotPasswordMobileSchema = Yup.object().shape({
  phoneCode: Yup.number().required('Phone code is required'),
  phoneNumber: Yup.string()
    .matches(phoneRegExp, 'Phone number is not valid')
    .required('Phone number is required')
    .min(8)
    .max(10)
    .test('no-only-spaces', 'Phone number cannot contain spaces', (value) => {
      return !onlySpacesRegex.test(value)
    })
})

export const verifyForgotPasswordSchema = Yup.object().shape({
  password: Yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~`!@#$%^&*()_\-+={[}\]|:;"'<>,.?/]).{8,20}$/,
      'Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character'
    ),
  confirmPassword: Yup.string()
    .required('Confirm Password is required')
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
})

export const verifyPhoneForgotPasswordSchema = Yup.object().shape({
  password: Yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      'Password must include at least one uppercase and lowercase letter, one numeric digit, and one special character (@$!%*?&)'
    ),
  confirmPassword: Yup.string()
    .required('Confirm Password is required')
    .oneOf([Yup.ref('password'), null], 'Passwords must match'),

  otp: Yup.string()
    .matches(numberPattern, 'OTP is not valid')
    .max(6, 'String length must not exceed 6 characters')
    .required('OTP is required')
})
