import { makeStyles } from '@mui/styles'

import { LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({

  btnWhiteGradient: {
    transition: "all 0.3s ease 0s",
    "& .btn-gradient": {
      "&.MuiButtonBase-root": {
        background: theme.colors.btnSecondaryBg,
        boxShadow: theme.shadows[1],
        borderRadius: "30px",
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: "relative",
        overflow: "hidden",
        padding: theme.spacing(0.375, 1.5),
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        "&:before": {
          position: "absolute",
          width: "700px",
          height: "100%",
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: "1rem 1rem",
        },
        "& span": {
          position: "relative",
          color: theme.colors.authCardBg,
          zIndex: "2",
          fontWeight: theme.typography.fontWeightSemiBold,
        },
        "&:hover": {
          background: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
          "& span": {
            color: theme.colors.white,
          }
        }
      },
    }
  },
  btnGradientWrap: {
    "& .btn-gradient": {
      "&.MuiButtonBase-root": {
        background: theme.colors.primaryGradient,
        boxShadow: theme.shadows[1],
        borderRadius: "30px",
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: "relative",
        overflow: "hidden",
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        "&:before": {
          position: "absolute",
          width: "700px",
          height: "100%",
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: "1rem 1rem",
        },
        "& .btn-span": {
          position: "relative",
          color: theme.colors.white,
          zIndex: "2",
          fontWeight: theme.typography.fontWeightSemiBold,
          display: "flex",
        },
        "&:hover": {
          backgroundColor: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
        },
      },
    },

  },

  lobbyRight: {
    ...LobbyRight(theme),
    "& .reward-page-wrap": {
      // "& .lobby-banner-wrap": {
      //   borderRadius: "10px",
      //   overflow: "hidden",
      //   position: "relative",
      //   height: "250px",
      //   display: "flex",
      //   alignItems: "center",
      //   justifyContent: "center",
      //   marginBottom: theme.spacing(3),
      //   [theme.breakpoints.down('md')]: {
      //     backgroundPosition: "53% 100%",
      //     justifyContent: "center",
      //     padding: "0",
      //   },
      //   "& > img": {
      //     width: "100%",
      //     height: "100%",
      //     objectFit: "cover",
      //   },
      //   "& .banner-content-wrap": {
      //     paddingRight: theme.spacing(2),
      //     height: "100%",
      //     position: "absolute",
      //     display: "flex",
      //     flexDirection: "column",
      //     justifyContent: "center",

      //     "& .MuiTypography-h1": {
      //       fontSize: theme.spacing(3),
      //       fontWeight: theme.typography.fontWeightExtraBold,
      //       lineHeight: "1",
      //       background: theme.colors.textGradient,
      //       WebkitBackgroundClip: "text",
      //       WebkitTextFillColor: "transparent",
      //       textDecoration: "none",
      //       [theme.breakpoints.down('lg')]: {
      //         fontSize: theme.spacing(2),
      //       },
      //       [theme.breakpoints.down('md')]: {
      //         fontSize: theme.spacing(2),
      //       },
      //     },
      //     "& .MuiTypography-body1": {
      //       color: theme.colors.white,
      //       fontSize: theme.spacing(1.563),
      //       fontWeight: theme.typography.fontWeightMedium,
      //       textTransform: "capitalize",
      //       [theme.breakpoints.down('lg')]: {
      //         fontSize: theme.spacing(1),
      //       },
      //       [theme.breakpoints.down('md')]: {
      //         fontSize: theme.spacing(1),
      //       },
      //     },
      //     "& .MuiButtonBase-root": {
      //       display: "flex",
      //       alignItems: "center",
      //       justifyContent: "center",
      //       minHeight: "40px",
      //       marginTop: theme.spacing(1),
      //     },
      //     [theme.breakpoints.down('md')]: {
      //       paddingRight: "0",
      //       textAlign: "center",
      //       display: "flex",
      //       justifyContent: "center",
      //       flexDirection: "column",
      //       alignItems: "center"
      //     },
      //   },

      // },
      "& .reward-content-section": {
        position: "relative",
        [theme.breakpoints.down('ml')]: {
          overflowX: "auto",
        },
        "&:before": {
          position: "absolute",
          width: "100%",
          height: "100%",
          content: "''",
          backgroundPosition: "top",
          backgroundSize: "100% 100%",
          [theme.breakpoints.down('ml')]: {
            backgroundSize: "cover"
          },
        },
        "& .user-badge-section": {
          position: "relative",
          zIndex: "1",
          "& .user-badge-row": {
            display: "grid",
            gridGap: theme.spacing(0.625),
            gridTemplateColumns: "repeat(7, 1fr)",
            [theme.breakpoints.down('ml')]: {
              display: "flex",
              paddingBottom: theme.spacing(1.5),
            },
            "& .user-badge-card": {
              borderRadius: "20px",
              height: "220px",
              overflow: "hidden",
              textAlign: "center",
              position: 'relative',
              padding: theme.spacing(1.5),
              margin: theme.spacing(0, 0.313),
              [theme.breakpoints.down('ml')]: {
                minWidth: "150px"
              },
              "&:before": {
                backgroundRepeat: "no-repeat",
                position: "absolute",
                content: "''",
                height: "100%",
                width: "100%",
                backgroundSize: "100%",
                left: "0",
                filter: "grayscale(1)",

              },
              "& .MuiTypography-h4": {
                fontWeight: theme.typography.fontWeightExtraBold,
                fontSize: theme.spacing(1.5),
                position: 'relative',
                color: theme.colors.white,
                marginTop: theme.spacing(1),
              },
              "& .user-badge-Icon": {
                position: "absolute",
                right: "-30px",
                bottom: "-40px",
                transform: "rotate(-20deg)",
                "& img": {
                  width: "160px",
                }
              },
              "&.active": {
                background: theme.colors.gcGradient
              },

            },
            "& .user-badge-card-placeholder": {
              height: "100%",
              width: "16.67%",
            }
          }

        },
        "& .user-reward-section": {
          padding: theme.spacing(2, 0),
          position: "relative",
          zIndex: "1",
          // [theme.breakpoints.down('ml')]: {
          //     overflowX:"auto",
          //  },
          "& .user-reward-content": {
            display: "flex",
            alignItems: "center",
            "& .user-reward-left": {
              position: "relative",
              zIndex: "1",
              "& .user-reward-sc-card": {
                background: theme.colors.rewardScGradient,
                padding: theme.spacing(2),
                borderRadius: "0px 20px 20px 0px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                minHeight: "240px",
                minWidth: "208px",
                "& img": {
                  width: "120px",
                }
              },
              "& .user-reward-gc-card": {
                background: theme.colors.rewardGcGradient,
                padding: theme.spacing(2),
                borderRadius: "0px 20px 20px 0px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                minHeight: "240px",
                minWidth: "208px",
                "& img": {
                  width: "120px",
                }
              },
            }
          },

          "& .user-reward-right": {
            width: '100%',
            position: "relative",
            "& .reward-score-wrap": {
              display: "grid",
              gridGap: theme.spacing(2),
              gridTemplateColumns: "repeat(7, 1fr)",
              position: 'relative',
              marginBottom: theme.spacing(5),
              "& .reward-score-card": {
                borderRadius: theme.spacing(0.625),
                padding: theme.spacing(0.313, 0.625),
                position: "relative",
                minWidth: "140px",
                background: theme.colors.gcGradient,
                "&:before": {
                  position: "absolute",
                  left: "58%",
                  bottom: theme.spacing(-3),
                  content: "''",
                  width: "100%",
                  height: theme.spacing(2),
                  transform: "translate(-50%, -50%)",
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "100px",
                },
                "&.disabled": {
                  filter: "grayscale(1)",
                  opacity: "0.3"
                },
                "& .reward-coin": {
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  margin: theme.spacing(1, 0),
                  '& img': {
                    width: theme.spacing(1.5),
                    height: theme.spacing(1.5),
                    marginRight: theme.spacing(0.313),
                  },
                  "& .MuiTypography-body1": {
                    fontWeight: theme.typography.fontWeightBold,
                    color: theme.colors.white,
                    wordBreak: "break-word",
                  }
                },
                "& .reward-boost-section": {
                  background: theme.colors.white,
                  borderRadius: theme.spacing(0.625),
                  padding: theme.spacing(0.625),
                  textAlign: "center",
                  marginBottom: theme.spacing(0.625),
                  "& .MuiTypography-body1": {
                    color: theme.colors.authCardBg,
                    lineHeight: "1.2",
                  }
                },
              },
              "& .MuiTypography-body1": {
                fontWeight: theme.typography.fontWeightBold,
                color: theme.colors.themeText
              },


            },
            "& .custom-progressbar": {
              position: "relative",
              margin: theme.spacing(0),
              "& .MuiLinearProgress-colorPrimary": {
                background: theme.colors.authCardBg,
                height: "16px",
                borderRadius: "0px",
              },
              "& .MuiLinearProgress-bar": {
                background: theme.colors.primaryGradient,
                borderRadius: "0px",
                overflow: "hidden",
                "&:before": {
                  position: "absolute",
                  width: "100%",
                  height: "100%",
                  content: "''",
                  backgroundImage: theme.colors.btnSecondryStrip,
                  backgroundSize: "1rem 1rem",
                }
              },
            },

          },

          "& .progress-cta-wrap": {
            display: "grid",
            gridGap: theme.spacing(3),
            position: "absolute",
            top: "-27px",
            // left:"12px",
            gridTemplateColumns: "repeat(7, 1fr)",
            width: "100%",
            "& .reward-btn-wrap": {
              minWidth: "130px",
              textAlign: "center",
              [theme.breakpoints.down('lg')]: {
                minWidth: "125px",
              },
              "& .reward-btn": {
                background: theme.colors.authCardBg,
                height: "60px",
                width: "60px",
                margin: "0 auto",
                borderRadius: "100%",
                padding: theme.spacing(0.313),
                display: "block",
                "& img": {
                  width: "100%",
                },
              }
            },
          }
        }
      }

    }



  },




}))
