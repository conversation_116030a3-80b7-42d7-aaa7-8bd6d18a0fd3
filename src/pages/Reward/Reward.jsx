import React, { useEffect, useState } from 'react'
import useStyles from './Reward.styles'
import { Button, Grid, Link, Typography } from '@mui/material'
import Success from '../../components/ui-kit/icons/utils/success.webp'
import WaitingIcon from '../../components/ui-kit/icons/utils/waiting.webp'
import RewardBannerBg from '../../components/ui-kit/icons/banner/reward-banner.webp'
import gcNoShadow from '../../components/ui-kit/icons/svg/gc-no-shadow.svg'
import scNoShadow from '../../components/ui-kit/icons/svg//sc-no-shadow.svg'
import { styled } from '@mui/material/styles'
import LinearProgress, { linearProgressClasses } from '@mui/material/LinearProgress'
import { useVipTiersMutation } from '../../reactQuery/rewardQuery'
import { useUserStore } from '../../store/useUserSlice'
import { PlayerRoutes } from '../../routes'
import { useNavigate } from 'react-router-dom'
import { useBannerStore } from '../../store/useBannerSlice'
import ScrollToTop from '../../components/ScrollToTop'
import ImageRenderer from '../../components/ImageRenderer'

const Reward = () => {
  const classes = useStyles()
  const navigate = useNavigate()
  const [data, setData] = useState([])
  const userDetails = useUserStore((state) => state.userDetails)
  const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
    height: 10,
    borderRadius: 5,
    [`&.${linearProgressClasses.colorPrimary}`]: {
      backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800]
    },
    [`& .${linearProgressClasses.bar}`]: {
      borderRadius: 5,
      backgroundColor: theme.palette.mode === 'light' ? '#1a90ff' : '#308fe8'
    }
  }))

  const vipTierMutation = useVipTiersMutation({
    onSuccess: (res) => {
      let response = res?.data?.vipTiers
      setData(response)
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const { rewardBanners } = useBannerStore((state) => state)
  useEffect(() => vipTierMutation?.mutate(), [])

  return (
    <>
      <ScrollToTop />
      <Grid className={classes.lobbyRight}>
        <Grid className='reward-page-wrap'>
          <Grid className='lobby-banner-wrap'>
            <ImageRenderer src={(rewardBanners && rewardBanners[0]?.desktopImageUrl) || RewardBannerBg} alt='Reward' />
            <Grid className='banner-content-wrap'>
              <Typography variant='h1'>
                {(rewardBanners && rewardBanners[0]?.textOne) || 'Unlock Exclusive VIP Rewards'}
              </Typography>
              <Typography>
                {(rewardBanners && rewardBanners[0]?.textTwo) ||
                  'Purchase Gold Coin Packages To Receive Free SC & Level Up Your Gameplay'}
              </Typography>
              <Grid className={classes.btnGradientWrap}>
                <Button variant='contained' className='btn-gradient' onClick={() => navigate(PlayerRoutes.Store)}>
                  <span className='btn-span'>{(rewardBanners && rewardBanners[0]?.btnText) || 'Deposit Now'}</span>
                </Button>
              </Grid>
            </Grid>
          </Grid>
          <Grid className='reward-content-section'>
            <Grid className='user-badge-section'>
              <Grid className='user-badge-row'>
                {data?.map((tier, index) =>
                  tier?.level <= userDetails?.vipTierDetail?.currentTier?.level ? (
                    <Grid className='user-badge-card active' key={index}>
                      <Grid className='user-badge-content'>
                        <Grid className='ribbon-pop'>Claimed</Grid>
                        <Typography variant='h4'>{tier?.name}</Typography>
                      </Grid>
                      <Grid className='user-badge-Icon'>
                        <img src={tier?.icon} alt='' />
                      </Grid>
                    </Grid>
                  ) : (
                    <Grid className='user-badge-card'>
                      <Grid className='user-badge-content'>
                        <Typography variant='h4'>{tier?.name}</Typography>
                      </Grid>
                      <Grid className='user-badge-Icon'>
                        <img src={tier?.icon} alt='' />
                      </Grid>
                    </Grid>
                  )
                )}
              </Grid>
            </Grid>
            <Grid className='user-reward-section'>
              <Grid className='user-reward-content'>
                <Grid className='user-reward-right'>
                  <Grid className='reward-score-wrap'>
                    {data?.map((tier, index) =>
                      (userDetails?.vipTierDetail && tier?.level) <= userDetails?.vipTierDetail?.currentTier?.level ? (
                        <Grid className='reward-score-card' key={index}>
                          <Grid className='reward-coin'>
                            <img src={scNoShadow} alt='Sc Coin' />
                            <Typography>{tier?.bonusSc}</Typography>
                          </Grid>
                          <Grid className='reward-coin'>
                            <img src={gcNoShadow} alt='Gc Coin' />
                            <Typography>{tier?.bonusGc}</Typography>
                          </Grid>
                          <Grid className='reward-boost-section'>
                            <Typography>{tier?.boost}% Boost</Typography>
                            <Typography>{tier?.rakeback}% Rakeback</Typography>
                          </Grid>
                        </Grid>
                      ) : (
                        <Grid className='reward-score-card disabled'>
                          <Grid className='reward-coin'>
                            <img src={scNoShadow} alt='Sc Coin' />
                            <Typography>{tier?.bonusSc}</Typography>
                          </Grid>
                          <Grid className='reward-coin'>
                            <img src={gcNoShadow} alt='Gc Coin' />
                            <Typography>{tier?.bonusGc}</Typography>
                          </Grid>
                          <Grid className='reward-boost-section'>
                            <Typography>{tier?.boost}% Boost</Typography>
                            <Typography>{tier?.rakeback}% Rakeback</Typography>
                          </Grid>
                        </Grid>
                      )
                    )}
                  </Grid>
                  <Grid className='custom-progressbar'>
                    <BorderLinearProgress variant='determinate' />
                    <Grid className='progress-cta-wrap'>
                      {data?.map((tier, index) =>
                        userDetails?.vipTierDetail && tier?.level <= userDetails?.vipTierDetail?.currentTier?.level ? (
                          <Grid className='reward-btn-wrap' key={index}>
                            <Link href='#' className='reward-btn active'>
                              <img src={Success} alt='Check' />
                            </Link>
                          </Grid>
                        ) : (
                          <Grid className='reward-btn-wrap'>
                            <Link href='#' className='reward-btn'>
                              <img src={WaitingIcon} alt='Check' />
                            </Link>
                          </Grid>
                        )
                      )}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default Reward
