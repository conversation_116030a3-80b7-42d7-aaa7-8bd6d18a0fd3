import React from 'react'

import { But<PERSON>, Grid, IconButton, Typography, DialogContent } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from '../../pages/Lobby/components/UsernamePopup/Username.styles'

const OtpSuccess = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const handleClose = () => {
    portalStore.closePortal()
  }
  return (
    <>
      <Grid className='inner-modal-header'>
        <Typography variant='h4'>Phone Number is verified</Typography>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
      <DialogContent>
        <Grid className='user-name-content-wrap'>
          <Grid className='phone-verified'>
            <Typography variant='h3'>Your Phone Number is verified</Typography>
          </Grid>
        </Grid>
        <Grid className='btn-wrap'>
          <Grid className={classes.btnWhiteGradient}>
            <Button variant='outlined' className='btn-gradient' onClick={handleClose}>
              <span>Close</span>
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </>
  )
}

export default OtpSuccess
