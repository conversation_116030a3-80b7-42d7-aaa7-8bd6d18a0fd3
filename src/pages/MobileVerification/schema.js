import * as Yup from 'yup'
const onlySpacesRegex = /^\s*$/
const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/
const numberPattern = /^-?\d+(\.\d+)?$/

export const userMobileVerificationSchema = Yup.object().shape({
  phoneCode: Yup.number()
    .typeError('Phone code must be a number')
    .required('Phone code is required'),

  phoneNumber: Yup.string()
    .required('Phone number is required')
    .matches(phoneRegExp, 'Phone number is not valid')
    .min(8, 'Phone number must be at least 8 digits')
    .max(10, 'Phone number must be at most 10 digits')
    .test(
      'no-only-spaces',
      'Phone number cannot contain only spaces',
      (value) => value !== undefined && !onlySpacesRegex.test(value)
    )
});

export const userOtpSchema = Yup.object().shape({
  otp: Yup.string()
    .matches(numberPattern, 'OTP is not valid')
    .max(6, 'String length must not exceed 6 characters')
    .required('OTP is required')
})
