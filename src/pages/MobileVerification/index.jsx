import React, { useCallback, useEffect, useState } from 'react'
import {
  Button,
  Grid,
  IconButton,
  Typography,
  DialogContent,
  Select,
  MenuItem,
  Box,
  CircularProgress,
  InputAdornment,
  TextField
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-hot-toast'
import { userMobileVerificationSchema } from './schema'
import { getOtpMutation, useGetProfileMutation } from '../../reactQuery'
import OtpVerification from './OtpVerification'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './style'
import OtpMobile from '../../components/ui-kit/icons/webp/otp-mobile.webp'
import UsaFlag from '../../components/ui-kit/icons/svg/usa-flag.svg'
import indiaFlag from '../../components/ui-kit/icons/svg/indiaFlag.svg'
import { useUserStore } from '../../store/useUserSlice'

const MobileVerification = ({ onMobileVerification, calledFor, handlePlayNow, onClose }) => {
  const classes = useStyles()
  const [phoneCode, setPhoneCode] = useState('1') // Default value set here
  const [phoneNumber, setPhoneNumber] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [timer, setTimer] = useState(0)
  const user = useUserStore((state) => state)
  const environment = import.meta.env.VITE_NODE_ENV
  const portalStore = usePortalStore((state) => state)
  const {
    register,
    formState: { errors },
    handleSubmit,
    setError,
    reset,
    setValue
  } = useForm({
    resolver: yupResolver(userMobileVerificationSchema),
    defaultValues: {
      phoneCode: '1', // Set default phone code here
      phoneNumber: '' // Optionally, set a default value for phone number
    }
  })

  useEffect(() => {
    let interval
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [timer])

  useEffect(() => {
    register('phoneCode') // Register phoneCode to ensure it's captured in the form data
    register('phoneNumber') // Register phoneNumber to ensure it's captured in the form data
  }, [register])

  useEffect(() => {
    setPhoneNumber('')
  }, [phoneCode])

  const handleOtpOpen = () => {
    portalStore.openPortal(
      () => (
        <OtpVerification
          phone={phoneNumber}
          code={phoneCode}
          onMobileVerification={onMobileVerification}
          calledFor={calledFor}
          handlePlayNow={handlePlayNow}
          onClose={onClose}
        />
      ),
      'innerModal'
    )
  }
  const { mutate: getProfileMutation } = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails(res?.data?.data)
    },
    onError: (error) => {
      console.log('Error getting profile:', error)
    }
  })
  const mutation = getOtpMutation({
    onSuccess: () => {
      toast.success('Verification code sent successfully')
      handleOtpOpen()
      setIsLoading(false)
      reset()
    },
    onError: (error) => {
      if (error?.response?.status === 429) {
        // toast.error(error.response.statusText);
      } else if (error?.response?.data?.errors?.[0]?.errorCode === 3016) {
        // toast.error(error?.response?.data?.errors?.[0]?.description);
        setError(
          'phoneNumber',
          {
            type: 'focus',
            message:
              'This phone number is already registered at The Money Factory, please use a different phone number.'
          },
          { shouldFocus: true }
        )
      } else if (error?.response?.data?.errors?.[0]?.errorCode === 3019) {
        // toast.success(error?.response?.data?.errors?.[0]?.description);
        user.setUserDetails.phoneVerified = true
        getProfileMutation()
      }
      // else {
      //     toast.error(error?.response?.data?.errors?.[0]?.description);
      // }
      setIsLoading(false)
    }
  })

  const formatPhoneNumber = (number) => {
    if (phoneCode === '1') {
      const cleaned = ('' + number).replace(/\D/g, '')
      const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/)
      if (match) {
        return `(${match[1]}) ${match[2]}-${match[3]}`
      }
      return cleaned
    }
    return number
  }

  const handlePhoneNumberChange = (event) => {
    const value = event.target.value
    const formattedValue = formatPhoneNumber(value)
    setPhoneNumber(formattedValue)
    setValue('phoneNumber', value, { shouldValidate: true }) // Update the form value
  }

  const onPhoneNumberSubmit = useCallback(
    async (data) => {
      setIsLoading(true)
      const unformattedPhoneNumber = data.phoneNumber.replace(/\D/g, '') // Send the unformatted number
      setPhoneNumber(unformattedPhoneNumber)
      mutation.mutate({
        phoneCode: `${data.phoneCode}`,
        phone: unformattedPhoneNumber
      })
    },
    [mutation]
  )

  const handlePhoneCodeChange = (event) => {
    const value = event.target.value
    setPhoneCode(value)
    setValue('phoneCode', value, { shouldValidate: true }) // Update the form value
  }

  const handleClose = () => {
    portalStore.closePortal()
    if (calledFor === 'gamePlayMobileVerification') {
      onClose()
    }
  }

  const phoneCodeOptions = [{ code: '1', flag: UsaFlag, label: '+1' }]

  if (environment !== 'production') phoneCodeOptions.push({ code: '91', flag: indiaFlag, label: '+91' })
  return (
    <DialogContent className={classes.valutModal}>
      <Typography variant='h4' className='modal-title'>
        Phone Number Verification
      </Typography>
      <Grid className='modal-close'>
        <IconButton edge='start' color='inherit' className='close' onClick={handleClose} aria-label='close'>
          <CloseIcon />
        </IconButton>
      </Grid>
      <Box className={classes.redeemModalContainer}>
        <Box className='otp-graphics'>
          <img src={OtpMobile} alt='Otp' />
        </Box>
        <form onSubmit={handleSubmit(onPhoneNumberSubmit)}>
          <Grid className='custom-phone-input'>
            <Typography>Enter your phone number to receive a verification code</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <TextField
                id='outlined-start-adornment'
                sx={{ m: 1, width: '25ch' }}
                placeholder='Phone number'
                type='text'
                value={phoneNumber}
                onChange={handlePhoneNumberChange}
                inputProps={{
                  maxLength: 10
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position='start'>
                      <Select
                        value={phoneCode}
                        onChange={handlePhoneCodeChange}
                        displayEmpty
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          '& .MuiSelect-select': {
                            padding: '8px 0px' // Adjust padding to better align with the input field
                          }
                        }}
                        inputProps={{
                          sx: {
                            padding: 0, // Remove default padding to fit within the input
                            display: 'flex',
                            alignItems: 'center'
                          }
                        }}
                      >
                        {phoneCodeOptions.map((option) => (
                          <MenuItem key={option.code} value={option.code} sx={{ color: '#fff', fontWeight: 600 }}>
                            <img src={option.flag} style={{ marginRight: '8px' }} alt={`Flag of ${option.label}`} />
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </InputAdornment>
                  )
                }}
              />
            </Box>
          </Grid>
          {errors?.phoneCode && <p className='inputError' style={{ textAlign: 'center', paddingTop: '10px' }}>{errors.phoneCode.message}</p>}
          {errors?.phoneNumber && <p className='inputError' style={{ textAlign: 'center', paddingTop: '10px'}}>{errors.phoneNumber.message}</p>}
          <Grid className='modal-btn'>
            <Button type='submit' className='btn btn-primary' disabled={isLoading || timer > 0}>
              {timer > 0 ? `Resend Code (${timer})` : 'Send Code'}
              {isLoading && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
            </Button>
          </Grid>
        </form>
      </Box>
    </DialogContent>
  )
}

export default MobileVerification
