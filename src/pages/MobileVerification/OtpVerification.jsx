import React, { useEffect, useState } from 'react'
import { Button, Grid, IconButton, Box, Typography, DialogContent, CircularProgress } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-hot-toast'
import useStyles from './style'
import OtpInput from 'react-otp-input'
import { userOtpSchema } from './schema'
import { getResendOtpMutation, useGetProfileMutation, usePhoneVerifyMutation } from '../../reactQuery'
import { usePortalStore } from '../../store/userPortalSlice'
import { useUserStore } from '../../store/useUserSlice'
import PropTypes from 'prop-types'
import otpGraphics from '../../components/ui-kit/icons/webp/otp.webp'

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

const OtpVerification = ({ phone, code, calledFor, handlePlayNow, onClose }) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const [timer, setTimer] = useState(60) // Timer for resend OTP
  const [isTimerActive, setIsTimerActive] = useState(true)
  const [otp, setOtp] = useState('')

  const user = useUserStore((state) => state)
  const {
    register,
    formState: { errors },
    handleSubmit: handleOtpSubmit,
    setError,
    setValue
  } = useForm({ defaultValues: { otp: '' }, resolver: yupResolver(userOtpSchema) })

  // Timer countdown logic
  useEffect(() => {
    let interval
    if (isTimerActive && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1)
      }, 1000)
    } else if (timer === 0) {
      setIsTimerActive(false)
    }
    return () => clearInterval(interval)
  }, [isTimerActive, timer])

  const mutation = usePhoneVerifyMutation({
    onSuccess: (res) => {
      toast.success('Phone number verified successfully')
      if (res?.data?.success) {
        getProfileMutation()
        portalStore.closePortal()
        if (calledFor === 'gamePlay') {
          handlePlayNow()
        }
      }
    },
    onError: (error) => {
      if (error?.response?.data?.errors?.[0]?.errorCode === 3025) {
        setError('otp', { type: 'focus', message: 'Invalid OTP. Please enter again.' }, { shouldFocus: true })
      }
    }
  })

  const { mutate: getProfileMutation, isLoading } = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails(res?.data?.data)
    },
    onError: (error) => {
      console.log('Error getting profile:', error)
    }
  })

  const onOtpSubmit = (data) => {
    const newData = {
      otp: String(data.otp),
      code: Number(code),
      phone
    }
    mutation.mutate({ ...newData })
  }

  const resetMutation = getResendOtpMutation({
    onSuccess: (res) => {
      toast.success(res.data.message)
      setTimer(60) // Reset the timer to 15 seconds
      setIsTimerActive(true) // Start the timer
    },
    onError: () => {
    }
  })

  const resetOtpFun = () => {
    const newData = {
      phoneCode: String(code),
      phone
    }
    resetMutation.mutate({ ...newData })
  }

  const handleClose = () => {
    portalStore.closePortal()
    if (calledFor === 'gamePlayMobileVerification') {
      onClose()
    }
  }

  const handleOtp = (otp) => {
    setOtp(otp)
    setValue('otp', otp)
  }

  return (
    <>
      <Grid>
        <Grid>
          <DialogContent className={classes.valutModal}>
            <Typography variant='h4' className='modal-title'>
              Verify Your Otp
            </Typography>
            <Grid className='modal-close'>
              <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
                <CloseIcon />
              </IconButton>
            </Grid>
            <Grid sx={{ width: '100%' }}>
              <form onSubmit={handleOtpSubmit(onOtpSubmit)}>
                <Box className={classes.redeemModalContainer}>
                  <Box className='otp-graphics'>
                    <img src={otpGraphics} alt='Otp' />
                  </Box>
                  <Grid className='enter-otp-wrap'>
                    <Typography>
                      Enter your 6 Digit code sent to {code && `+${code}`} - {phone}{' '}
                    </Typography>

                    <Grid className='amount-input opt-input'>
                      <OtpInput
                        value={otp}
                        inputType='number'
                        onChange={handleOtp}
                        numInputs={6}
                        // renderSeparator={<span>-</span>}
                        className='otpNumber'
                        inputStyle={{ userSelect: 'none' }} // Prevents user selection
                        containerStyle={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          userSelect: 'none',
                          width: '100%'
                        }}
                        renderInput={(props) => <input {...props} />}
                      />
                    </Grid>
                    {errors?.otp && <p className='inputError'>{errors?.otp?.message}</p>}

                    <Grid className='resend-otp'>
                      <Typography>
                        Don’t get the OTP?
                        <button
                          type='button'
                          style={{ cursor: !(resetMutation.isLoading || isTimerActive) && 'pointer' }}
                          disabled={resetMutation.isLoading || isTimerActive}
                          onClick={resetOtpFun}
                        >
                          Resend OTP{' '}
                        </button>
                        {isTimerActive && `(${timer}s)`}
                      </Typography>
                    </Grid>
                  </Grid>
                  <Grid className='modal-btn'>
                    <Button variant='contained' className='btn btn-primary' type='submit' disabled={mutation.isLoading}>
                      {mutation.isLoading ? <CircularProgress size={24} style={{ marginRight: 8 }} /> : 'Verify OTP'}
                    </Button>
                  </Grid>
                </Box>
              </form>
            </Grid>
          </DialogContent>
        </Grid>
      </Grid>
    </>
  )
}

export default OtpVerification
