import { makeStyles } from '@mui/styles'

import { otpBg } from '../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  valutModal: {
    width: '100%',
    height: '100%',
    backgroundImage: `url(${otpBg})`,
    backgroundSize: 'cover',
    padding: `${theme.spacing(3, 1)} !important`,
    minWidth: theme.spacing(33.875),
    borderRadius: theme.spacing(0.313),
    overflowX: 'hidden',
    position: 'relative',
    [theme.breakpoints.down('sm')]: {
      minWidth: '100%'
    },

    '& .modal-close': {
      color: theme.colors.textWhite,
      marginLeft: 'auto',
      position: 'absolute',
      right: theme.spacing(1.6875),
      top: theme.spacing(1.8125),
      cursor: 'pointer',
      zIndex: '5',
      [theme.breakpoints.down('sm')]: {
        right: theme.spacing(0.6875),
        top: theme.spacing(0.8125)
      }
    },

    '& .modal-title': {
      fontSize: theme.spacing(1.75),
      fontWeight: '700',
      color: theme.colors.textWhite,
      textAlign: 'center',
      width: '100%',
      marginBottom: theme.spacing(2.1875),
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(1)
      }
    },

    '& .custom-phone-input': {
      textAlign: 'center',
      maxWidth: theme.spacing(23.625),
      margin: '0 auto',
      '& .MuiFormControl-root': {
        width: '100%',
        margin: 0,
        color: theme.colors.textWhite,
        '& .MuiInputBase-input': {
          color: theme.colors.textWhite,
          fontSize: theme.spacing(1.125),
          fontWeight: '500',
          minHeight: theme.spacing(1.875),
          borderRadius: theme.spacing(0.625),
          padding: theme.spacing(0.625, 1.2),
          '&:-webkit-autofill': {
            boxShadow: 'none !important',
            borderColor: 'transparent'
          }
        },
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.colors.optBorder,
          borderRadius: theme.spacing(0.625)
        },
        '& .MuiInputAdornment-root': {
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none'
          },
          '& .MuiSvgIcon-root': {
            right: theme.spacing(-1.5),
            top: theme.spacing(0),
            color: theme.colors.textWhite
          },
          '& .MuiInputBase-input': {
            padding: 0,
            borderRadius: 0,
            display: 'flex',
            alignItems: 'center',
            minWidth: theme.spacing(3),
            '& img': {
              width: theme.spacing(2),
              height: theme.spacing(2)
            }
          }
        }
      },
      '&  p': {
        marginBottom: theme.spacing(0.0625),
        fontSize: theme.spacing(1),
        fontWeight: '500',
        textTransform: 'capitalize',
        color: theme.colors.landingFooterText,
        '&.inputError': {
          color: theme.colors.errorText,
          maxWidth: theme.spacing(23.625),
          textAlign: 'left !important',
          margin: '0 auto !important',
          position: 'absolute'
        }
      }
    },
    '& .otp-graphics': {
      textAlign: 'center',
      marginBottom: theme.spacing(3.3125),
      '& img': {
        margin: '0 auto',
        [theme.breakpoints.down('sm')]: {
          width: '100%'
        }
      }
    },
    '& .modal-tabs': {
      marginTop: theme.spacing(2),
      padding: theme.spacing(0, 2),
      borderRadius: theme.spacing(1.875),
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(0.625, 0)
      },
      '& .MuiTabs-flexContainer': {
        background: theme.colors.inputBg,
        borderRadius: theme.spacing(1.875),
        '& .MuiButtonBase-root': {
          borderRadius: theme.spacing(1.875),
          color: theme.colors.textWhite,
          background: theme.colors.inputBg,
          fontSize: theme.spacing(1.125),
          fontWeight: '700',
          flex: '1',
          '&.Mui-selected': {
            background: theme.colors.modalTabBtnActive
          }
        }
      },
      '& .MuiTabs-indicator': {
        display: 'none'
      },
      '& .MuiBox-root': {
        padding: theme.spacing(1, 0)
      }
    },
    '& .theme-select': {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing(1),
      width: 'auto',
      '& .theme-select-content': {
        position: 'relative',
        width: '30%',
        '& .reactInnerCoinSelect__control': {
          width: '100%',
          height: theme.spacing(3.5),
          background: theme.colors.inputBg,
          borderRadius: theme.spacing(0.625),
          border: 'none',
          paddingLeft: theme.spacing(2.2),
          '& div': {
            color: theme.colors.textWhite
          }
        },
        '& .css-t3ipsp-control': {
          boxShadow: '0 0 0 1px #FDB72E'
        },
        '& .reactInnerCoinSelect__menu': {
          background: theme.colors.inputBg,
          color: theme.colors.textWhite,

          '& .reactInnerCoinSelect__option': {
            '&.reactInnerCoinSelect__option--is-focused, &.reactInnerCoinSelect__option--is-selected': {
              background: theme.colors.YellowishOrange,
              color: theme.colors.textBlack
            }
          }
        }
      },

      '& p': {
        color: theme.colors.textWhite,

        fontWeight: '600',
        minWidth: theme.spacing(6)
      },
      '& .MuiInputBase-root': {
        borderRadius: theme.spacing(0.625),
        flex: 1,
        minWidth: '50px',
        [theme.breakpoints.down('sm')]: {
          minWidth: 'unset'
        },
        '&.Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderRadius: theme.spacing(0.625)
            // borderColor:"red",
          }
        }
      },
      '& .select-coin': {
        position: 'absolute',
        left: theme.spacing(0.625),
        top: theme.spacing(1)
      },
      '& .MuiFormLabel-root ': {
        marginLeft: theme.spacing(2)
      }
    },
    '& .amount-input': {
      position: 'relative',
      margin: theme.spacing(2, 0, 0.9375),
      '& .MuiInputBase-root': {
        width: '100%',
        paddingRight: '0',
        background: theme.colors.inputBg,
        borderRadius: theme.spacing(0),
        '& input': {
          width: '100%',
          color: theme.colors.textWhite,

          height: theme.spacing(1.125)
        },
        '&.Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderRadius: theme.spacing(0.625),
            borderColor: theme.colors.YellowishOrange
          }
        },
        '& .MuiInputAdornment-root': {
          position: 'absolute',
          right: theme.spacing(1),

          '& .MuiButtonBase-root': {
            color: theme.colors.textWhite
          }
        }
      },
      '& .input-cta': {
        position: 'absolute',
        right: '2px',
        top: theme.spacing(2.15),
        display: 'flex',
        gap: theme.spacing(1),
        '& .MuiButtonBase-root': {
          background: theme.colors.modalTabBtnActive,
          borderRadius: theme.spacing(0.625),

          fontWeight: '500',
          minHeight: theme.spacing(3.125),
          color: theme.colors.textWhite
        }
      },
      '& .amount-input-text': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: theme.spacing(0, 1, 0.625),
        '& p': {
          color: theme.colors.landingFooterText,
          fontSize: theme.spacing(1)
        }
      },
      '& .MuiInputBase-root-MuiOutlinedInput-root': {
        '&:hover': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent'
          }
        }
      },
      '& .inputError': {
        color: theme.colors.error,
        fontSize: `${theme.spacing(0.8)}!important`,
        margin: '0 !important',
        lineHeight: 'normal !important',
        minHeight: '16px',
        fontWeight: '600'
      },
      '&.opt-input': {
        display: 'flex',
        gap: theme.spacing(1),

        '& > div': {
          gap: theme.spacing(1),
          [theme.breakpoints.down('sm')]: {
            gap: theme.spacing(0.3)
          }
        },
        '&  input': {
          width: '61px !important',
          height: '50px !important',
          borderRadius: 0,
          background: 'transparent',
          textAlign: 'center',
          border: 'none',
          fontWeight: '400',
          fontSize: theme.spacing(1.25),
          borderBottom: '2px solid #5F5E5E',
          color: theme.colors.textWhite,
          outline: 'none',
          [theme.breakpoints.down('sm')]: {
            width: '35px !important',
            height: '35px !important',
            borderRadius: '0'
          }
        }
      }
    },
    '& .modal-btn': {
      marginTop: theme.spacing(2.5),
      textAlign: 'center',
      '& button': {
        width: '100%',
        fontSize: theme.spacing(1),
        color: theme.colors.textBlack,
        maxWidth: theme.spacing(19.1875),
        padding: '0.325rem 1.5rem',
        fontWeight: '700',
        //
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.875)
        },
        '&.Mui-disabled': {
          opacity: '0.3',
          background: '#FDB72E'
        }
      }
    },
    '&  .balance-tab': {
      '& .balance-details': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: theme.spacing(1),
        '& h4': {
          fontSize: theme.spacing(1.4375),
          fontWeight: '700',
          color: theme.colors.textWhite
        },
        '& p': {
          fontSize: theme.spacing(0.875),
          color: theme.colors.valutMOdalText,
          fontWeight: '500'
        }
      },
      '& .balance-card-wrap': {
        // display:"flex",
        gap: theme.spacing(1),
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing(1),
        '& .balance-card': {
          display: 'flex',
          gap: theme.spacing(1),
          justifyContent: 'space-between',
          alignItems: 'center',
          background: theme.colors.inputBg,
          borderRadius: theme.spacing(0.625),
          padding: theme.spacing(0.625),
          width: '100%',
          marginBottom: '10px'
        }
      },
      '& p': {
        fontSize: theme.spacing(0.875),
        color: theme.colors.textWhite
      },
      '& .two-fector': {
        '& p': {
          margin: theme.spacing(1, 0)
        }
      }
    },
    '& .two-fector': {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      margin: theme.spacing(1, 0),
      '& .MuiButtonBase-root': {
        width: '100%',
        fontSize: theme.spacing(1.25),
        // color:theme.colors.textBlack,

        margin: theme.spacing(0.625, 0),
        color: '#000',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      },
      '& p': {
        color: theme.colors.landingFooterText,
        fontSize: theme.spacing(1)
      },
      '& a': {
        textDecoration: 'none'
      }
    },

    '& .inputError': {
      color: theme.colors.error,
      fontSize: `${theme.spacing(0.8)}!important`,
      margin: '0 !important',
      lineHeight: 'normal !important',
      minHeight: '16px',
      fontWeight: '600'
    },
    '& .enter-otp-wrap': {
      '& p': {
        marginBottom: theme.spacing(0.0625),
        fontSize: theme.spacing(1),
        fontWeight: '500',
        textTransform: 'capitalize',
        color: theme.colors.landingFooterText,
        textAlign: 'center',
        '&.inputError': {
          color: theme.colors.error,
          fontSize: `${theme.spacing(0.8)}!important`,
          margin: '0 !important',
          lineHeight: 'normal !important',
          minHeight: '16px',
          fontWeight: '600'
        }
      }
    },
    '& .resend-otp': {
      display: 'flex',
      justifyContent: 'center',

      '& p': {
        fontSize: theme.spacing(0.875),
        textTransform: 'capitalize',
        color: theme.colors.landingFooterText,
        gap: theme.spacing(0.2),
        display: 'flex',
        alignItems: 'center'
      },
      '& button': {
        background: 'transparent',
        border: 'none',
        color: theme.colors.resendText,
        fontWeight: '700',
        textTransform: 'capitalize',
        fontSize: theme.spacing(0.875),
        '&:disabled': {
          opacity: '0.5'
        }
      },
      '& span': {
        color: theme.colors.textWhite,
        paddingLeft: theme.spacing(0.2)
      }
    }
  }
  // submitBtn: {
  //   "& button": {
  //     minWidth: "100%",
  //     color: theme.colors.textBlack,
  //     "&:hover": {
  //       color: theme.colors.YellowishOrange,
  //     }
  //   }
  // }
}))
