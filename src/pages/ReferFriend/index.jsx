import React from 'react'
import useStyles from './../Lobby/Lobby.styles'
import { Button, Grid, List, ListItem, Typography, Divider } from '@mui/material'
import ReferOne from '../../components/ui-kit/icons/webp/referOne.webp'
import ReferTwo from '../../components/ui-kit/icons/webp/referTwo.webp'
import referFriend from '../../components/ui-kit/icons/webp/refer-frnd.webp'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import referIcon from '../../components/ui-kit/icons/opImages/refer.webp'
import referIcon2 from '../../components/ui-kit/icons/opImages/refer2.webp'
import referIcon3 from '../../components/ui-kit/icons/opImages/refer3.webp'
import toast from 'react-hot-toast'
import { copyToClipBoard } from '../../utils/helpers'
import referAFriendQuery from '../../reactQuery/ReferAFriend'
import { useBannerStore } from '../../store/useBannerSlice'
import ScrollToTop from '../../components/ScrollToTop'

const ReferFriend = () => {
  const classes = useStyles()
  const { referAfriend } = useBannerStore((state) => state)
  const onSuccess = (data) => {
    // Handle successful data retrieval
    console.log('Data retrieved successfully:', data)
  }

  const onError = (error) => {
    // Handle error
    console.error('Error fetching data:', error)
  }

  const { data } = referAFriendQuery({ onSuccess, onError })

  const handleCopyCode = (e) => {
    const isCopySuccessfull = copyToClipBoard(`${data.referralLink}`)
    if (!isCopySuccessfull) {
      return toast.error('Failed to copy code!')
    }
    toast.success('Refferal link copied!')
    return null
  }

  return (
    <>
      <ScrollToTop />
      <Grid className={classes.lobbyRight}>
        <Grid className={classes.wrapper}>
          <Grid className={classes.referSection}>
            <Grid className={classes.referBanner}>
              {referAfriend ? (
                referAfriend?.map((info) => (
                  <Grid className='banner'>
                    <img src={info?.desktopImageUrl} className='img-1' />
                    <img src={info?.mobileImageUrl} className='img-2' />
                    <Grid className='bannerText'>
                      <Typography>
                        {info?.textOne}
                        <br />
                        {info?.textTwo}
                      </Typography>
                      {info?.btnText ? <Button className='becomePartner'>{info?.btnText}</Button> : <></>}
                    </Grid>
                  </Grid>
                ))
              ) : (
                <>
                  <Grid>
                    <Typography className='heading' variant='h5'>
                      INVITE YOUR FRIENDS
                    </Typography>
                    <Typography className='text'>Invite friends</Typography>
                  </Grid>
                  <Grid className='referImg'>
                    <img src={referFriend} alt='Reffer Friend' sx={{ width: '100%' }} />
                  </Grid>
                </>
              )}
            </Grid>
            <List className='listItem'>
              <ListItem>Share your referral link with friends</ListItem>
            </List>

            <Typography className='text1'>
              Copy your unique link and invite friends directly or share it on social media platforms like Facebook,
              Instagram, TikTok, and more
            </Typography>

            <Typography className='text2'>And remember: The bigger your network, the more rewards.</Typography>

            <Grid className='referInput'>
              <Grid className='referSelect'>
                <input type='text' placeholder={data?.referralLink} disabled />
                <Button type='button' className='createBonusBtn' onClick={handleCopyCode}>
                  Copy
                </Button>
              </Grid>
            </Grid>

            <Typography variant='h5' sx={{ margin: '20px 0' }}>
              <b>HOW DOES IT WORK?</b>
            </Typography>

            <Grid className='howToWork'>
              <Grid className='grid1'>
                <img src={referIcon} />
                <Typography variant='h6'>Refer a Friend</Typography>
                <Typography>
                  Invite your friends to join Money Factory with your special link. Let's unlock rewards together! Share
                  now to get started on your journey to success
                </Typography>
              </Grid>

              <Grid className='grid1'>
                <img src={referIcon2} />
                <Typography variant='h6'>Your friend will join Money Factory</Typography>
                <Typography>
                  Tell your friends to join Money Factory using your shared link. They simply need to purchase a Coin
                  package and you will earn your reward{' '}
                </Typography>
              </Grid>

              <Grid className='grid1'>
                <img src={referIcon3} />
                <Typography variant='h6'>Earn Rewards with Every Referral</Typography>
                <Typography>
                  Once your friends buy a Gold Coin package, you will receive{' '}
                  <span>{data?.referralBonus?.scAmount}</span> Sweep Coins and{' '}
                  <span>{data?.referralBonus?.gcAmount}</span> Gold Coins.
                </Typography>
              </Grid>
            </Grid>

            <Grid className='friendStatics'>
              <Grid>
                <Typography variant='h6'>
                  <b>FRIENDS STATISTICS</b>
                </Typography>
              </Grid>

              <Grid className='sectionGroup'>
                <Grid className='section1'>
                  <Grid>
                    <img src={ReferOne} alt='img1' height={40} width={40} />
                  </Grid>
                  <Grid className='section2'>
                    <Typography>
                      <b>{data?.referredUsers}</b>
                    </Typography>
                    <Typography className='text3'>Referred Friends</Typography>
                  </Grid>
                </Grid>

                <Grid className='section1'>
                  <Grid>
                    <img src={ReferTwo} alt='img2' height={40} width={40} />
                  </Grid>
                  <Grid>
                    <Typography className='section2'>
                      <b>{data?.qualifiedUsers}</b>
                    </Typography>
                    <Typography className='text3'>Friends Purchases</Typography>
                  </Grid>
                </Grid>

                <Divider className='divider' />

                <Grid className='section1'>
                  <Grid>
                    <img src={usdchipIcon} alt='img3' />
                  </Grid>

                  <Grid>
                    <Typography className='section2'>
                      <b style={{ color: '#AAD840' }}>{data?.gcCoinsEarned}</b>
                    </Typography>
                    <Typography className='text3'>Gold Coins earned</Typography>
                  </Grid>
                </Grid>

                <Grid className='section1'>
                  <Grid>
                    <img src={usdIcon} alt='im4' />
                  </Grid>

                  <Grid className='section2'>
                    <Typography>
                      <b style={{ color: '#AAD840' }}>{data?.scCoinsEarned}</b>
                    </Typography>
                    <Typography className='text3'>Sweep Coins earned</Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default ReferFriend
