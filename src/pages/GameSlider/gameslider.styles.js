import { Box } from '@mui/material'
import { styled } from '@mui/system'

export const GameSwiperWrapper = styled(Box)(({ theme }) => {
  return {
    '& .gameHeading ': {
      position: 'relative',
      '& .heading': {
        display: 'flex',
        gap: theme.spacing(1),
        alignItems: 'center',
        padding: theme.spacing(1, 0),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0.625, 0, 0.313)
        }
      },
      '& p': {
        color: theme.colors.textWhite,
        fontSize: theme.spacing(1.5625),
        fontWeight: theme.typography.fontWeightMedium,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1)
        },
        '& span': {
          cursor: 'pointer'
        }
      },
      '& button': {
        border: 'none',
        '&:after': {
          display: 'none'
        }
      },
      '& .swiper-button-next': {
        borderRadius: theme.spacing(0, 4.1875, 4.1875, 0),
        background: theme.colors.<PERSON><PERSON><PERSON><PERSON>,
        padding: theme.spacing(0.625, 1.875),
        zIndex: '1',
        top: '-36px',
        marginRight: '-12px',
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0, 1),
          height: theme.spacing(1.25),
          top: '-5px'
        },
        '& .nextImg': {
          display: 'none'
        },
        '&.swiper-button-disabled': {
          opacity: '1',
          '& .nextYellowImg': {
            display: 'none'
          },
          '& .nextImg': {
            display: 'block'
          }
        },
        '& img': {
          [theme.breakpoints.down('md')]: {
            width: '10px !important'
          },
          width: '15px'
        }
      },
      '& .swiper-button-prev': {
        top: '-36px',
        right: '68px',
        left: 'auto',
        borderRadius: theme.spacing(4.1875, 0, 0, 4.1875),
        background: theme.colors.GreenishCyan,
        zIndex: '1',
        padding: theme.spacing(0.625, 1.875),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0, 1),
          height: theme.spacing(1.25),
          right: '38px',
          top: '-5px'
        },
        '& .PreviousImg': {
          display: 'none'
        },
        '&.swiper-button-disabled': {
          opacity: '1',
          '& .PreviousYellowImg': {
            display: 'none'
          },
          '& .PreviousImg': {
            display: 'block'
          }
        },
        '& img': {
          [theme.breakpoints.down('md')]: {
            width: '10px !important'
          },
          width: '15px'
        }
      }
    },
    '& .swiper-wrapper': {
      marginTop: theme.spacing(0.5),
      '& .swiper-slide': {
        transition: 'all 200ms ease-in-out',
        lineHeight: '0',
        '& .casino-card': {
          textAlign: 'center',
          // maxWidth:'130px',
          position: 'relative',
          // maxHeight: '184px',
          // position: 'relative',
          transition: 'all 200ms ease-in-out',
          // paddingBottom:"120%",
          '&:hover': {
            transform: 'translateY(-0.5rem)',
            transition: 'all 0.5s ease-in-out',
            '& .overlayPlay': {
              display: 'flex',
              borderRadius: '8px'
            }
          },
          '& .fav-icon': {
            width: '20px',
            height: '20px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            top: '10px',
            right: '10px',
            zIndex: '8',
            '& img': {
              width: '20px',
              height: '20px',
              objectFit: 'contain',
              objectPosition: 'center'
            },
            '&:hover': {
              cursor: 'pointer'
            }
          },
          '& .casinoGame-img': {
            width: '100% !important',
            borderRadius: '8px',
            height: '100% !important',
            // width:"100%",
            // position:"absolute",
            // left:"0",
            // top:"0",
            '&:hover': {
              backgroundColor: theme.colors.textWhite,
              cursor: 'pointer'
            }
          },
          '& .tournamentLogo': {
            position: 'absolute',
            left: '2px',
            top: '5px',
            width: '30px',
            height: '30px'
          },
          '& .prgamatic-jackpot-amount-wrapper': {
            position: 'absolute',
            top: '12px',
            left: '47%',
            display: 'flex',
            justifyContent: 'center',
            gap: '4px',
            alignItems: 'center',
            background: '#000000B2',
            borderRadius: '17px',
            whiteSpace: 'nowrap',
            transform: 'translate(-50%, 0)',
            padding: '1px 5px'
          }
        }
      }
    },
    '& .overlayPlay': {
      position: 'absolute',
      display: 'none',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      margin: '0 auto',
      inset: '0',
      flexDirection: 'column',
      background: 'linear-gradient(180deg, rgba(255,84,37,0.7) 0%, rgba(251,162,83,0.7) 100%)',
      cursor: 'pointer',
      transition: 'all 0.5s ease-in-out',
      borderRadius: '8px',
      '& a': {
        color: theme.colors.textWhite,
        textDecoration: 'none'
      },
      '& .MuiTypography-h6': {
        // wordBreak:'break-all',
        [theme.breakpoints.down('xl')]: {
          fontSize: theme.spacing(0.75)
        }
      },
      '& .playtext': {
        [theme.breakpoints.down('xl')]: {
          fontSize: theme.spacing(0.75)
        }
      }
    }
  }
})
