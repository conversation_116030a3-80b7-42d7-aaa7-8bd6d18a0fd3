import React, { useState } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import useStyles from '../Lobby/Lobby.styles'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import { FreeMode, Navigation } from 'swiper/modules'
import 'swiper/css/navigation'
import { Grid, Typography, useTheme, Link, Tooltip } from '@mui/material'
import { GameSwiperWrapper } from './gameslider.styles'
import CasinoCard from '../../components/ui-kit/icons/utils/casinoGames.webp'
import Heart from '../../components/ui-kit/icons/utils/heart.webp'
import HeartFill from '../../components/ui-kit/icons/utils/heartFill.webp'
import whitePlay from '../../components/ui-kit/icons/svg/white-play-button.svg'
import populerHeadingIcon from '../../components/ui-kit/icons/svg/populer-games-heading-icon.svg'
import PreviousWhiteIcon from '../../components/ui-kit/icons/svg/previous-white.svg'
import PreviousYellowIcon from '../../components/ui-kit/icons/svg/previous-yellow.svg'
import NextYellowIcon from '../../components/ui-kit/icons/svg/next-yellow.svg'
import NextWhiteIcon from '../../components/ui-kit/icons/svg/next-white.svg'
import TournamentLogo from '../../components/ui-kit/icons/png/tournament-logo.png'
import { getItem, getLoginToken } from '../../utils/storageUtils'
import { useUserStore } from '../../store/useUserSlice'
import { useGamesStore, usePortalStore } from '../../store/store'
import MobileVerification from '../MobileVerification'
import { useGetProfileMutation } from '../../reactQuery'
import LazyImage from '../../utils/lazyImage'
import { formatPriceWithCommas } from '../../utils/helpers'
import { usePragmaticJackpotStore } from '../../store/usePragmaticJackpot'
import PragmaticJackpotSCLogo from '../../components/ui-kit/icons/svg/pragmatic-sc.svg'
import PragmaticJackpotGCLogo from '../../components/ui-kit/icons/svg/pragmatic-gc.svg'

const GameSlider = ({ index, subCategory, keySubCategory, toggleFavorite, handlePlayNow }) => {
  const {
    isLoading,
    favorites
  } = useGamesStore((state) => state)

  const theme = useTheme()
  const auth = useUserStore((state) => state)
  const classes = useStyles()
  const setSelectedSubCat = useGamesStore((state) => state.setSelectedSubCat)
  const portalStore = usePortalStore((state) => state)
  const coinType = getItem('coin')
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const [gameId, setGameId] = useState(null)
  const [name, setName] = useState('')
  const [gameType, setGameType] = useState('')
  const { pragmaticJackpotSc, pragmaticJackpotGc } = usePragmaticJackpotStore()
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (!res?.data?.data?.phoneVerified) {
        portalStore.openPortal(
          () => <MobileVerification calledFor='gamePlay' handlePlayNow={() => handlePlayNow(gameId, name, gameType)} />,
          'innerModal'
        )
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })
  const handlePhoneVerification = (gameId, name, gameType) => {
    if (coinType === 'SC' && !auth?.userDetails?.phoneVerified && (!!getLoginToken() || auth.isAuthenticate)) {
      setGameId(gameId)
      setName(name)
      setGameType(gameType)
      getProfileMutation.mutate()
    } else {
      handlePlayNow(gameId, name, gameType)
    }
  }

  return (
    <div key={`sliderGrid-${subCategory?.name}-${index}`}>
      <GameSwiperWrapper theme={theme}>
        <Grid className='gameHeading'>
          <Grid className='heading'>
            <LazyImage
              src={subCategory.imageUrl?.thumbnail || populerHeadingIcon}
              alt='category'
              style={{ width: '24px', height: '24px' }}
            />
            <Typography>
              <span
                onClick={() => {
                  keySubCategory(subCategory?.name)
                  setSelectedSubCat(subCategory?.name)
                }}
              >
                {subCategory?.name}
              </span>
            </Typography>
          </Grid>

          <Grid>
            <button
              id={`swiper-button-next-${subCategory?.masterGameSubCategoryId}`}
              className='swiper-button-next'
            >
              <LazyImage
                src={NextYellowIcon}
                alt='Next Icon'
                className='nextYellowImg'
                style={{ width: '20px', height: '20px' }}
              />
              <LazyImage
                src={NextWhiteIcon}
                alt='Next Icon'
                className='nextImg'
                style={{ width: '20px', height: '20px' }}
              />
            </button>

            <button
              id={`swiper-button-prev-${subCategory?.masterGameSubCategoryId}`}
              className='swiper-button-prev'
            >
              <LazyImage
                src={PreviousYellowIcon}
                alt='Next Icon'
                className='PreviousYellowImg'
                style={{ width: '20px', height: '20px' }}
              />
              <LazyImage
                src={PreviousWhiteIcon}
                alt='Prev Icon'
                className='PreviousImg'
                style={{ width: '20px', height: '20px' }}
              />
            </button>
          </Grid>
        </Grid>

        <Grid key={`mySwiper-${subCategory?.name}-${index}}`}>
          <Swiper
            spaceBetween={10}
            freeMode
            navigation={{
              nextEl: `#swiper-button-next-${subCategory?.masterGameSubCategoryId}`,
              prevEl: `#swiper-button-prev-${subCategory?.masterGameSubCategoryId}`
            }}
            modules={[FreeMode, Navigation]}
            className='mySwiper'
            breakpoints={{
              0: {
                slidesPerView: 3
              },
              768: {
                slidesPerView: 4
              },
              1024: {
                slidesPerView: 6.5
              }
            }}
          >
            {subCategory?.subCategoryGames?.map((game, idx) => {
              const gameId = String(game?.masterCasinoGameId)

              const isScPragmatic = coinType === 'SC' && pragmaticJackpotSc?.hasOwnProperty(gameId)
              const isGcPragmatic = coinType === 'GC' && pragmaticJackpotGc?.hasOwnProperty(gameId)
              const jackpotValue = isScPragmatic
                ? pragmaticJackpotSc?.[gameId]
                : isGcPragmatic
                  ? pragmaticJackpotGc?.[gameId]
                  : null

              return (
                <SwiperSlide key={`${game?.masterCasinoGameId}_${idx}`}>
                  <div
                    className='custom-col-2'
                    key={`${game?.masterCasinoGameId}_${idx}`}
                    transition={{ delay: idx / 10 }}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                  >
                    <Tooltip
                      title={game?.gameInTournament ? 'TOURNAMENT' : ''}
                      arrow
                      disableHoverListener={!game?.gameInTournament}
                      placement='top-start' // You can adjust this if needed
                      componentsProps={{
                        tooltip: {
                          sx: {
                            backgroundColor: '#FF3000 ',
                            color: 'white',
                            textAlign: 'center',
                            fontWeight: '800',
                            fontSize: '14px'
                          },
                          style: {
                            maxWidth: 'none' // optional: ensures full text is visible
                          }
                        },
                        arrow: {
                          sx: {
                            color: '#FF3000',
                            position: 'absolute',
                            left: '16px !important',
                            transform: 'translate(0px, 0px) !important',
                            marginTop: '-8px !important'
                          }
                        }
                      }}
                    >
                      <Grid className='casino-card'>
                        <LazyImage
                          src={game?.imageUrl || CasinoCard}
                          className='casinoGame-img'
                          alt='Casino'
                          style={{ width: '100%', height: '200px' }}
                        />

                        {(!!getLoginToken() || auth.isAuthenticate) && (
                          <Grid className='fav-icon'>
                            <span
                              onClick={
                                !isLoading
                                  ? () =>
                                      toggleFavorite(
                                        game.masterCasinoGameId,
                                        favorites[game.masterCasinoGameId?.toString()]
                                      )
                                  : undefined
                              }
                            >
                              <LazyImage
                                alt='heart'
                                src={(game.FavoriteGames || favorites[game.masterCasinoGameId?.toString()]) ? HeartFill : Heart}
                              />
                            </span>
                          </Grid>
                        )}
                        <Grid
                          className='overlayPlay'
                          onClick={() => {
                            handlePhoneVerification(game?.masterCasinoGameId, game.name, subCategory?.name)
                          }}
                        >
                          <Typography variant='h6' sx={{ lineHeight: '20px', textAlign: 'center', padding: '0 10px' }}>
                            <b>{game.name}</b>
                          </Typography>
                          <Link to='/'>
                            <LazyImage src={whitePlay} alt='Play' className={classes.playImg} />
                            <Typography className='playtext'>
                              <b>Play Now</b>
                            </Typography>
                          </Link>
                        </Grid>
                        {jackpotValue !== null && (
                          <div className='prgamatic-jackpot-amount-wrapper'>
                            <LazyImage
                              src={coinType === 'SC' ? PragmaticJackpotSCLogo : PragmaticJackpotGCLogo}
                              alt='prgamatic-jakcpot-logo'
                            />
                            <Typography
                              style={{
                                color: ` ${coinType === 'SC' ? '#00C80E' : '#FDB72E'}`,
                                fontWeight: '700',
                                fontSize: '10px'
                              }}
                            >
                              {formatPriceWithCommas(jackpotValue)} {coinType}
                            </Typography>
                          </div>
                        )}
                        {game?.gameInTournament && (
                          <LazyImage src={TournamentLogo} alt='tournament-logo' className='tournamentLogo' />
                        )}
                      </Grid>
                    </Tooltip>
                  </div>
                </SwiperSlide>
              )
            })}
          </Swiper>
        </Grid>
      </GameSwiperWrapper>
    </div>
  )
}

export default GameSlider
