import React from 'react';
import CustomModal from '../../components/CustomModal';
import useStyles from './affiliates.styles';
import { Grid, Typography } from '@mui/material';
import { affiModalBanner } from '../../components/ui-kit/icons/utils';

const AffiliateThanksModal = () => {
  const classes = useStyles()
  return (
    <div>
      <CustomModal>
        <Grid className={classes.affiliateModalContent}>
          <Grid className='textDiv'>
            <Typography variant='h2'>
              Your request has been sent
            </Typography>

            <Typography>
              We will review your application and give you feedback on it as soon as possible. Please give us some time and a personal manager will contact you
            </Typography>
          </Grid>

          <Grid className='imgDiv'>
            <img src={affiModalBanner} />
          </Grid>
        </Grid>
      </CustomModal>
    </div>
  )
}

export default AffiliateThanksModal
