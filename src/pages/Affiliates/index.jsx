import React, { useState } from 'react'
import useStyles from './affiliates.styles'
import { Grid, Typography, Box } from '@mui/material'
import { useBannerStore } from '../../store/useBannerSlice'
import { useLocation } from 'react-router-dom'
import ScrollToTop from '../../components/ScrollToTop'
import { updateSDKPageVisit } from '../../utils/optimoveHelper'
import CreateAffiliate from './CreateAffiliate'
import AffiliateThanksYouPage from './AffiliateThanksYouPage'
import BannerManagement from '../../components/BannerManagement'
import JackpotBadge from '../Jackpot/JackpotBadge'

const Affiliates = () => {
  const classes = useStyles()
  const [showThankYouPage, setShowThankYouPage] = useState(true)
  const { affiliatePage } = useBannerStore((state) => state)
  const location = useLocation()

  const currentUrl = window.location.origin + location.pathname + location.search
  if (import.meta.env.VITE_NODE_ENV === 'production') {
    updateSDKPageVisit(currentUrl, 'Affiliates')
  }

  return (
    <>
      <ScrollToTop />
      <Grid className={classes.lobbyRight}>
        <Box className='affiliate-page'>
          <Grid>
            <BannerManagement bannerData={affiliatePage} />
          </Grid>
          <Box className='how-section'>
            <Grid className='inner-heading'>
              <Typography variant='h4'>How to Get Started</Typography>
            </Grid>
            <Box className='reffer-theme-card'>
              <Grid className='how-it-works-listing'>
                <ol>
                  <li>
                    <Grid className='how-it-works-list-card'>
                      <Typography variant='h4'>Sign Up</Typography>
                      <Typography>Easily join by filling out a quick registration form.</Typography>
                    </Grid>
                  </li>
                  <li>
                    <Grid className='how-it-works-list-card'>
                      <Typography variant='h4'>Promote & Earn</Typography>
                      <Typography>
                        Promote our casino with your affiliate link via various platforms and generate rewards!
                      </Typography>
                    </Grid>
                  </li>
                  <li>
                    <Grid className='how-it-works-list-card'>
                      <Typography variant='h4'>Track / optimize</Typography>
                      <Typography>Monitor performance and optimize strategies with our tracking tools.</Typography>
                    </Grid>
                  </li>
                </ol>
              </Grid>
            </Box>
          </Box>

          {showThankYouPage ? (
            <CreateAffiliate setShowThankYouPage={setShowThankYouPage} />
          ) : (
            <AffiliateThanksYouPage />
          )}
        </Box>
        <JackpotBadge />
      </Grid>
    </>
  )
}

export default Affiliates
