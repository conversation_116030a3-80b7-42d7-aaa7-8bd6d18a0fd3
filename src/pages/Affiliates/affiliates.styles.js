import { makeStyles } from '@mui/styles'

import { AffiliateWebBanner } from '../../components/ui-kit/icons/banner'
import { LobbyRight, InnerBanner } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    minHeight: 'auto',

    '& .affiliate-page': {
      maxWidth: theme.spacing(74.6875),
      margin: '0 auto',
      '& .how-section': {
        padding: theme.spacing(2, 0),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1, 0)
        },
        '& .how-it-works-listing': {
          '& ol': {
            paddingLeft: theme.spacing(1),
            counterReset: 'item',
            listStyle: 'none',
            [theme.breakpoints.down('md')]: {
              paddingLeft: 0
            },
            '& li': {
              margin: theme.spacing(2.75, 0),
              counterIncrement: 'item',
              fontSize: theme.spacing(1.25),
              fontWeight: '700',
              display: 'flex',
              [theme.breakpoints.down('md')]: {
                margin: theme.spacing(2, 0)
              },
              '&:before': {
                marginRight: theme.spacing(2),
                content: 'counter(item)',
                background: theme.colors.Promosuccess,
                height: theme.spacing(2.5),
                width: theme.spacing(2.5),
                minWidth: theme.spacing(2.5),
                borderRadius: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: '3',
                [theme.breakpoints.down('md')]: {
                  marginRight: theme.spacing(2)
                }
              },
              '&:after': {
                marginRight: theme.spacing(2),
                height: theme.spacing(3),
                width: theme.spacing(3),
                minWidth: theme.spacing(3),
                borderRadius: '100%',
                border: `1px dashed ${theme.colors.refferStepBorder}`,
                content: "''",
                left: theme.spacing(-0.313),
                top: theme.spacing(-0.313),
                position: 'absolute',
                background: theme.colors.inputBg,
                zIndex: '1'
              },
              '& .how-it-works-list-card': {
                position: 'relative',
                fontSize: theme.spacing(1.25),
                fontWeight: '600',
                [theme.breakpoints.down('md')]: {
                  fontSize: theme.spacing(1)
                },
                '&:before': {
                  position: 'absolute',
                  left: theme.spacing(-3.25),
                  top: theme.spacing(3.125),
                  height: '100%',
                  width: '2px',
                  borderLeft: `1px dashed ${theme.colors.refferStepBorder}`,
                  content: "''"
                  // zIndex:"2",
                },
                '& h4': {
                  fontSize: theme.spacing(1.25),
                  fontWeight: '700',
                  color: theme.colors.YellowishOrange,
                  lineHeight: theme.spacing(1.5625)
                },
                '& p': {
                  fontWeight: '400',
                  fontSize: theme.spacing(0.8125),
                  color: theme.colors.textWhite
                }
              },
              '&:last-child': {
                '& .how-it-works-list-card': {
                  '&:before': {
                    display: 'none'
                  }
                }
              }
            }
          }
        }
      },
      '& .reffer-theme-card': {
        background: theme.colors.inputBg,
        borderRadius: theme.spacing(0.5625),
        border: `1px solid ${theme.colors.refferCardBorder}`,
        padding: theme.spacing(0, 2),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1)
        }
      },
      '& .inner-heading': {
        marginBottom: theme.spacing(4.1875),
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(1.125)
        },
        '& h4': {
          fontSize: theme.spacing(1.875),
          fontWeight: '500',
          lineHeight: theme.spacing(2),
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      },
      '& .genral-tab': {
        border: `1px solid ${theme.colors.modalTabBtnActive}`,
        borderRadius: theme.spacing(0.625),
        margin: theme.spacing(2, 0),
        '& .setting-card-header': {
          background: theme.colors.coinBundle,
          padding: theme.spacing(1, 2),
          borderRadius: theme.spacing(0.5625),
          minHeight: theme.spacing(4.1875),
          display: 'flex',
          alignItems: 'center',
          '& h4': {
            fontSize: theme.spacing(1.25),
            fontWeight: '500',
            display: 'flex',
            gap: theme.spacing(1.125)
          }
        },
        '& .setting-card-details': {
          padding: theme.spacing(2.1875, 2.9375),
          [theme.breakpoints.down('lg')]: {
            padding: theme.spacing(1)
          },
          '& .MuiFormControl-root': {
            // minWidth: theme.spacing(37.5),
            [theme.breakpoints.down('lg')]: {
              minWidth: '100%'
            },
            '& .MuiInputBase-root': {
              paddingRight: 0,
              '& .MuiInputBase-input': {
                border: `2px solid ${theme.colors.modalTabBtnActive}`,
                color: theme.colors.textWhite,
                borderRadius: theme.spacing(0.625),

                padding: theme.spacing(0.625, 1)
                // height:theme.spcing()
              },
              '&.Mui-focused': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.colors.YellowishOrange,
                  borderRadius: theme.spacing(0.625)
                }
              }
            },
            '& .MuiFormLabel-root': {
              color: theme.colors.textWhite,
              lineHeight: theme.spacing(1)
            }
          },
          '& p': {
            marginBottom: theme.spacing(1)
          },
          '& .MuiIconButton-edgeEnd': {
            color: theme.colors.textWhite
          },
          '& .MuiInputAdornment-root': {
            position: 'absolute',
            right: theme.spacing(2)
          },
          '& .privacy-card': {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(1),
            padding: theme.spacing(1.5, 0),
            // margin:theme.spacing(1.2, 0),
            '& h4': {
              fontSize: theme.spacing(1.125),
              fontWeight: '500',
              marginBottom: theme.spacing(0.313)
            },
            '& p': {
              color: theme.colors.switchText,
              fontWeight: '400',

              margin: '0'
            },
            '& .custom-radio-wrap': {
              '& .MuiButtonBase-root': {
                color: theme.colors.sidebarNavBg,
                background: theme.colors.switchTrackoff,
                padding: 0,
                marginRight: theme.spacing(1),
                '&.Mui-checked': {
                  color: theme.colors.YellowishOrange
                }
              },
              '& .MuiFormControlLabel-root': {
                paddingBottom: theme.spacing(1)
              }
            },
            '& .kyc-content': {
              '& h3': {
                fontWeight: '700',
                fontSize: theme.spacing(2.2),
                marginBottom: theme.spacing(0.313)
              },
              '&.kyc-success': {
                textAlign: 'center',
                width: '100%',
                '& .verified-img': {
                  height: theme.spacing(5),
                  width: theme.spacing(5),
                  margin: '0 auto 1rem'
                }
              }
            }
          }
        },
        '& .setting-card-footer': {
          padding: theme.spacing(1, 2.9375),
          borderTop: `1px solid ${theme.colors.modalTabBtnActive}`,
          display: 'flex',
          justifyContent: 'space-between',
          gap: theme.spacing(1),
          [theme.breakpoints.down('sm')]: {
            flexDirection: 'column',
            padding: theme.spacing(1)
          },
          '& .btn-primary': {
            color: theme.colors.textBlack,
            [theme.breakpoints.down('sm')]: {
              minWidth: theme.spacing(10.125)
            },
            '&:hover': {
              color: theme.colors.YellowishOrange
            },
            '&:disabled': {
              color: theme.colors.textBlack,
              background: theme.colors.disabledBtn
            }
          },
          '& .MuiCheckbox-root': {
            '& svg': {
              color: theme.colors.modalTabBtnActive
            }
          },
          '& .inputCheck': {
            display: 'flex',
            alignItems: 'center'
            // gap: theme.spacing(0.625),
          }
        },
        '& .input-wrap': {
          marginBottom: theme.spacing(1)
        }
      },
      '& .text-area': {
        '& .MuiInputBase-root': {
          padding: '0'
        }
      },
      '& .MuiFormControl-root': {
        width: '100%'
      }
    }
  },
  errorLabel: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    lineHeight: 'normal !important',
    fontWeight: '600 !important'
  },

  InnerBanner: {
    ...InnerBanner(theme),
    background: `url(${AffiliateWebBanner})`,
    minHeight: theme.spacing(26.75),
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    [theme.breakpoints.down('md')]: {
      minHeight: theme.spacing(16.875)
    },
    [theme.breakpoints.down('sm')]: {
      minHeight: theme.spacing(11.875),
      backgroundPosition: '57% 100%'
    },
    '& .banner-content': {
      marginLeft: theme.spacing(4),
      [theme.breakpoints.down('md')]: {
        marginLeft: '0'
      },
      '& h2': {
        fontSize: theme.spacing(2.5),
        fontWeight: '700',
        lineHeight: theme.spacing(2.75),
        maxWidth: theme.spacing(37.125),
        marginBottom: theme.spacing(1.875),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.5),
          lineHeight: theme.spacing(2)
        }
      }
    }
  }
}))
