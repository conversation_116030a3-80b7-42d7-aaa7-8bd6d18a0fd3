import * as Yup from 'yup';

const affiliateSchema = Yup.object().shape({
  // firstName: Yup.string().min(3, 'First Name must be at-least 3 characters')
  //   .max(50, "First Name must be at most 50 characters")
  //   .matches(/^[a-zA-Z]+(\s[a-zA-Z]+)?$/, 'Only Alphabets and Space Allowed and Must Start with Alphabet')
  //   .required('Please provide first name'),
  // lastName: Yup.string().min(2, 'Last Name must be at-least 2 characters')
  //   .max(50, "Last Name must be at most 50 characters")
  //   .matches(/^[a-zA-Z]+([ \-]{0,1}[a-zA-Z]+){0,1}$/, 'Only Alphabets  Space and hypens  Allowed and Must Start with Alphabet')
  //   .required('Please provide last name'),
  fullName: Yup.string()
    // .trim()
    .min(3, 'Full Name must be at least 3 characters')
    .max(100, 'Full Name must be at most 100 characters')
    .test(
      'no-leading-trailing-spaces',
      'Full Name should not have leading or trailing spaces',
      value => value && value === value.trim()
    )
    .matches(/^(?!\s)(?!.*\s{2,})[a-zA-Z]+(?: [a-zA-Z]+)*$/, 'Only alphabets allowed with single spaces between words')
    .required('Please provide your full name'),
  email: Yup.string()
    .max(150, 'Email must be at most 150 characters')
    .required('Please enter an email address')
    .test(
      'no-leading-trailing-spaces',
      'Email should not have leading or trailing spaces',
      (value) => {
        if (!value) return false; // Ensure value exists
        return value.trim() === value; // Reject if it has leading/trailing spaces
      }
    )
    .test('is-email', 'Invalid email address', value => {
      if (!value) return true;
      const emailRegex = /^(([^<>()[\]\\.,+;:\s@"]+(\.[^<>()[\]\\.,+;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return emailRegex.test(value);
    })
    .required('Please enter an email address'),
  phone: Yup.string()
    .matches(/^\d{10}$/, 'Phone number must be 10 digits')
    .required('Please provide a valid phone number'),
  // state: Yup.string()
  //   .required('State is required'),
  // preferredContact: Yup.string()
  //   .required('Please enter your preferred contact method'),
  trafficSource: Yup.string()
    .max(500, 'Traffic source must be at most 500 characters')
    .test(
      'no-leading-trailing-spaces',
      'Traffic Source should not have leading or trailing spaces',
      value => value && value === value.trim()
    )
    .matches(/^(?!\s)(?!.*\s{2,})[a-zA-Z0-9.,]+(?: [a-zA-Z0-9.,]+)*$/, 'No leading/trailing spaces, and only single spaces between words')
    .required('Please enter your traffic source'),
  plan: Yup.string()
    .max(500, 'Plan must be at most 500 characters')
    .test(
      'no-leading-trailing-spaces',
      'Plan should not have leading or trailing spaces',
      value => value && value === value.trim()
    )
    .matches(/^(?!\s)(?!.*\s{2,})[a-zA-Z0-9.,]+(?: [a-zA-Z0-9.,]+)*$/, 'No leading/trailing spaces, and only single spaces between words')
    .required('Please tell us how you plan to attract people'),
  isTermsAccepted: Yup.boolean()
    .oneOf([true], 'Please accept the terms and conditions'),
});

export default affiliateSchema;
