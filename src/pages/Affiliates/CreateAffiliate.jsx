import React from 'react'
import { Button, Box, Grid, Typography, FormControl, OutlinedInput, CircularProgress, Checkbox } from '@mui/material'
import { useForm } from 'react-hook-form'
import { useAffiliateMutation } from '../../reactQuery'
import useStyles from './affiliates.styles'
import { yupResolver } from '@hookform/resolvers/yup'
import affiliateSchema from './Schema'
import toast from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'

const CreateAffiliate = ({ setShowThankYouPage }) => {
  const classes = useStyles()
  const label = { inputProps: { 'aria-label': 'Checkbox demo' } }
  const {
    register,
    handleSubmit,
    formState: { errors, isValid, values }
  } = useForm({
    defaultValues: {
      fullName: '',
      email: '', // Initial value for email
      phone: '',
      phoneCode: '+1',
      trafficSource: '', // Initial value for attracting people details
      plan: '',
      isTermsAccepted: false
    },
    resolver: yupResolver(affiliateSchema),
    mode: 'onChange'
  })
  const navigate = useNavigate()

  const onAffiliateSuccess = (data) => {
    toast.success(`${data?.data?.message}`)
    setTimeout(() => {
      setShowThankYouPage(false)
    }, 1000)
  }
  const onAffiliateError = (err) => {
    // toast.error(`${err?.response?.data?.errors[0]?.description}`)
    if (err?.response?.data?.errors.length > 0) {
      const { errors } = err.response.data
      errors.forEach((error) => {
        if (error?.description) {
          // if (error.errorCode === 3007) {
          // } else toast.error(error?.description)
        }
      })
    }
  }
  const affiliateMutation = useAffiliateMutation({
    onSuccess: onAffiliateSuccess,
    onError: onAffiliateError
  })
  const onSubmit = (data) => affiliateMutation.mutate(data)
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid id='partnerForm'>
        <Box className='affiliate-page'>
          <Box className='genral-tab'>
            <Grid className='setting-card-header'>
              <Typography variant='h4'>Become a Partner</Typography>
            </Grid>
            <Grid className='setting-card-details'>
              <Grid container spacing={1}>
                <Grid item xs={12} sm={5} lg={12}>
                  <Grid className='input-wrap '>
                    <FormControl variant='outlined'>
                      <OutlinedInput
                        id='f-name'
                        variant='outlined'
                        placeholder='Enter Full Name'
                        type='text'
                        {...register('fullName')}
                      />
                      <Typography className={classes.errorLabel}>
                        {errors?.fullName && errors?.fullName?.message}
                      </Typography>
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={5} lg={6}>
                  <Grid className='input-wrap'>
                    <FormControl variant='outlined'>
                      <OutlinedInput
                        id='email'
                        variant='outlined'
                        placeholder='Email'
                        type='text'
                        {...register('email')}
                      />
                      <Typography className={classes.errorLabel}>{errors?.email && errors?.email?.message}</Typography>
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={5} lg={6}>
                  <Grid className='input-wrap'>
                    <FormControl variant='outlined'>
                      <OutlinedInput
                        {...register('phone')}
                        id='mobile-number'
                        variant='outlined'
                        placeholder='Mobile Number'
                        type='number'
                        onInput={(e) => {
                          e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, 10)
                        }}
                        min={0}
                        onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                      />
                      <Typography className={classes.errorLabel}>{errors?.phone && errors?.phone?.message}</Typography>
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={5} lg={12}>
                  <Grid className='input-wrap text-area'>
                    <FormControl variant='outlined'>
                      <OutlinedInput
                        multiline
                        rows={8}
                        placeholder='Traffic source* (provide Description and Link)'
                        type='text'
                        {...register('trafficSource')}
                      />
                      <Typography className={classes.errorLabel}>
                        {errors?.trafficSource && errors?.trafficSource?.message}
                      </Typography>
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={5} lg={12}>
                  <Grid className='input-wrap text-area'>
                    <FormControl variant='outlined'>
                      <OutlinedInput
                        multiline
                        rows={8}
                        placeholder='How do you plan to attract people? '
                        type='text'
                        {...register('plan')}
                      />
                      <Typography className={classes.errorLabel}>{errors?.plan && errors?.plan?.message}</Typography>
                    </FormControl>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>

            <Grid className='setting-card-footer' alignItems='center' justifyContent='space-between' display='flex'>
              <Grid>
                <Grid
                  className='inputCheck'
                  id='isTermsAccepted'
                  name='isTermsAccepted'
                  {...register('isTermsAccepted')}
                >
                  <Checkbox {...label} id='isTermsAccepted' name='isTermsAccepted' {...register('isTermsAccepted')} />
                  {/* <input type="checkbox"  /> */}
                  <label htmlFor='isTermsAccepted'>
                    {' '}
                    I accept
                    <Button onClick={() => navigate(`/cms/affiliate-tc`)}>Terms and Conditions</Button>
                  </label>
                </Grid>
                <Typography className={classes.errorLabel} style={{ marginTop: '5px', paddingLeft: '12px' }}>
                  {errors?.isTermsAccepted && errors?.isTermsAccepted?.message}
                </Typography>
              </Grid>
              <Grid>
                <Button
                  className='btn btn-primary'
                  type='submit'
                  disabled={affiliateMutation.isLoading || !isValid}
                  style={{ opacity: !isValid ? 0.5 : 1 }}
                >
                  {affiliateMutation.isLoading ? (
                    <CircularProgress size={24} style={{ marginRight: 8 }} />
                  ) : (
                    <span>Apply</span>
                  )}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Grid>
    </form>
  )
}

export default CreateAffiliate
