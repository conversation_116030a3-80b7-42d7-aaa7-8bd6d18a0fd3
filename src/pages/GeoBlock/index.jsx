import { Button, Grid, Typography } from '@mui/material'
import React from 'react'
import useStyles from '../GeoBlock/GeoBlock.styles'
import GeoBlockImg from '../../components/ui-kit/icons/utils/geoblock.webp'

const GeoBlock = () => {
  const classes = useStyles()
  return (
    <Grid className={classes.lobbyRight}>
      <Grid className={classes.geoblockWrap}>
        <Grid className='geoblock-content'>
          <img src={GeoBlockImg} alt='Groblock' className='geoblock-img' />
          <Typography variant='h2'>Hey! We're Sorry!</Typography>
          <Typography variant='h4'>The Money Factory Scratchers is not available in your area</Typography>
          <Typography>
            Please connect our customer support team if you need further assistance or believe there is an error
          </Typography>
          <Grid className={classes.btnGradientWrap}>
            <Button variant='contained' className='btn-gradient'>
              <span className='btn-span'>Contact Us</span>
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default GeoBlock
