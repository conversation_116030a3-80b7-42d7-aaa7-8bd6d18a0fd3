
import { makeStyles } from '@mui/styles'

import { LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  btnWhiteGradient: {
    transition: "all 0.3s ease 0s",
    "& .btn-gradient": {
      "&.MuiButtonBase-root": {
        background: theme.colors.btnSecondaryBg,
        boxShadow: theme.shadows[1],
        borderRadius: "30px",
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: "relative",
        overflow: "hidden",
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        "&:before": {
          position: "absolute",
          width: "700px",
          height: "100%",
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: "1rem 1rem",
        },
        "& span": {
          position: "relative",
          color: theme.colors.authCardBg,
          zIndex: "2",
          fontWeight: theme.typography.fontWeightSemiBold,
        },
        "&:hover": {
          background: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
          "& span": {
            color: theme.colors.white,
          }
        }
      },
    }
  },
  btnGradientWrap: {
    "& .btn-gradient": {
      "&.MuiButtonBase-root": {
        background: theme.colors.primaryGradient,
        boxShadow: theme.shadows[1],
        borderRadius: "30px",
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: "relative",
        overflow: "hidden",
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        "&:before": {
          position: "absolute",
          width: "700px",
          height: "100%",
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: "1rem 1rem",
        },
        "& .btn-span": {
          position: "relative",
          color: theme.colors.white,
          zIndex: "2",
          fontWeight: theme.typography.fontWeightSemiBold,
          display: "flex",
        },
        "&:hover": {
          backgroundColor: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
        },
      },
    },

  },
  lobbyRight: {
    ...LobbyRight(theme),
  },
  geoblockWrap: {
    "& .geoblock-content": {
      background: theme.colors.sidebarNavBg,
      backdropFilter: "blur(1px)",
      padding: theme.spacing(0.625),
      color: theme.colors.white,
      textAlign: "center",
      maxWidth: "600px",
      margin: "0 auto",
      "& .geoblock-img": {
        width: "300px",
        margin: "0 auto 2rem",
      },
      "& .MuiTypography-h2": {
        fontWeight: theme.typography.fontWeightExtraBold,
        background: theme.colors.textGradient,
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        textTransform: "uppercase",
        fontSize: theme.spacing(3),
        textAlign: "center",
      },
      "& .MuiTypography-h4": {
        fontWeight: theme.typography.fontWeightBold,
        margin: theme.spacing(0.625, 0),
        fontSize: theme.spacing(1.5),
      },
      "& .MuiTypography-body1": {
        fontSize: theme.spacing(1),
        color: theme.colors.themeText,
      },
      "& .MuiButtonBase-root": {
        margin: theme.spacing(2, 0, 0),
      }
    }
  }



}))
