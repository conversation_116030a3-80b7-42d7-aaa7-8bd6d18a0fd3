.comming-soon {

    &-body {
        background: url('../../assets/abstract-style-bg.jpg') center/cover no-repeat;
        height: 100dvh;
        width: 100dvw;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        position: relative;

        .countdown {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2rem;
            z-index: 999;
            position: relative;

            &-box {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background-color: var(--secondary-purple);
                padding: 1rem;
                border-radius: 15px;
                min-width: 145px;

                &.disable-box{
                    opacity: 0.5;
                }

                .count-day {
                    font-size: 3rem;
                    font-weight: 700;

                    @media screen and (max-width: 767px) {
                        font-size: 2.5rem;
                    }

                    @media screen and (max-width: 575px) {
                        font-size: 1.5rem;
                    }
                }

                .common-count {
                    font-size: 1.2rem;

                    @media screen and (max-width: 575px) {
                        font-size: .75rem;
                    }
                }

                @media screen and (max-width: 767px) {
                    padding: 0.5rem;
                    min-width: 110px;
                }

                @media screen and (max-width: 575px) {
                    padding: 0.5rem;
                    min-width: 80px;
                }

                @media screen and (max-width: 575px) {
                    padding: 0.5rem;
                    min-width: 70px;
                }
            }

            @media screen and (max-width: 767px) {
                gap: 1rem;
            }

            @media screen and (max-width: 575px) {
                gap: 0.5rem;
            }
        }

        .gold-logo {
            text-align: center;
            z-index: 9999;
            position: relative;


            img {
                @media screen and (max-width: 767px) {
                    width: 150px;
                }

                @media screen and (max-width: 575px) {
                    width: 100px;
                }
            }
        }
    }

    &-heading {

        padding: 0 0.313rem;

        h1 {
            text-transform: uppercase;
            font-size: 3.2rem;
            text-align: center;

            .were-text {
                @media screen and (max-width: 575px) {
                    display: block;
                }
            }

            @media screen and (max-width: 767px) {
                font-size: 2.5rem;
            }

            @media screen and (max-width: 575px) {
                font-size: 2rem;
            }

        }
    }


}

.gc-coin {
    &-first {
        position: absolute;
        top: 10%;
        left: 61%;
        width: 79px;
        transform: rotate(-92deg);
    }

    &-second {
        position: absolute;
        bottom: 11%;
        left: 10%;
        width: 100px;
        transform: rotate(-92deg);
    }

    &-third {
        position: absolute;
        top: 36%;
        left: 60%;
        width: 139px;
        opacity: 0.2;
        transform: translate(44%, 36%);
    }

    &-four {
        position: absolute;
        top: -6%;
        left: 10%;
        width: 52px;
        opacity: 0.2;
        transform: rotate(-92deg);
        animation: gcfcoin 10s 2s infinite linear;
    }

    &-five {
        position: absolute;
        top: 0%;
        right: 10%;
        width: 52px;
        opacity: 0.2;
        transform: rotate(-92deg);
        animation: gcfivecoin 10s infinite linear;
    }

}

.sc-coin {
    &-first {
        position: absolute;
        top: 20%;
        left: 10%;
        width: 52px;
        opacity: 0.2;
        transform: rotate(-92deg);
        animation: fcoin 10s infinite linear;
    }

    &-second {
        position: absolute;
        top: 60%;
        left: 40%;
        width: 139px;
        opacity: 0.2;
        transform: rotate(-92deg);
    }

    &-third {
        position: absolute;
        top: 86%;
        right: 10%;
        width: 90px;
        opacity: 0.2;
        transform: rotate(-92deg);
    }

    &-four {
        position: absolute;
        top: -6%;
        right: 10%;
        width: 52px;
        opacity: 0.2;
        transform: rotate(-92deg);
        animation: fourcoin 10s 3s infinite linear;
    }

}


@keyframes gcfcoin {
    0% {
        top: 0;
        left: 10%;
    }

    100% {
        top: 100%;
        left: 25%;
    }
}

@keyframes gcfivecoin {
    0% {
        top: 0;
        right: 10%;
    }

    100% {
        top: 100%;
        right: 25%;
    }
}

@keyframes fcoin {
    0% {
        top: 0;
        left: 10%;
    }

    100% {
        top: 100%;
        left: 20%;
    }
}

@keyframes fourcoin {
    0% {
        top: 0;
        right: 10%;
    }

    100% {
        top: 100%;
        right: 20%;
    }
}

.launch-btn {
    text-align: center;
    position: relative;
    z-index: 9999;
    margin-top: 2rem;

    &.disable-box{
        pointer-events: none;
        opacity: 0.8;
    }

    button {
        padding: 1rem 3rem;
        font-size: 1.5rem;

        @media screen and (max-width: 767px) {
            font-size: 1rem !important;
            padding: 0.75rem 2rem !important;
        }
    }
}

