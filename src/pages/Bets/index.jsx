import React from 'react'
import LeaderBoard from '../Lobby/components/LeaderBoard/LeaderBoard'
import LeaderBoardTabs from '../Lobby/components/LeaderBoardTabs/LeaderBoardTabs'
import useStyles from './bets.styles'
import { Grid } from '@mui/material'

const Bets = () => {

  const classes = useStyles()

  return (
    <Grid className={`${classes.lobbyRight}`} >
      <Grid className={classes.wrapper}>
        <LeaderBoardTabs />
        <LeaderBoard />
      </Grid>
    </Grid>
  )
}

export default Bets
