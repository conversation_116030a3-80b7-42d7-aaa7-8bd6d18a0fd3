import { useMutation } from '@tanstack/react-query'

import { KeyTypes } from '../../../../reactQuery/KeyTypes'
import { trustlyDeposit, trustlyRedeem } from '../../../../utils/apiCalls'


const initTrustlyPayMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.TRUSTLY_DEPOSIT],
    mutationFn: (data) => trustlyDeposit(data),
    onSuccess,
    onError
  })
}

const initTrustlyRedeemMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.TRUSTLY_PAY_REDEEM],
    mutationFn: () => trustlyRedeem(),
    onSuccess,
    onError
  })
}

export const useTrustlyPay = {
  initTrustlyPayMutation,
  initTrustlyRedeemMutation
}