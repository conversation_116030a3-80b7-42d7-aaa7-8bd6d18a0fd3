import { yupResolver } from '@hookform/resolvers/yup'
import { useCallback, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'

import { GeneralQuery } from '../../../../reactQuery'
import { useStateStore } from '../../../../store/store'
import { useUserStore } from '../../../../store/useUserSlice'
import { personalInfoSchemaPayment } from '../../../Accounts/schema'
const usePersonalInfo = (handleClose) => {
  const userDetails = useUserStore((state) => state.userDetails)
  const user = useUserStore((state) => state)
  const stateList = useStateStore((state) => state.stateList)

  const [selectedState, setSelectedState] = useState('')
  const [selectedDate, setSelectedDate] = useState(userDetails?.dateOfBirth ? userDetails?.dateOfBirth : '')
  const [initialState] = useState({
    firstName: userDetails?.firstName || '',
    lastName: userDetails?.lastName || '',
    dateOfBirth: userDetails?.dateOfBirth || '',
    zipCode: userDetails?.zipCode || '',
    addressLine_1: userDetails?.addressLine_1 || '',
    addressLine_2: userDetails?.addressLine_2 === 'undefined' ? '' : userDetails?.addressLine_2,
    city: userDetails?.city || '',
    state: userDetails?.state || '',
    country: +199
  })

  useEffect(() => {
    if (userDetails) {
      setSelectedState(userDetails?.state)
      // setSelectedGender(userDetails?.gender);
      reset({
        ...initialState,
        firstName: userDetails?.firstName || '',
        lastName: userDetails?.lastName || '',
        // middleName: userDetails?.middleName === 'undefined' ? '' : userDetails?.middleName || '',
        dateOfBirth: userDetails?.dateOfBirth || '',
        // gender: userDetails?.gender || selectedGender,
        addressLine_1: userDetails?.addressLine_1 || '',
        addressLine_2: userDetails?.addressLine_2 === 'undefined' ? '' : userDetails?.addressLine_2,
        city: userDetails?.city || '',
        state: userDetails?.state || selectedState,
        zipCode: userDetails?.zipCode || ''
      })
    }
  }, [userDetails])
  const {
    handleSubmit,
    control,
    register,
    formState: { errors, isSubmitted },
    getValues,
    setValue,
    reset,
    watch
  } = useForm({
    resolver: yupResolver(personalInfoSchemaPayment),
    mode: 'onChange',
    defaultValues: initialState
  })

  const handelReset = () => {
    setSelectedState('')
    let address1Field = document.querySelector('#addressLine_12')
    let address1 = ''
    if (address1Field) {
      if (userDetails?.addressLine_1) {
        address1Field.value = userDetails?.addressLine_1
        address1 = userDetails?.addressLine_1
      } else {
        address1Field.value = address1
      }
    }

    reset({
      ...initialState,
      firstName: userDetails?.firstName || '',
      lastName: userDetails?.lastName || '',
      dateOfBirth: userDetails?.dateOfBirth || null,
      zipCode: userDetails?.zipCode || '',
      addressLine_1: address1,
      addressLine_2: userDetails?.addressLine_2 === 'undefined' ? '' : userDetails?.addressLine_2,
      city: userDetails?.city || '',
      state: userDetails?.state || ''
    })
  }
  const [userField, setUserField] = useState(getValues())

  const onStateChangeHandler = (event) => {
    setSelectedState(event.target.value)
  }

  const successToggler = (data) => {
    setUserField({ ...userDetails, ...data })
    user.setUserDetails({ ...userDetails, ...data })
    handleClose()
    toast.success('Profile updated successfully.')
  }
  const errorToggler = () => {
    // toast.error(error?.response?.data?.errors?.[0]?.description)
  }

  const { mutate: submitPersonalMutation, isLoading: isSubmitFormLoading } = GeneralQuery.usePersonalFormMutation({
    successToggler,
    errorToggler
  })

  const handleOnFormSubmit = useCallback(async (data) => {
    let stateId = 0
    if (data?.state) {
      stateId = stateList.find((x) => x.stateCode === data.state)?.state_id
    }
    let tempData = {
      firstName: data?.firstName,
      lastName: data?.lastName,
      zipCode: data?.zipCode,
      middleName: userDetails?.middleName === 'undefined' ? '' : userDetails?.middleName || '',
      email: userDetails?.email,
      dateOfBirth: data?.dateOfBirth,
      gender: userDetails?.gender || '',
      addressLine_1: data?.addressLine_1 || '',
      addressLine_2: data?.addressLine_2 || '',
      city: data?.city || '',
      state: stateId,
      country: +199
    }
    submitPersonalMutation(tempData)
  }, [])

  return {
    handleOnFormSubmit,
    handleSubmit,
    register,
    isSubmitFormLoading,
    control,
    errors,
    setValue,
    initialState,
    getValues,
    userDetails,
    isSubmitted,
    reset,
    handelReset,
    userField,
    watch,
    onStateChangeHandler,
    selectedState,
    stateList,

    setSelectedState,
    selectedDate,
    setSelectedDate
  }
}

export default usePersonalInfo
