import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import useStyles from '../../Lobby/components/UsernamePopup/Username.styles'
import { Button, Grid, Box, Typography, CircularProgress, TextField, FormControlLabel } from '@mui/material'
import AmexImg from '../../../components/ui-kit/icons/svg/AmexImg.svg'
import MasterImg from '../../../components/ui-kit/icons/svg/MasterImg.svg'
import VisaImg from '../../../components/ui-kit/icons/svg/VisaImg.svg'
import DiscoverImg from '../../../components/ui-kit/icons/svg/DiscoverImg.svg'
import skrill from '../../../components/ui-kit/icons/svg/skrill.svg'
import applePayIcon from '../../../components/ui-kit/icons/svg/apple-pay.svg'
import Bank from '../../../components/ui-kit/icons/svg/bank.svg'
import MaestroIcon from '../../../components/ui-kit/icons/opImages/maestro.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import { PaymentQuery } from '../../../reactQuery'
import PropTypes from 'prop-types'
import { toast } from 'react-hot-toast'
import {
  useUserStore,
  useStateStore,
  usePromocodeAppliedStore,
  useDepositInitStore,
  usePaymentProcessStore
} from '../../../store/store'
import usePaymentStyles from './payment.style'
import useStepperStore from '../../../store/useStepperStore'
import GeocomplyPopup from '../../../Context/GeocomplyPopup'
import ApplyPromoModal from './components/ApplyPromoModal'
import { getOSAndBrowser } from '../../../utils/helpers'
import { initiateTrustlyScript } from './utils/trustly/initiators'
import PaymentLoader from '../../../components/Loader/PaymentLoader'
import AddPayByBankAccount from './components/AddPayByBankAccount'
import VerifyModal from '../VerifyModal/Verify'
import SkrillPaymentMethod from './components/SkrillPaymentMethod'
import useSeon from '../../../utils/useSeon'
import PreferredPaymentCard from './components/PreferredPaymentCard'
import { useTrustlyPay } from './hooks/useTrustlyPay'
import TrustlyStatus from './TrustlyStatus'
import DepositInprogressModal from './components/DepositInprogressModal'
import PurchaseSummaryAccordion from './components/PurchaseSummaryAccordion'
import PreferredPaymentBox from './components/PreferredPaymentBox'
import BtnLoader from './components/BtnLoader'
import TagManager from 'react-gtm-module'
import { usePaymentStore } from '../../../store/usePaymentStore'
import PromocodeSection from './components/PromocodeSection'
import PaymentMethodsGrid from './components/PaymentMethodsGrid'
import { setupPaysafeFields } from './utils/paysafe/paysafeSetup'
import CustomSwitch from '../../../components/CustomSwitch'
import { handleApplePayPayment, handleCardPayment, handleSkrillPayment } from './utils/paysafe/paysafePaymentProcessors'
import { handlePbbError, handlePbbSuccess } from './utils/paysafe/helpers'
import { paymentSocket } from '../../../utils/socket'
import { loadLottieScript } from '../../../utils/loadLottieScript'
function CustomTabPanel (props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <>{children}</>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

// Payment Methods Form Component
const PaymentMethodForm = ({ id }) => {
  const { setPaymentCard, setCardErrors } = usePaymentStore()
  const activePaymentMethods = usePaymentStore((state) => state.data.activePaymentMethods)
  const cardBrandRecognition = usePaymentStore((state) => state.card.cardBrandRecognition)
  const cardErrors = usePaymentStore((state) => state.card.cardErrors)

  if (id === 0 && activePaymentMethods?.includes('CARD')) {
    return (
      <>
        <>
          <TextField
            id='holderName'
            placeholder='Full Name *'
            variant='outlined'
            type='text'
            className='payment-form-input'
            onKeyDown={(evt) => {
              // Allow only letters and a single space between words
              const isValidKey =
                /^[a-zA-Z]$/.test(evt.key) ||
                (evt.key === ' ' && evt.target.value.slice(-1) !== ' ') ||
                evt.key === 'Backspace' ||
                evt.key === 'Delete' ||
                evt.key === 'ArrowLeft' ||
                evt.key === 'ArrowRight' ||
                evt.key === 'Tab'
              if (!isValidKey) {
                evt.preventDefault()
              }
            }}
            onChange={(event) => {
              const value = event.target.value
                .replace(/[^a-zA-Z ]/g, '')
                .replace(/\s+/g, ' ')
                .trim()
              setPaymentCard('cardHolderName', value)
              setCardErrors('cardHolderName', '')
            }}
          />
          {cardErrors?.cardHolderName && <Typography className='card-error'>{cardErrors?.cardHolderName}</Typography>}

          <div id='cardNumber' className='payment-form-input-card' />
          {cardErrors?.cardNumber && <Typography className='card-error'>{cardErrors?.cardNumber}</Typography>}

          {cardBrandRecognition === 'VI' && <img src={VisaImg} className='payment-form-card-brand' />}
          {cardBrandRecognition === 'DI' && <img src={DiscoverImg} className='payment-form-card-brand' />}
          {cardBrandRecognition === 'MC' && <img src={MasterImg} className='payment-form-card-brand' />}
          {cardBrandRecognition === 'MD' && <img src={MaestroIcon} className='payment-form-card-brand' />}
          {cardBrandRecognition === 'AM' && <img src={AmexImg} className='payment-form-card-brand' />}

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              width: '100% !important',
              paddingTop: '10px',
              height: '60px'
            }}
          >
            <Box
              style={{
                width: '70%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'start'
              }}
            >
              <div id='expiryDate' className='payment-form-date-input' />
              {cardErrors?.expiryDate && <Typography className='card-error'>{cardErrors?.expiryDate}</Typography>}
            </Box>

            <Box
              style={{
                width: '30%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'start'
              }}
            >
              <div id='cvv' className='payment-form-cvv-input' />
              {cardErrors?.cvv && <Typography className='card-error'>{cardErrors?.cvv}</Typography>}
            </Box>
          </Box>
        </>
      </>
    )
  } else if (id === 2 && activePaymentMethods?.includes('APPLEPAY')) {
    return <></>
  } else if (id === 4) {
    return (
      <>
        <div hidden id='cardNumber' className='payment-form-input-card' />
        <div hidden id='expiryDate' className='payment-form-date-input' />
        <div hidden id='cvv' className='payment-form-cvv-input' />
      </>
    )
  } else {
    return <></>
  }
}

const PaymentModal = (props) => {
  const navigate = useNavigate()
  const location = useLocation()
  const classes = useStyles()
  const paymentClasses = usePaymentStyles()
  const portalStore = usePortalStore((state) => state)
  const userDetails = useUserStore((state) => state.userDetails)
  const stateList = useStateStore((state) => state.stateList)
  const stateName = stateList?.find((x) => x?.state_id === Number(userDetails?.state))?.stateCode
  const { stepperCalledFor } = useStepperStore((state) => state)
  const packageDetails = props.packageDetails

  const [inprogressModalOpen, setInprogressModalOpen] = useState(false)

  // Paysafe Setup - New flow
  const ENV = import.meta.env.VITE_NODE_ENV
  const publicPaymentKey = import.meta.env.VITE_PUBLIC_PAYMENT_KEY
  const accountID = import.meta.env.VITE_PAYSAFE_ACCOUNT_ID
  const trustlyAccessId = import.meta.env.VITE_TRUSTLY_ACCESS_ID
  const trustlyMerchantId = import.meta.env.VITE_TRUSTLY_MERCHANT_ID
  const API_KEY = btoa(publicPaymentKey)

  // UTILS
  const browserType = getOSAndBrowser()
  const sessionId = useSeon()
  const isAppleDevice = browserType.os === 'MacOS' || browserType.os === 'iOS'

  // SETTERS - PAYMENT STORE
  const {
    setPaymentData,
    setPaymentToken,
    setPaymentStatus,
    setPaymentCard,
    setCardErrors,
    setPaymentPromocode,
    setPaymentTrustly
  } = usePaymentStore()

  // TOKENS
  const { transactionId, singleUseCustomerToken, paymentHandleToken } = usePaymentStore((state) => state.tokens)

  // DATA
  const {
    depositInitiated,
    demoInstance,
    finalAmount,
    discountAmount,
    activePaymentMethods,
    selectedPaymentMethod,
    preferredPayments,
    selectedPreferredPayment,
    selectedPreferredPaymentId
  } = usePaymentStore((state) => state.data)

  // STATUS
  const { value, isLoading, confettiPopper, paymentRedirection, paymentDisabled, showAllPayments } = usePaymentStore(
    (state) => state.status
  )

  // CARD
  const cardHolderName = usePaymentStore((state) => state.card.cardHolderName)
  const savePaymentToggle = usePaymentStore((state) => state.card.savePaymentToggle)
  const selectedSavedCard = usePaymentStore((state) => state.card.selectedSavedCard)

  // PROMOCODE
  const { promocodeSuccess } = usePaymentStore((state) => state.promocode)

  // TRUSTLY
  const { showTrustlyStatus, isTrustlyWidgetLoading, isTrustlyCheckboxSelected, trustlyTokenExpiredScriptLoaded, isPreferredHasTrustly } =
    usePaymentStore((state) => state.trustly)

  // DEPOSIT STORE
  const setDepositTransactionId = useDepositInitStore((state) => state.setDepositTransactionId)
  const setDepositDetails = useDepositInitStore((state) => state.setDepositDetails)
  const setPaymentMethod = usePaymentProcessStore((state) => state.setPaymentMethod)
  const setPaymentDepositTransactionId = usePaymentProcessStore((state) => state.setPaymentDepositTransactionId)
  const setPaymentErrorState = usePaymentProcessStore((state) => state.setPaymentErrorState)

  // Trustly state
  const [paymentMethodName, setPaymentMethodName] = useState('')

  //  trustly Step 2
  const initTrustlyDeposit = useTrustlyPay.initTrustlyPayMutation({
    onSuccess: (res) => {
      const establish = res?.data?.data?.establishData
      const trustlyOptions = res?.data?.data?.TrustlyOptions
      establish.merchantId = trustlyMerchantId
      establish.accessId = trustlyAccessId
      localStorage.setItem('requestSignature', res?.data?.data?.establishData?.requestSignature)
      localStorage.setItem('merchantReference', res?.data?.data?.establishData?.merchantReference)
      localStorage.setItem('isTrustlyLoaded', true)
      Trustly.selectBankWidget(establish, trustlyOptions)
      setPaymentTrustly('isTrustlyWidgetLoading', false)
      setPaymentStatus('isLoading', false)
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  useEffect(() => {
    return () => {
      const ele = document.getElementById('trustly-script')
      if (ele) {
        document.head.removeChild(ele)
      }
    }
  }, [])

  const handleSelectPreferredPayment = (transId, prefPayment) => {
    if (isLoading) return
    if (selectedPreferredPaymentId === prefPayment?.paymentMethodName) {
      setPaymentData('selectedPreferredPayment', null)
      setPaymentData('selectedPreferredPaymentId', null)
      setPaymentData('selectedPaymentMethod', null)
      setPaymentToken('paymentHandleToken', '')
      setPaymentCard('selectedSavedCard', '')
      setPaymentCard('cardHolderName', '')
    } else {
      setPaymentData('selectedPreferredPaymentId', prefPayment?.paymentMethodName)
      setPaymentData('selectedPreferredPayment', prefPayment)
      setPaymentData('selectedPaymentMethod', 4)
      setPaymentMethod(prefPayment?.paymentMethodType)
      if (prefPayment?.paymentMethodType === 'CARD') {
        setPaymentStatus('isLoading', true)
        setPaymentData('depositInitiated', true)
        setPaymentCard('selectedSavedCard', prefPayment?.paymentMethodName)
        setPaymentCard('cardHolderName', prefPayment?.moreDetails?.holderName)
        savedCardInitDeposit.mutate({
          transactionId: transId || transactionId,
          lastFourDigit: prefPayment?.moreDetails?.lastDigits
        })
      } else if (prefPayment?.paymentMethodType === 'PAY_BY_BANK') {
        setPaymentStatus('isLoading', false)
        setPaymentToken('paymentHandleToken', prefPayment?.moreDetails?.paymentHandleToken)
        setPaymentData('depositInitiated', true)
        props?.setIsPaymentScreenLoading(false)
      } else if (prefPayment?.paymentMethodType === 'TRUSTLY') {
        setPaymentStatus('isLoading', false)
        setPaymentData('depositInitiated', true)
        setPaymentMethodName(prefPayment?.paymentMethodName)
        setPaymentData('selectedPreferredPaymentId', prefPayment?.paymentMethodName)
        setPaymentTrustly('isTrustlyCheckboxSelected', false)
      } else if (prefPayment?.paymentMethodType === 'APPLE_PAY') {
        setPaymentStatus('isLoading', true)
        if (isAppleDevice) {
          setupPaysafeFields({
            API_KEY,
            options: {
              currencyCode: 'USD',
              environment: ENV === 'production' ? 'LIVE' : 'TEST',
              accounts: {
                default: +accountID
              },
              fields: {
                applePay: {
                  selector: '#apple-pay',
                  type: 'pay',
                  label: 'Apple Pay',
                  optional: true
                }
              }
            },
            onSuccess: (instance) => {
              setPaymentData('demoInstance', instance)
              setPaymentData('depositInitiated', true)
              setPaymentStatus('isLoading', false)
            },
            onFailure: (error) => {
              if (error?.code === '9086') {
                toast.error(error?.displayMessage)
                setPaymentStatus('isLoading', false)
                console.error('Paysafe setup failed:', error)
                return
              }
              console.error('Paysafe setup failed:', error)
              setPaymentStatus('isLoading', false)
              portalStore.closePortal()
              navigate(`${location.pathname}?status=failed`)
            }
          })
          props?.setIsPaymentScreenLoading(false)
        }
      } else {
        setPaymentData('depositInitiated', true)
        setPaymentStatus('isLoading', false)
        props?.setIsPaymentScreenLoading(false)
      }
    }
  }

  // Change payment method
  const changePaymentMethod = (key) => {
    if (isLoading) return null
    setPaymentData('selectedPaymentMethod', key)
    setPaymentToken('paymentHandleToken', '')
    setPaymentCard('selectedSavedCard', '')
    setPaymentCard('cardHolderName', '')
    setPaymentStatus('paymentDisabled', false)
    setPaymentData('selectedPreferredPaymentId', '')
    setPaymentData('selectedPreferredPayment', null)
  }

  useEffect(() => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'checkout_payment_method',
        user_id: userDetails?.userId,
        email: userDetails?.email,
        phone: userDetails?.phoneNumber,
        item_id: packageDetails?.packageId,
        item_name: packageDetails?.packageName,
        price: packageDetails?.amount,
        catalog: packageDetails?.isSpecialPackage ? 'Special_Package' : 'Basic_Package',
        gcCoin: packageDetails?.gcCoin,
        scCoin: packageDetails?.scCoin
      }
    })
  }, [])

  const handleGeocomplyPopup = (data) => {
    portalStore.openPortal(() => <GeocomplyPopup open errorData={data} />, 'tournamentEndPopup')
  }
  // Promocode

  const deleteAppliedPromocode = PaymentQuery.deleteAppliedPromocodeMutation({
    onSuccess: (res) => {
      console.log('Promocode Removed', res?.data)
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const setPromocodeApplied = usePromocodeAppliedStore((state) => state.setPromocodeApplied)
  const handleRemovePromocode = (e) => {
    e.stopPropagation()
    if (selectedPaymentMethod === 5) {
      initiateTrustlyNewUserFlow()
    }
    setPaymentStatus('confettiPopper', false)
    setPaymentData('finalAmount', packageDetails?.amount)
    setPaymentData('discountAmount', '')
    deleteAppliedPromocode.mutate({ transactionId: transactionId })
    setPromocodeApplied({ promocodeApplied: false, promocode: '' })
    setPaymentPromocode('promocode', '')
    setPaymentPromocode('promocodeSuccess', false)
    setPaymentPromocode('selectedPromocode', '')
  }

  // Paysafe Setup - New flow
  // Deposit Success

  const successToggler = (result) => {
    if (result?.success === false) {
      setPaymentData('depositInitiated', true)
      setDepositDetails(result)
      setInprogressModalOpen(result?.success === false)
      setPaymentData('activePaymentMethods', result?.activePaymentMethods)
    } else {
      setDepositDetails(result)
      setInprogressModalOpen(!result?.success)
      setPaymentToken('transactionId', result?.transactionId)
      setDepositTransactionId({ depositTransactionId: result?.transactionId })
      window.localStorage.setItem('transactionId', result?.transactionId)
      window.localStorage.setItem('transactionSuccess', true)
      setPaymentData('activePaymentMethods', result?.activePaymentMethods)

      if (result?.preferredPayment?.length > 0) {
        setPaymentData('preferredPayments', result?.preferredPayment)
        handleSelectPreferredPayment(result?.transactionId, result?.preferredPayment[0])
      } else {
        setPaymentData('depositInitiated', true)
        props?.setIsPaymentScreenLoading(false)
      }
    }
  }

  // Deposit Error
  const errorToggler = (error) => {
    if (error?.response?.data?.errors[0]?.name === 'KycRequiredError') {
      setPaymentData('depositInitiated', true)
      props?.setIsPaymentScreenLoading(false)
      portalStore.openPortal(() => <VerifyModal packageDetails={packageDetails} />, 'innerModal')
    } else if (error?.response?.data?.errors[0]?.name === 'DepositInitiatedError') {
      setPaymentData('depositInitiated', true)
      props?.setIsPaymentScreenLoading(false)
      console.log('Payment Initialization Failed, Try again !', error?.response?.data?.errors[0])
      portalStore.closePortal()
    } else if (
      error?.response?.data?.data?.state === 'DECLINE' ||
      error?.response?.data?.data?.ipDetails?.vpn === true ||
      error?.response?.data?.data?.ipDetails?.web_proxy === true
    ) {
      localStorage.setItem('allowedUserAccess', false)
      handleGeocomplyPopup(error?.response?.data?.data)
    } else {
      setPaymentData('depositInitiated', true)
      props?.setIsPaymentScreenLoading(false)
      console.log('Payment Initialization Failed, Try again !', error?.response?.data?.errors[0])
      portalStore.closePortal()
    }
    setPaymentErrorState({
      paymentError: error,
      paymentErrorMessage: 'Payment Initialization Failed'
    })
  }

  // Initalize Deposit
  const initDeposit = PaymentQuery.initPayMutation({ successToggler, errorToggler })

  useEffect(() => {
    if (sessionId) {
      initDeposit.mutate({
        packageId: packageDetails?.packageId,
        subPackageId: props?.packageDetails?.subPackageId,
        isAppleDevice: isAppleDevice,
        sessionKey: sessionId,
        rtyuioo: sessionId === ' ' ? true : false
      })
    }
  }, [sessionId])

  // Cancel Deposit
  const setCancelDeposit = usePaymentProcessStore((state) => state.setCancelDeposit)
  const cancelDeposit = PaymentQuery.cancelDepositMutation({
    onSuccess: (res) => {
      setPaymentToken('transactionId', '')
      setPaymentData('depositInitiated', false)
      setCancelDeposit(false)
      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')

      if (res?.data?.success) {
        initDeposit.mutate({
          packageId: packageDetails?.packageId,
          subPackageId: packageDetails?.subPackageId,
          isAppleDevice: isAppleDevice
        })
      }
    },
    onError: (error) => {
      console.log('Internal Server Error', error)
      setCancelDeposit(false)
    }
  })

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      const tranID = window.localStorage.getItem('transactionId')
      const trustlyLoaded = window.localStorage.getItem('isTrustlyLoaded')

      if (tranID && !paymentRedirection && !showTrustlyStatus && !trustlyLoaded) {
        cancelDeposit.mutate({ transactionId: tranID }) // Call function before closing the popup
        if (promocodeSuccess) {
          deleteAppliedPromocode.mutate({ transactionId: transactionId })
        }
      } else {
        window.localStorage.removeItem('isTrustlyLoaded')
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [])

  const paysafeCheckError = PaymentQuery.addPaymentErrorsMutation({
    onSuccess: (res) => {
      console.log('Successfully sent error to backend', res)
    },
    onError: (error) => {
      console.log('Internal Server Error', error)
    }
  })

  // PAYSAFE INITIALIZATION

  const handlePaysafeMethodInit = async () => {
    try {
      switch (selectedPaymentMethod) {
        case 0:
          setPaymentStatus('isLoading', true)
          setCardErrors({})
          await setupPaysafeFields({
            API_KEY,
            options: {
              currencyCode: 'USD',
              environment: ENV === 'production' ? 'LIVE' : 'TEST',
              accounts: {
                default: +accountID
              },
              fields: {
                cardNumber: {
                  selector: '#cardNumber',
                  separator: ' ',
                  accessibilityLabel: 'Card number',
                  placeholder: 'Card Number *',
                  iframeTitle: 'Credit card details container',
                  accessibilityErrorMessage: 'Invalid card number',
                  autoComplete: 'off'
                },
                expiryDate: { selector: '#expiryDate', placeholder: 'MM/YY *', autoComplete: 'off' },
                cvv: { selector: '#cvv', placeholder: 'CVV *', mask: true, autoComplete: 'off' }
              },
              style: {
                input: {
                  'font-family': '"Rajdhani", sans-serif',
                  'font-size': '14px',
                  color: 'white'
                }
              }
            },
            onCardBrandRecognized: (brand) => {
              const brandMap = {
                Visa: 'VI',
                Discover: 'DI',
                MasterCard: 'MC',
                Maestro: 'MD',
                'American Express': 'AM',
                'Diners Club': 'DC'
              }
              setPaymentCard('cardBrandRecognition', brandMap[brand] || '')
            },
            onCardError: (field, message) => {
              setCardErrors(field, message)
            },
            onSuccess: (instance) => {
              setPaymentData('demoInstance', instance)
              setPaymentData('depositInitiated', true)
              setPaymentStatus('paymentDisabled', false)
              setPaymentStatus('isLoading', false)
            },
            onFailure: (error) => {
              console.error('Paysafe setup failed:', error)
              setPaymentStatus('isLoading', false)
              setPaymentStatus('paymentDisabled', false)
              portalStore.closePortal()
              navigate(`${location.pathname}?status=failed`)
            }
          })
          break

        case 1:
          setPaymentStatus('isLoading', false)
          setPaymentStatus('paymentDisabled', false)
          break

        case 2:
          setPaymentStatus('isLoading', true)
          await setupPaysafeFields({
            API_KEY,
            options: {
              currencyCode: 'USD',
              environment: ENV === 'production' ? 'LIVE' : 'TEST',
              accounts: {
                default: +accountID
              },
              fields: {
                applePay: {
                  selector: '#apple-pay',
                  type: 'pay',
                  label: 'Apple Pay',
                  optional: true
                }
              }
            },
            onSuccess: (instance) => {
              setPaymentData('demoInstance', instance)
              setPaymentData('depositInitiated', true)
              setPaymentStatus('isLoading', false)
            },
            onFailure: (error) => {
              if (error?.code === '9086') {
                toast.error(error?.displayMessage)
                setPaymentStatus('isLoading', false)
                console.error('Paysafe setup failed:', error)
                return
              }
              console.error('Paysafe setup failed:', error)
              setPaymentStatus('isLoading', false)
              portalStore.closePortal()
              navigate(`${location.pathname}?status=failed`)
            }
          })
          setPaymentStatus('paymentDisabled', false)
          break

        case 3:
          setPaymentStatus('paymentDisabled', false)
          setPaymentStatus('isLoading', false)
          break

        case 4:
          setPaymentStatus('paymentDisabled', false)
          break

        case 5:
          setPaymentStatus('isLoading', false)
          setPaymentStatus('paymentDisabled', false)
          break

        default:
          setPaymentStatus('isLoading', false)
          setPaymentStatus('paymentDisabled', false)
          break
      }
    } catch (error) {
      setDepositTransactionId({ depositTransactionId: '' })
      console.error('Paysafe initialization error:', error)
      setPaymentStatus('paymentDisabled', true)
      portalStore.closePortal()
      navigate(`${location.pathname}?status=failed`)
    }
  }

  // On click PAY button
  const handlePayment = async () => {
    try {
      setPaymentStatus('isLoading', true)
      switch (selectedPaymentMethod) {
        case 0:
          await handleCardPayment({
            demoInstance,
            cardHolderName,
            selectedSavedCard,
            paymentHandleToken,
            singleUseCustomerToken,
            transactionId,
            finalAmount,
            userDetails,
            stateName,
            setCardErrors,
            setPaymentStatus,
            handlePaymentCompletion,
            paysafeCheckError,
            setDepositTransactionId,
            setPaymentErrorState,
            portalStore,
            navigate,
            location
          })
          break
        case 1:
          await handleSkrillPayment(
            transactionId,
            location,
            portalStore,
            navigate,
            skrillPayment,
            setPaymentDepositTransactionId,
            setPaymentStatus,
            setPaymentErrorState,
            (redirectUrl) =>
              portalStore.openPortal(() => <SkrillPaymentMethod redirectUrl={redirectUrl} />, 'innerModal')
          )
          break
        case 2:
          await handleApplePayPayment(
            demoInstance,
            transactionId,
            finalAmount,
            userDetails,
            stateName,
            setPaymentStatus,
            handlePaymentCompletion,
            paysafeCheckError,
            setDepositTransactionId,
            setPaymentErrorState,
            portalStore,
            navigate,
            location,
            toast
          )
          break
        case 3:
          await handlePbbPayment()
          break
        case 4:
          await handlePreferredPayment()
          break
        case 5:
          await handleTrustlyPreferedPayment()
          break
        default:
          console.log('No Payment method selected')
          break
      }
    } catch (error) {
      console.error('No Payment Method Selected:', error)
      setDepositTransactionId({ depositTransactionId: '' })
    }
  }

  // SKRILL PAYMENT MUTATION
  const skrillPayment = PaymentQuery.skrillPaymentMutation({
    onSuccess: () => { },
    onError: (error) => {
      console.log('Internal Server Error', error)
    }
  })

  // PAY_BY_BANK PAYMENT MUTATION
  const pbbPayment = PaymentQuery.pbbPaymentMutation({
    onSuccess: (res) => {
      handlePbbSuccess(res, setPaymentDepositTransactionId, setPaymentStatus, (redirectUrl) =>
        portalStore.openPortal(() => <AddPayByBankAccount redirectUrl={redirectUrl} />, 'innerModal')
      )
    },
    onError: (error) => {
      console.log('Payment Error', error)
      handlePbbError(error, setPaymentErrorState, setPaymentStatus, setDepositTransactionId, portalStore, navigate)
    }
  })

  // SAVED PAY_BY_BANK PAYMENT MUTATION
  const savedPbbPayment = PaymentQuery.savedPbbPaymentMutation({
    onSuccess: (res) => {
      handlePbbSuccess(res, setPaymentDepositTransactionId, setPaymentStatus, (redirectUrl) =>
        portalStore.openPortal(() => <AddPayByBankAccount redirectUrl={redirectUrl} />, 'innerModal')
      )
    },
    onError: (error) => {
      console.log('Payment Error', error)
      handlePbbError(error, setPaymentErrorState, setPaymentStatus, setDepositTransactionId, portalStore, navigate)
    }
  })

  // SAVED TRUSTLY PAYMENT MUTATION
  const savedTrustlyPayment = PaymentQuery.savedTrustlyPaymentMutation({
    onSuccess: (res) => {
      paymentSocket.connect()
      const isTokenExpired = res?.data?.data?.tokenExpired
      const establishData = res?.data?.data?.establishData
      const trustlyOptions = res?.data?.data?.TrustlyOptions
      const isTransactionFailed = res?.data?.transactionFailed

      if (!isTokenExpired && !isTransactionFailed) {
        setPaymentTrustly('showTrustlyStatus', true)
      }

      if (isTransactionFailed || isTokenExpired) {
        setPaymentTrustly('trustlyTokenExpiredScriptLoaded', isTokenExpired)
        initiateTrustlyScript({
          callback: () => {
            setTimeout(() => {
              if (isTokenExpired) {
                Trustly.establish(establishData, trustlyOptions)
              } else {
                Trustly.selectBankWidget(establishData, trustlyOptions)
              }
            }, 1000)
          }
        })
      }

      setPaymentDepositTransactionId({
        paymentDepositTransactionId: res?.data.data.depositTransactionId
      })
      setPaymentStatus('isLoading', false)
      setPaymentStatus('paymentRedirection', true)
    },
    onError: (error) => {
      console.error('Error>>>', error)
      setPaymentStatus('isLoading', false)
      toast.error('Payment Failed !')
    }
  })

  // On click on trustly online banking btn
  const initiateTrustlyNewUserFlow = async () => {
    setPaymentTrustly('isTrustlyWidgetLoading', true)
    initiateTrustlyScript({
      callback: () => {
        console.log('## TRUSRTLY SCRIPT INITATED')
        initTrustlyDeposit.mutate({ transactionId: transactionId, redirectUrlPath: '/user/store' })
      },
      failure: () => {
        setPaymentStatus('isLoading', false)
        setPaymentTrustly('isTrustlyWidgetLoading', false)
        setPaymentData('selectedPaymentMethod', null)
        setPaymentStatus('paymentDisabled', true)
        setPaymentData('selectedPreferredPaymentId', '')
        setPaymentData('selectedPreferredPayment', null)
      }
    })
  }

  const onAccountRefresh = (establishData, trustlyOptions) => {
    initiateTrustlyScript({
      callback: () => {
        // TODO
        console.log('## INIT TRUSRTLY ON REFRESH')
        Trustly.establish(establishData, trustlyOptions)
      },
      failure: () => {
        setPaymentStatus('isLoading', false)
        setPaymentTrustly('isTrustlyWidgetLoading', false)
        setPaymentData('selectedPaymentMethod', null)
        setPaymentStatus('paymentDisabled', true)
        setPaymentData('selectedPreferredPaymentId', '')
        setPaymentData('selectedPreferredPayment', null)
      }
    })
  }

  // PAYSAFE PAY_BY_BANK PAYMENT
  const handlePbbPayment = async () => {
    const payload = { transactionId: transactionId, redirectUrlPath: location.pathname }
    if (paymentHandleToken !== '') {
      payload.paymentHandleToken = paymentHandleToken
      await savedPbbPayment.mutate(payload)
    } else {
      await pbbPayment.mutate(payload)
    }
  }

  const handleTrustlyPreferedPayment = async () => {
    const payload = { transactionId: transactionId, redirectUrlPath: location.pathname }
    if (paymentMethodName !== '') {
      payload.paymentMethodName = paymentMethodName
      await savedTrustlyPayment.mutate(payload)
    }
  }

  // SAVED CARD INIT DEPOSIT
  const savedCardInitDeposit = PaymentQuery.savedCardInitDepositMutation({
    onSuccess: (res) => {
      setPaymentToken('paymentHandleToken', res?.data?.data?.paymentHandleToken)
      setPaymentToken('singleUseCustomerToken', res?.data?.data?.singleUseCustomerToken)
      setupPaysafeFields({
        API_KEY,
        options: {
          currencyCode: 'USD',
          environment: ENV === 'production' ? 'LIVE' : 'TEST',
          accounts: {
            default: +accountID
          },
          fields: {
            cardNumber: {
              selector: '#cardNumber',
              placeholder: 'Card Number',
              optional: true
            },
            expiryDate: { selector: '#expiryDate', placeholder: 'MM/YY', optional: true },
            cvv: { selector: '#cvv', placeholder: 'CVV', optional: true, mask: true }
          },
          style: {
            input: {
              'font-family': '"Rajdhani", sans-serif',
              'font-size': '14px',
              color: 'white !important',
              '-webkit-text-fill-color': 'white !important'
            }
          }
        },
        onCardBrandRecognized: (brand) => {
          const brandMap = {
            Visa: 'VI',
            Discover: 'DI',
            MasterCard: 'MC',
            Maestro: 'MD',
            'American Express': 'AM',
            'Diners Club': 'DC'
          }
          setPaymentCard('cardBrandRecognition', brandMap[brand] || '')
        },
        onCardError: (field, message) => {
          setCardErrors(field, message)
        },
        onSuccess: (instance) => {
          setPaymentData('demoInstance', instance)
          setPaymentData('depositInitiated', true)
          setPaymentStatus('isLoading', false)
        },
        onFailure: (error) => {
          console.error('Paysafe setup failed:', error)
          setPaymentStatus('isLoading', false)
          portalStore.closePortal()
          navigate(`${location.pathname}?status=failed`)
        }
      })
    },
    onError: (error) => {
      console.log('Saved Card Init Deposit Error', error)
      setDepositTransactionId({ depositTransactionId: '' })
      setPaymentErrorState({
        paymentError: error,
        paymentErrorMessage: 'Payment Initialization Failed'
      })
      setPaymentStatus('isLoading', false)
      portalStore.closePortal()
      navigate(`${location.pathname}?status=failed`)
    }
  })

  // PREFERRED PAYMENT
  const handlePreferredPayment = async () => {
    if (selectedPreferredPayment?.paymentMethodType === 'SKRILL') {
      handleSkrillPayment(
        transactionId,
        location,
        portalStore,
        navigate,
        skrillPayment,
        setPaymentDepositTransactionId,
        setPaymentStatus,
        setPaymentErrorState,
        (redirectUrl) => portalStore.openPortal(() => <SkrillPaymentMethod redirectUrl={redirectUrl} />, 'innerModal')
      )
    } else if (selectedPreferredPayment?.paymentMethodType === 'CARD') {
      handleCardPayment({
        demoInstance,
        cardHolderName,
        selectedSavedCard,
        paymentHandleToken,
        singleUseCustomerToken,
        transactionId,
        finalAmount,
        userDetails,
        stateName,
        setCardErrors,
        setPaymentStatus,
        handlePaymentCompletion,
        paysafeCheckError,
        setDepositTransactionId,
        setPaymentErrorState,
        portalStore,
        navigate,
        location
      })
    } else if (selectedPreferredPayment?.paymentMethodType === 'APPLE_PAY') {
      handleApplePayPayment(
        demoInstance,
        transactionId,
        finalAmount,
        userDetails,
        stateName,
        setPaymentStatus,
        handlePaymentCompletion,
        paysafeCheckError,
        setDepositTransactionId,
        setPaymentErrorState,
        portalStore,
        navigate,
        location,
        toast
      )
    } else if (selectedPreferredPayment?.paymentMethodType === 'PAY_BY_BANK') {
      handlePbbPayment()
    } else if (selectedPreferredPayment?.paymentMethodType === 'TRUSTLY') {
      handleTrustlyPreferedPayment()
    }
  }

  // PROCESS PAYMENT
  const setPaymentState = usePaymentProcessStore((state) => state.setPaymentState)
  const paymentCompletion = PaymentQuery.paymentProcessMutation({
    onSuccess: (res) => {
      if (res?.data?.success) {
        TagManager.dataLayer({
          dataLayer: {
            event: 'purchase',
            amount: res?.data?.data?.amount,
            currency: 'USD',
            scCoin: res?.data?.data?.scCoin,
            gcCoin: res?.data?.data?.gcCoin,
            isFirstDeposit: res?.data?.data?.isFirstDeposit,
            totalPurchaseSum: res?.data?.data?.totalPurchaseSum,
            payment_type: 'Deposit',
            payment_method: res?.data?.data?.paymentMethod,
            transaction_id: res?.data?.data?.transactionId,
            item_id: res?.data?.data?.packageId,
            item_name: res?.data?.data?.packageName,
            user_id: userDetails?.userId,
            user_detail: {
              data: [
                {
                  first_name: userDetails?.firstName,
                  last_name: userDetails?.lastName,
                  email: userDetails?.email,
                  dob: userDetails?.dateOfBirth,
                  gender: userDetails?.gender,
                  phone: userDetails?.phone,
                  city: userDetails?.city,
                  state: stateName,
                  zipcode: userDetails?.zipCode,
                  country: 'US'
                }
              ]
            }
          }
        })
        
        setPaymentState(res?.data?.data)
        navigate(
          `${location.pathname}?status=success&transactionId=${transactionId}&paymentMethod=${selectedPaymentMethod}`
        )
      } else {
        setDepositTransactionId({ depositTransactionId: '' })
        cancelDeposit.mutate({ transactionId: transactionId })
        navigate(`${location.pathname}?status=failed`)
        setPaymentErrorState({
          paymentError: res,
          paymentErrorMessage: res?.data?.message
        })
      }
      setPaymentToken('transactionId', '')
      setPaymentToken('paymentHandleToken', '')
      setPaymentToken('singleUseCustomerToken', '')
      setPaymentData('selectedPreferredPayment', null)
      setPaymentData('selectedPreferredPaymentId', '')
      setPaymentStatus('isLoading', false)

      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')
      window.localStorage.removeItem('depositTransactionId')
      setPaymentDepositTransactionId({
        paymentDepositTransactionId: ''
      })

      portalStore.closePortal()
    },
    onError: (error) => {
      console.log('Paysafe Payment Fallback Error', error)
      setPaymentErrorState({
        paymentError: error,
        paymentErrorMessage: error?.data?.message
      })
      setPaymentDepositTransactionId({
        paymentDepositTransactionId: ''
      })
      setDepositTransactionId({ depositTransactionId: '' })
      cancelDeposit.mutate({ transactionId: transactionId })
      setPaymentToken('transactionId', '')
      setPaymentToken('paymentHandleToken', '')
      setPaymentToken('singleUseCustomerToken', '')
      setPaymentData('selectedPreferredPayment', null)
      setPaymentData('selectedPreferredPaymentId', '')
      setPaymentStatus('isLoading', false)

      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')
      window.localStorage.removeItem('depositTransactionId')

      portalStore.closePortal()
      navigate(`${location.pathname}?status=failed`)
    }
  })

  // Fallbacks
  const handlePaymentCompletion = (props) => {
    const payload = {
      transactionId: transactionId,
      paymentMethod: props?.paymentMethod
    }
    if (props?.paymentMethod === 'CARD' || props?.paymentMethod === 'APPLE_PAY') {
      payload.paymentHandleToken = props?.token
      if (savePaymentToggle) {
        payload.saveCard = true
      }
    } else {
      payload.depositTransactionId = props?.depositTransactionId
    }
    paymentCompletion.mutate(payload)
  }

  useEffect(() => {
    if (depositInitiated) {
      if (selectedPaymentMethod === null) {
        setPaymentStatus('paymentDisabled', true)
      } else {
        handlePaysafeMethodInit()
      }
    }
  }, [depositInitiated, selectedPaymentMethod])

  useEffect(() => {
    calculateFinalAmount()
  }, [packageDetails?.amount])

  const calculateFinalAmount = () => {
    setPaymentData('finalAmount', packageDetails?.amount)
  }

  useEffect(() => {
    loadLottieScript()
  }, [])

  useEffect(() => {
    if (!location.pathname.includes('/game-play')) {
      window.localStorage.removeItem('isGameplay')
      window.localStorage.removeItem('GameID')
    }
  }, [location])

  return (
    <>
      <Grid>
        <Grid className={paymentClasses.paymentStyleModal}>
          <Grid className='modal-section-container'>
            <Grid className='paymentoptionImage-Rightcontainer'>
              <Box className={classes.profileTabsDesign}>
                <Box className='profile-section-detail'>
                  {value === 'payment' && (
                    <Grid className='payment-modal-content'>
                      <Grid sx={!depositInitiated ? { width: '100%', display: 'hidden' } : { width: '100%' }}>
                        <PurchaseSummaryAccordion
                          packageDetails={packageDetails}
                          discountAmount={discountAmount}
                          finalAmount={finalAmount}
                        />
                      </Grid>
                      {(!depositInitiated || props?.isPaymentScreenLoading || paymentRedirection) && <PaymentLoader />}
                      <div style={!depositInitiated ? { display: 'hidden' } : {}}>
                        {!packageDetails?.subPackageId && (
                          <PromocodeSection handleRemovePromocode={handleRemovePromocode} />
                        )}
                        {preferredPayments?.length > 0 && (
                          <Grid className='preferred-payment-container'>
                            <Typography className='payment-methods-title'>Preferred Payment</Typography>
                            <Box className='preferred-box'>
                              {(showAllPayments ? preferredPayments : preferredPayments.slice(0, 3)).map(
                                (preferredPayment, index) => (
                                  <PreferredPaymentCard
                                    preferredPayment={preferredPayment}
                                    key={index}
                                    selectedPreferredPaymentId={selectedPreferredPaymentId}
                                    handleSelectPreferredPayment={handleSelectPreferredPayment}
                                    isPreferredHasTrustly={isPreferredHasTrustly}
                                  />
                                )
                              )}
                            </Box>
                            {trustlyTokenExpiredScriptLoaded && <div id='widget-id' />}
                            {showTrustlyStatus && <TrustlyStatus onAccountRefresh={onAccountRefresh} />}

                            {preferredPayments.length > 3 && (
                              <Typography
                                className='payment-methods-other-title'
                                onClick={() => setPaymentStatus('showAllPayments', !showAllPayments)}
                              >
                                {showAllPayments ? 'Hide' : 'Show'} Other Saved Payment Methods
                              </Typography>
                            )}
                          </Grid>
                        )}

                        <Grid>
                          <Typography className='payment-methods-title'>Payment Methods</Typography>
                          <PaymentMethodsGrid
                            activePaymentMethods={activePaymentMethods}
                            selectedPaymentMethod={selectedPaymentMethod}
                            changePaymentMethod={changePaymentMethod}
                            setPaymentMethod={setPaymentData}
                            isAppleDevice={isAppleDevice}
                            initiateTrustlyNewUserFlow={initiateTrustlyNewUserFlow}
                            isLoading={isLoading}
                            preferredPayments={preferredPayments}
                          />
                          <div className='apple-pay-container'>
                            <div hidden id='apple-pay' />
                          </div>
                        </Grid>
                        <Box className={[0].includes(selectedPaymentMethod) ? 'payment-form-container' : ''}>
                          <PaymentMethodForm id={selectedPaymentMethod} />
                          {selectedPaymentMethod === 0 && (
                            <Box className='payment-form-save-card-box'>
                              <Typography sx={{ color: 'white', width: '60%' }}>
                                Save This Method for faster Payment.
                              </Typography>

                              <FormControlLabel
                                control={
                                  <CustomSwitch
                                    checked={savePaymentToggle}
                                    onChange={() => setPaymentCard('savePaymentToggle', !savePaymentToggle)}
                                    color='warning'
                                  />
                                }
                                label=''
                              />
                            </Box>
                          )}
                        </Box>
                        <Grid className='order-summary-card' sx={{ color: '#FFFFFF' }}>
                          <Grid className='payment-method-section'>
                            <Grid className='payment-btn-wrap'>
                              <Grid className='btnWhiteGradient'>
                                <Box className='payment-btn-box'>
                                  {selectedPreferredPayment && (
                                    <PreferredPaymentBox
                                      selectedPreferredPayment={selectedPreferredPayment}
                                      isAppleDevice={isAppleDevice}
                                      paymentIcons={{
                                        VI: VisaImg,
                                        DI: DiscoverImg,
                                        MC: MasterImg,
                                        MD: MaestroIcon,
                                        AM: AmexImg,
                                        PAY_BY_BANK: Bank,
                                        SKRILL: skrill,
                                        APPLE_PAY: applePayIcon
                                      }}
                                    />
                                  )}
                                  {!isTrustlyCheckboxSelected
                                    ? (
                                      <Box
                                        style={{
                                          display: 'flex',
                                          justifyContent: !selectedPreferredPayment ? 'center' : 'end'
                                        }}
                                      >
                                        <Button
                                          variant='contained'
                                          className={
                                            selectedPaymentMethod === null || paymentDisabled || isLoading
                                              ? 'disabled'
                                              : 'btn-gradient'
                                          }
                                          data-tracking='Store.Checkout.Step3.Payment.Btn'
                                          data-tracking-caller={stepperCalledFor}
                                          onClick={handlePayment}
                                          disabled={selectedPaymentMethod === null || paymentDisabled || isLoading}
                                        >
                                          {isLoading ? <BtnLoader /> : `Pay ${finalAmount?.toFixed(2)}`}
                                        </Button>
                                      </Box>)
                                    : (
                                      <Box sx={{ position: 'relative', minHeight: '50px' }}>
                                        {isTrustlyWidgetLoading && (
                                          <CircularProgress size={24} style={{ marginLeft: 8 }} />
                                        )}
                                        <div id='widget-id' />
                                      </Box>)}
                                </Box>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                      </div>
                    </Grid>
                  )}
                </Box>
              </Box>
            </Grid>
            {confettiPopper && (
              <Grid className='payment-gif'>
                <lottie-player
                  src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/applied.json`}
                  background='transparent'
                  speed='1'
                  loop
                  autoplay
                  style={{ width: '500px', height: '500px' }}
                />
              </Grid>
            )}
          </Grid>
        </Grid>
        <DepositInprogressModal
          inprogressModalOpen={inprogressModalOpen}
          setInprogressModalOpen={setInprogressModalOpen}
          packageDetails={packageDetails}
          isAppleDevice={isAppleDevice}
          successToggler={successToggler}
          errorToggler={errorToggler}
          sessionId={sessionId}
        />
        {depositInitiated && (
          <ApplyPromoModal
            packageData={props?.packageDetails}
            transactionId={transactionId}
            selectedPaymentMethod={selectedPaymentMethod}
            initiateTrustlyNewUserFlow={initiateTrustlyNewUserFlow}
          />
        )}
      </Grid>
    </>
  )
}

export default PaymentModal
