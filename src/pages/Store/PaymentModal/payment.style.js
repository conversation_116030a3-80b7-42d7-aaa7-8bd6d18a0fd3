import { makeStyles } from '@mui/styles'
export default makeStyles((theme) => ({
  paymentStyleModal: {
    '& .MuiDialogContent-root': {
      padding: theme.spacing(2, 2, 0, 0),
      [theme.breakpoints.down(600)]: {
        padding: theme.spacing(1)
      }
    },
    '& .MuiDialogActions-root': {
      // padding: theme.spacing(1),
    },
    '& .modal-section-container': {
      display: 'flex',
      position: 'relative',
      [theme.breakpoints.down(600)]: {
        display: 'flex',
        flexDirection: 'column'
      },
      '& .payment-gif': {
        position: 'absolute',
        // left: 'auto',
        top: '20%',
        left: '0%',
        transform: 'translate(0%, -50%)',
        '& img': {
          width: '500px'
        }
      }
    },
    '& .paymentoptionImage-container': {
      minWidth: '353px',
      width: '100%',
      lineHeight: '0',
      margin: theme.spacing(0, -1.5),
      [theme.breakpoints.down(600)]: {
        display: 'none'
      }
    },
    '& .paymentoptionImage-Rightcontainer': {
      width: '100%',
      margin: theme.spacing(0, 0, 0, 0),

      position: 'relative'
    },
    '& .copy-icon': {
      fontSize: '16px'
    },
    '& .MuiPaper-root': {
      backgroundColor: '#000',
      color: '#fff',
      width: '100%',

      [theme.breakpoints.down(600)]: {
        minWidth: 'auto'
      }
    },
    '& .payment-option-tag': {
      color: '#FDB72E',
      fontWeight: '700',
      fontSize: '33px'
    },
    '& .payment-modal-text': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: '0.5rem',
      background: '#272727',
      minHeight: '43px',
      padding: '8px 16px',
      borderRadius: '10px'
    },

    '& .switch': {
      fontSize: '17px',
      position: 'relative',
      display: 'inline-block',
      width: '32px',
      height: '16px'
    },
    '& .switch input': { opacity: 0, width: '0', height: '0' },
    '& .slider': {
      position: 'absolute',
      cursor: 'pointer',
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
      backgroundColor: '#12131D',
      border: '1px solid gray',
      transition: '.4s',
      borderRadius: '30px'
    },
    'input:checked + .slider': {
      backgroundColor: '#gray',
      border: '1px solid #272727'
    },
    'input:focus + .slider': { boxShadow: '0 0 1px #007bff' },
    'input:checked + .slider:before': {
      transform: 'translateX(.8em)',
      backgroundColor: '#fff'
    },

    '& .payment-modal-text-leftSide': {
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem',
      '& .MuiTypography-body1': {
        fontWeight: '600',
        fontSize: '14px'
      }
    },
    '& .orderSummary': {
      display: 'flex',
      alignItems: 'center',
      fontSize: theme.spacing(1.3),
      padding: theme.spacing(1.55, 0.125, 0.3, 0),
      fontWeight: 'bold',
      color: '#D3D3D3'
    },
    '& .order-summary-content': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0.688rem 1.5rem',
      [theme.breakpoints.down('sm')]: {
        padding: '0.688rem 0rem'
      },
      '& .amount': {
        color: '#818181',
        fontWeight: 700
      },
      '& .dot-line': {
        color: '#454545',
        fontWeight: 700,
        [theme.breakpoints.down('sm')]: {
          display: 'none'
        }
      },
      '& .border-v': {
        padding: '0px 1px',
        marginLeft: '20px',
        background: 'rgba(204, 204, 204, 0.4)'
      },
      '& .bonus-coins': {
        width: '200px',
        background: 'red'
      }
    },
    '& .order-summary-discount': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0.688rem 1.5rem',
      paddingTop: '8px',
      [theme.breakpoints.down('sm')]: {
        padding: '0.688rem 0rem'
      },
      '& .discount': {
        display: 'flex',
        justifyContent: 'end',
        color: '#219653',
        fontWeight: 700,
        '& .border-v': {
          padding: '0px 1px',
          marginLeft: '20px',
          background: 'rgba(204, 204, 204, 0.4)'
        }
      },
      '& .dot-line': {
        color: '#454545',
        fontWeight: 700,
        [theme.breakpoints.down('sm')]: {
          display: 'none'
        }
      }
    },
    '& .order-total-card': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      background: '#272727',
      padding: theme.spacing(0.688, 1.5),
      borderRadius: theme.spacing(0.5),
      margin: theme.spacing(0.4, 0),
      '& h4': {
        color: '#D3D3D3',
        fontSize: theme.spacing(1.5),
        fontWeight: '700'
      }
    },
    '& .woho-order-coins-wrap': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      margin: theme.spacing(1, 0),
      gap: theme.spacing(0.5),
      '& .wohoContainer': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(0.5),
        '& .woho-tag': {
          fontWeight: '700',
          fontSize: theme.spacing(1.313)
        },
        '& .woho-tagline': {
          fontWeight: '400',
          fontSize: theme.spacing(1.125)
        }
      }
      // "& p": {
      //   fontWeight: 'bold',
      //   fontSize: theme.spacing(1),
      // },
      // "& img": {
      //   width: "30px",
      // },
    },
    '& .order-coin-wrap-leftTag': {
      display: 'flex',
      flexDirection: 'column',
      '& .coin-wrap-left-total': {
        fontSize: '18px',
        fontWeight: '700'
      },
      '& .order-coin-wrap-leftTag-bonus': {
        display: 'flex',
        '& .MuiTypography-body1': {
          fontSize: '13px',
          fontWeight: '500',
          color: '#818181'
        }
      }
    },
    '& .order-coin-wrap-rightTag': {
      display: 'flex',
      flexDirection: 'column',
      '& .MuiTypography-body1': {
        fontSize: '13px',
        fontWeight: '500'
      },
      '& .coin-wrap-left-total': {
        fontSize: '18px',
        fontWeight: '700'
      },
      '& .order-coin-wrap-rightTag-bonus': {
        display: 'flex',
        '& .MuiTypography-body1': {
          fontSize: '13px',
          fontWeight: '500',
          color: '#818181'
        }
      }
    },
    '& .order-coin-bonus-section': {
      display: 'flex',
      justifyContent: 'center',
      gap: '10px'
    },
    '& .daily-order-coins-wrap': {
      display: 'flex',
      minWidth: '100%',
      justifyContent: 'space-between',
      width: '100%',
      gap: '0.625rem',
      '& .dialy-order-card': {
        display: 'flex',
        alignItems: 'center',
        background: '#222222',
        borderRadius: '8.62px',
        padding: '18px',
        // padding: "6px 0px 4px 10px",
        maxWidth: '232px',
        width: '100%',
        margin: '1rem 0rem',
        gap: '0.5rem',
        [theme.breakpoints.down('sm')]: {
          padding: '10px'
        },
        '& p': {
          fontWeight: '700',
          fontSize: theme.spacing(1.125),
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1)
          }
        }
      },
      '& .dialy-order-card-plus': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        '& p': {
          fontSize: theme.spacing(2)
        }
      },
      '& .border-v': {
        height: '100%',
        width: '2px',
        background: 'rgba(204, 204, 204, 0.4)'
      }
    },
    '& .order-coins-wrap': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'start',
      background: '#222222',
      borderRadius: '8.62px',
      padding: '4px 4px 4px 4px',
      maxWidth: '232px',
      width: '100%',
      margin: theme.spacing(1, 0),
      gap: theme.spacing(0.5),
      [theme.breakpoints.down(600)]: {
        flexDirection: 'column',
        justifyContent: 'center'
      }
    },
    '& .MuiTypography-subtitle1': {
      fontWeight: 'bold',
      margin: theme.spacing(1, 0),
      textAlign: 'center'
    },
    '& .payment-method-card': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    '& .payment-method-icon': {
      gap: '20px',
      display: 'flex',
      '& img': {
        width: '30px'
      }
    },
    '& .payment-method-right': {
      display: 'flex',
      '& img': {
        filter: 'invert(1)'
      }
    },
    '& .btnWhiteGradient': {
      zIndex: 9,
      textAlign: 'center',
      margin: theme.spacing(1, 0),
      '& .payment-btn-box': {
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        '& .payment-box-preffered': {
          // border: '1px solid #378c37',
          // borderRadius: theme.spacing(7.8),
          '& .payment-box-preffered-text': {
            display: 'flex',
            fontWeight: '600',
            textAlign: 'start',
            fontSize: '1rem',
            color: '#fff',
            gap: '.875rem',

            '&.v-line': {
              position: 'relative',
              '&::before': {
                position: 'absolute',
                content: '""',
                height: '22px',
                width: '2px',
                background: '#606060',
                left: '-8px'
              }
            }
          },
          '& p': {
            fontWeight: '600',
            fontSize: '14px',
            color: '#606060'
          }
        }
      },
      '& button': {
        color: '#000',
        padding: theme.spacing(0.5, 0),
        fontSize: theme.spacing(1.2),
        // minWidth: "400px",
        minWidth: '150px',
        background: '#FDB72E',
        boxShadow:
          '0px 0.637px 1.401px 0px #EAB647, 0px 1.932px 4.25px 0px rgba(234, 182, 71, 0.04), 0px 5.106px 11.233px 0px rgba(234, 182, 71, 0.09), 0px 16px 35.2px 0px rgba(234, 182, 71, 0.30)',
        fontWeight: 600,
        borderRadius: theme.spacing(7.8),
        textTransform: 'capitalize',
        [theme.breakpoints.down('sm')]: {
          padding: theme.spacing(0.25, 0),
          fontSize: theme.spacing(0.875),
          minWidth: '130px'
        },
        '&.disabled': {
          background: '#FDB72E',
          opacity: '0.4',
          cursor: 'not-allowed',
          userSelect: 'none'
        }
      }
    },
    '& .payment-help-section': {
      textAlign: 'center',
      '& h4': {
        fontSize: theme.spacing(1.125),
        color: '#D3D3D3',
        fontWeight: '700',
        marginBottom: '10px'
      },
      '& p': {
        fontSize: theme.spacing(0.875),
        fontWeight: '400',
        color: '#D3D3D3'
      }
    },
    '& .payment-accordion': {
      width: '100% !important',
      background: 'transparent',
      margin: '0 !important',
      '& .payment-accordion-summary': {
        border: '1px solid #EAB647',
        borderRadius: '10px',
        maxHeight: '40px',
        padding: '0',
        minHeight: '40px',
        background: 'linear-gradient(105.41deg, #2C2C2C 29.35%, #404040 88.89%)',
        '&.Mui-expanded': {
          borderBottomLeftRadius: '0',
          borderBottomRightRadius: '0',
          borderBottom: 'none'
        },
        '& .MuiAccordionSummary-content': {
          '&.Mui-expanded': {
            margin: '8px 0',
            maxHeight: '40px'
          }
        },

        '& .MuiAccordionSummary-expandIconWrapper': {
          '& svg': {
            marginRight: '7px'
          },
          '&.Mui-expanded': {
            '& svg': {
              marginLeft: '7px'
            }
          }
        }
      },
      '& .payment-accordion-summary-title': {
        paddingLeft: '12px',
        color: 'white !important',
        fontWeight: '600'
      },
      '& .payment-accordion-details': {
        padding: '0.75rem',
        border: '1px solid #EAB647',
        borderBottomLeftRadius: '10px',
        borderBottomRightRadius: '10px',
        background: 'linear-gradient(105.41deg, #2C2C2C 29.35%, #404040 88.89%)',
        '& .dot-line': {
          color: '#51504F',
          fontWeight: 700,
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        }
      },
      '& .payment-accordion-detail-item': {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        '& .payment-accordion-detail-item-title': {
          color: 'white !important',
          fontWeight: '500',
          width: '100px'
        },
        '& .payment-accordion-detail-item-content': {
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-end'
        },
        '& .payment-accordion-detail-item-img': {
          height: '20px',
          marginRight: '10px'
        },
        '&.total-amount': {
          paddingTop: '0.75rem',
          marginTop: '0.5rem',
          borderTop: '1px dashed #51504F'
        }
      }
    },
    '& .apple-pay-container': {
      '& #apple-pay': {
        display: 'none !important'
      }
    },
    '& .payment-coupon-main': {
      marginTop: '15px',
      width: '100% !important',
      '& .payment-accordion-coupon': {
        width: '100% !important',
        '& .MuiAccordionSummary-content': {
          width: '100%',
          margin: '0 !important'
        },
        '& .payment-accordion-coupon-content': {
          padding: '0.5rem'
        }
      },
      '& .payment-coupon-container': {
        borderRadius: '10px',
        padding: '12px 16px',
        background: 'linear-gradient(105.41deg, #2C2C2C 29.35%, #404040 88.89%)',
        [theme.breakpoints.down('sm')]: {
          padding: '10px'
        },
        '& .payment-coupon-text': {
          fontSize: '18px',
          color: 'white !important',
          fontWeight: '600'
          // marginRight: '150px'
        },
        '& .payment-coupon-box': {
          display: 'flex',
          flexDirection: 'row',
          gap: '0.325rem',
          justifyContent: 'space-between',
          cursor: 'pointer',
          marginTop: '0.5rem',
          paddingTop: '0.75rem',
          borderTop: '1px dashed #51504F',
          '& .coupon-wrap': {
            display: 'flex',
            alignItems: 'start',
            gap: '0.5rem',
            '& .applied-text': {
              color: '#00A30B',
              fontSize: '1rem',
              fontWeight: '600',
              [theme.breakpoints.down('sm')]: {
                fontSize: '0.875rem',
                lineHeight: '1'
              }
            },
            '& .view-text': {
              color: '#717171',
              fontWeight: '600',
              fontSize: '0.875rem',
              cursor: 'pointer'
            }
          },
          '& .remove-text': {
            color: '#DA0000',
            fontSize: '1rem',
            fontWeight: '600'
          },
          '& .MuiSvgIcon-root': {
            fill: 'white',
            width: '1rem',
            [theme.breakpoints.down('sm')]: {
              width: '0.75rem'
            }
          }
        },
        '& .payment-coupon-btn': {
          background: '#FFA538',
          padding: '5px !important',
          minWidth: '30px !important',
          [theme.breakpoints.down('sm')]: {
            padding: '3px !important',
            minWidth: '24px !important'
          }
        },
        '& .payment-coupon-icon': {
          color: 'white !important',
          fontSize: '20px',
          rotate: '90deg !important'
        },
        '& .dot-line': {
          color: '#51504F',
          fontWeight: 'bolder',
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        }
      },
      '& .payment-btn-container': {
        padding: '12px 16px',
        background: 'linear-gradient(105.41deg, #2C2C2C 29.35%, #404040 88.89%)',
        marginBottom: '16px',
        borderRadius: '12px',
        [theme.breakpoints.down('sm')]: {
          padding: '10px'
        },
        '& .payment-coupon-text': {
          fontSize: '18px',
          color: 'white !important',
          fontWeight: '600',
          textAlign: 'center',
          // marginRight: '150px',
          [theme.breakpoints.down('sm')]: {
            fontSize: '14px'
          }
        },
        '& .deposit-failed': {
          textAlign: 'center',
          fontSize: '1.5rem',
          textTransform: 'uppercase',
          color: '#fff',
          fontWeight: '700'
        },
        '& .payment-button-container': {
          display: 'flex',
          marginBottom: '10px',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '1.5rem',
          '& button': {
            fontSize: '1rem',
            marginTop: '1.5rem',
            minHeight: '2.25rem',
            maxWidth: '6rem',
            width: '100%'
          }
        },
        '& .failed-transactions-details': {
          border: '1px dashed #fdb72e',
          padding: '6px 10px',
          borderRadius: '9px',
          '& p': {
            color: '#fdb72e !important',
            textAlign: 'start',
            '& span': {
              color: '#fff',
              marginLeft: '0.5rem'
            }
          }
        }
      }
    },
    '& .payment-methods-title': {
      marginTop: '15px',
      marginBottom: '15px',
      fontSize: '16px',
      color: 'white !important',
      fontWeight: '600'
    },
    '& .preferred-payment-container': {
      color: 'white !important',
      '& .preferred-box': {
        border: '1px solid #373737',
        borderRadius: '10px',
        marginBottom: '5px',
        '& .preferred-card': {
          padding: '10px 12px',
          display: 'flex',
          minHeight: '60px',
          gap: '0.25rem',
          borderBottom: '1px solid #373737',
          cursor: 'pointer',
          '& .card-detail': {
            display: 'flex',
            alignItems: 'center',
            gap: '2rem',
            '& img': {
              width: '30px',
              height: '20px'
            },
            '& .bank-details': {
              // maxWidth: '150px',
              width: 'fit-content',
              [theme.breakpoints.down('md')]: {
                // maxWidth: '155px'
                width: 'fit-content'
              },
              '&::before': {
                content: "''",
                position: 'absolute',
                left: '-1.25rem',
                top: '0px',
                height: '100%',
                width: '1px',
                background: '#494949'
              },
              '& .bank-name': {
                fontSize: '14px',
                display: 'flex',
                justifyContent: 'space-between',
                color: '#fff',
                gap: '0.25rem',
                '& .MuiTypography-root': {
                  fontWeight: '700'
                }
              },
              '& .bank-number': {
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '14px',
                height: '14px',
                color: '#606060',
                '& .MuiTypography-root': {
                  fontWeight: '600'
                },
                '& .holder-name': {
                  color: '#b7b7b7',
                  fontWeight: '500',
                  fontSize: '16px',
                  lineHeight: '1.25rem',
                  '&.border-right': {
                    borderRight: '1px solid #b7b7b7',
                    marginRight: '6px',
                    paddingRight: '6px',
                    height: '15px',
                    display: 'flex',
                    alignItems: 'center',
                    paddingTop: '3.5px'
                  }
                }
              },
              '& .bank-holder': {
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '14px',
                height: '14px',
                color: '#b7b7b7',
                '& .MuiTypography-root': {
                  fontWeight: '600'
                }
              },
              '& .bank-numbers': {
                color: theme.colors.textWhite,
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '12px',
                fontWeight: '600'
              }
            }
          },
          '& .card-select': {
            width: 'auto',
            display: 'flex',
            alignItems: 'center',
            '& .MuiFormControlLabel': {
              marginLeft: '0px',
              marginRight: '0px'
            },
            '& .MuiButtonBase-root': {
              color: '#4D4D4D',
              borderRadius: '20px'
            },
            '& .Mui-checked': {
              color: '#FFA538'
            },
            '& .MuiFormControlLabel-root': {
              marginRight: '0px'
            }
          },
          '&:last-child': {
            borderBottom: 'none'
          }
        }
      },
      '& .active': {
        border: '1px solid #FFA538'
      },
      '& .payment-methods-other-title': {
        marginTop: '10px',
        cursor: 'pointer',
        fontWeight: '600',
        lineHeight: '9.16px',
        color: '#0E5E90;',
        textDecoration: 'underline',
        width: 'fit-content'
      },
      '& #apple-pay': {
        display: 'none !important',
        visibility: 'hidden !important'
      }
    },
    '& .payment-methods': {
      '& .payment-methods-item': {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        height: '70px',
        padding: '6px',
        border: '1px solid #858383',
        borderRadius: '12px',
        cursor: 'pointer',
        fontWeight: '600',
        '&:hover': {
          border: '1px solid #FFA538',
          '& p': {
            color: '#FFA538'
          },
          [theme.breakpoints.down('md')]: {
            border: '1px solid #858383',
            '& p': {
              color: '#fff'
            }
          }
        },
        [theme.breakpoints.down('md')]: {
          // flexDirection: 'row',
          justifyContent: 'start',
          gap: '0.25rem',
          height: 'auto',
          maxHeight: '58px',
          padding: '0.125rem 0.5rem',
          '& p': {
            fontSize: '12px'
          },
          '& img': {
            width: '30px',
            maxHeight: '30px'
          }
        }
      },
      '& .active': {
        border: '1px solid #FFA538 !important',
        '& p': {
          color: '#FFA538 !important'
        }
      },
      '& .apple-pay-container': {
        '& #apple-pay': {
          display: 'none !important'
        }
      },
      '& .no-active-pay': {
        fontSize: '14px',
        fontWeight: 500,
        textAlign: 'center',
        width: '100%',
        marginTop: '0px',
        color: '#d60000'
      }
    },
    '& .payment-form-container': {
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-evenly',
      maxWidth: '480px',
      alignItems: 'center',
      border: '1px solid #858383',
      borderRadius: '9px',
      marginTop: '15px !important',
      padding: '16px',
      '& .card-error': {
        fontSize: '14px',
        fontWeight: 500,
        textAlign: 'start',
        width: '100%',
        marginTop: '0px',
        color: '#d60000'
      }
    },
    '& .payment-form-save-card-box': {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: '10px',
      paddingBottom: '20px',
      '& .my-custom-switch': {
        cursor: 'pointer',
        userSelect: 'none'
      }
      // '& .MuiSwitch-track': {
      //   borderRadius: '0 3px 3px 0 !important',
      //   background: 'transparent',
      //   borderRight: 'none',
      //   border: '2px solid #838281'
      // }
    },
    '& .payment-form-input': {
      width: '100%',
      color: 'white',
      border: '1px solid #858383',
      borderRadius: '10px',
      margin: '5px 0',
      '& input': {
        color: 'white',
        padding: '8px 10px',
        fontSize: '16px',
        fontWeight: '600',
        '&::-webkit-autofill': {
          background: 'transparent !important',
          boxShadow: 'none'
        },
        '&::placeholder': {
          fontWeight: '600 !important',
          fontSize: '16px !important'
        }
      },
      '& label': {
        color: ' #858383',
        fontSize: '14px'
      },
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none'
      }
    },
    '& .MuiOutlinedInput-root': {
      '&.Mui-focused': {
        borderColor: 'transparent',
        boxShadow: 'none'
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: 'transparent'
      }
    },
    '& .MuiInputLabel-root': {
      '&.Mui-focused': {
        color: 'inherit' // Prevent label color change
      }
    },
    '& .input-autofill-style': {
      WebkitTextFillColor: 'white !important'
    },
    '& .payment-form-input-card': {
      width: '100%',
      height: '40px',
      padding: '4px 10px',
      color: 'white',
      border: '1px solid #858383',
      borderRadius: '10px',
      margin: '5px 0',
      '& input': {
        color: 'white',
        '&:-webkit-autofill': {
          color: 'white !Important',
          boxShadow: 'none',
          background: 'transparent'
        }
      },
      '& label': {
        color: ' #858383'
      }
    },
    '& .payment-form-card-brand': {
      position: 'absolute',
      top: '87px',
      right: '10%'
    },
    '& .payment-form-date-input': {
      // width: '70%',
      height: '40px',
      padding: '4px 10px',
      color: 'white',
      border: '1px solid #858383',
      marginRight: '10px',
      borderRadius: '10px',
      '& input': {
        color: 'white'
      },
      '& label': {
        color: ' #858383'
      }
    },
    '& .payment-form-cvv-input': {
      // width: '30%',
      height: '40px',
      padding: '4px 10px',
      color: 'white',
      border: '1px solid #858383',
      borderRadius: '10px',
      '& input': {
        color: 'white',
        WebkitTextFillColor: 'white'
      },
      '& label': {
        color: ' #858383'
      }
    },
    '& .payment-form-cvv': {
      width: '30%',
      color: 'white',
      height: '50px',
      border: '1px solid #858383',
      borderRadius: '10px',
      '& input': {
        color: 'white'
      },
      '& label': {
        color: ' #858383'
      }
    }
  },
}))
