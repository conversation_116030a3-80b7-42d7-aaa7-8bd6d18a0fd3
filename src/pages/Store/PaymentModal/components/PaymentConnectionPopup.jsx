import React, { useEffect } from 'react'
import { Grid, Box, Typography } from '@mui/material'
import useStyles from '../../../../components/PaymentStatus/paymentStatusStyles.js'
import { usePortalStore } from '../../../../store/store.js'
import { loadLottieScript } from '../../../../utils/loadLottieScript.js'

const PaymentConnectionPopup = ({ navigate }) => {
  const portalStore = usePortalStore()
  const classes = useStyles()
  useEffect(() => {
    const timer = setTimeout(() => {
      portalStore.closePortal()
      window.localStorage.removeItem('paymentMethod')
      navigate('/user/store')
    }, 10000)

    return () => clearTimeout(timer)
  }, [navigate])

  useEffect(() => {
    loadLottieScript()
  }, [])

  return (
    <Box className={classes.root}>
      <div className='payment-status-container'>
        <Grid className='inner-modal-header'>
          <Typography className='status-title' variant='h4'>
            Payment Status
          </Typography>
          <Grid className='modal-close' />
        </Grid>
        <Box className='payment-status-modal processing'>
          {/* PAYMENT FAILED */}
          <Grid className='payment-status-content'>
            <Box className='processing-animation'>
              <lottie-player
                src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/failed.json`}
                background='transparent'
                speed='1'
                autoplay
              />
            </Box>
            <Typography className='processing' variant='h4'>
              In Progress
            </Typography>
            <Typography>
              Your payment is still being processed by the payment provider. Please check the status in My Plays after
              some time. Sorry for the inconvenience, and thank you for your patience.
            </Typography>
          </Grid>
        </Box>
      </div>
    </Box>
  )
}

export default PaymentConnectionPopup
