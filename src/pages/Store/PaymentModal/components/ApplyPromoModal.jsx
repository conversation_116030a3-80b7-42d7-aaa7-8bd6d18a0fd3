import React from 'react'
import { <PERSON>, <PERSON><PERSON>, Card, Card<PERSON>ontent, CircularProgress, Dialog, TextField, Typo<PERSON> } from '@mui/material'
import useStyles from '../Accounts.styles'
import CloseIcon from '@mui/icons-material/Close'
import { useApplicablePromocodeListQuery } from '../hooks/usePromocodeList'
import { PaymentQuery, usePackagePromoCodeMutation } from '../../../../reactQuery'
import { usePromocodeAppliedStore } from '../../../../store/store'
import { usePaymentStore } from '../../../../store/usePaymentStore'
import nopromocode from '../../../../components/ui-kit/icons/opImages/no-promocode.webp'

const colors = [
  'linear-gradient(105.41deg, #464028 29.35%, #7C724A 88.89%) !important',
  'linear-gradient(105.41deg, #1A144E 29.35%, #2E2583 88.89%)!important',
  'linear-gradient(105.41deg, #3A0A32 29.35%, #6D1A5F 88.89%) !important'
]

const ApplyPromoModal = ({
  packageData,
  transactionId,
  selectedPaymentMethod,
  initiateTrustlyNewUserFlow
}) => {
  // SETTERS
  const { setPaymentData, setPaymentStatus, setPaymentPromocode } = usePaymentStore()

  // GETTERS
  const { promocodeModalOpen, promocode, promocodeSuccess, selectedPromocode, isPromocodeLoading } = usePaymentStore(
    (state) => state.promocode
  )

  const setPromocodeApplied = usePromocodeAppliedStore((state) => state.setPromocodeApplied)

  const handleClose = () => {
    setPaymentPromocode('promocodeModalOpen', false)
  }
  const classes = useStyles()

  // Get Promocodes
  const { data: promoCodesData } = useApplicablePromocodeListQuery({
    params: {
      packageId: packageData?.packageId,
      transactionId: transactionId
    },
    enabled: promocodeModalOpen,
    onSuccess: () => {
      setPaymentPromocode('isPromocodeLoading', false)
    },
    onError: (error) => {
      setPaymentPromocode('isPromocodeLoading', false)
      console.log('**************error', error)
    }
  })

  const deleteAppliedPromocode = PaymentQuery.deleteAppliedPromocodeMutation({
    onSuccess: (res) => {
      console.log('Promocode Removed', res?.data)
      if (selectedPaymentMethod === 5) {
        initiateTrustlyNewUserFlow()
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const applyPromocodeMutate = usePackagePromoCodeMutation({
    onSuccess: (res) => {
      setPaymentData('finalAmount', res?.data?.data?.amount)
      setPaymentData('discountAmount', res?.data?.data)
      setPaymentStatus('confettiPopper', true)
      setPaymentPromocode('promocodeSuccess', true)
      setPromocodeApplied({ promocodeApplied: true, promocode: promocode })
      setPaymentPromocode('selectedPromocode', res?.data?.data?.promocode)
      setPaymentPromocode('promocode', res?.data?.data?.promocode)
      setTimeout(() => {
        setPaymentStatus('confettiPopper', false)
      }, 2000)
      if (selectedPaymentMethod === 5) {
        initiateTrustlyNewUserFlow()
      }
    },
    onError: (err) => {
      if (err?.response?.data?.errors?.length > 0) {
        const { errors } = err.response.data
        console.log(errors)
        setPaymentPromocode('promocode', '')
      }
    }
  })

  const handlePromocodeChange = (event) => {
    if (selectedPromocode) return
    const code = event.target.value
    setPaymentPromocode('promocode', code)
  }

  const handleApplyPromocodeOnSelect = (promo) => {
    if (selectedPromocode) {
      deleteAppliedPromocode.mutate(
        { transactionId },
        {
          onSuccess: () => {
            setPaymentPromocode('promocode', '')
            setPaymentPromocode('promocodeSuccess', false)
            setPromocodeApplied({ promocodeApplied: false, promocode: '' })
            setPaymentPromocode('selectedPromocode', '')
            applyPromocodeMutate.mutate({ promocode: promo, transactionId })
            setPaymentPromocode('promocodeModalOpen', false)
          },
          onError: (error) => {
            console.log(error)
          }
        }
      )
    } else {
      applyPromocodeMutate.mutate({ promocode: promo, transactionId })
      setPaymentPromocode('promocodeModalOpen', false)
    }
  }

  const handleApplyPromocode = () => {
    applyPromocodeMutate.mutate({ promocode: promocode, transactionId: transactionId })
    setPaymentPromocode('promocodeModalOpen', false)
  }

  const handleRemovePromocode = (e) => {
    e.stopPropagation()
    setPaymentData('finalAmount', packageData?.amount)
    setPaymentData('discountAmount', '')
    setPaymentStatus('confettiPopper', false)
    deleteAppliedPromocode.mutate({ transactionId: transactionId })
    setPaymentPromocode('promocode', '')
    setPaymentPromocode('promocodeSuccess', false)
    setPromocodeApplied({ promocodeApplied: false, promocode: '' })
    setPaymentPromocode('selectedPromocode', '')
  }

  return (
    <Dialog onClose={handleClose} open={promocodeModalOpen}>
      <Box className={classes.couponBox} sx={{ padding: '15px', background: '#1B1B1B' }}>
        <Box className='header-wrap'>
          <Typography>Apply Promocode</Typography>
          <CloseIcon sx={{ cursor: 'pointer' }} onClick={handleClose} />
        </Box>
        <Card
          sx={{
            background: 'linear-gradient(105.41deg, #2C2C2C 29.35%, #585858 88.89%)!important',
            color: '#fff',
            padding: '1.5rem',
            borderRadius: 2
          }}
        >
          <Typography fontSize='16px' fontWeight='bold'>
            Promocode
          </Typography>
          <Typography fontSize='14px' color='#AFAEAE' marginBottom='12px' fontWeight='bold'>
            Apply promocode to get Exciting offer
          </Typography>
          <Box display='flex' gap={1}>
            <TextField
              variant='outlined'
              placeholder='Enter Promocode'
              size='small'
              disabled={promocodeSuccess}
              value={promocode || selectedPromocode}
              onChange={handlePromocodeChange}
              sx={{
                minWidth: '60%',
                borderRadius: '10px',
                border: '2.2px solid #807D7D',
                minHeight: '40px',
                maxHeight: '40px',
                input: { color: '#807D7D', padding: '6px 14px', fontWeight: '600', border: 'none' },
                '& .MuiInputBase-root': {
                  border: 'none'
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none'
                },
                '& .Mui-disabled': {
                  color: '#fff',
                  WebkitTextFillColor: '#fff !important'
                }
              }}
            />
            {promocodeSuccess ? (
              <Button
                variant='contained'
                className='btn btn-primary'
                sx={{
                  backgroundColor: '#FFA538 !important',
                  padding: '2px 6px',
                  width: '100%',
                  borderRadius: '10px !important',
                  color: '#000',
                  cursor: 'pointer',
                  '&:hover': {
                    background: 'transparent !important'
                  }
                }}
                onClick={(e) => handleRemovePromocode(e)}
              >
                Remove
              </Button>
            ) : (
              <Button
                variant='contained'
                className='btn btn-primary'
                sx={{
                  backgroundColor: '#FFA538 !important',
                  padding: '2px 6px',
                  width: '100%',
                  borderRadius: '10px !important',
                  color: '#000',
                  maxHeight: '40px',
                  cursor: 'pointer',
                  '&:hover': {
                    background: 'transparent !important'
                  }
                }}
                onClick={(e) => handleApplyPromocode(e)}
                disabled={promocode ? false : true} // eslint-disable-line
              >
                Apply
              </Button>
            )}
          </Box>
          {promocodeSuccess && (
            <Typography variant='body2' sx={{ mb: '5px' }}>
              Promocode Applied successfully.
            </Typography>
          )}
        </Card>
        <Box>
          {isPromocodeLoading ? (
            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', padding: '20px' }}>
              <CircularProgress size={24} style={{ marginLeft: 8 }} />
            </Box>
          ) : promoCodesData?.data?.data?.updatedPromocode?.length <= 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', marginTop: '1rem' }}>
              <img src={nopromocode} style={{ margin: '0 auto', maxWidth: '150px', width: '100%' }} />
              <Typography style={{ color: '#fff', fontWeight: '600', textAlign: 'center' }}>
                No promocode available
              </Typography>
            </div>
          ) : (
            <Box sx={{ marginTop: '10px !important', height: '350px', paddingRight: '4px', overflowY: 'auto' }}>
              {promoCodesData?.data?.data?.updatedPromocode?.map((item, index) => (
                <Card
                  key={index}
                  sx={{
                    background: colors[Math.floor(Math.random() * 3)],
                    color: '#fff',
                    borderRadius: 2,
                    marginTop: '10px'
                  }}
                >
                  <CardContent sx={{ padding: '0.75rem 1.5rem' }}>
                    <Typography variant='h6' fontWeight='bold'>
                      {item.promocode}
                    </Typography>
                    {item?.description ? (
                      <Typography variant='body2' sx={{ mb: 1, opacity: 0.8, fontWeight: '600', fontSize: '15px' }}>
                        {item?.description}
                      </Typography>
                    ) : (
                      <Typography variant='body2' sx={{ mb: 1, opacity: 0.8, fontWeight: '600', fontSize: '15px' }}>
                        Grab the package now with {item.discountPercentage}%{' '}
                        {item.isDiscountOnAmount ? 'discount' : 'bonus'}.
                      </Typography>
                    )}

                    {selectedPromocode === item.promocode ? (
                      <Button
                        variant='contained'
                        className='btn btn-primary'
                        sx={{
                          backgroundColor: '#FFA538 !important',
                          padding: '3px 6px !important',
                          minHeight: '30px',
                          borderRadius: '6px !important',
                          maxHeight: '25px',
                          '&:hover': {
                            background: 'transparent !important'
                          }
                        }}
                        onClick={(e) => handleRemovePromocode(e)}
                      >
                        Remove
                      </Button>
                    ) : (
                      <Button
                        variant='contained'
                        className='btn btn-primary'
                        sx={{
                          backgroundColor: '#FFA538 !Important',
                          padding: '3px 6px !important',
                          borderRadius: '6px !important',
                          maxHeight: '25px',
                          minHeight: '30px',
                          '&:hover': {
                            background: 'transparent !important'
                          }
                        }}
                        onClick={() => handleApplyPromocodeOnSelect(item.promocode)}
                      >
                        Apply
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}
        </Box>
      </Box>
    </Dialog>
  )
}

export default ApplyPromoModal
