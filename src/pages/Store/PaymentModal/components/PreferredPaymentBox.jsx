import React from 'react';
import { Box, Typography } from '@mui/material';

const PreferredPaymentBox = ({ selectedPreferredPayment, isAppleDevice, paymentIcons }) => {
  if (!selectedPreferredPayment) return null;

  const { moreDetails, paymentMethodType } = selectedPreferredPayment;

  return (
    <Box className='payment-box-preffered'>
      <Box style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
        {moreDetails?.cardType && paymentIcons[moreDetails.cardType] && (
          <img src={paymentIcons[moreDetails.cardType]} alt={moreDetails.cardType} />
        )}
        {paymentMethodType && paymentIcons[paymentMethodType] && (
          <img src={paymentIcons[paymentMethodType]} alt={paymentMethodType} />
        )}
        {paymentMethodType === "TRUSTLY" && (
          <img
            src={`https://paywithmybank.com/start/assets/institutions/icons/${selectedPreferredPayment?.moreDetails?.bankIdentifier}.png`}
            alt="Bank Logo"
            style={{ width: '30px', height: 'inherit' }}
          />
        )}
        <Typography>PAY USING</Typography>
      </Box>
      {paymentMethodType === 'CARD' && (
        <Typography className='payment-box-preffered-text'>
          {moreDetails?.cardBin} ....{moreDetails?.lastDigits}
        </Typography>
      )}
      {/* {['SKRILL', 'APPLE_PAY'].includes(paymentMethodType) && (
        isAppleDevice || paymentMethodType !== 'APPLE_PAY' ? (
          <Typography className='payment-box-preffered-text'>Apple Pay</Typography>
        ) : null
      )} */}
      {(paymentMethodType === 'APPLE_PAY' && isAppleDevice) && (
        <Typography className='payment-box-preffered-text'>APPLE PAY</Typography>
      )}
      {paymentMethodType === 'SKRILL' && (
        <Typography className='payment-box-preffered-text'>SKRILL</Typography>
      )}
      {paymentMethodType === 'PAY_BY_BANK' && (
        <Typography className='payment-box-preffered-text'>
          {moreDetails?.bankName}
          <Typography className='payment-box-preffered-text v-line'>....{moreDetails?.lastDigits}</Typography>
        </Typography>
      )}

      {paymentMethodType === 'TRUSTLY' && (
        <Typography className='payment-box-preffered-text'>
          {moreDetails?.bankName}
          <Typography className='payment-box-preffered-text v-line'>....{moreDetails?.accountNumber}</Typography>
        </Typography>
      )}

    </Box>
  );
};

export default PreferredPaymentBox;
