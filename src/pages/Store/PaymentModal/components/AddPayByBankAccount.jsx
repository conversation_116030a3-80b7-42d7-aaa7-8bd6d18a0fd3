import React, { useEffect, useState, useRef } from 'react'
import { CircularProgress, Box, IconButton } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePaymentProcessStore, usePortalStore } from '../../../../store/store'
import useStyles from '../../../../components/StepperForm/StepperForm.styles'
import { useNavigate } from 'react-router-dom'
import { PaymentQuery } from '../../../../reactQuery'

const AddPayByBankAccount = ({ redirectUrl }) => {
  const navigate = useNavigate()
  const iframeRef = useRef(null)
  const [loading, setLoading] = useState(true)
  const [showIFrame, setShowIFrame] = useState(true)
  const portalStore = usePortalStore((state) => state)
  const classes = useStyles()

  const transactionId = window.localStorage.getItem('transactionId')

  // Cancel Deposit
  const setCancelDeposit = usePaymentProcessStore((state) => state.setCancelDeposit)
  const cancelDeposit = PaymentQuery.cancelDepositMutation({
    onSuccess: (res) => {
      setCancelDeposit(false)
      window.localStorage.removeItem('transactionId')
    },
    onError: (error) => {
      console.log('Internal Server Error', error)
      setCancelDeposit(false)
    }
  })

  useEffect(() => {
    const interval = setInterval(() => {
      if (iframeRef.current) {
        const redirectURL = iframeRef.current.contentWindow.location.href
        const url = new URL(redirectURL)
        const relativeUrl = url.pathname + url.search + url.hash
        if (redirectURL.includes('?status=success')) {
          setShowIFrame(false)
          navigate(relativeUrl)
          portalStore.closePortal()
        } else if (redirectURL.includes('?status=failed')) {
          setShowIFrame(false)
          navigate(relativeUrl)
          portalStore.closePortal()
        } else if (redirectURL.includes('?status=cancelled')) {
          setShowIFrame(false)
          navigate(relativeUrl)
          portalStore.closePortal()
        }
      }
    }, 1000) // Check URL every second

    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    const iframe = iframeRef.current
    if (!iframe) return

    const handleLoad = () => {
      setLoading(false)
    }

    iframe.addEventListener('load', handleLoad)

    return () => {
      iframe.removeEventListener('load', handleLoad)
    }
  }, [])

  const handleClose = () => {
    if (transactionId) {
      cancelDeposit.mutate({ transactionId: transactionId })
    }
    portalStore.closePortal()
  }

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      const tranID = window.localStorage.getItem('transactionId')
      if (tranID) {
        cancelDeposit.mutate({ transactionId: tranID }) // Call function before closing the popup
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [])

  return (
    <Box className={classes.iframeContainer}>
      <IconButton onClick={handleClose} className='iframe-close'>
        <CloseIcon />
      </IconButton>
      {loading && (
        <Box className='loader-overlay'>
          <CircularProgress />
        </Box>
      )}
      {showIFrame && (
        <iframe
          ref={iframeRef}
          id='iframe-id-paybybank'
          src={redirectUrl}
          title='Add Bank Account'
          width='100%'
          height='600px'
          style={{ border: 'none' }}
        // onLoad={handleLoad}
        />
      )}
    </Box>
  )
}

export default AddPayByBankAccount
