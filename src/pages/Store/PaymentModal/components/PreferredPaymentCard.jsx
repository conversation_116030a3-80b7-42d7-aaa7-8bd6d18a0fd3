import React, { useState } from 'react'
import { Box, Typography, FormControlLabel, Radio, IconButton } from '@mui/material'
import DeleteIcon from '@mui/icons-material/Delete'
import {
  AmexImg,
  MasterImg,
  VisaImg,
  DiscoverImg,
  skrill,
  Bank,
  applePayIcon
} from '../../../../components/ui-kit/icons/svg'
import { MaestroIcon } from '../../../../components/ui-kit/icons/opImages'
import { getOSAndBrowser } from '../../../../utils/helpers'
import { usePaymentStore } from '../../../../store/usePaymentStore'
import paymentQuery from '../../../../reactQuery/paymentQuery'
import toast from 'react-hot-toast'
import { HourglassTop } from '@mui/icons-material'

const PreferredPaymentCard = ({ preferredPayment, selectedPreferredPaymentId, handleSelectPreferredPayment, isPreferredHasTrustly }) => {
  const browserType = getOSAndBrowser()
  const [paymentMethodToRemove, setPaymentMethodToRemove] = useState('')
  const [paymentMethodDeleteLoading, setpaymentMethodDeleteLoading] = useState(false)
  // DATA
  const {
    preferredPayments
  } = usePaymentStore((state) => state.data)

  // SETTERS - PAYMENT STORE
  const {
    setPaymentData,
    setPaymentToken,
    setPaymentTrustly
  } = usePaymentStore()
  // DELETE USER PREFERRED PAYMENT METHODS
  const deletePaymentMethod = paymentQuery.deletePreferredPaymentMutation({
    onSuccess: (res) => {
      const updatedPayments = preferredPayments.filter(
        (payment) => payment.paymentMethodName !== paymentMethodToRemove
      )
      setPaymentData('preferredPayments', updatedPayments)
      setpaymentMethodDeleteLoading(false)
      toast.success('Preferred Payment Removed Successfully')
    },
    onError: (err) => {
      console.log('PaymentDeleteError', err)
      setpaymentMethodDeleteLoading(false)
      toast.error('Preferred Payment Remove Failed')
    }
  })

  const onDeleteMethod = (paymentMethodName) => {
    setpaymentMethodDeleteLoading(true)
    setPaymentMethodToRemove(paymentMethodName)
    setPaymentData('selectedPreferredPayment', null)
    setPaymentData('selectedPreferredPaymentId', null)
    setPaymentData('selectedPaymentMethod', null)
    setPaymentToken('paymentHandleToken', '')
    deletePaymentMethod.mutate({ paymentMethodName: paymentMethodName })
  }

  const paymentCardMap = {
    CARD: (
      <>
        {preferredPayment?.moreDetails?.cardType === 'VI' && <img src={VisaImg} />}
        {preferredPayment?.moreDetails?.cardType === 'DI' && <img src={DiscoverImg} />}
        {preferredPayment?.moreDetails?.cardType === 'MD' && <img src={MaestroIcon} />}
        {preferredPayment?.moreDetails?.cardType === 'MC' && <img src={MasterImg} />}
        {preferredPayment?.moreDetails?.cardType === 'AM' && <img src={AmexImg} />}
        <Box className='bank-details'>
          <Box className='bank-name'>
            <Typography>{preferredPayment?.moreDetails?.holderName}</Typography>
          </Box>
          <Box className='bank-number'>
            <Typography className='holder-name border-right'>
              **** {preferredPayment?.moreDetails?.lastDigits}
            </Typography>
            <Typography className='holder-name'>
              {preferredPayment?.moreDetails?.cardExpiry?.month}/{preferredPayment?.moreDetails?.cardExpiry?.year}
            </Typography>
          </Box>
        </Box>
      </>
    ),
    PAY_BY_BANK: (
      <>
        <img src={Bank} />
        <Box className='bank-details'>
          <Box className='bank-name'>
            <Typography>{preferredPayment?.moreDetails?.bankName}</Typography>
          </Box>
          <Box className='bank-number'>
            <Typography className='holder-name'>**** **{preferredPayment?.moreDetails?.lastDigits}</Typography>
          </Box>
        </Box>
      </>
    ),
    TRUSTLY: (
      <>
        {isPreferredHasTrustly
          ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.25, width: '60px !important' }}>
              <Box>
                <IconButton
                  aria-label='delete'
                  size='small'
                  sx={{ color: 'error.main' }}
                  onClick={() => onDeleteMethod(preferredPayment?.paymentMethodName)}
                >
                  {paymentMethodDeleteLoading
                    ? (<HourglassTop fontSize='small' />)
                    : (<DeleteIcon fontSize='small' />)}
                </IconButton>
              </Box>
              <Box
                component='img'
                src={`https://paywithmybank.com/start/assets/institutions/icons/${preferredPayment?.moreDetails?.bankIdentifier}.png`}
                alt='Bank Logo'
                sx={{ width: 30, height: 30, objectFit: 'contain' }}
              />
            </Box>)
          : (
            <img
              src={`https://paywithmybank.com/start/assets/institutions/icons/${preferredPayment?.moreDetails?.bankIdentifier}.png`}
              alt='Bank Logo'
              style={{ width: '30px', height: 'inherit' }}
            />)}
        <Box className='bank-details'>
          <Box className='bank-name'><Typography>{preferredPayment?.moreDetails?.bankName}</Typography></Box>
          <Box className='bank-number'>
            <Typography className='holder-name'>{preferredPayment?.moreDetails?.accountType
              ?.toLowerCase()
              .replace(/\b\w/g, c => c.toUpperCase())} | ****{preferredPayment?.moreDetails?.accountNumber}
            </Typography>
          </Box>
        </Box>
      </>
    ),
    SKRILL: (
      <>
        <img src={skrill} />
        <Box className='bank-details'>
          <Box className='bank-name'>
            <Typography>{preferredPayment?.paymentMethodType}</Typography>
          </Box>
        </Box>
      </>
    ),
    APPLE_PAY: browserType.os === 'MacOS' || browserType.os === 'iOS'
      ? (
        <>
          <img src={applePayIcon} />
          <Box className='bank-details'>
            <Box className='bank-name'><Typography>Apple Pay</Typography></Box>
          </Box>
        </>)
      : null
  }

  if (!paymentCardMap[preferredPayment.paymentMethodType]) return null

  return (
    <Box
      className='preferred-card' onClick={() => {
        if (paymentMethodDeleteLoading) {
          return
        }
        handleSelectPreferredPayment('', preferredPayment, 0)
        setPaymentTrustly('isTrustlyCheckboxSelected', false)
      }}
    >
      <Box className='card-detail'>{paymentCardMap[preferredPayment.paymentMethodType]}</Box>
      <Box className='card-select'>
        <FormControlLabel
          value={preferredPayment.paymentMethodName}
          control={<Radio checked={selectedPreferredPaymentId === preferredPayment.paymentMethodName} />}
        />
      </Box>
    </Box>
  )
}

export default PreferredPaymentCard
