import React from 'react'
import { makeStyles } from '@mui/styles'

const useStyles = makeStyles({
  loaderContainer: {
    display: 'flex',
    alignItems: 'center',
    height: '34px'
    // width: "50px",
  },
  loaderInner: {
    display: 'flex'
  },
  dot: {
    fontSize: '12px',
    opacity: 0,
    animation: '$fadeInOut 1.5s infinite ease-in-out'
  },
  '@keyframes fadeInOut': {
    '0%': { opacity: 0 },
    '50%': { opacity: 1 },
    '100%': { opacity: 0 }
  },
  dot2: {
    animationDelay: '0.1s'
  },
  dot3: {
    animationDelay: '0.2s'
  },
  dot4: {
    animationDelay: '0.3s'
  }
})

const BtnLoader = () => {
  const classes = useStyles()
  return (
    <div className={classes.loaderContainer}>
      <div className={classes.loaderInner}>
        <label className={classes.dot}>●</label>
        <label className={`${classes.dot} ${classes.dot2}`}>●</label>
        <label className={`${classes.dot} ${classes.dot3}`}>●</label>
        <label className={`${classes.dot} ${classes.dot4}`}>●</label>
      </div>
    </div>
  )
}

export default BtnLoader
