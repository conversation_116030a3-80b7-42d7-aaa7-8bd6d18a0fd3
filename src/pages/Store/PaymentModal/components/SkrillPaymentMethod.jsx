import React, { useState, useRef, useEffect } from 'react'
import { CircularProgress, Box } from '@mui/material'
import useStyles from '../../../../components/StepperForm/StepperForm.styles'
import { usePortalStore } from '../../../../store/store'
import { useNavigate } from 'react-router-dom'

const SkrillPaymentMethod = ({ redirectUrl }) => {
  const navigate = useNavigate()
  const iframeRef = useRef(null)
  const [loading, setLoading] = useState(true)
  const classes = useStyles()
  const [showIFrame, setShowIFrame] = useState(true)
  const portalStore = usePortalStore((state) => state)

  // ✅ Safe handler for iframe load
  const handleIframeLoad = () => {
    try {
      const currentUrl = iframeRef.current?.contentWindow?.location?.href

      const url = new URL(currentUrl)
      const relativeUrl = url.pathname + url.search + url.hash
      const status = url.searchParams.get('status')

      console.log('$$$$SKRILL REDIRECT URL', currentUrl, relativeUrl, url.search, url.hash, status)
      // Check only if we're back to the same origin
      if (currentUrl?.startsWith(window.location.origin)) {
        if (status === 'success' || status === 'failed' || status === 'cancelled') {
          setShowIFrame(false)
          navigate(relativeUrl)
          portalStore.closePortal()
        }
      }
    } catch (err) {
      // Expected when iframe is still on a cross-origin URL
      console.warn('Cross-origin access blocked — waiting for redirect back.', err)
    }
  }

  // ✅ New auto-redirect logic after 10 minutes
  useEffect(() => {
    const timeout = setTimeout(() => {
      navigate('/user/store')
      portalStore.closePortal()
    }, 10 * 60 * 1000) // 10 minutes

    return () => clearTimeout(timeout)
  }, [])

  useEffect(() => {
    const iframe = iframeRef.current
    if (!iframe) return

    const handleLoad = () => {
      setLoading(false)
      handleIframeLoad()
    }

    iframe.addEventListener('load', handleLoad)

    return () => {
      iframe.removeEventListener('load', handleLoad)
    }
  }, [])

  return (
    <Box className={classes.iframeContainer}>
      {loading && (
        <Box className='loader-overlay'>
          <CircularProgress />
        </Box>
      )}
      {showIFrame && (
        <iframe
          ref={iframeRef}
          id='iframe-id-paybyskrill'
          src={redirectUrl}
          title='Skrill Payment'
          width='100%'
          height='800px'
          style={{ border: 'none' }}
        />
      )}
    </Box>
  )
}

export default SkrillPaymentMethod
