import React from 'react'
import { Grid, Box, Typography, Button } from '@mui/material'
import { Sell } from '@mui/icons-material'
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos'
import viewAll from '../../../../components/ui-kit/icons/svg/view-all-arrow.svg'
import { usePaymentStore } from '../../../../store/usePaymentStore'

const PromocodeSection = ({
  handleRemovePromocode
}) => {
  // PROMOCODE
  const { setPaymentPromocode } = usePaymentStore()
  const {
    promocodeSuccess,
    selectedPromocode
  } = usePaymentStore((state) => state.promocode)
  return (
    <Grid className='payment-coupon-main'>
      <Grid className='payment-coupon-container'>
        <Typography className='payment-coupon-text'>Savings Corner</Typography>
        <Box className='payment-coupon-box' onClick={() => setPaymentPromocode('promocodeModalOpen', true)}>
          <Box className='coupon-wrap'>
            <Button className='payment-coupon-btn'>
              <Sell className='payment-coupon-icon' />
            </Button>
            {promocodeSuccess
              ? (
                <Box>
                  <Typography className='applied-text'>
                    {selectedPromocode} Promocode Applied Successfully.
                  </Typography>
                  <Typography className='view-text'>
                    View All Promocodes{' '}
                    <img src={viewAll} alt='arrow' style={{ width: '5px', marginLeft: '3px', top: '1px', position: 'relative' }} />
                  </Typography>
                </Box>)
              : (
                <Typography className='payment-coupon-text'>Apply Promocode</Typography>)}
          </Box>
          {promocodeSuccess
            ? (
              <Typography onClick={handleRemovePromocode} className='remove-text'>Remove</Typography>)
            : (
              <ArrowForwardIosIcon />)}
        </Box>
      </Grid>
    </Grid>
  )
}

export default PromocodeSection
