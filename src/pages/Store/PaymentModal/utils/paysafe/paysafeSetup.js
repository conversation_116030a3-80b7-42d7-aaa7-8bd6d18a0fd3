export const setupPaysafeFields = async ({
  API_KEY,
  options,
  onCardBrandRecognized,
  onCardError,
  onSuccess,
  onFailure,
}) => {
  let retryCount = 0
  const MAX_RETRIES = 3

  const attemptSetup = async () => {
    try {
      const instance = await window.paysafe.fields.setup(API_KEY, options)
      await instance.show()

      // Listeners
      if (instance.fields.cardNumber) {
        instance.fields.cardNumber.on('FieldValueChange', () => {
          onCardError('cardNumber', '')
        })
      }

      if (instance.fields.expiryDate) {
        instance.fields.expiryDate.on('FieldValueChange', () => {
          const valid = instance.fields.expiryDate.isValid()
          const empty = instance.fields.expiryDate.isEmpty()
          onCardError('expiryDate', !valid && !empty ? 'Expiry date must be in the future' : '')
        })
      }

      if (instance.fields.cvv) {
        instance.fields.cvv.on('FieldValueChange', () => {
          const valid = instance.fields.cvv.isValid()
          const empty = instance.fields.cvv.isEmpty()
          onCardError('cvv', !valid && !empty ? 'Invalid CVV' : '')
        })
      }

      if (instance.cardBrandRecognition) {
        instance.cardBrandRecognition((_, event) => {
          onCardBrandRecognized(event?.data?.cardBrand)
        })
      }

      onSuccess(instance)
    } catch (error) {
      console.log('error', error)
      if (error?.code === '9028' && retryCount < MAX_RETRIES) {
        retryCount++
        attemptSetup()
      } else {
        onFailure(error)
      }
    }
  }

  attemptSetup()
}
