import { useLocation } from "react-router-dom"

// Common PBB Success Handler
export const handlePbbSuccess = (res, setPaymentDepositTransactionId, setPaymentStatus, openPBBIframe) => {
  const transactionId = res?.data?.data?.depositTransactionId
  const redirectUrl = res?.data?.data?.redirectUrl
  const success = res?.data?.success ?? true // fallback true for pbbPayment that might not have `success`
  window.localStorage.setItem('depositTransactionId', transactionId)
  setPaymentDepositTransactionId({ paymentDepositTransactionId: transactionId })
  setPaymentStatus('isLoading', false)
  setPaymentStatus('paymentRedirection', true)
  if (success && redirectUrl) {
    openPBBIframe(redirectUrl)
  }
}

// Common PBB Error Handler
export const handlePbbError = (error, setPaymentErrorState, setPaymentStatus, setDepositTransactionId, portalStore, navigate) => {
  console.log('Payment Error', error)
  const location = useLocation()
  const isBadRequest = error?.response?.status === 400
  setPaymentErrorState({
    paymentError: error,
    paymentErrorMessage: 'Online Banking Payment Failed'
  })
  setPaymentStatus('isLoading', false)
  if (!isBadRequest) {
    setDepositTransactionId({ depositTransactionId: '' })
    portalStore.closePortal()
    navigate(`${location.pathname}?status=failed`)
  }
}
