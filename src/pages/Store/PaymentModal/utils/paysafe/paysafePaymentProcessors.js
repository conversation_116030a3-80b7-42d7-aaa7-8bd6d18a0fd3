// CARD
export const handleCardPayment = async ({
  demoInstance,
  cardHolderName,
  selectedSavedCard,
  paymentHandleToken,
  singleUseCustomerToken,
  transactionId,
  finalAmount,
  userDetails,
  stateName,
  setCardErrors,
  setPaymentStatus,
  handlePaymentCompletion,
  paysafeCheckError,
  setDepositTransactionId,
  setPaymentErrorState,
  portalStore,
  navigate,
  location
}) => {
  try {
    setCardErrors({})
    const amountInCents = Math.round((finalAmount || 0) * 100)

    const commonDetails = {
      amount: amountInCents,
      currencyCode: 'USD',
      transactionType: 'PAYMENT',
      paymentType: 'CARD',
      merchantRefNum: transactionId,
      customerDetails: {
        billingDetails: {
          country: 'US',
          zip: userDetails?.zipCode || '',
          street: userDetails?.addressLine_1 || '',
          city: userDetails?.city || '',
          state: stateName || ''
        },
        profile: {
          firstName: userDetails?.firstName || '',
          lastName: userDetails?.lastName || '',
          email: userDetails?.email || ''
        }
      },
      threeDs: {
        merchantUrl: window.location.href,
        deviceChannel: 'BROWSER',
        messageCategory: 'PAYMENT',
        transactionIntent: 'GOODS_OR_SERVICE_PURCHASE',
        authenticationPurpose: 'PAYMENT_TRANSACTION'
      }
    }

    const tokenizationOptions = {
      ...commonDetails,
      customerDetails: {
        ...commonDetails.customerDetails,
        holderName: cardHolderName || ''
      }
    }

    const savedCardOptions = {
      ...tokenizationOptions,
      singleUseCustomerToken,
      paymentTokenFrom: paymentHandleToken
    }

    console.log('$$$TOKENIZE', tokenizationOptions)

    let result
    if (selectedSavedCard && paymentHandleToken) {
      result = await demoInstance.tokenize(savedCardOptions)
    } else {
      const { cardNumber, expiryDate, cvv } = demoInstance.fields

      if (!cardHolderName?.trim()) {
        setCardErrors('cardHolderName', 'Card Holder Name is empty')
        return setPaymentStatus('isLoading', false)
      }

      if (!/^[A-Za-z]+( [A-Za-z]+)*$/.test(cardHolderName)) {
        setCardErrors('cardHolderName', 'Only letters and single spaces allowed')
        return setPaymentStatus('isLoading', false)
      }

      if (cardNumber.isEmpty()) {
        setCardErrors('cardNumber', 'Card Number is empty')
        return setPaymentStatus('isLoading', false)
      }

      if (expiryDate.isEmpty()) {
        setCardErrors('expiryDate', 'Expiry Date is empty')
        return setPaymentStatus('isLoading', false)
      }

      if (cvv.isEmpty()) {
        setCardErrors('cvv', 'CVV is empty')
        return setPaymentStatus('isLoading', false)
      }

      const isValid = await demoInstance.areAllFieldsValid()
      if (!isValid) {
        setCardErrors('cardNumber', 'Invalid card details')
        return setPaymentStatus('isLoading', false)
      }

      result = await demoInstance.tokenize(tokenizationOptions)
    }

    handlePaymentCompletion({ paymentMethod: 'CARD', token: result.token })
  } catch (error) {
    console.log('$$$$$$ERROR_PAYSAFE', error)
    paysafeCheckError.mutate({
      detailedMessage: error?.detailedMessage,
      paysafeResponse: error,
      depositTransactionId: window.localStorage.getItem('depositTransactionId'),
      transactionId
    })

    setDepositTransactionId({ depositTransactionId: '' })
    setPaymentErrorState({
      paymentError: error,
      paymentErrorMessage: 'Card Payment Failed'
    })

    portalStore.closePortal()
    navigate(`${location.pathname}?status=failed`)
  }
}

// APPLE PAY
export const handleApplePayPayment = async (
  demoInstance,
  transactionId,
  finalAmount,
  userDetails,
  stateName,
  setPaymentStatus,
  handlePaymentCompletion,
  paysafeCheckError,
  setDepositTransactionId,
  setPaymentErrorState,
  portalStore,
  navigate,
  location,
  toast
) => {
  try {
    const result = await demoInstance.tokenize({
      amount: +Math.round(finalAmount * 100),
      transactionType: 'PAYMENT',
      paymentType: 'APPLEPAY',
      applePay: { country: 'US' },
      customerDetails: {
        billingDetails: {
          country: 'US',
          zip: userDetails?.zipCode || '',
          street: userDetails?.addressLine_1 || '',
          city: userDetails?.city || '',
          state: stateName || ''
        }
      },
      merchantRefNum: transactionId
    })
    demoInstance.complete('success')
    if (result.token) {
      handlePaymentCompletion({ paymentMethod: 'APPLE_PAY', token: result.token })
    }
  } catch (error) {
    paysafeCheckError.mutate({
      detailedMessage: error?.detailedMessage,
      paysafeResponse: error,
      depositTransactionId: window.localStorage.getItem('depositTransactionId'),
      transactionId: transactionId
    })
    if (error?.code === '9086' || error?.code === '9001' || error?.code === '9002' || error?.code === '9152') {
      toast.error(error?.displayMessage)
      setPaymentStatus('isLoading', false)
      return
    }
    demoInstance.complete('fail')
    setPaymentStatus('isLoading', false)
    setDepositTransactionId({ depositTransactionId: '' })
    setPaymentErrorState({
      paymentError: error,
      paymentErrorMessage: 'displayMessage'
    })
    portalStore.closePortal()
    navigate(`${location.pathname}?status=failed`)
  }
}

// SKRILL
export const handleSkrillPayment = async (
  transactionId,
  location,
  portalStore,
  navigate,
  skrillPayment,
  setPaymentDepositTransactionId,
  setPaymentStatus,
  setPaymentErrorState,
  openSkrillModal
) => {
  if (!transactionId) {
    portalStore.closePortal()
    navigate(`${location.pathname}?status=failed`)
    console.error('Transaction ID is required for Skrill payment')
    return
  }

  try {
    const res = await skrillPayment.mutateAsync({
      transactionId,
      redirectUrlPath: location.pathname
    })

    if (res?.data?.success) {
      localStorage.setItem('depositTransactionId', res?.data.data.depositTransactionId)
      setPaymentDepositTransactionId({
        paymentDepositTransactionId: res?.data.data.depositTransactionId
      })
      setPaymentStatus('isLoading', false)
      setPaymentStatus('paymentRedirection', true)
      openSkrillModal(res.data.data.redirectUrl)
    }
  } catch (error) {
    console.error('Skrill Payment Error', error)
    setPaymentDepositTransactionId({ paymentDepositTransactionId: '' })
    setPaymentStatus('isLoading', false)
    setPaymentErrorState({
      paymentError: error,
      paymentErrorMessage: 'Internal Server Error'
    })
    navigate(`${location.pathname}?status=failed`)
  }
}

// PAY BY BANK
export const handlePbbPayment = async (
  transactionId,
  redirectUrlPath,
  paymentHandleToken,
  savedPbbPayment,
  pbbPayment
) => {
  const payload = { transactionId: transactionId, redirectUrlPath: redirectUrlPath }
  if (paymentHandleToken !== '') {
    payload.paymentHandleToken = paymentHandleToken
    await savedPbbPayment.mutate(payload)
  } else {
    await pbbPayment.mutate(payload)
  }
}
