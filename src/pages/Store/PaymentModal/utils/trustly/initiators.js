import toast from 'react-hot-toast'

export const initiateTrustlyScript = ({ callback = () => {}, failure = () => {} }) => {
  const ele = document.getElementById('trustly-script')
  const trustlyAccessId = import.meta.env.VITE_TRUSTLY_ACCESS_ID
  // const trustlyMerchantId = import.meta.env.VITE_TRUSTLY_MERCHANT_ID

  if (ele) {
    callback()
  } else {
    const script = document.createElement('script')
    script.src = `${import.meta.env.VITE_TRUSTLY_URL}/start/scripts/trustly.js?accessId=${trustlyAccessId}`
    script.async = true
    script.defer = true
    script.id = 'trustly-script'
    document.head.appendChild(script)
    script.addEventListener('load', () => {
      callback()
    })
    script.addEventListener('error', (err) => {
      console.log('Trustly script failed to load', err)
      toast.error('Trustly Connection Failed !, Please try again.')
      failure()
    })
  }
}
