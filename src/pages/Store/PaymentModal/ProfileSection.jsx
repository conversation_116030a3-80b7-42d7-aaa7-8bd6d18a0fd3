import React, { useEffect, useRef, useState } from 'react'
import useStyles from './Accounts.styles'
import '../../../../src/App.css'
import {
  Button,
  Box,
  Grid,
  Typography,
  CircularProgress,
  InputAdornment,
  IconButton,
  OutlinedInput
} from '@mui/material'
import usePersonalInfo from './hooks/useProfile'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import { useUserStore } from '../../../store/useUserSlice'
import CustomDatePicker from '../../../components/CustomDatePicker'

const ProfileSection = ({ handleClose, redeemCalled }) => {
  const classes = useStyles()
  const autocompleteInput = useRef(null)
  const user = useUserStore((state) => state)
  const {
    handleSubmit,
    register,
    isSubmitFormLoading,
    errors,
    onStateChangeHand<PERSON>,
    selectedState,
    reset,
    stateList: stateData,
    watch,
    handleOnFormSubmit,
    handelReset,
    setValue,
    setSelectedState, selectedDate, setSelectedDate
  } = usePersonalInfo(handleClose)
  const [formError, setFormError] = useState('');
  const firstNameValue = watch('firstName')
  const lastNameValue = watch('lastName')
  const zipCodeValue = watch('zipCode')
  const address_line1_Value = watch('addressLine_1')
  const address_line2_Value = watch('addressLine_2')
  const cityValue = watch('city')
  const stateValue = watch('state')

  const isFieldDisabled = () => {
    return false
  }

  let autoComplete;
  let address1Field;
  let postalField;
  useEffect(() => {
    try {
      if (window.google && window.google.maps) {
        address1Field = document.querySelector('#ship-address')
        postalField = document.querySelector("#postcode");
        autoComplete = new window.google.maps.places.Autocomplete(address1Field, {
          componentRestrictions: { country: ['us'] },
          fields: ['address_components', 'geometry'],
          types: ['address']
        })
        autoComplete.addListener('place_changed', fillInAddress)
      }
    } catch (error) {
      console.error("GOOGLE MAPS ERROR in initializeGoogleMapsAPI:", error);
    }
  }, [])

  const handleDateValidation = (isValid, message, date) => {
    setValue('dateOfBirth', date)
    setSelectedDate(date)
    if (!isValid) {
      setFormError("You must be at least 18 years old."); // Set error if the selected date is not valid
    } else {
      setFormError(''); // Clear error if the selected date is valid
    }
  };

  const fillInAddress = () => {
    const place = autoComplete.getPlace()
    let address1 = ''
    let postcode = ''
    let city = ''
    let stateCode = ''
    for (const component of place.address_components) {
      const componentType = component.types[0];

      switch (componentType) {
        case "street_number": {
          address1 = `${component.long_name} ${address1}`;
          break;
        }

        case "route": {
          address1 += component.short_name;
          break;
        }

        case "postal_code": {
          postcode = `${component.long_name}${postcode}`;
          break;
        }

        case "postal_code_suffix": {
          postcode = `${postcode}-${component.long_name}`;
          break;
        }
        case "locality":
          city = component.long_name
          document.querySelector("#locality").value = component.long_name;
          break;
        case "administrative_area_level_1": {
          stateCode = component.short_name
          document.querySelector("#state").value = component.short_name;
          break;
        }
      }
    }
    address1Field.value = address1;
    postalField.value = postcode;
    setValue('addressLine_1', address1, { shouldValidate: true })
    setValue('city', city, { shouldValidate: true })
    setValue('state', stateCode, { shouldValidate: true })
    setValue('zipCode', postcode, { shouldValidate: true })
    setSelectedState(stateCode)
  }
  const handleFormSubmit = (formData) => {
    if (!selectedDate) {
      setFormError('Date of birth is required');
      return;
    }

    handleOnFormSubmit(formData, selectedDate);
  };
  return (
    < >
      {!redeemCalled &&
        <Grid display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
          <Typography variant='heading'>Profile</Typography>
          <Grid className='modal-close'>
            <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
              <CloseIcon />
            </IconButton>
          </Grid>
        </Grid>
      }

      <form
        onSubmit={handleSubmit(handleFormSubmit)}
        className={classes.profileModal}
        id='address-form'
        action=''
        method='get'
        autocomplete='abc'
      >
        <Box className='profile'>
          <Grid className='profileGrid'>
            <Grid>
              <OutlinedInput
                id='outlined-basic'
                disabled={isFieldDisabled('firstName')}
                label=''
                className='inputSelect'
                style={{ width: '100%' }}
                variant='outlined'
                placeholder='First Name *'
                {...register('firstName')}
                endAdornment={
                  <InputAdornment position='end'>
                    <IconButton
                      style={{ right: '20px', position: 'absolute' }}
                      aria-label='First Name visibility'
                      edge='end'
                    >
                      {errors?.firstName?.message && firstNameValue?.length >= 0 ? (
                        <CloseIcon style={{ color: 'red' }} />
                      ) : (
                        ''
                      )}
                      {!errors?.firstName?.message && firstNameValue?.length > 0 ? (
                        <CheckIcon style={{ color: 'green' }} />
                      ) : (
                        ''
                      )}
                    </IconButton>
                  </InputAdornment>
                }
              />

              {errors?.firstName && <p className={classes.inputError}> {errors?.firstName?.message}</p>}
            </Grid>

            <Grid>
              <OutlinedInput
                id='outlined-basic'
                label=''
                className='w-100'
                style={{ width: '100%' }}
                variant='outlined'
                placeholder='Last Name *'
                disabled={isFieldDisabled('lastName')}
                {...register('lastName')}
                endAdornment={
                  <InputAdornment position='end'>
                    <IconButton
                      style={{ right: '20px', position: 'absolute' }}
                      aria-label='last Name visibility'
                      edge='end'
                    >
                      {errors?.lastName?.message && lastNameValue?.length >= 0 ? (
                        <CloseIcon style={{ color: 'red' }} />
                      ) : (
                        ''
                      )}
                      {!errors?.lastName?.message && lastNameValue?.length > 0 ? (
                        <CheckIcon style={{ color: 'green' }} />
                      ) : (
                        ''
                      )}
                    </IconButton>
                  </InputAdornment>
                }
              />
              {errors?.lastName && <p className={classes.inputError}>{errors?.lastName?.message} </p>}
            </Grid>

            <Grid >
              < CustomDatePicker
                setDate={user?.dateOfBirth}
                onDateValidation={handleDateValidation} />
              {formError !== '' && <p className={classes.inputError}> {formError}</p>}
              {errors?.dateOfBirth && <p className={classes.inputError}> {errors?.dateOfBirth?.message}</p>}
            </Grid>

            <Grid>
              <input
                id='ship-address'
                label=''
                {...register('addressLine_1')}
                className='inputSelect'
                style={{ width: '100%' }}
                placeholder='Address Line 1 *'
                // ref={autocompleteInput}
                // autocomplete='off'
                disabled={isFieldDisabled('addressLine_1')}
              />
              {errors?.addressLine_1 && <p className={classes.inputError}> {errors?.addressLine_1?.message}</p>}
            </Grid>

            <Grid>
              <OutlinedInput
                id='address2'
                className='inputSelect'
                style={{ width: '100%' }}
                label=''
                variant='outlined'
                placeholder='Apartment, unit, suite, or floor #'
                disabled={isFieldDisabled('addressLine_2')}
                {...register('addressLine_2')}
                endAdornment={
                  <InputAdornment position='end'>
                    <IconButton
                      style={{ right: '20px', position: 'absolute' }}
                      aria-label='last Name visibility'
                      edge='end'
                    >
                      {errors?.addressLine_2?.message && address_line2_Value?.length >= 0 ? (
                        <CloseIcon style={{ color: 'red' }} />
                      ) : (
                        ''
                      )}
                      {!errors?.addressLine_2?.message && address_line2_Value?.length > 0 ? (
                        <CheckIcon style={{ color: 'green' }} />
                      ) : (
                        ''
                      )}
                    </IconButton>
                  </InputAdornment>
                }
              />
              {errors?.addressLine_2 && <p className={classes.inputError}> {errors?.addressLine_2?.message}</p>}
            </Grid>

            <Grid>
              <OutlinedInput
                id='locality'
                className='inputSelect'
                label=''
                variant='outlined'
                placeholder='City *'
                disabled={isFieldDisabled('city')}
                style={{ width: '100%' }}
                {...register('city')}
                endAdornment={
                  <InputAdornment position='end'>
                    <IconButton
                      style={{ right: '20px', position: 'absolute' }}
                      aria-label='last Name visibility'
                      edge='end'
                    >
                      {errors?.city?.message && cityValue?.length >= 0 ? <CloseIcon style={{ color: 'red' }} /> : ''}
                      {!errors?.city?.message && cityValue?.length > 0 ? <CheckIcon style={{ color: 'green' }} /> : ''}
                    </IconButton>
                  </InputAdornment>
                }
              />
              {errors?.city && <p className={classes.inputError}>{errors?.city?.message}</p>}
            </Grid>

            <Grid>
              {/* <input
              id='state'
              className='inputSelect uppercaseInput'
              label=''
              variant='outlined'
              placeholder='state *'
              disabled={isFieldDisabled('state')}
              {...register('state')}
            />
            {errors?.state && <p className={classes.inputError}>{errors?.state?.message}</p>} */}
              <select
                id='state'
                value={selectedState}
                className='inputSelect'
                disabled={isFieldDisabled('state')}
                label='State'
                {...register('state')}
                onChange={onStateChangeHandler}
              >
                <option key={'State'} value='' defaultValue>
                  Select a State *
                </option>

                {stateData?.length > 0 &&
                  stateData?.map((state) => (
                    <option key={state.state_id} value={state.stateCode}>
                      {state.name}
                    </option>
                  ))}
              </select>
              {selectedState === '' && <p className={classes.inputError}>{errors?.state?.message}</p>}
            </Grid>
            <Grid>
              <OutlinedInput
                id='postcode'
                type='text'
                className='inputSelect'
                style={{ width: '100%' }}
                label=''
                variant='outlined'
                placeholder='Postal code *'
                disabled={isFieldDisabled('zipCode')}
                {...register('zipCode')}
                endAdornment={
                  <InputAdornment position='end'>
                    <IconButton
                      style={{ right: '20px', position: 'absolute' }}
                      aria-label='last Name visibility'
                      edge='end'
                    >
                      {errors?.zipCode?.message && zipCodeValue?.length >= 0 ? (
                        <CloseIcon style={{ color: 'red' }} />
                      ) : (
                        ''
                      )}
                      {!errors?.zipCode?.message && zipCodeValue?.length > 0 ? (
                        <CheckIcon style={{ color: 'green' }} />
                      ) : (
                        ''
                      )}
                    </IconButton>
                  </InputAdornment>
                }
              />
              {errors?.zipCode && <p className={classes.inputError}>{errors?.zipCode?.message}</p>}
            </Grid>
          </Grid>
          <Grid className={`${classes.profileBottom} profileBottom`}>
            <Button variant='contained' className='btn btn-primary' type='submit' disabled={isSubmitFormLoading}>
              {isSubmitFormLoading ? (
                <CircularProgress size={24} style={{ marginRight: 8 }} />
              ) : (
                <span className='btn-span'>Save</span>
              )}
            </Button>

            <Button variant='contained' className='btn btn-secondary' onClick={() => handelReset()}>
              <span className='btn-span'>Reset</span>
            </Button>
          </Grid>
        </Box>
      </form>
    </>
  )
}

export default ProfileSection
