import { makeStyles } from '@mui/styles'

import { Container, ButtonSecondary, ButtonPrimary, LobbyRight } from '../../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme)
  },
  profileTabsDesign: {
    '& .headingLogout': {
      display: 'flex',
      marginTop: '65px',
      justifyContent: 'space-between',
      alignItems: 'center',
      [theme.breakpoints.down('md')]: {
        marginTop: '20px'
      }
    },
    '& h2': {
      fontSize: '25px',
      fontWeight: '500',
      [theme.breakpoints.down('md')]: {
        width: '100%',
        marginLeft: '0'
      }
    },

    '& .MuiTabs-root': {
      width: 'fit-content',
      [theme.breakpoints.down('md')]: {
        width: '100%'
      },
      '& .MuiTabs-scroller': {
        maxWidth: '100%',
        display: 'flex',
        whiteSpace: 'nowrap',
        flexWrap: 'nowrap',
        overflowX: 'auto',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        padding: theme.spacing(0.31),
        margin: '38px 0 68px 0 !important',
        [theme.breakpoints.down('md')]: {
          margin: '20px 0 20px 0 !important'
        },
        '& .MuiTabs-indicator': {
          borderRadius: theme.spacing(4.1875),
          background: theme.colors.YellowishOrange,
          fontWeight: theme.typography.fontWeightExtraBold,
          color: theme.colors.textBlack,
          height: 'calc(100% - 0.62rem)',
          top: '0.31rem'
        },

        '& .MuiTab-iconWrapper': {
          margin: '0'
        },

        '& button': {
          color: theme.colors.textWhite,
          position: 'relative',
          marginRight: theme.spacing(0.5),
          fontWeight: theme.typography.fontWeightExtraBold,
          cursor: 'pointer',
          fontSize: theme.spacing(1),
          transition: 'none',
          transform: 'none',
          minWidth: 'auto',
          zIndex: '1',
          minHeight: 'auto',
          padding: '13px 20px',

          '&:hover .MuiTab-iconWrapper': {
            '& .image2': {
              display: 'block'
            },
            '& .image1': {
              display: 'none'
            }
          },
          '& .MuiTab-iconWrapper': {
            '& .image2': {
              display: 'none'
            },
            '& .image1': {
              display: 'block'
            }
          },
          '&.Mui-selected': {
            color: theme.colors.textBlack,
            '& .MuiTab-iconWrapper': {
              '& .image1': {
                display: 'none'
              },
              '& .image2': {
                display: 'block !important'
              }
            }
          },

          '&:hover': {
            borderRadius: theme.spacing(4.1875),
            background: theme.colors.YellowishOrange,
            transition: 'none',
            transform: 'none',
            color: theme.colors.textBlack,
            border: '0'
          },
          '& img': {
            marginRight: theme.spacing(0.62),
            width: '25px',
            aspectRatio: '1'
          }
        }
      }
    },

    '& .profile-section-detail': {
      '& .profile': {
        display: 'flex',
        gap: '30px',
        flexWrap: 'wrap'
      },
      '& .MuiSelect-select, & .MuiTextField-root': {
        width: '100%',
        border: `1px solid ${theme.colors.inputBorder}`,
        borderRadius: '0.25rem',
        height: '43px',
        '&:focus': {
          border: `1px solid ${theme.colors.YellowishOrange}`
        }
      },
      '& .inputSelect': {
        width: '-webkit-fill-available',
        border: `1px solid ${theme.colors.inputBorder}`,
        borderRadius: '0.25rem',
        color: `${theme.colors.textWhite}!important`,
        padding: '12px 14px',
        backgroundColor: 'transparent',
        position: 'relative',
        '&:focus': {
          border: `1px solid ${theme.colors.YellowishOrange}`
        },
        '& option': {
          color: theme.colors.textBlack
        },
        '&:focus-visible': {
          outline: 'none'
        },
        '&:disabled': {
          color: 'rgba(255, 255, 255, 0.6) !important',
          WebkitTextFillColor: theme.colors.textWhite
        },
        /* Add the following lines for input[type=number] styling */
        '&::-webkit-inner-spin-button, &::-webkit-outer-spin-button': {
          '-webkit-appearance': 'none',
          margin: 0
        },

        '& #spin': {
          display: 'none'
        }
      },

      '& .datePicker': {
        '& .MuiStack-root': {
          padding: '0 !important',
          width: '100% !important'
          // height: "43px !important",
        },
        '& .MuiInputBase-root': {
          padding: '0 !important',
          height: '100% !important',
          '& input': {
            padding: '0 0 0 15px !important',
            height: '100% !important',
            color: theme.colors.textWhite,
            '&:disabled': {
              WebkitTextFillColor: theme.colors.textWhite
            }
          }
        },
        '& .MuiInputAdornment-positionEnd': {
          marginLeft: '0 !important',
          '& button': {
            margin: '0 !important',
            '& svg': {
              color: theme.colors.textWhite
            }
          },
          position: 'absolute',
          right: '10px'
        },

        '& .Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            border: '0',
            borderColor: 'transparent'
          }
        }
      }
    },

    '& .saveBtn': {
      ...ButtonPrimary(theme),
      '&:hover': {
        ...ButtonPrimary(theme)
      }
    },

    '& .cancelBtn': {
      ...ButtonSecondary(theme),
      '&:hover': {
        ...ButtonSecondary(theme)
      }
    },

    '& .gridHeading': {
      marginBottom: '30px',
      '& h3': {
        fontSize: '25px',
        marginBottom: '15px',
        fontWeight: '500'
      }
    }
  },
  couponBox: {
    width: '450px',
    [theme.breakpoints.down('sm')]: {
      width: 'auto'
    },
    '& .header-wrap': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingBottom: '0.75rem',
      color: '#fff',
      '& .MuiTypography-root': {
        color: theme.colors.YellowishOrange,
        fontSize: '1.5rem',
        fontWeight: '700',
        textTransform: 'uppercase'
      }
    }
  },
  inProgress: {
    '& .payment-accordion': {
      width: '100% !important',
      background: 'transparent',
      margin: '0 !important',
      '& .payment-accordion-summary': {
        border: '1px solid #EAB647',
        borderRadius: '10px',
        padding: '0',
        maxHeight: '40px',
        minHeight: '40px',
        background: 'linear-gradient(105.41deg, #2C2C2C 29.35%, #404040 88.89%)',
        '&.Mui-expanded': {
          borderBottomLeftRadius: '0',
          borderBottomRightRadius: '0',
          borderBottom: 'none'
        },
        '& .MuiAccordionSummary-content': {
          '&.Mui-expanded': {
            margin: '8px 0',
            maxHeight: '40px'
          }
        },

        '& .MuiAccordionSummary-expandIconWrapper': {
          '& svg': {
            marginRight: '7px'
          },
          '&.Mui-expanded': {
            '& svg': {
              marginLeft: '7px'
            }
          }
        }
      },
      '& .payment-accordion-summary-title': {
        paddingLeft: '12px',
        color: 'white !important',
        fontWeight: '600'
      },
      '& .payment-accordion-details': {
        padding: '0.75rem',
        border: '1px solid #EAB647',
        borderBottomLeftRadius: '10px',
        borderBottomRightRadius: '10px',
        background: 'linear-gradient(105.41deg, #2C2C2C 29.35%, #404040 88.89%)',
        '& .dot-line': {
          color: '#51504F',
          fontWeight: 700,
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        }
      },
      '& .payment-accordion-detail-item': {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        '& .payment-accordion-detail-item-title': {
          color: 'white !important',
          fontWeight: '500',
          width: '100px'
        },
        '& .payment-accordion-detail-item-content': {
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-end',
          color: '#fff',
          fontWeight: '500',
          '& p': {
            color: '#fff',
            fontWeight: '500'
          }
        },
        '& .payment-accordion-detail-item-img': {
          height: '20px',
          marginRight: '10px'
        },
        '&.total-amount': {
          paddingTop: '0.75rem',
          marginTop: '0.5rem',
          color: '#fff',
          borderTop: '1px dashed #51504F'
        }
      }
    },
    '& .failed-transactions-details': {
      border: '1px solid #fdb72e',
      margin: '1rem 0',
      borderRadius: '9px',
      '& .inprogress-head': {
        padding: '0.25rem 0.75rem',
        borderBottom: '1px solid #fdb72e',
        '& .MuiTypography-root': {
          color: '#fff !important',
          fontWeight: '600'
        }
      },
      '& p': {
        color: '#fdb72e !important',
        textAlign: 'start',
        fontWeight: '500'
      },
      '& span': {
        color: '#fff',
        // marginLeft: '0.5rem',
        marginLeft: 'auto',
        fontWeight: '500'
      },
      '& .detail-box': {
        display: 'flex',
        alignItems: 'center',
        padding: '2px 10px',
        justifyContent: 'space-between',
        '& img': {
          width: '20px',
          marginRight: '6px',
          position: 'relative',
          top: '2px'
        }
      }
    },
    '& .payment-button-container': {
      display: 'flex',
      marginBottom: '10px',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '1rem',
      '& button': {
        fontSize: '1rem',
        marginTop: '0.5rem',
        minHeight: '2rem',
        maxWidth: '7rem',
        padding: '0.25rem 1rem',
        width: '100%'
      }
    },
    '& .deposit-failed': {
      fontSize: '1.625rem',
      textAlign: 'center',
      lineHeight: '1.2',
      textTransform: 'uppercase',
      fontWeight: '600',
      color: theme.colors.YellowishOrange,
      [theme.breakpoints.down('md')]: {
        fontSize: '1.25rem'
      }
    },
    '& .payment-head-text': {
      color: theme.colors.textWhite,
      fontWeight: '600',
      textAlign: 'center',
      lineHeight: '1.2',
      marginBottom: '1.5rem',

      '&.limit-text': {
        maxWidth: '350px',
        margin: '0.5rem auto'
      }
    },
    '& .limit-reached': {
      // maxWidth: '420px',
      width: '100%',
      margin: '0 auto'
    }
  },

  container: {
    ...Container(theme),
    [theme.breakpoints.down('md')]: {
      marginBottom: '20px'
    }
  },

  userImgWrap: {
    position: 'relative'
  },

  userImg: {
    width: '120px',
    height: '120px',
    [theme.breakpoints.down('sm')]: {
      width: '80px',
      height: '80px'
    },
    '& img': {
      width: '100%',
      borderRadius: '50%'
    }
  },

  uploadImgBtn: {
    top: '0',
    right: '0',
    position: 'absolute !important',
    borderRadius: '50% !important',
    minHeight: '30px !important',
    minWidth: '30px !important',
    width: '30px !important',
    background: '#FDB72E !important',
    '& img': {
      width: '13px'
    }
  },
  errorLabel: {
    color: `${theme.colors.error} !important`,
    fontSize: `${theme.spacing(0.8)}!important`,
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600 !important',
    textAlign: 'left'
  },

  changePWD: {
    '& .MuiOutlinedInput-root': {
      padding: '0',
      height: '43px'
    },
    '& input': {
      padding: '0',
      color: theme.colors.textWhite,
      height: '100%',
      paddingLeft: '15px',
      width: '-webkit-fill-available',
      border: `1px solid ${theme.colors.inputBorder}`,
      borderRadius: theme.spacing(0.25),
      '&:focus': {
        border: `1px solid ${theme.colors.YellowishOrange}`
      }
    },
    '& .MuiInputAdornment-positionEnd': {
      marginLeft: '-60px',
      '& svg': {
        color: theme.colors.textWhite
      }
    },
    '& fieldset': {
      border: 'none'
    }
  },

  '& .Mui-focused': {
    '& .MuiOutlinedInput-notchedOutline': {
      borderWidth: '0px',
      borderColor: 'transparent !important'
    }
  },

  inputError: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    margin: '0 !important',
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600'
  },
  inputDetail: {
    [theme.breakpoints.down('xs')]: {
      flexDirection: 'column !important'
    }
  },
  profileBottom: {
    display: 'flex',
    gap: '15px',
    marginTop: '38px',
    [theme.breakpoints.down('md')]: {
      marginTop: '0px',
      marginBottom: '0'
    }
  },

  userKyc: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
    [theme.breakpoints.down('lg')]: {
      flexDirection: 'column !important',
      gap: '20px'
    }
  },

  kycAndRefferal: {
    display: 'flex',
    gap: '20px',
    width: '100%',
    '& .MuiTouchRipple-root': {
      display: 'none'
    },
    '& .variant1,  & .variant2': {
      color: '#293937',
      position: 'relative',
      fontSize: '1rem',
      textAlign: 'center',
      border: '2px solid #293937',
      padding: theme.spacing(0.8, 5),
      borderRadius: '50px',
      fontWeight: 'bold',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 2.5)
      },
      '&:hover': {
        color: '#293937',
        position: 'relative',
        fontSize: '1rem',
        textAlign: 'center',
        border: '2px solid #293937',
        padding: theme.spacing(0.8, 5),
        borderRadius: '50px',
        fontWeight: 'bold'
      },
      '& span': {
        left: '0',
        color: theme.colors.textWhite,
        right: '0',
        bottom: '-18px',
        position: 'absolute',
        fontSize: '0.8rem',
        fontWeight: 500,
        backgroundColor: '#1B181E',
        borderRadius: '10px',
        width: 'fit-content',
        padding: '5px 14px',
        margin: '0 auto'
      }
    },
    '& .variant2': {
      border: '2px solid transparent',
      backgroundColor: '#FDB72E',
      color: '#05051C',
      '&:hover': {
        border: '2px solid transparent !important',
        backgroundColor: '#FDB72E !important',
        color: '#05051C'
      }
    },
    '& .variant3': {
      fontSize: '1rem',
      textAlign: 'center',
      border: 'none',
      backgroundColor: '#FDB72E',
      color: '#05051C',
      padding: theme.spacing(0.8, 2),
      borderRadius: '50px',
      fontWeight: 'bold',
      display: 'flex',
      gap: '10px',
      whiteSpace: 'nowrap',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 1)
      },
      '&:hover': {
        fontSize: '1rem',
        textAlign: 'center',
        border: 'none',
        backgroundColor: '#FDB72E',
        color: '#05051C',
        padding: '0.8rem 2rem',
        borderRadius: '50px',
        fontWeight: 'bold',
        display: 'flex',
        gap: '10px',
        whiteSpace: 'nowrap'
      }
    },
    '& .referalBtn': {
      fontSize: '1rem',
      textAlign: 'center',
      border: `2px solid ${theme.colors.textWhite}`,
      color: theme.colors.textWhite,
      padding: theme.spacing(0.8, 2),
      borderRadius: '50px',
      fontWeight: 'bold',
      display: 'flex',
      gap: '10px',
      whiteSpace: 'nowrap',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 1)
      }
    }
  },

  userDetailsRight: {
    width: '100%',
    '& h4': {
      fontWeight: '900',
      [theme.breakpoints.down('md')]: {
        fontSize: `${theme.spacing(1.5)}!important`
      }
    },
    '& p': {
      [theme.breakpoints.down('md')]: {
        fontSize: `${theme.spacing(0.8)}!important`
      }
    }
  },

  profileModal: {
    '& input, & select': {
      color: '#fff !important',
      width: '-webkit-fill-available',
      border: '1px solid #293937',
      padding: '0 14px',
      borderRadius: '0.625rem',
      backgroundColor: 'transparent',
      height: '41px',
      '&:focus': {
        border: `1px solid ${theme.colors.YellowishOrange}`
      },
      '&:focus-visible': {
        outline: 'none'
      },
      '& option': {
        color: '#000 !important'
      }
    },
    // "& .MuiInputAdornment-root": {
    //   display: "none",
    // },
    '& .MuiOutlinedInput-root': {
      padding: '0'
    },
    '& .profileGrid': {
      gridGap: '18px 12px',
      alignItems: 'stretch',
      display: 'grid',
      gap: '18px 12px',
      // gridTemplateColumns: "repeat(auto-fill, minmax(45%, 1fr))",
      marginTop: '10px',
      '& fieldset': {
        display: 'none'
      },
      '& .MuiInputAdornment-root': {
        position: 'absolute',
        right: '0'
      }
    },
    '& .profileBottom': {
      justifyContent: 'center',
      margin: theme.spacing(1, 0)
    },
    '& .MuiTouchRipple-root': {
      display: 'none'
    }
  },
  lobbySearchWrap: {
    position: 'relative',
    minWidth: theme.spacing(18.75),
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1)
    },
    '& .inputSelect': {
      borderRadius: '10rem !important',
      paddingLeft: '50px'
    },
    '& .MuiTextField-root': {
      width: '100%',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        '&::placeholder': {
          color: 'red'
        },
        '&::-ms-input-placeholder': {
          color: 'red'
        },
        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(2.18)
        }
      }
    }
  }
}))
