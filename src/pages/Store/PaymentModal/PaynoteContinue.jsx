import React, { useEffect } from 'react'
import '../../../../src/App.css'
import { Grid, IconButton, Typography } from '@mui/material'
import DialogContent from '@mui/material/DialogContent'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../../store/userPortalSlice'
import { generateRandomToken } from '../../../utils/helpers'
import { useUserStore } from '../../../store/useUserSlice'

const PaynoteContinue = ({ finalAmount, packageDetails }) => {
  const portalStore = usePortalStore((state) => state)
  const userDetails = useUserStore((state) => state.userDetails)

  const handleClose = () => {
    portalStore.closePortal()
  }

  const loadScript = () => {
    const payment_token = generateRandomToken(10)
    var objRequestRedirect = {
      publicKey: import.meta.env.PAYNOTE_PUBLICKEY,
      paymentToken: `${payment_token}`,
      sandbox: true,
      widgetContainerSelector: 'btn-wrap-fiat',
      displayMethod: 'redirect',
      endpoint: null,
      storeName: 'theMoneyFactory',
      saveBankDetails: false, t,
      style: {
        buttonClass: 'btn pay-with-fiat-btn',
        buttonLabel: 'Continue Payment',
        border: 'none'
      },
      checkout: {
        totalValue: `${finalAmount}`,
        currency: 'USD',
        description: `Play Payment - packageId: ${packageDetails?.packageId}`,
        items: [
          {
            title: 'The Money Factory Plan',
            price: `${finalAmount}`
          }
        ],
        customerEmail: `${userDetails?.email}`,
        customerFirstName: `${userDetails?.firstName}`,
        customerLastName: `${userDetails?.lastName}`
      },
      lightBox: {
        title: 'Buy Plan',
        subtitle: 'Pay with Paynote',
        logoUrl: 'https://foxx-social-prod-assets.s3.amazonaws.com/logo.svg',
        formButtonLabel: 'PAY',
        show_cart_summary: false
      },
      onSuccess: null,
      onExit: null,
      onError: null,
      onCancel: null
    }

    var paynoteRedirect = new PAYNOTE(objRequestRedirect)
    paynoteRedirect.render()
  }

  useEffect(() => {
    loadScript()
  }, [])


  return (

    <>
      <Grid className='inner-modal-header'>
        <Typography variant='h4'>Payment</Typography>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
      <DialogContent>
        <Grid className='kyc-content'>
          <Typography variant='h4'>Complete Your Payment</Typography>
          <Typography>
            You will be redirected to a URL for processing payment. Please click on continue to proceed.
          </Typography>
          <Grid className='btn-wrap-fiat'>
          </Grid>
        </Grid>
      </DialogContent>
    </>
  )
}

export default PaynoteContinue
