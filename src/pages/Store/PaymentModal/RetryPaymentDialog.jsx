import { Dialog, Grid, Typography, Button } from '@mui/material'
import usePaymentStyles from './payment.style'
import { useState } from 'react'

const RetryPaymentDialog = ({ emitDepositObj, onRetry, onCancel }) => {
  const paymentClasses = usePaymentStyles()
  const [isRetry, setIsRetry] = useState(false)

  return (
    <Dialog open>
      {isRetry
        ? <div id='widget-id' />
        : (
          <Grid className={paymentClasses.paymentStyleModal}>
            <Grid className='payment-coupon-main'>
              <Grid className='payment-btn-container'>
                <Typography className='payment-coupon-text'>{emitDepositObj?.message}</Typography>
                <Grid className='failed-transactions-details'>
                  <Typography className='payment-coupon-text'>Amount: <span>$ {emitDepositObj?.amount}</span></Typography>
                  <Typography className='payment-coupon-text'>SC Coin: <span>{emitDepositObj?.scCoin} SC {emitDepositObj?.bonusSc && ` + ${emitDepositObj?.bonusSc} Bonus`}</span></Typography>
                  <Typography className='payment-coupon-text'>GC Coin: <span>{emitDepositObj?.gcCoin} GC {emitDepositObj?.bonusGc && ` + ${emitDepositObj?.bonusGc} Bonus`}</span></Typography>
                  <Typography className='payment-coupon-text'>Transaction Id: <span>{emitDepositObj?.transactionId}</span></Typography>
                </Grid>
                <Grid className='payment-button-container'>
                  <Button
                    className='yes-button btn btn-primary'
                    onClick={() => {
                      onRetry()
                      setIsRetry(true)
                    }}
                  >
                    Retry
                  </Button>
                  <Button
                    className='no-button btn btn-primary'
                    onClick={() => {
                      onCancel()
                    }}
                  >
                    Cancel
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>)}
    </Dialog>
  )
}

export default RetryPaymentDialog
