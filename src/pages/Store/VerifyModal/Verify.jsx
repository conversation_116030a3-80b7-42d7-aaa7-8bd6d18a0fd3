import * as React from 'react'
import { useState, useEffect } from 'react'
import { Button, Typography, Box, Grid, IconButton } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import useStyles from '../../../components/StepperForm/StepperForm.styles'
import KYCSection from '../../../pages/Accounts/components/KYCSections'
import { usePortalStore } from '../../../store/userPortalSlice'
import { useUserStore } from '../../../store/useUserSlice'
import StepperForm from '../../../components/StepperForm'

const VerifyModal = ({ packageDetails }) => {
  const classes = useStyles()
  const { closePortal } = usePortalStore((state) => state)
  const portalStore = usePortalStore((state) => state)

  const [initializeKYC, setInitializeKYC] = useState(false)
  const userDetails = useUserStore((state) => state.userDetails)

  useEffect(() => {
    if (userDetails?.kycStatus === 'K4' || userDetails?.kycStatus === 'K5') {
      portalStore.openPortal(
        () => (
          <StepperForm
            stepperCalledFor='purchase'
            packageDetails={packageDetails}
          />
        ),
        'StepperModal'
      )
    }
  }, [userDetails?.kycStatus])

  const handleKYCInitialize = () => {
    setInitializeKYC(!initializeKYC)
  }

  const handleClose = () => {
    closePortal()
  }

  return (
    <Grid className={classes.StepperModal} data-tracking='Store.Checkout.Step3.KycVerification.Dialog'>
      <Grid>
        <Box className='verification-code-container kyc-main-content'>
          <Typography className='verification-code-text'>KYC VERIFICATION</Typography>
          <Box className='small-subheader-container'>
            <Typography className='small-subheader-text'>
              Account Verification is required. You are about to start your Identity Verification. Please prepare a
              valid ID and have your mobile device handy.
            </Typography>
          </Box>
          <IconButton
            aria-label='close'
            onClick={handleClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500]
            }}
          >
            <CloseIcon />
          </IconButton>

          {
            (userDetails?.kycStatus === 'K2' || userDetails?.kycStatus === 'K3') && initializeKYC
              ? (
                <KYCSection handleClose={handleKYCInitialize} />
                )
              : (
                <>
                  <Box sx={{ textAlign: 'center', padding: '15px 0' }}>
                    <Button className='btn btn-primary' onClick={handleKYCInitialize} disabled={initializeKYC} data-tracking='Store.Checkout.Step3.StartVerification.Btn'>
                      Start Verification
                    </Button>
                  </Box>
                </>
                )
          }
        </Box>
      </Grid>
    </Grid>
  )
}

export default VerifyModal
