import { makeStyles } from '@mui/styles'

import { Container, ButtonPrimary, LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme)
  },

  wrapper: {
    ...Container(theme),
    '&  .becomePartner': {
      ...ButtonPrimary(theme),
      padding: '15px 30px',
      textDecoration: 'none',
      marginTop: '20px',
      display: 'inline-block',
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        padding: '15px 30px'
      },
      [theme.breakpoints.down('sm')]: {
        padding: '8px 16px',
        marginTop: '10px'
      }
    },
    '& .banner': {
      position: 'relative',
      borderRadius: '20px',
      overflow: 'hidden',
      '& img': {
        width: '100%',
        height: '100%',
        '&.img-1': {
          [theme.breakpoints.down('sm')]: {
            display: 'none'
          }
        },
        '&.img-2': {
          display: 'none',
          [theme.breakpoints.down('sm')]: {
            display: 'block'
          }
        }
      },
      '& .bannerText': {
        '& p': {
          fontSize: '32px',
          fontWeight: 'bold',
          [theme.breakpoints.down('lg')]: {
            fontSize: '19px'
          },
          [theme.breakpoints.down('sm')]: {
            fontSize: '14px'
          }
        },
        position: 'absolute',
        top: '0',
        bottom: '0',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
        left: '45px',
        [theme.breakpoints.down('sm')]: {
          left: '25px'
        }
      }
    }
  },

  parentBg: {
    backgroundColor: theme.colors.Pastel,
    display: 'flex',
    flexDirection: 'column !important',
    justifyContent: 'space-between',
    borderRadius: '20px',
    padding: theme.spacing(0.5, 0.5, 0.5, 0.5),
    position: 'relative',
    cursor: 'pointer',
    height: '100%',
    '&::before': {
      content: "''",
      position: 'absolute',
      width: 'calc(100% + 2px)',
      height: '50.5%',
      background: 'linear-gradient(180deg, rgba(155,190,187,1) 42%, rgba(155,190,187,0.3841911764705882) 80%)',
      zIndex: -1,
      left: '50%',
      top: '-3px',
      borderRadius: '20px 20px 0 0',
      transform: 'translate(-50%, 2px)'
    },

    '&::after': {
      content: "''",
      position: 'absolute',
      width: 'calc(100% + 2px)',
      height: '50.5%',
      background: 'linear-gradient(360deg, rgba(155,190,187,1) 42%, rgba(155,190,187,0.3841911764705882) 80%)',
      zIndex: -1,
      left: '50%',
      bottom: '-1px',
      borderRadius: '0px 0px 20px 20px',
      transform: 'translate(-50%, 0px)'
    },

    '&:hover': {
      transition: 'all 0.3s ease-in-out',
      '&::before': {
        content: "''",
        position: 'absolute',
        width: 'calc(100% + 2px)',
        height: '50.5%',
        background: 'linear-gradient(180deg, rgba(253,183,46,1) 42%, rgba(253,183,46,0.16010154061624648) 80%)',
        zIndex: -1,
        left: '50%',
        top: '-3px',
        borderRadius: '20px 20px 0 0',
        transform: 'translate(-50%, 2px)'
      },

      '&::after': {
        content: "''",
        position: 'absolute',
        width: 'calc(100% + 2px)',
        height: '50.5%',
        background: 'linear-gradient(360deg, rgba(253,183,46,1) 42%, rgba(253,183,46,0.16010154061624648) 80%)',
        zIndex: -1,
        left: '50%',
        bottom: '-1px',
        borderRadius: '0px 0px 20px 20px',
        transform: 'translate(-50%, 0px)'
      }
    },

    '& .coin-box-img': {
      textAlign: 'center',
      '& img': {
        // maxWidth: "123px",
        height: 'auto',
        width: '150px'
      }
    }
  },
  ribbon: {
    width: '150px',
    height: '50px',
    zIndex: 1,
    fontSize: '22px',
    fontWeight: '500',
    color: '#fff',
    '--f': '.5em',
    position: 'absolute',
    top: '0',
    left: '0',
    lineHeight: 1.8,
    paddingInline: '1lh',
    paddingBottom: 'var(--f)',
    borderImage: 'conic-gradient(#0008 0 0) 51%/var(--f)',
    clipPath:
      'polygon(\n    100% calc(100% - var(--f)),100% 100%,calc(100% - var(--f)) calc(100% - var(--f)),var(--f) calc(100% - var(--f)), 0 100%,0 calc(100% - var(--f)),999px calc(100% - var(--f) - 999px),calc(100% - 999px) calc(100% - var(--f) - 999px))',
    transform: 'translate(calc((cos(46deg) - 1)*100%), -102%) rotate(-45deg)',
    transformOrigin: '100% 100%',
    backgroundColor: '#AAD840'
  },

  childBg: {
    background: theme.colors.jungleGreen,
    padding: '10px 20px',
    display: 'flex',
    alignItems: 'center',
    borderRadius: '20px',
    justifyContent: 'space-between',
    '& img': {
      width: '35px'
    },
    '& h2': {
      fontWeight: 'bold',
      fontSize: '38px'
    }
  },
  PurchasedsAreaFirstSection: {
    display: 'flex',
    flexDirection: 'column !important',
    gap: theme.spacing(1)
  },
  secondSectionTagArea: {
    // display: 'flex',
    // justifyContent: 'center',
    // gap: '10px',
    // // paddingTop: '20px',
    // [theme.breakpoints.down('sm')]: {
    //   flexDirection:"column !important",
    //   alignItems:"center",
    // },
  },
  secondSectionMainContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'column !important',
      alignItems: 'center'
    },

    // flexDirection:'column !important',
    gap: theme.spacing(0.75)
  },
  secondSection: {
    display: 'flex',
    justifyContent: 'center',
    maxWidth: '131px',
    width: '100%',
    gap: theme.spacing(0.313),
    padding: theme.spacing(1, 0.313),
    borderRadius: theme.spacing(0.426),
    background: '#324846',
    // gap: '10px',
    // paddingTop: '20px',
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'column !important',
      alignItems: 'center'
    },
    '& .MuiTypography-body1': {
      fontSize: '1.125rem',
      fontWeight: '700',
      lineHeight: '1.438rem'
    }
  },
  NotPurchasedsSecondSection: {
    display: 'flex',
    width: '100%',
    alignItems: 'center',
    // flexDirection:'column !important',
    justifyContent: 'center',
    // padding: theme.spacing(1, 1, 1, 1),
    gap: '10px',
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'column !important',
      alignItems: 'center'
    }
  },

  partitionLine: {
    height: theme.spacing(0.005),
    background: '#818181',
    margin: '3px 0px 10px 0px !important'
    // marginTop:'3px',
    // marginBottom:'10px'
  },
  firstPurchaseDescription: {
    textAlign: 'center'
    // '& .MuiTypography-body1':{
    //   // fontWeight: 700,
    //   // fontSize:"21px"
    // },
  },
  leftSectionHeadTag: {
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column !important'
  },
  wohotag: {
    fontWeight: '700 !important',
    color: '#FDB72E',
    fontSize: '21px !important'
  },
  leftSection: {
    // flexDirection: 'column !important',
    maxWidth: '131px',
    background: '#324846',
    borderRadius: theme.spacing(0.426),
    display: 'flex',
    justifyContent: 'space-around',
    width: '100%',
    alignItems: 'center',
    [theme.breakpoints.down('sm')]: {
      maxWidth: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },

    // gap: theme.spacing(1),
    '& img': {
      width: '25px'
    },
    '& p': {
      fontSize: theme.spacing(1),
      fontWeight: '700'
    }
  },
  cardCoinImage: {
    display: 'flex',
    '& .MuiTypography-body1': {
      fontSize: theme.spacing(1.125),
      lineHeight: theme.spacing(1.438),
      fontWeight: '700'
    }
  },
  cardCoinTotal: {
    display: 'flex',
    '& .MuiTypography-body1': {
      fontWeight: '500',
      lineHeight: theme.spacing(0.785),
      fontSize: theme.spacing(0.785),
      color: '#818181'
    }
  },

  rightSection: {
    maxWidth: '131px',
    background: '#324846',
    display: 'flex',
    justifyContent: 'space-around',
    width: '100%',
    borderRadius: theme.spacing(0.426),
    alignItems: 'center',
    [theme.breakpoints.down('sm')]: {
      maxWidth: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },

    // gap: theme.spacing(1),

    '& img': {
      width: '25px'
    },
    '& p': {
      fontSize: theme.spacing(1),
      fontWeight: '700'
    }
  },

  amountSection: {
    display: 'flex',
    alignItems: 'center',
    gap: '20px',
    justifyContent: 'center',
    padding: '25px 0px 2px 0px'
  },

  discountAmt: {
    color: theme.colors.Grey,
    fontSize: '1rem !important',
    fontWeight: 'bold !important',
    textDecoration: 'line-through'
  },

  amtBtn: {
    color: `${theme.colors.textBlack} !important`,
    fontSize: '1rem !important',
    minWidth: '120px !important',
    background: `${theme.colors.YellowishOrange} !important`,
    boxShadow:
      '0px 0.637px 1.401px 0px #EAB647, 0px 1.932px 4.25px 0px rgba(234, 182, 71, 0.04), 0px 5.106px 11.233px 0px rgba(234, 182, 71, 0.09), 0px 16px 35.2px 0px rgba(234, 182, 71, 0.30) !important',
    fontWeight: '700 !important',
    borderRadius: '7.8rem !important'
  }

  // confirmationModal: {
  //   padding: '20px',
  //   display: 'flex',
  //   flexDirection: 'column !important',
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   '& p': {
  //     margin: '20px'
  //   },
  //   '& .action-btn': {
  //     color: `${theme.colors.textBlack} !important`,
  //     fontSize: "1rem !important",
  //     minWidth: "120px !important",
  //     background: `${theme.colors.YellowishOrange} !important`,
  //     boxShadow:
  //       "0px 0.637px 1.401px 0px #EAB647, 0px 1.932px 4.25px 0px rgba(234, 182, 71, 0.04), 0px 5.106px 11.233px 0px rgba(234, 182, 71, 0.09), 0px 16px 35.2px 0px rgba(234, 182, 71, 0.30) !important",
  //     fontWeight: "700 !important",
  //     borderRadius: "7.8rem !important",
  //     margin: '5px'
  //   }
  // }
}))
