import React, { useState } from 'react'
import useStyles from './Store.styles'
import { Box, Button, Grid, Typography } from '@mui/material'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import usePackages from './hooks/usePackages'
import storeBanner from '../../components/ui-kit/icons/banner/buy-banner.webp'
import FirstPurchaseCard from '../../components/ui-kit/icons/banner/firstpuchaseCard.webp'
import { useUserStore } from '../../store/useUserSlice'
import { usePromocodeAppliedStore } from '../../store/store'
import { formatPriceWithCommas, formatValueWithB } from '../../utils/helpers'
import { useBannerStore } from '../../store/useBannerSlice'
import PaymentModal from './PaymentModal/index'
import { usePortalStore } from '../../store/userPortalSlice'
import { useGetProfileMutation } from '../../reactQuery'
import MobileVerification from '../../pages/MobileVerification'
import ProfileSection from './PaymentModal/ProfileSection'
import ImageRenderer from '../../components/ImageRenderer'
const Store = () => {
  const classes = useStyles()
  const userDetails = useUserStore((state) => state.userDetails)
  const portalStore = usePortalStore((state) => state)
  const { packageData } = usePackages()
  const [paymentData, setPaymentData] = React.useState(null)
  const [packageDetails, setPackageDetails] = useState()
  const { buyPage } = useBannerStore((state) => state)

  const setPaymentHandle = (res) => {
    setPaymentData(res)
  }
  const handleCloseProfile = () => {
    portalStore.closePortal()
  }
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      if (
        res?.data?.data?.firstName === null ||
        res?.data?.data?.lastName === null ||
        res?.data?.data?.email === null ||
        res?.data?.data?.dateOfBirth === null ||
        res?.data?.data?.userId === null
      ) {
        portalStore.openPortal(() => <ProfileSection handleClose={handleCloseProfile} />, 'innerModal')
      } else if (!res?.data?.data?.phoneVerified) {
        portalStore.openPortal(() => <MobileVerification />, 'innerModal')
      } else {
        portalStore.openPortal(
          () => <PaymentModal packageDetails={packageDetails} setPaymentHandle={setPaymentHandle} />,
          'withdrawModal'
        )
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const handleBuyNow = (item) => {
    ;(function () {
      window._conv_q = window._conv_q || []
      _conv_q.push(['pushRevenue', 'credit', item, '100466670'])
    })()
    setPackageDetails(item)
    getProfileMutation.mutate()
  }

  const setPaysafeOpen = usePromocodeAppliedStore((state) => state.setPaysafeOpen)

  const handleClose = () => {
    setPaymentData(null)
    setPaysafeOpen({ paysafeOpen: false })
    portalStore.closePortal()
  }

  const { storeBanners } = useBannerStore((state) => state)

  return (
    <>
      <Grid className={classes.lobbyRight}>
        <Grid className={classes.wrapper}>
          <Grid style={{ marginBottom: '40px' }}>
            {buyPage ? (
              buyPage?.map((info) => (
                <Grid className='banner'>
                  <img src={info.desktopImageUrl} className='img-1' />
                  <img src={info.mobileImageUrl} className='img-2' />
                  <Grid className='bannerText'>
                    <Typography>
                      <span style={{ color: '#FDB72E' }}>{info.textOne}</span>
                      <br />
                      {info.textTwo}
                      <br />
                      {info.textThree}
                    </Typography>
                    {info.btnText ? <Button className='becomePartner'>{info.btnText}</Button> : <></>}
                  </Grid>
                </Grid>
              ))
            ) : (
              <ImageRenderer
                src={(storeBanners && storeBanners[0]?.desktopImageUrl) || storeBanner}
                alt='storeBanner'
                style={{ width: '100%' }}
              />
            )}
          </Grid>

          <Grid className={classes.cardView}>
            <Grid container spacing={1}>
              {packageData?.packageData?.rows?.length > 0
                ? packageData?.packageData?.rows?.map((item, index) => {
                    return (
                      <Grid item xs={6} sm={6} md={4} lg={3}>
                        <Grid className={`${classes.parentBg} active`}>
                          {item?.firstPurchaseApplicable && item?.isUserFirstPurchaseEligible && (
                            <Box className={classes.ribbon}>Bonus!</Box>
                          )}
                          <Box className='coin-box-img'>
                            <ImageRenderer
                              src={item?.imageUrl ? item?.imageUrl : FirstPurchaseCard}
                              alt='store-coin-box'
                            />
                          </Box>

                          <Grid>
                            {item?.firstPurchaseApplicable && item?.isUserFirstPurchaseEligible ? (
                              <>
                                <Grid className={classes.PurchasedsAreaFirstSection}>
                                  <Grid className={classes.secondSectionTagArea}>
                                    <Grid className={classes.leftSectionHeadTag}>
                                      {/* <Box className={classes.firstPurchaseDescription}><Typography className={classes.wohotag} >Woohoo! </Typography><Typography>You've unlocked special first <br/> purchase bonus! </Typography> </Box> */}
                                    </Grid>
                                  </Grid>
                                  {/* <Typography className={classes.partitionLine}></Typography> */}
                                  <Grid className={classes.secondSectionMainContainer}>
                                    <Grid className={classes.secondSection}>
                                      <img className='store-coin' src={usdchipIcon} alt='cardCoin' />
                                      <Grid className={classes.leftSection}>
                                        <Box className={classes.cardCoinImage}>
                                          <Typography>{item?.totalGcAmt} GC</Typography>
                                        </Box>

                                        <Box className={classes.cardCoinTotal}>
                                          {/* <Typography>
                                    {item.gcCoin > 0
                                      ? formatPriceWithCommas(formatValueWithB(Number(item.gcCoin)))
                                      : item.gcCoin}{' '}
                                    GC +
                                  </Typography>

                                  <Typography>
                                    {item?.firstPurchaseGcBonus} GC</Typography> */}
                                        </Box>
                                      </Grid>
                                    </Grid>
                                    +
                                    <Grid className={classes.secondSection}>
                                      <img className='store-coin' src={usdIcon} alt='cardCoin' />
                                      <Grid className={classes.rightSection}>
                                        <Box className={classes.cardCoinImage}>
                                          <Typography>{item?.totalScAmt} SC</Typography>
                                        </Box>

                                        <Box className={classes.cardCoinTotal}>
                                          {/* <Typography>
                                    {item.scCoin > 0
                                      ? formatPriceWithCommas(formatValueWithB(Number(item.scCoin)))
                                      : item.scCoin}{' '}
                                    SC +
                                  </Typography>
                                  <Typography>
                                    {item?.firstPurchaseScBonus} SC</Typography> */}
                                        </Box>
                                      </Grid>
                                    </Grid>
                                  </Grid>
                                </Grid>
                              </>
                            ) : (
                              <>
                                <Grid className={classes.NotPurchasedsSecondSection}>
                                  <Grid className={classes.secondSection}>
                                    <img className='store-coin' src={usdchipIcon} alt='cardCoin' />
                                    <Typography>
                                      {item.gcCoin > 0
                                        ? formatPriceWithCommas(formatValueWithB(Number(item.gcCoin)))
                                        : item.gcCoin}{' '}
                                      GC
                                    </Typography>
                                  </Grid>
                                  +
                                  <Grid className={classes.secondSection}>
                                    <img className='store-coin' src={usdIcon} alt='cardCoin' />
                                    <Typography>
                                      {item.scCoin > 0
                                        ? formatPriceWithCommas(formatValueWithB(Number(item.scCoin)))
                                        : item.scCoin}{' '}
                                      SC
                                    </Typography>
                                  </Grid>
                                </Grid>
                              </>
                            )}

                            <Grid className={classes.amountSection}>
                              {/* {item?.previousAmount && (<Typography className={classes.discountAmt}>
                              {item?.previousAmount}
                            </Typography>
                            )} */}
                              <Button variant='contained' className={classes.amtBtn} onClick={() => handleBuyNow(item)}>
                                ${item?.amount}
                              </Button>
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                    )
                  })
                : 'No Active Packages '}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default Store
