
import { TransactionsQuery } from "../../../reactQuery"

const usePayment = (paymentRef,providerType,enabled , successToggler, errorToggler) => {

    const {
        data: paymentStatusData,
    } = TransactionsQuery.getPaymentStatusQuery({
        params: {
            providerType: providerType,
            paymentReference: paymentRef,
        },
        enabled,
        successToggler,
        errorToggler
    })

    return {
        paymentStatusData
    }
}

export default usePayment

