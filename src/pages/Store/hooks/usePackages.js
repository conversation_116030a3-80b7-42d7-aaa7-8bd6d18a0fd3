
import { useState } from "react"

import { PackageQuery } from "../../../reactQuery"


const usePackages = () => {

  const [pagingValue] = useState({
    page: 1,
    limit: 100
  })

    const {
      data: packageData,refetch,isloading
    } = PackageQuery.getPackageListQuery({
      params: pagingValue,
    })


    return {
      packageData,refetch,isloading
      }
}

export default usePackages

