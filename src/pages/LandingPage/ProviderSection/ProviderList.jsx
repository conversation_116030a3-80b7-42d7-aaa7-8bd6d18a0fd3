import { Grid, useTheme } from '@mui/material'
import React from 'react'
import useStyles from './style'
import { FreeMode, Navigation } from 'swiper/modules'
import WhiteLeft from '../../../components/ui-kit/icons/svg/white-left-arrow.svg'
import WhiteRight from '../../../components/ui-kit/icons/svg/white-right-arrow.svg'
import { GameSwiperWrapper } from '../../GameSlider/gameslider.styles'
import { Swiper, SwiperSlide } from 'swiper/react'
import { CasinoQuery } from '../../../reactQuery'
import { toast } from 'react-hot-toast'
import { useGamesStore, useSelectedProviderStore } from '../../../store/store'
import LazyImage from '../../../utils/lazyImage'

const ProviderList = () => {
  const theme = useTheme()
  const classes = useStyles()

  const {
    options,
    selectedProvider,
    setSelectedProvider
  } = useGamesStore((state) => state)

  const selectedProviderStore = useSelectedProviderStore((state) => state)
  const selectedProviderId = useSelectedProviderStore((state) => state.selectedProviderId)

  // get all provider list
  const { data: providerData } = CasinoQuery.getProviderListQuery()

  // on provider change
  const onLobbyProviderChange = (selectedProviderKey) => {
    selectedProviderStore.setSelectedProviderId(selectedProviderKey)
    const data = options?.find((x) => x?.value === selectedProviderKey)
    setSelectedProvider(data)
  }

  // on provider click
  const handleProviderClick = (providerId) => {
    if (selectedProviderId !== providerId) {
      toast.success('DeSelect Provider for all games.')
      onLobbyProviderChange(providerId)
    } else {
      selectedProviderStore.setSelectedProviderId('')
    }
  }

  return (
    <GameSwiperWrapper theme={theme} className={classes.LobbyProviderSection}>
      <Grid>
        <button id='swiper-button-next-provider' className='button-next'>
          <LazyImage src={WhiteRight} alt='Right' style={{ width: '100%', height: '100%' }} />
        </button>

        <button id='swiper-button-prev-provider' className='button-prev'>
          <LazyImage src={WhiteLeft} alt='Left' style={{ width: '100%', height: '100%' }} />
        </button>
      </Grid>

      <Grid className='provider-section'>
        <Grid className='provider-card-wrap'>
          <Swiper
            spaceBetween={10}
            freeMode
            navigation={{
              nextEl: '#swiper-button-next-provider',
              prevEl: '#swiper-button-prev-provider'
            }}
            modules={[FreeMode, Navigation]}
            className='mySwiper'
            breakpoints={{
              0: {
                slidesPerView: 3
              },
              768: {
                slidesPerView: 4
              },
              1024: {
                slidesPerView: 4
              },
              1199: {
                slidesPerView: 7
              }
            }}
          >
            {providerData?.map((data, idx) => {
              return (
                <SwiperSlide key={idx}>
                  <div
                    className='custom-col-2'
                  >
                    <Grid
                      className={
                        selectedProvider === data?.masterCasinoProviderId ||
                        selectedProviderId === data?.masterCasinoProviderId
                          ? 'selected-provider-card'
                          : 'provider-card'
                      }
                      onClick={() => handleProviderClick(data?.masterCasinoProviderId)}
                    >
                      <LazyImage
                        src={data?.thumbnailUrl}
                        alt='Provider'
                        width='100%'
                        height='100%'
                        style={{ width: '100%', height: '100%' }}
                      />
                    </Grid>
                  </div>
                </SwiperSlide>
              )
            })}
          </Swiper>
        </Grid>
      </Grid>
    </GameSwiperWrapper>
  )
}

export default ProviderList
