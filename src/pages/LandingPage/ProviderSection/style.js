import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  LobbyProviderSection: {
    //padding: theme.spacing(3, 0),
    margin: theme.spacing(2, 0, 1),
    [theme.breakpoints.down('sm')]: {
      margin: theme.spacing(0.625, 0)
    },
    position: 'relative',
    '& .heading': {
      color: theme.colors.yellow,
      fontSize: theme.spacing(1.5),
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(2)
      },
      marginBottom: '20px'
    },
    '& .provider-section': {
      padding: theme.spacing(1.2, 3),
      borderRadius: theme.spacing(1),
      // border : '1px solid #958484',
      background: theme.colors.textBlack,
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(0.625, 1.5)
      },
      '& .swiper-wrapper': {
        marginTop: '0'
      },
      '& .provider-card-wrap': {
        '& .provider-card': {
          borderRadius: theme.spacing(0.625),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: theme.colors.sidebarBg,
          padding: theme.spacing(1),
          width: '100%',
          minHeight: theme.spacing(6.6875),
          flex: '0 0 auto',
          cursor: 'pointer',
          border: '1px solid transparent',
          [theme.breakpoints.down('md')]: {
            minHeight: theme.spacing(3.75),
            padding: theme.spacing(0.625),
            borderRadius: theme.spacing(0.313)
          },
          [theme.breakpoints.down('sm')]: {
            // minHeight: theme.spacing(3),
            padding: theme.spacing(0.25)
          },
          '&:hover': {
            // borderColor: `${theme.colors.YellowishOrange}`,
            [theme.breakpoints.down('md')]: {
              borderColor: 'transparent'
            }
          },
          '& img': {
            width: '80% !important',
            height: 'auto !important',
            [theme.breakpoints.down(991)]: {
              width: '60% !important'
            },
            [theme.breakpoints.down('sm')]: {
              width: '100% !important'
            }
          }
        },
        '& .selected-provider-card': {
          borderRadius: theme.spacing(0.625),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: theme.colors.sidebarBg,
          padding: theme.spacing(1),
          width: '100%',
          minHeight: theme.spacing(6.6875),
          flex: '0 0 auto',
          cursor: 'pointer',
          border: '1px solid transparent',
          borderColor: `${theme.colors.YellowishOrange}`,
          [theme.breakpoints.down('md')]: {
            minHeight: theme.spacing(3.75),
            padding: theme.spacing(0.625),
            borderRadius: theme.spacing(0.313)
          },
          // [theme.breakpoints.down('sm')]: {
          //   minHeight: theme.spacing(2.5)
          // },
          '& img': {
            width: '80%',
            [theme.breakpoints.down('sm')]: {
              width: '100%'
            }
          }
        }
      }
    },
    '& .button-next, & .button-prev': {
      border: 'none',
      zIndex: '1',
      padding: theme.spacing(0.2),
      background: 'transparent',
      position: 'absolute',
      left: theme.spacing(1.5),
      top: theme.spacing(4.75),
      transform: 'translate(-50%, -50%)',
      cursor: 'pointer',
      [theme.breakpoints.down('md')]: {
        top: theme.spacing(3.2)
      },
      [theme.breakpoints.down('sm')]: {
        top: theme.spacing(2.75)
      },
      '& img': {
        width: '12px',
        position: 'static',
        [theme.breakpoints.down('md')]: {
          width: '10px'
          // top: "35px",
        },
        [theme.breakpoints.down('sm')]: {
          width: '8px'
        }
      }
    },
    '&  .button-next': {
      right: theme.spacing(0),
      left: 'auto',
      [theme.breakpoints.down('md')]: {
        right: '-1px'
      }
    },
    '& .button-prev': {
      [theme.breakpoints.down('md')]: {
        left: theme.spacing(0.875)
      }
    }
  }
}))
