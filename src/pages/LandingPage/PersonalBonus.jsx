import React from 'react'
import useStyles from '../Lobby/Lobby.styles'
import {
  <PERSON>ton,
  Grid,
  List,
  ListItem,
  Pagination,
  TableRow,
  Table,
  TableBody,
  TableCell,
  TableHead,
  Typography,
  Box
} from '@mui/material'
import Personal3Image from '../../components/ui-kit/icons/landing/personal3.webp'
import Personal2Image from '../../components/ui-kit/icons/landing/personal2.webp'
import Personal4Image from '../../components/ui-kit/icons/landing/personal4.webp'
import Personal1Image from '../../components/ui-kit/icons/landing/personal1.webp'
import PersonalImage from '../../components/ui-kit/icons/landing/personal-bonus.webp'
import referralIcon from '../../components/ui-kit/icons/landing/referral-icon.webp'
import copyIcon from '../../components/ui-kit/icons/svg/copy-icon.svg'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import usePersonalBonus from './hooks/usePersonalBonus'
import { commonDateTimeFormat, formatPriceWithCommas } from '../../utils/helpers'
import moment from 'moment'
import toast from 'react-hot-toast'
import Select from 'react-select'
import { useBannerStore } from '../../store/useBannerSlice'
/* eslint-disable multiline-ternary */

const options = [
  { value: 'SC', label: 'SC' },
  { value: 'GC', label: 'GC' }
]

const PersonalBonus = () => {
  const classes = useStyles()
  const {
    betsData,
    isBetsLoading,
    handlePromoCodeChange,
    promoCode,
    handleClaimOnSubmit,
    pageNo,
    setPageNo,
    limit,
    handleSubmit,
    setCoinType,
    coinType,
    register,
    handleOnSubmit,
    errors,
    setValue,
    user,
    setError,
    clearErrors
  } = usePersonalBonus()

  const handleChange = (event, value) => {
    setPageNo(value)
  }
  const { personalBonus } = useBannerStore((state) => state)
  const handleCopyClick = async (code) => {
    try {
      await navigator.clipboard.writeText(code)
      toast.success('Copied')
    } catch (err) {
      console.error('Failed to copy to clipboard', err)
    }
  }

  const handleAmount = (e) => {
    const userWallet = user.userDetails?.userWallet
    const amount = e.target.value
    setValue('amount', amount)
    if (coinType.value === 'GC' && userWallet) {
      if (userWallet.gcCoin < amount) {
        setError('amount', { type: 'focus', message: 'Insufficient Balance' }, { shouldFocus: true })
      } else {
        clearErrors('amount')
      }
    }
    if (coinType.value === 'SC' && userWallet) {
      if (userWallet.totalScCoin < amount) {
        setError('amount', { type: 'focus', message: 'Insufficient Balance' }, { shouldFocus: true })
      } else {
        clearErrors('amount')
      }
    }
  }

  const handleCoinType = (e) => {
    setPageNo(1)
    setCoinType(e)
    clearErrors('amount')
  }
  return (
    <Grid className={classes.lobbyRight}>
      <Grid className={classes.wrapper}>
        <Grid className={classes.personalSection}>
          <Grid className='referImg'>
            {personalBonus ? (
              personalBonus?.map((info) => (
                <Grid className='banner'>
                  <img src={info?.desktopImageUrl} className='img-1' />
                  <img src={info?.mobileImageUrl} className='img-2' />
                  <Grid className='bannerText'>
                    <Typography>
                      {info?.textOne}
                      <br />
                      <span style={{ color: '#FDB72E' }}>{info?.textTwo}</span> <br />
                      {info?.textThree}
                    </Typography>
                    {info?.btnText ? <Button className='becomePartner'>{info?.btnText}</Button> : <></>}
                  </Grid>
                </Grid>
              ))
            ) : (
              <img src={PersonalImage} alt='Reffer Friend' sx={{ width: '100%' }} />
            )}
          </Grid>
          <Grid className='referral-section-text'>
            <Grid className='referral-section'>
              <Typography variant='heading1'>Enter Bonus Code</Typography>
              <input
                type='text'
                value={promoCode}
                onChange={handlePromoCodeChange}
                placeholder='Enter invite url here'
              />
              <Button disabled={promoCode?.length > 0 ? false : true} onClick={handleClaimOnSubmit}>
                Apply Referral
              </Button>

              <Grid className={classes.bonusBoxImg}>
                <img src={referralIcon} alt='' />
              </Grid>
            </Grid>

            <Grid className='textRightSection'>
              <Typography variant='heading2'>Your Exclusive </Typography>
              <Typography variant='heading3'>Personal Bonus Awaits!</Typography>
              <Typography variant='text'>
                We hope you're well! We're thrilled to inform you that you've been chosen as the unique recipient of our
                special invite bonus!
              </Typography>
              <List>
                <ListItem>
                  <Grid>
                    <img src={Personal1Image} alt='message' />
                  </Grid>
                  <Grid display={'flex'} flexDirection={'column'}>
                    <Typography variant='text1'>Invite Your Friends</Typography>
                    <Typography variant='text2'>
                      Feel the excitement with others! Come join <b style={{ color: '#FDB72E' }}>THE MONEY FACTORY</b>{' '}
                      with your friends, family, and coworkers.
                    </Typography>
                  </Grid>
                </ListItem>

                <ListItem>
                  <Grid>
                    <img src={Personal2Image} alt='message' />
                  </Grid>
                  <Grid display={'flex'} flexDirection={'column'}>
                    <Typography variant='text1'>They Join, You Earn</Typography>
                    <Typography variant='text2'>
                      Both you and your friend will receive Personal Bonus Benefits for each successful referral. It's
                      our way of thanking thanks for letting people know about us!
                    </Typography>
                  </Grid>
                </ListItem>

                <ListItem>
                  <Grid>
                    <img src={Personal3Image} alt='message' />
                  </Grid>
                  <Grid display={'flex'} flexDirection={'column'}>
                    <Typography variant='text1'>How to Claim</Typography>
                    <Typography variant='text2'>
                      When your buddy makes a purchase or signs up, your bonus will be automatically credited to your
                      account.
                    </Typography>
                  </Grid>
                </ListItem>

                <ListItem display={'flex'} gap='2'>
                  <Grid>
                    <img src={Personal4Image} alt='message' />
                  </Grid>
                  <Grid display={'flex'} flexDirection={'column'}>
                    <Typography variant='text1'>Limited Time Offer</Typography>
                    <Typography variant='text2'>
                      Hurry up! This special invite incentive is only available for a limited period.
                    </Typography>
                  </Grid>
                </ListItem>
              </List>
            </Grid>
          </Grid>

          <Grid className='friendStatics'>
            <Grid>
              <Typography variant='h6'>
                <b>Available amount for creating a Personal bonus:</b>
              </Typography>
            </Grid>

            <Grid className='sectionGroup'>
              <Grid className='section1'>
                <Grid>
                  <img src={usdchipIcon} alt='img3' />
                </Grid>

                <Grid>
                  <Typography className='section2'>
                    <b style={{ color: '#AAD840' }}>
                      {`${formatPriceWithCommas(user?.userDetails?.userWallet?.gcCoin || 0)} GC`}
                    </b>
                  </Typography>
                  <Typography className='text3'>Total GC Amount</Typography>
                </Grid>
              </Grid>

              <Grid className='section1'>
                <Grid>
                  <img src={usdIcon} alt='im4' />
                </Grid>

                <Grid className='section2'>
                  <Typography>
                    <b style={{ color: '#AAD840' }}>
                      {`${formatPriceWithCommas(user?.userDetails?.userWallet?.scCoin?.bsc || 0)} BSC`}
                    </b>
                  </Typography>
                  <Typography className='text3'>Bonus SC</Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>

          <Grid>
            <Typography variant='bonusSection'>Create Friends Bonus</Typography>
            <form onSubmit={handleSubmit(handleOnSubmit)} style={{ width: '100%' }}>
              <Grid className='create-bonus-section'>
                <Grid container spacing={1}>
                  <Grid item xs={12} lg={2}>
                    <Select
                      // defaultMenuIsOpen
                      value={coinType}
                      onChange={handleCoinType}
                      options={options}
                      className={classes.reactCoinSelect}
                      classNamePrefix='reactInnerCoinSelect'
                      placeholder={'Select Coin'}
                    />
                  </Grid>
                  <Grid item xs={12} lg={4}>
                    <input
                      type='number'
                      onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                      min='1'
                      id='amount'
                      onChange={handleAmount}
                      {...register('amount')}
                      placeholder='Enter Amount'
                    />
                    {errors?.amount && <p className='input-error'> {errors?.amount?.message}</p>}
                  </Grid>
                  <Grid item xs={12} lg={5}>
                    <Grid sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <Button type='submit'>Create Bonus</Button>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </form>
            {betsData?.bonusDetails.rows?.length > 0 && (
              <Grid className='friendTable' style={{ marginTop: '30px' }}>
                <Box className='friendBonusTable'>
                  <Table aria-label='a dense table'>
                    <TableHead>
                      <TableRow>
                        <TableCell style={{ flex: 1 }}>Date/Time</TableCell>
                        <TableCell style={{ flex: 1 }}>Amount</TableCell>
                        <TableCell style={{ flex: 1 }}>Status</TableCell>
                        <TableCell style={{ flex: 1 }}>Share Code </TableCell>
                        <TableCell style={{ flex: 1 }}>Claimed By </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {betsData?.bonusDetails.rows?.length ? (
                        betsData?.bonusDetails?.rows?.map((item, index) => {
                          return (
                            <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                              <TableCell style={{ flex: 1 }}>
                                {moment(new Date(item?.createdAt)).format(commonDateTimeFormat.dateWithTime)}
                              </TableCell>
                              <TableCell style={{ flex: 1 }}>
                                {`${formatPriceWithCommas(item?.amount)} ${item?.coinType}`}
                              </TableCell>
                              <TableCell style={{ flex: 1 }}>
                                {item?.status === 'ACTIVE' && (
                                  <p style={{ color: '#1ebf1eeb', fontWeight: '600' }}>{item?.status} </p>
                                )}
                                {item?.status === 'CLAIMED' && (
                                  <p style={{ color: 'red', fontWeight: '600' }}>{item?.status} </p>
                                )}
                              </TableCell>
                              <TableCell style={{ flex: 1 }}>
                                {item?.status === 'ACTIVE' ? (
                                  <Button className='referalBtn' variant='contain'>
                                    {item?.bonusCode}
                                    <img
                                      src={copyIcon}
                                      style={{ marginLeft: '5px' }}
                                      alt={copyIcon}
                                      onClick={() => handleCopyClick(item?.bonusCode)}
                                    />
                                  </Button>
                                ) : (
                                  <>-</>
                                )}
                              </TableCell>
                              <TableCell style={{ flex: 1 }}>
                                {item?.status === 'CLAIMED' ? <p>{item?.claimedUser?.username} </p> : <>-</>}
                              </TableCell>
                            </TableRow>
                          )
                        })
                      ) : (
                        <TableRow style={{ background: 'transparent' }}>
                          <TableCell colSpan={10} style={{ textAlign: 'center', color: 'red' }}>
                            <span>No Records Found</span>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </Box>
                {betsData?.bonusDetails.rows?.length > 0 && !isBetsLoading && (
                  <Pagination
                    className={classes.tablePagination}
                    style={{ marginTop: '15px', marginBottom: '15px' }}
                    count={Math.ceil(betsData?.bonusDetails.count / limit)}
                    page={pageNo}
                    onChange={handleChange}
                    defaultPage={3}
                    siblingCount={1}
                  />
                )}
              </Grid>
            )}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default PersonalBonus
