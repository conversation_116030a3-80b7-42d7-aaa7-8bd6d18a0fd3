import React, { useState, useEffect } from 'react'
import { Box, Grid, Typography, useTheme } from '@mui/material'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import 'swiper/css/navigation'

import UserIcon from '../../components/ui-kit/icons/svg/user-icon.svg'
import TrophyIcon from '../../components/ui-kit/icons/svg/trophy-logo.svg'
import GCIcon from '../../../src/components/ui-kit/icons/utils/card-coin2.webp'
import DefaultImg from '../../../src/components/ui-kit/icons/utils/casinoGames.webp'
import ScIcon from '../../components/ui-kit/icons/utils/usd-cash.webp'
import { LatestWinnerWrapper } from './Latestwinners.styles'
import { useUserStore } from '../../store/useUserSlice'
import { liveWinnerSocket } from '../../utils/socket'
import useLiveWinners from './hooks/useLiveWiners'
import LazyImage from '../../utils/lazyImage'

const LiveWinners = (props) => {
  const theme = useTheme()
  const liveWinnerData = useLiveWinners()

  const liveWinnerSocketConnection = useUserStore((state) => state.liveWinnerSocketConnection)

  const [winnerList, setWinnerList] = useState(liveWinnerData?.data?.data?.data || [])

  const onGetLiveWinners = (winnerData) => {
    setWinnerList((winnerList) => {
      const updatedWinners = [winnerData?.data, ...winnerList.slice(0, winnerList.length - 1)]
      return updatedWinners
    })
  }
  useEffect(() => {
    if (liveWinnerSocketConnection) {
      liveWinnerSocket.on('LIVE_GAME_WINNERS', (data) => {
        onGetLiveWinners(data)
      })
    }
    return () => {
      liveWinnerSocket.off('LIVE_GAME_WINNERS', () => {})
    }
  }, [liveWinnerSocketConnection])

  useEffect(() => {
    setWinnerList(liveWinnerData?.data?.data?.data)
  }, [liveWinnerData?.data?.data?.data])

  if (!winnerList || winnerList.length === 0) return null
  return (
    <div key={`sliderGrid-${props.subCategory?.name?.EN}-${props.index}`}>
      <LatestWinnerWrapper theme={theme}>
        <Grid className='gameHeading'>
          <Grid className='heading'>
            <LazyImage src={TrophyIcon} lazy={false} width='28px' height='28px' />
            <Typography>Live Winners</Typography>
          </Grid>
        </Grid>
        <Box className='winners-wrap'>
          {winnerList &&
            winnerList?.map((winner, index) => (
              <Box key={`winner_${index}`} className='outer-box'>
                <Box className='live-winner-box'>
                  <LazyImage
                    src={winner.gameImage || DefaultImg}
                    alt={`${winner.username}'s winning game`}
                    className='casinoGame-img'
                    width='100%'
                    height='71px'
                  />

                  <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
                    <Box className='winnerName' display='flex' justifyContent='center' alignItems='center'>
                      <LazyImage src={UserIcon} alt='User icon' />
                      <Typography component='span'>{winner.username}</Typography>
                    </Box>

                    <Typography
                      className={`winnerAmount ${winner.isScActive ? 'green-text' : 'yellow-text'}`}
                      display='flex'
                      justifyContent='center'
                      alignItems='center'
                    >
                      <LazyImage
                        src={winner.isScActive ? ScIcon : GCIcon}
                        alt={winner.isScActive ? 'SC icon' : 'GC icon'}
                      />
                      <span>
                        {' '}
                        {winner.winAmount} {winner.isScActive ? 'SC' : 'GC'}
                      </span>
                    </Typography>
                  </Grid>
                </Box>
              </Box>
            ))}
        </Box>
      </LatestWinnerWrapper>
    </div>
  )
}

export default LiveWinners
