import React, { useEffect, useState, useCallback, useMemo } from 'react'
import '../../../src/App.css'
import { Grid, Box, Tabs, Tab } from '@mui/material'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import useStyles from '../Lobby/Lobby.styles'
import { SubCategoryConstants } from '../../components/SideBar/constants'
import allGamesHeaderImage from '../../components/ui-kit/icons/svg/allGamesHeaderImage.svg'
import selectedHeaderLobby from '../../components/ui-kit/icons/svg/selectedHeaderLobby.svg'
import allGameSelectedHeader from '../../components/ui-kit/icons/svg/allGameSelectedHeader.svg'
import headerLobby from '../../components/ui-kit/icons/svg/headerLobby.svg'
import defaultCategory from '../../components/ui-kit/icons/svg/defaultCategory.svg'
import { useGamesStore } from '../../store/store'
import LazyImage from '../../utils/lazyImage'

// Memoized Tab Icon component to avoid re-creating <img> on each render
const TabIcon = React.memo(({ src, alt }) => (
  <LazyImage src={src} alt={alt} height='100%' width='100%' />
))

const LobbyFilter = () => {
  const classes = useStyles()

  const {
    setSubCategoryName,
    setPageNo,
    setGameData,
    featuredSubcategory,
    selectedSubCat,
    setSelectedSubCat
  } = useGamesStore((state) => ({
    setSubCategoryName: state.setSubCategoryName,
    setPageNo: state.setPageNo,
    setGameData: state.setGameData,
    featuredSubcategory: state.featuredSubcategory,
    selectedSubCat: state.selectedSubCat,
    setSelectedSubCat: state.setSelectedSubCat
  }))

  const [value, setValue] = useState(0)

  const getTabIndexFromSelectedSubCat = useCallback(() => {
    if (
      selectedSubCat === 'Lobby' ||
      selectedSubCat === SubCategoryConstants.FAVORITE_GAMES ||
      selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED
    ) {
      return 0
    }
    if (selectedSubCat === SubCategoryConstants.ALL_GAMES) return 1

    // Check if the selected subcategory is featured
    // Filter featured subcategories first to match the tab order
    const featuredSubcategories = featuredSubcategory.filter((subCategory) => subCategory?.isFeatured)
    const featuredIndex = featuredSubcategories.findIndex(
      (item) => item?.name === selectedSubCat
    )

    if (featuredIndex !== -1) {
      return featuredIndex + 2
    }

    // For non-featured subcategories, keep Lobby tab selected
    return 0
  }, [selectedSubCat, featuredSubcategory])

  useEffect(() => {
    if (!selectedSubCat) {
      setSelectedSubCat('Lobby')
      setSubCategoryName('Lobby')
      setValue(0)
      return
    }
    setSubCategoryName(selectedSubCat)
    const newIndex = getTabIndexFromSelectedSubCat()
    setValue((prevValue) => (prevValue !== newIndex ? newIndex : prevValue))
  }, [selectedSubCat, getTabIndexFromSelectedSubCat, setSelectedSubCat, setSubCategoryName])

  const handleChange = useCallback(
    (_event, newValue) => {
      setValue(newValue)
      setPageNo(1)
      setGameData([])

      // Update selectedSubCat based on tab index to keep them in sync
      if (newValue === 0) {
        setSelectedSubCat('Lobby')
        setSubCategoryName('Lobby')
      } else if (newValue === 1) {
        setSelectedSubCat(SubCategoryConstants.ALL_GAMES)
        setSubCategoryName(SubCategoryConstants.ALL_GAMES)
      } else {
        // For featured tabs (index 2+)
        const featuredIndex = newValue - 2
        const featuredSubcategories = featuredSubcategory.filter((subCategory) => subCategory?.isFeatured)
        if (featuredIndex >= 0 && featuredIndex < featuredSubcategories.length) {
          const selectedFeatured = featuredSubcategories[featuredIndex]
          setSelectedSubCat(selectedFeatured.name)
          setSubCategoryName(selectedFeatured.name)
        }
      }
    },
    [setPageNo, setGameData, setSelectedSubCat, setSubCategoryName, featuredSubcategory]
  )

  const handleSubCategoryClick = useCallback(
    (key) => {
      setSelectedSubCat(key)
      setSubCategoryName(key)
      setPageNo(1)
      setGameData([])
    },
    [setSelectedSubCat, setSubCategoryName, setPageNo, setGameData]
  )

  const onAllGamesClick = useCallback(() => {
    setPageNo(1)
    if (
      selectedSubCat === SubCategoryConstants.FAVORITE_GAMES ||
      selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED
    ) {
      setGameData([])
    }
    handleSubCategoryClick(SubCategoryConstants.ALL_GAMES)
  }, [selectedSubCat, handleSubCategoryClick, setPageNo, setGameData])

  const featuredTabs = useMemo(
    () =>
      featuredSubcategory
        .filter((subCategory) => subCategory?.isFeatured)
        .map((subCategory) => {
          const isSelected = selectedSubCat === subCategory.name
          const iconSrc = isSelected
            ? subCategory.imageUrl?.selectedThumbnail || defaultCategory
            : subCategory.imageUrl?.thumbnail || defaultCategory

          return (
            <Tab
              key={subCategory.name}
              label={subCategory.name}
              icon={<TabIcon src={iconSrc} alt='Subcategory icon' />}
              onClick={() => handleSubCategoryClick(subCategory.name)}
              disableRipple
              disableFocusRipple
            />
          )
        }),
    [featuredSubcategory, selectedSubCat, handleSubCategoryClick]
  )

  // Show lobby as selected only when "Lobby" itself is selected
  const lobbyIcon = selectedSubCat === 'Lobby' ? selectedHeaderLobby : headerLobby

  const allGamesIcon =
    selectedSubCat === SubCategoryConstants.ALL_GAMES
      ? allGameSelectedHeader
      : allGamesHeaderImage

  return (
    <Grid className={classes.lobbyFilterWrap}>
      <Grid container spacing={1}>
        <Grid item xs={12} lg={12}>
          <Grid className={classes.lobbyFilterSection}>
            <Box className='lobby-filter-left'>
              <Grid className={classes.roundedThemeTabs}>
                <Tabs
                  value={value}
                  onChange={handleChange}
                  variant='scrollable'
                  scrollButtons='auto'
                  aria-label='scrollable auto tabs example'
                >
                  <Tab
                    label='Lobby'
                    icon={<TabIcon src={lobbyIcon} alt='Lobby header icon' />}
                    onClick={() => handleSubCategoryClick('Lobby')}
                    disableRipple
                    disableFocusRipple
                  />
                  <Tab
                    label='All games'
                    icon={<TabIcon src={allGamesIcon} alt='All games' />}
                    onClick={onAllGamesClick}
                    disableRipple
                    disableFocusRipple
                  />
                  {featuredTabs}
                </Tabs>
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default React.memo(LobbyFilter)
