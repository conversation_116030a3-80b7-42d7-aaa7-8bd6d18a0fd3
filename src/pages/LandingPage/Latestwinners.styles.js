import { Box } from '@mui/material'
import { styled } from '@mui/system'

import WinBg from '../../../src/components/ui-kit/icons/webp/background-jackpot.webp'

export const LatestWinnerWrapper = styled(Box)(({ theme }) => {
  return {
    '& .gameHeading ': {
      position: 'relative',
      '& .heading': {
        display: 'flex',
        gap: theme.spacing(1),
        alignItems: 'center',
        padding: theme.spacing(1, 0),
        position: 'relative',
        '& p': {
          color: theme.colors.textWhite,
          fontSize: theme.spacing(1.5625),
          width: 'fit-content',
          fontWeight: '700',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1)
          },
          '&:after': {
            position: 'absolute',
            content: '""',
            height: '0.75rem',
            width: '0.75rem',
            right: '-20px',
            top: '12px',
            borderRadius: '1rem',
            background: '#00A30B',
            [theme.breakpoints.down('md')]: {
              height: '0.5rem',
              width: '0.5rem',
              right: '-15px',
              top: '8px'
            }
          }
        }
      },
      '& button': {
        border: 'none',
        '&:after': {
          display: 'none'
        }
      },
      '& .swiper-button-next': {
        borderRadius: theme.spacing(0, 4.1875, 4.1875, 0),
        background: theme.colors.GreenishCyan,
        padding: theme.spacing(0.625, 1.875),
        zIndex: '1',
        marginRight: '-12px',
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0, 1),
          height: theme.spacing(1.25)
        },
        '& img': {
          [theme.breakpoints.down('md')]: {
            width: '10px'
          },
          width: '15px'
        }
      },
      '& .swiper-button-prev': {
        right: '68px',
        left: 'auto',
        borderRadius: theme.spacing(4.1875, 0, 0, 4.1875),
        background: theme.colors.GreenishCyan,
        zIndex: '1',
        padding: theme.spacing(0.625, 1.875),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0, 1),
          height: theme.spacing(1.25),
          right: '38px'
        },
        '& img': {
          [theme.breakpoints.down('md')]: {
            width: '10px'
          },
          width: '15px'
        }
      }
    },
    '& .winners-wrap': {
      padding: '1.25rem',
      background: '#1C1C1C',
      backgroundImage: `url(${WinBg})`,
      backgroundSize: 'cover',
      display: 'flex',
      borderRadius: '10px',
      gap: '1rem',
      transition: 'all 0.5s ease-in-out',
      marginBottom: '0.5rem',
      [theme.breakpoints.down('lg')]: {
        '& > :nth-child(5)': {
          display: 'none'
        }
      },
      [theme.breakpoints.down('md')]: {
        padding: '0.75rem',
        gap: '10px',
        height: '110px',
        '& > :nth-child(4), & > :nth-child(5)': {
          display: 'none'
        }
      },

      [theme.breakpoints.down('sm')]: {
        '& > :nth-child(3)': {
          display: 'none'
        }
      }
    },

    '& .outer-box': {
      transition: 'all 200ms ease-in-out',
      lineHeight: '0',
      padding: '2px',
      borderRadius: '10px',
      width: '100%',
      background: 'linear-gradient(180deg, #6A6A6A 8.65%, #363636 100%)',

      '& .live-winner-box': {
        height: '100%',
        display: 'flex',
        background: '#1e1e1e',
        gap: '0.5rem',
        alignItems: 'center',
        borderRadius: '10px',
        justifyContent: 'start',
        padding: '0.875rem 0.5rem',
        [theme.breakpoints.down(1250)]: {
          gap: '0.5rem',
          padding: '0.5rem'
        }
      },
      '& img': {
        width: '100%',
        maxWidth: '52px',
        maxHeight: '72px',
        [theme.breakpoints.down('md')]: {
          maxWidth: '38px',
          maxHeight: '56px'
        },
        '&.casinoGame-img': {
          borderRadius: theme.spacing(0.5),
          boxShadow: '2.65px 2.65px 2.65px 0px #B451B499'
        }
      },
      '& .casino-card': {
        textAlign: 'center',
        position: 'relative',
        transition: 'all 200ms ease-in-out',
        '&:hover': {
          transform: 'translateY(-0.25rem)',
          transition: 'all 600ms ease-in-out',
          '& .overlayPlay': {
            display: 'flex',
            borderRadius: '8px'
          }
        },
        '& .fav-icon': {
          width: '20px',
          height: '20px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: '8',
          '& img': {
            width: '20px',
            height: '20px',
            objectFit: 'contain',
            objectPosition: 'center'
          },
          '&:hover': {
            backgroundColor: theme.colors.textWhite,
            cursor: 'pointer'
          }
        },
        '& .casinoGame-img': {
          width: '100%',
          aspectRatio: '1',
          borderRadius: '8px',
          '&:hover': {
            backgroundColor: theme.colors.textWhite,
            cursor: 'pointer'
          }
        },
        '& .tournamentLogo': {
          position: 'absolute',
          left: '2px',
          top: '5px',
          width: '30px',
          height: '30px'
        },
        '& .prgamatic-jackpot-amount-wrapper': {
          position: 'absolute',
          top: '12px',
          left: '47%',
          display: 'flex',
          justifyContent: 'center',
          gap: '4px',
          alignItems: 'center',
          background: '#000000B2',
          borderRadius: '17px',
          whiteSpace: 'nowrap',
          transform: 'translate(-50%, 0)',
          padding: '1px 5px'
        }
      }
    },
    '& .overlayPlay': {
      position: 'absolute',
      display: 'none',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      margin: '0 auto',
      inset: '0',
      flexDirection: 'column',
      background: 'rgba(215, 143, 31, 0.9)',
      cursor: 'pointer',
      transition: 'all 200ms ease-in-out',
      borderRadius: '8px',
      '& a': {
        color: theme.colors.textWhite,
        textDecoration: 'none'
      }
    },

    '& .swiper-slide': {
      // background: '#093931',
      borderRadius: theme.spacing(1)
    },

    '& .winnerParent': {
      // padding: theme.spacing(1.5, 0)
      display: 'flex',
      flexDirection: 'column',
      gap: '0.5rem',
      justifyContent: 'center'
    },

    '& .winnerName': {
      color: theme.colors.textWhite,
      fontSize: theme.spacing(1),
      fontWeight: '600',
      textAlign: 'start',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'start',
      gap: theme.spacing(0.475),
      '& span': {
        maxWidth: '69px',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        [theme.breakpoints.down(1350)]: {
          maxWidth: '53px'
        },
        [theme.breakpoints.down('sm')]: {
          maxWidth: '60px'
        }
      },
      '& img': {
        width: '20px !important'
      },
      [theme.breakpoints.down('sm')]: {
        gap: theme.spacing(0.25),
        fontSize: theme.spacing(1)
      }
    },

    '& .winnerAmount': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'start',
      whiteSpace: 'nowrap',
      gap: theme.spacing(0.5),
      fontSize: '1.125rem',
      '& img': {
        width: '20px !important'
      },
      [theme.breakpoints.down('md')]: {
        gap: theme.spacing(0.25),
        fontSize: '0.875rem'
      },
      '&.green-text': {
        color: '#00A30B',
        fontWeight: '700'
      },
      '&.yellow-text': {
        color: '#EAB647',
        fontWeight: '700'
      },
      '& span': {        
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        
      }
    }
  }
})
