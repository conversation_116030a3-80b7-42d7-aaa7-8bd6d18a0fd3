import { yupR<PERSON>olver } from "@hookform/resolvers/yup"
import { useCallback, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "react-hot-toast"

import { PersonalBnusQuery } from "../../../reactQuery"
import { usePersonalBonusMutation, useClaimPersonalBonusMutation } from "../../../reactQuery/bonusQuery"
import { useUserStore } from "../../../store/useUserSlice"
import { userPersonalBonus } from "../../Accounts/schema"

const usePersonalBonus =() => {
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const [coinType, setCoinType] = useState(  { value: 'GC', label: 'GC' });
  const [promoCode, setPromoCode] = useState('');
  const user = useUserStore((state) => state)
  const {
    register,
    formState: { errors ,isValid,isDirty},
    setError,
    reset,
    handleSubmit,
    setValue,
    watch,
    clearErrors
  } = useForm({
    resolver: yupResolver(userPersonalBonus),
    mode: "onChange",
  })


  const [limit, setLimit] = useState(10)
  const [pageNo, setPageNo] = useState(1)
  const {
    data: betsData,
    isFetching: isBetsLoading,
    refetch,
  } = PersonalBnusQuery.getPersonalBonusListQuery({
    params: {
      limit, page: pageNo
    }
  })

  const mutationPersonalBonus = usePersonalBonusMutation({
    onSuccess: (res) => {
    setIsFormSubmitting(false)

      toast.success(res?.data?.message)
      reset({
        amount: null,
      })
      refetch()
    },
    onError: (err) => {
    setIsFormSubmitting(false)
      if (err?.response?.data?.errors.length > 0) {
        const { errors } = err.response.data
        console.log(errors, 'errors')
        errors.forEach((error) => {
          if (error?.description) {
            // toast.error(error?.description)
          }
        })
      }
    }
  })

  const mutationClaimPersonalBonus = useClaimPersonalBonusMutation({
    onSuccess: (res) => {
      setPromoCode('');
      toast.success(res?.data?.message)
    },
    onError: (err) => {
      if (err?.response?.data?.errors.length > 0) {
        const { errors } = err.response.data
        console.log(errors, 'errors')
        errors.forEach((error) => {
          if (error?.description) {
            // toast.error(error?.description)
          }
        })
      }
    }
  })

  const handlePromoCodeChange = (event) => {
    setPromoCode(event.target.value);
  };

  const handleOnSubmit = useCallback(async (data) => {
    setIsFormSubmitting(true)
    mutationPersonalBonus.mutate({
      amount: data.amount,
      coinType: coinType.value     
    })
  }, [coinType.value])


  const handleClaimOnSubmit = useCallback(async () => {
    mutationClaimPersonalBonus.mutate({
      bonusCode: promoCode,
    })
  }, [promoCode])

  return {
    handleSubmit,
    register,
    handleOnSubmit,
    errors,
    reset,
    watch,
    isValid,
    isFormSubmitting,
    isDirty,
    setCoinType,
    coinType,
    pageNo,
    limit,
    setLimit,
    setPageNo,
    isBetsLoading,
    betsData,
    promoCode,
    handlePromoCodeChange,
    handleClaimOnSubmit,
    setValue,
    user,
    setError,
    clearErrors
  }
}

export default usePersonalBonus
