import React from 'react'
import { useUserStore } from '../../store/useUserSlice'
import useStyles from './../Lobby/Lobby.styles'
import { Button, Grid, List, ListItem, Typography, Divider } from '@mui/material'
import statistic1 from '../../components/ui-kit/icons/webp/statistic-1.webp'
import statistic2 from '../../components/ui-kit/icons/webp/statistic-2.webp'
import referFriend from '../../components/ui-kit/icons/webp/refer-frnd.webp'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import referIcon from '../../components/ui-kit/icons/opImages/refer.webp'
import referIcon2 from '../../components/ui-kit/icons/opImages/refer2.webp'
import referIcon3 from '../../components/ui-kit/icons/opImages/refer3.webp'
import toast from 'react-hot-toast'
import { copyToClipBoard } from '../../utils/helpers'
import { useBannerStore } from '../../store/useBannerSlice'
/* eslint-disable multiline-ternary */

const ReferFriend = () => {
  const classes = useStyles()
  const user = useUserStore((state) => state)
  const { referAfriend } = useBannerStore((state) => state)

  const handleCopyCode = (e) => {
    const isCopySuccessfull = copyToClipBoard(
      `${window.location.origin}/referral?referralcode=${user?.userDetails?.referralCode}`
    )
    if (!isCopySuccessfull) {
      return toast.error('Failed to copy code!')
    }
    toast.success('Refferal link copied!')
    return null
  }

  return (
    <Grid className={classes.lobbyRight}>
      <Grid className={classes.wrapper}>
        <Grid className={classes.referSection}>
          <Grid className={classes.referBanner}>
            {referAfriend ? (
              referAfriend?.map((info) => (
                <Grid className='banner'>
                  <Grid>
                    <Typography className='heading' variant='h5'>
                      {info?.textOne}
                    </Typography>
                    <Typography className='text'>{info?.textTwo}</Typography>
                  </Grid>
                  <Grid className='referImg'>
                    <img src={info?.desktopImageUrl} className='img-1' />
                    <img src={info?.mobileImageUrl} className='img-2' />
                  </Grid>
                </Grid>
              ))
            ) : (
              <>
                <Grid>
                  <Typography className='heading' variant='h5'>
                    INVITE YOUR FRIENDS
                  </Typography>
                  <Typography className='text'>Invite friends</Typography>
                </Grid>
                <Grid className='referImg'>
                  <img src={referFriend} alt='Reffer Friend' sx={{ width: '100%' }} />
                </Grid>
              </>
            )}
          </Grid>

          <Typography className='middleText'>
            Share <span className='gc'>900,000</span> Coins and <span className='sc'>2,500</span> Free Entries with your
            qualified friend!
          </Typography>

          <List className='listItem'>
            <ListItem>Copy the link and send it to your friends.</ListItem>
          </List>

          <Typography className='text1'>
            You can send invitations directly to your friends and also share your referral link on your Facebook,
            Instagram, TikTok, etc.
          </Typography>

          <Typography className='text2'>
            <b>And remember:</b> the bigger your network is, the more rewards you get!
          </Typography>

          <Grid className='referInput'>
            <Grid className='referSelect'>
              <input
                type='text'
                placeholder={`${window.location.origin}/referral?referralcode=${user?.userDetails?.referralCode}`}
                disabled
              />
            </Grid>
            <Button type='button' className='createBonusBtn' onClick={handleCopyCode}>
              Copy
            </Button>
          </Grid>

          <Typography variant='h5' sx={{ margin: '20px 0' }}>
            <b>HOW DOES IT WORK?</b>
          </Typography>

          <Grid className='howToWork'>
            <Grid className='grid1'>
              <img src={referIcon} />
              <Typography variant='h6'>You send an invitation</Typography>
              <Typography>Invite your friend to join Money Factory using referral link.</Typography>
            </Grid>

            <Grid className='grid1'>
              <img src={referIcon2} />
              <Typography variant='h6'>Your friend joins and get qualified</Typography>
              <Typography>
                Your friend should join Money Factory by clicking on the link shared by you and purchase Coin packages.{' '}
              </Typography>
            </Grid>

            <Grid className='grid1'>
              <img src={referIcon3} />
              <Typography variant='h6'>You send an invitation</Typography>
              <Typography>
                You receive <span>500,000</span> Coins and <span>2,000</span> Free Entries. Your friend gets a reward of{' '}
                <span>400,000</span> Coins and <span>500</span> Free Entries.
              </Typography>
            </Grid>
          </Grid>

          <Grid className='friendStatics'>
            <Grid>
              <Typography variant='h6'>
                <b>FRIENDS STATISTICS</b>
              </Typography>
            </Grid>

            <Grid className='sectionGroup'>
              <Grid className='section1'>
                <Grid>
                  <img src={statistic1} alt='img1' />
                </Grid>
                <Grid className='section2'>
                  <Typography>
                    <b>0</b>
                  </Typography>
                  <Typography className='text3'>Friends invited</Typography>
                </Grid>
              </Grid>

              <Grid className='section1'>
                <Grid>
                  <img src={statistic2} alt='img2' />
                </Grid>
                <Grid>
                  <Typography className='section2'>
                    <b>0</b>
                  </Typography>
                  <Typography className='text3'>Friends qualified</Typography>
                </Grid>
              </Grid>

              <Divider className='divider' />

              <Grid className='section1'>
                <Grid>
                  <img src={usdchipIcon} alt='img3' />
                </Grid>

                <Grid>
                  <Typography className='section2'>
                    <b style={{ color: '#AAD840' }}>0</b>
                  </Typography>
                  <Typography className='text3'>Coins earned</Typography>
                </Grid>
              </Grid>

              <Grid className='section1'>
                <Grid>
                  <img src={usdIcon} alt='im4' />
                </Grid>

                <Grid className='section2'>
                  <Typography>
                    <b style={{ color: '#AAD840' }}>0</b>
                  </Typography>
                  <Typography className='text3'>Entries earned</Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default ReferFriend
