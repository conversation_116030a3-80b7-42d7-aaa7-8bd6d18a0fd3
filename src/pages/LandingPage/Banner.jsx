import React, { useState, memo } from 'react'
import '../../../src/App.css'
import { Grid, Typography, Button, Box } from '@mui/material'
import { getLoginToken } from '../../utils/storageUtils'
import { useUserStore } from '../../store/useUserSlice'
import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import { EffectCoverflow, Autoplay } from 'swiper/modules'
import BannerImage1 from '../../components/ui-kit/icons/banner/banner.webp'
import { useBannerStore } from '../../store/useBannerSlice'
import useGetDeviceType from '../../utils/useGetDeviceType'
import CountDownTimer from '../../components/CountDownTimer'
import Signup from '../../components/Modal/Signup'
import { bannerRouteConst } from '../../utils/cmsConstants'
import { useNavigate } from 'react-router-dom'
import { usePortalStore } from '../../store/store'
/* eslint-disable multiline-ternary */
const Banner = () => {
  const navigate = useNavigate()
  //  const auth = useUserStore((state) => state);
  const auth = useUserStore((state) => state)
  const { isMobile } = useGetDeviceType()
  const { lobbySlider } = useBannerStore((state) => state)
  const [imageIsLoaded, setImageIsLoaded] = useState(false)
  const portalStore = usePortalStore()

  const handleRedirection = (key) => {
    if (getLoginToken() || auth?.isAuthenticate) {
      navigate(bannerRouteConst[key])
    } else {
      portalStore.openPortal(() => <Signup />, 'signupModal')()
    }
  }

  const MemoizedSwiperSlide = memo(({ info, index }) => (
    <Grid className='bannerLobbySlider' onClick={() => handleRedirection(info?.btnRedirection)}>
      <img
        src={isMobile ? info.mobileImageUrl : info.desktopImageUrl}
        alt={info.textOne || 'Banner Image'}
        loading={index === 0 ? 'eager' : 'lazy'}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          position: 'absolute',
          top: 0,
          left: 0
        }}
        onLoad={() => setImageIsLoaded(true)}
      />
      <Grid className='bannerTextLobbySlider'>
        <Box>
          <Typography>
            {info.isCountDown && new Date(info?.endDate) > new Date(info?.startDate) ? (
              <CountDownTimer eventDateTime={info.endDate} />
            ) : null}
          </Typography>
        </Box>
        <Typography>
          {info.textOne}
          <br />
          <span style={{ color: '#FDB72E' }}>{info.textTwo}</span>
          <br />
          {info.textThree}
        </Typography>
        {info.btnText ? (
          <Button
            className='becomePartnerLobbySlider'
            onClick={() => handleRedirection(info?.btnRedirection)}
            style={{ zIndex: 2 }}
          >
            {info.btnText}
          </Button>
        ) : null}
      </Grid>
    </Grid>
  ))

  return (
    <Grid container spacing={0.3} className='lobby-banner-wrap'>
      <Grid item xs={12}>
        <Swiper
          effect='coverflow'
          grabCursor
          centeredSlides
          loop
          slidesPerView='1'
          slideshadows='true'
          autoplay={{ delay: 5000 }}
          spaceBetween={100}
          modules={[Autoplay, EffectCoverflow]}
          className='mySwiper'
        >
          {lobbySlider?.length > 0 ? (
            lobbySlider.map((info, index) => (
              <SwiperSlide className='lobby-slider-section' key={info.pageBannerId}>
                <MemoizedSwiperSlide info={info} index={index} key={info.pageBannerId} />
              </SwiperSlide>
            ))
          ) : (
            <SwiperSlide>
              {!imageIsLoaded ? (
                <div className='skeleton-banner' />
              ) : (
                <img
                  src={BannerImage1}
                  alt='banner-default'
                  height='100%'
                  width='100%'
                  loading='eager'
                  onLoad={() => setImageIsLoaded(true)}
                />
              )}
            </SwiperSlide>
          )}
        </Swiper>
      </Grid>
    </Grid>
  )
}

export default Banner
