import React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, IconButton, Typography } from '@mui/material'
import { Close as CloseIcon } from '@mui/icons-material'
import useStyles from './style'
import { usePortalStore } from '../../store/userPortalSlice'
import Vault from '.'
import vaultBanner from '../../components/ui-kit/icons/banner/vault-banner.webp'
import ImageRenderer from '../../components/ImageRenderer'

const VaultDescription = () => {
  const portalStore = usePortalStore((state) => state)
  const handleOnClose = () => {
    portalStore.closePortal()
    portalStore.openPortal(() => <Vault />, 'valutModal')
  }

  const classes = useStyles()
  return (
    <DialogContent className={classes.valutModal} sx={{ padding: '0' }}>
      <ImageRenderer src={vaultBanner} alt='Banner' className='vault-banner' />
      <IconButton edge='end' color='inherit' onClick={handleOnClose} aria-label='close' className='vault-close-icon'>
        <CloseIcon sx={{ fontSize: '40px' }} />
      </IconButton>

      <Grid className='vault-detail-wrap'>
        <Typography className='vault-description'>
          The Vault is a special feature that allows players to secure their Gold Coins and Sweepstakes Coins for
          safekeeping.{' '}
        </Typography>

        <Typography className='vault-description'>
          If a player plans to take a long break from the website, they can vault their coins to ensure they do not
          expire.{' '}
        </Typography>

        <Typography className='vault-description'>
          Once vaulted, these coins are protected indefinitely and will remain in the vault until the player chooses to
          withdraw them.
        </Typography>
      </Grid>
    </DialogContent>
  )
}

export default VaultDescription
