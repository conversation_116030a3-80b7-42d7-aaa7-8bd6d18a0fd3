import { Grid, Box, DialogContent, Typography } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './TimeBreakPopup.style'
import closeIcon from '../../components/ui-kit/icons/png/sidebar-cross.png'
import Dynamo2 from '../../components/ui-kit/icons/opImages/dynamo2.webp'
import moment from 'moment/moment'

const TimeBreakPopup = (timeBreakData) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const handleClose = () => {
    portalStore.closePortal()
  }
  const userLocalTime = moment.utc(timeBreakData?.timeBreakData).local().format('Do MMMM YYYY, hh:mm A')
  console.log(userLocalTime)
  return (
    <Grid className={classes.TimeBreakPopup}>
      <DialogContent sx={{ padding: '0' }}>
        <Box sx={{ width: '100%' }} style={{ padding: '0' }} className={classes.modalWrapper}>
          <img className='close-icon' onClick={handleClose} src={closeIcon} alt='Close' />
          <img
            className='bet-img'
            style={{ maxWidth: '300px' }}
            src={Dynamo2}
            height={100}
            width={100}
            alt='idle-bg'
            loading='lazy'
          />

          <Box className='bet-body'>
            <Typography variant='h5' textAlign='center'>
              Time Break
            </Typography>
            <Typography sx={{ color: '#FFFFFF' }} className='idle-text idle-text2 ' variant='h6'>
              Hey! You are on break until {userLocalTime}
            </Typography>
          </Box>
        </Box>
      </DialogContent>
    </Grid>
  )
}

export default TimeBreakPopup
