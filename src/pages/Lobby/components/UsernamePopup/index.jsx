import React, { useState } from 'react'
import useStyles from '../../../../components/Modal/Signin/Signin.styles'
import {
  Button,
  Grid,
  Box,
  IconButton,
  TextField,
  Typography,
  DialogContent,
  Link,
  CircularProgress
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { userUsernameCheckSchema } from '../lobbySchema'
import { useUserNameExistMutation } from '../../../../reactQuery'

import { usePortalStore } from '../../../../store/userPortalSlice'
import Signin from '../../../../components/Modal/Signin'
import PropTypes from 'prop-types'
import DialogTitle from '@mui/material/DialogTitle'

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

const UsernamePopup = ({ mutation, userData }) => {
  const classes = useStyles()
  const [username, setUsername] = useState('')
  const portalStore = usePortalStore((state) => state)

  const {
    register,
    formState: { errors },
    handleSubmit: handleUsernameSubmit,
    setError
  } = useForm({ resolver: yupResolver(userUsernameCheckSchema), mode: 'onChange' })

  const onUsernameSubmit = (data) => {
    if (data.username !== ' ') {
      setUsername(data.username)
      usernameExistMutation.mutate({ username: data.username })
    }
  }

  const usernameExistMutation = useUserNameExistMutation({
    onSuccess: (res) => {
      if (res?.data?.user) {
        if (res?.data?.user?.isUserNameExist) {
          setError('username', { type: 'focus', message: 'Username already exists.' }, { shouldFocus: true })
        } else {
          if (mutation) {
            mutation.mutate({ ...userData, username })
          }
          handleClose()
        }
      }
    },
    onError: (err) => {
      if (err?.response?.data?.errors.length > 0) {
        const { errors } = err.response.data
        errors.forEach((error) => {
          if (error?.description) {
            // toast.error(error?.description)
          }
        })
      }
    }
  })

  const handleClose = () => {
    portalStore.closePortal()
  }
  const openLogin = () => {
    portalStore.openPortal(() => <Signin />, 'loginModal')
  }

  return (
    <>
      <Grid>
        <Grid>
          <DialogContent sx={{ padding: '0' }}>
            <Grid className='modal-section'>
              <Grid sx={{ width: '100%' }}>
                <DialogTitle sx={{ m: 0, p: 2 }} id='customized-dialog-title'>
                  Create your username
                </DialogTitle>
                <IconButton
                  aria-label='close'
                  onClick={handleClose}
                  sx={{
                    position: 'absolute',
                    right: 8,
                    top: 8,
                    color: (theme) => theme.palette.grey[500]
                  }}
                >
                  <CloseIcon />
                </IconButton>
                <form onSubmit={handleUsernameSubmit(onUsernameSubmit)}>
                  <Box
                    sx={{ width: '100%' }}
                    style={{ marginTop: '20px', marginBottom: '20px' }}
                    className={classes.modalWrapper}
                  >
                    <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                      <TextField
                        id='outlined-basic'
                        label=''
                        variant='outlined'
                        placeholder='Enter a Username'
                        {...register('username')}
                      />
                      {errors?.username && <p className={classes.errorLabel}>{errors?.username?.message}</p>}
                    </Grid>
                  </Box>

                  <Box className={classes.bottomSection}>
                    <Grid className={classes.submitBtn}>
                      <Button variant='contained' type='submit' disabled={mutation.isLoading}>
                        {usernameExistMutation.isLoading ? (
                          <CircularProgress size={24} style={{ marginRight: 8 }} />
                        ) : (
                          <span>Continue</span>
                        )}
                      </Button>
                    </Grid>

                    <Grid className={classes.dontAccount}>
                      <Typography>
                        Already a member ? <Link onClick={openLogin}> Sign in here</Link>
                      </Typography>
                    </Grid>
                  </Box>
                </form>
              </Grid>
            </Grid>
          </DialogContent>
        </Grid>
      </Grid>
    </>
  )
}

export default UsernamePopup
