import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  btnGradientWrap: {
    marginTop: theme.spacing(2),
    '& .btn-gradient': {
      '&.MuiButtonBase-root': {
        background: theme.colors.primaryGradient,
        boxShadow: theme.shadows[1],
        borderRadius: '30px',
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: 'relative',
        overflow: 'hidden',
        padding: theme.spacing(0.375, 1.25),
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '&:before': {
          position: 'absolute',
          width: '700px',
          height: '100%',
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: '1rem 1rem'
        },
        '& .btn-span': {
          position: 'relative',
          color: theme.colors.white,
          zIndex: '2',
          fontWeight: theme.typography.fontWeightSemiBold,
          display: 'flex'
        },
        '&:hover': {
          backgroundColor: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder
        }
      }
    }
  },
  promoCodeTable: {
    '& table': {
      [theme.breakpoints.down('sm')]: {},
      '& thead': {
        '& tr': {
          '& th': {
            color: theme.colors.osloGrey,
            fontSize: '1rem',
            fontStyle: 'normal',
            fontWeight: 700,
            lineHeight: 'normal',
            borderBottom: 'none',
            '&:nth-child(2)': {
              [theme.breakpoints.down('md')]: {},
              [theme.breakpoints.down('sm')]: {}
            },
            '&:nth-child(3)': {
              [theme.breakpoints.down('sm')]: {}
            },
            '&:nth-child(4)': {
              [theme.breakpoints.down('sm')]: {}
            }
          }
        }
      },
      '& tbody': {
        '& tr': {
          '&:nth-of-type(odd)': {
            background: `${theme.colors.GreenishCyan}`
          },
          '& td': {
            color: theme.colors.textWhite,
            fontSize: '1rem',
            fontStyle: 'normal',
            fontWeight: 400,
            lineHeight: 'normal',
            borderBottom: 'none',
            '&:first-child': {
              borderRadius: theme.spacing(3.5, 0, 0, 3.5)
            },
            '&:last-child': {
              borderRadius: theme.spacing(0, 3.5, 3.5, 0)
            },
            '&:nth-child(2)': {
              [theme.breakpoints.down('md')]: {},

              [theme.breakpoints.down('sm')]: {}
            },
            '&:nth-child(3)': {
              [theme.breakpoints.down('sm')]: {}
            },
            '&:nth-child(4)': {
              [theme.breakpoints.down('sm')]: {}
            }
          }
        }
      }
    }
  },
  redeemModal: {
    '& .MuiFormControlLabel-label': {
      fontSize: '0.7rem'
    },
    '& .leftSection': {
      flexGrow: 1,
      [theme.breakpoints.down('sm')]: {
        width: '100%'
      }
    },
    '& .inputWrap': {
      marginBottom: theme.spacing(1),
      marginTop: theme.spacing(0.5),
      '& .textAmount': {
        // '&:hover':{
        color: 'yellow'
        // }
      }
    },
    '& .MuiTypography-heading': {
      color: theme.colors.textWhite,
      fontWeight: 'bold',
      fontSize: theme.spacing(1.5)
    },
    '& .MuiFormLabel-root': {
      color: theme.colors.textWhite,
      fontWeight: '500',
      marginBottom: theme.spacing(0.313)
    },
    '& .redeemImg': {
      width: '40%',
      textAlign: 'right',
      padding: '1rem',
      '& img': {
        width: '100%'
      },
      [theme.breakpoints.down('sm')]: {
        display: 'none'
      }
    },
    '& .MuiTextField-root, & .MuiOutlinedInput-root': {
      width: '100%',
      '& input': {
        color: '#fff !important',
        width: '-webkit-fill-available',
        border: '1px solid #293937',
        padding: '0px 14px',
        borderRadius: '0.25rem',
        backgroundColor: 'transparent',
        height: '35px',

        '&.Mui-disabled': {
          background: theme.colors.disabledInput,
          textFillColor: theme.colors.textWhite
        }
      }
    },
    '& .MuiSvgIcon-root': {
      color: '#293937'
    },
    '& .btn-wrap': {
      '& button': {
        color: theme.colors.textBlack,
        padding: '0.375rem 2rem',
        fontSize: '1rem',
        minWidth: '115px',
        background: '#FDB72E',
        fontWeight: 700,
        borderRadius: '7.8rem',
        textTransform: 'uppercase',
        width: '65%'
      },
      '& .cancel-redeem-btn': {
        width: '35%',
        [theme.breakpoints.down('sm')]: {
          width: '60%'
        }
      }
    },
    '& .leftText': {
      flexGrow: '1',
      [theme.breakpoints.down('sm')]: {
        width: '100%'
      },
      '& h4': {
        fontWeight: 'bold',
        fontSize: theme.spacing(1.8)
      }
    },
    '& .rightImage': {
      width: '100%',
      textAlign: 'right',
      [theme.breakpoints.down('sm')]: {
        display: 'none'
      },
      '& img': {
        width: '100%',
        paddingLeft: '1rem'
      }
    },

    '& .input-grp2': {
      display: 'flex',
      position: 'relatve',
      '& .MuiTextField-root, & .MuiOutlinedInput-root': {
        '& input': {
          // borderTopRightRadius: '0 !important',
          // borderBottomRightRadius: '0 !important',
          background: theme.colors.inputBg,
          borderRadius: theme.spacing(0.625)
        },
        '& .MuiOutlinedInput-notchedOutline': {
          borderRadius: theme.spacing(0.625)
        },
        '& .MuiInputBase-root': {
          '&.Mui-focused': {
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: theme.colors.YellowishOrange
            }
          }
        }
      },
      '& .inner-btn': {
        minWidth: 'unset',
        color: theme.colors.textBlack,
        padding: '0.375rem 2rem',
        fontSize: '1rem',
        background: '#FDB72E',
        fontWeight: 700,
        borderRadius: theme.spacing(0, 0.313, 0.313, 0),
        textTransform: 'uppercase',
        position: 'absolute',
        // right: "4px",
        // top: "1px",
        right: '1px',
        top: '0px',
        '&.Mui-disabled': {
          background: theme.colors.disabledBtn
        }
      }
    },
    '& .themeCheckBoxWrap': {
      '& label': {
        '& .MuiFormControlLabel-label': {
          fontSize: theme.spacing(0.9)
        }
      }
    }
  },
  innerModal: {
    background: theme.spacing.innerModal,
    backdropFilter: 'blur(20px) drop-shadow(2px 4px 6px black)',
    overflow: 'hidden',
    position: 'relative',
    '& .MuiPaper-root': {
      borderRadius: theme.spacing(0.625),
      backdropFilter: 'blur(14px)',
      backgroundColor: theme.colors.textBlack,
      color: theme.colors.textWhite,
      minWidth: '600px',
      maxWidth: '600px',
      [theme.breakpoints.down('sm')]: {
        minWidth: '95%'
      }
    },
    '& .kyc-content': {
      textAlign: 'center',
      padding: theme.spacing(2),
      '& .MuiTypography-h4': {
        fontWeight: theme.typography.fontWeightBold,
        marginBottom: theme.spacing(0.625)
      },
      '& .btn-wrap': {
        margin: theme.spacing(1, 0, 0)
      }
    },
    '& .btn-wrap-fiat': {
      textAlign: 'center',
      '& .pay-with-fiat-btn': {
        border: 'none',
        background: theme.colors.primaryGradient,
        color: theme.colors.white,
        fontWeight: theme.typography.fontWeightBold,
        fontSize: theme.spacing(1),
        margin: theme.spacing(0.625, 0),
        cursor: 'pointer',
        padding: theme.spacing(0.6, 1),
        borderRadius: '30px'
      }
    },
    '& .inner-modal-header': {
      borderRadius: theme.spacing(0.625, 0.625, 0, 0),
      background: theme.colors.primaryGradient,
      padding: theme.spacing(1, 2),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      '& .MuiTypography-h4': {
        color: theme.colors.white,
        fontWeight: theme.typography.fontWeightExtraBold,
        fontSize: theme.spacing(1.375),
        textTransform: 'capitalize',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1)
        }
      },

      '& .modal-close': {
        '& .MuiButtonBase-root': {
          background: theme.colors.white,
          padding: '0.625rem',
          borderRadius: theme.spacing(0.625),
          height: theme.spacing(1.75),
          width: theme.spacing(1.75),
          [theme.breakpoints.down('sm')]: {
            marginRight: '0'
          },
          '& svg': {
            color: theme.colors.themeText,
            fontSize: theme.spacing(1.5)
          },
          '&:hover': {
            '& svg': {
              color: theme.colors.highlighColor
            }
          }
        }
      }
    },
    '& .user-detail-modal': {
      padding: '0',
      position: 'relative',
      '& .inner-modal-right-bg': {
        position: 'absolute',
        bottom: '0',
        right: '0',
        [theme.breakpoints.down('sm')]: {
          display: 'none'
        },
        '& img': {
          width: '250px'
        }
      }
    },
    '& .user-name-content-wrap': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      position: 'relative',
      '&.user-detail': {
        display: 'block',
        padding: theme.spacing(2),
        '& .inner-modal-left': {
          '& .username-top-content': {
            marginBottom: theme.spacing(2),
            '& .MuiTypography-h4': {
              fontSize: theme.spacing(1.375),
              fontWeight: theme.typography.fontWeightBold,
              marginBottom: theme.spacing(1)
            },
            '& .MuiTypography-body1': {
              fontSize: theme.spacing(0.875),
              maxWidth: '400px'
            },
            '& .waiting-icon': {
              width: theme.spacing(6),
              height: theme.spacing(6),
              margin: '1rem auto',
              [theme.breakpoints.down('sm')]: {
                width: theme.spacing(4),
                height: theme.spacing(4)
              }
            }
          },
          '& .username-bottom-content': {
            maxWidth: '300px',
            '& .MuiFormControl-root': {
              width: '100%',
              '& .MuiInputBase-root': {
                background: theme.colors.authCardBg,
                borderRadius: '10px',
                height: '46px',
                '& .MuiInputBase-input': {
                  height: '46px',
                  padding: theme.spacing(0, 1),
                  color: theme.colors.white,
                  display: 'flex',
                  alignItems: 'center'
                }
              },
              '& .MuiFormLabel-root': {
                top: '-5px',
                color: theme.colors.white
              },
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.colors.secondryBtnBorder
                  }
                }
              }
            },
            '& .input-error': {
              color: theme.colors.error,
              fontSize: `${theme.spacing(0.8)}!important`,
              marginBottom: `${theme.spacing(1.5)} !important`,
              lineHeight: 'normal !important',
              minHeight: '16px',
              '&.phone-input-error': {
                marginTop: '5px',
                marginBottom: '0',
                marginLeft: 'auto',
                maxWidth: '300px'
              }
            },
            '& .input-inline': {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              '& .MuiFormControl-root': {
                width: 'calc(50% - 10px)'
              }
            },
            '& .input-inline-row': {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              '& .MuiFormControl-root': {
                width: 'calc(33% - 10px)'
              }
            },
            '& svg': {
              color: theme.colors.white
            },
            '& .MuiTypography-body1': {
              marginBottom: theme.spacing(0.313),
              color: theme.colors.white,
              fontSize: theme.spacing(0.75)
            },
            '& .MuiFormLabel-root': {
              color: theme.colors.themeText,
              fontSize: theme.spacing(0.875),
              fontWeight: theme.typography.fontWeightMedium,
              marginBottom: theme.spacing(0.313),
              display: 'block'
            },
            '& .country-code-select': {
              width: '100px',
              marginRight: theme.spacing(1)
            },
            '& .auth-input-inner': {
              display: 'flex'
            }
          }
        }
      },

      '& .btn-wrap': {
        marginTop: theme.spacing(2),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      },

      '& .inner-modal-left': {
        minWidth: '400px',
        [theme.breakpoints.down('sm')]: {
          minWidth: '100%'
        }
      },
      '& .MuiFormLabel-root': {
        fontSize: theme.spacing(1),
        color: theme.colors.themeText,
        marginBottom: theme.spacing(0.313),
        display: 'block'
      },
      '& .otp-btn-wrap': {
        display: 'flex',
        justifyContent: 'center',
        margin: theme.spacing(1.5, 0, 1),
        '& .MuiButtonBase-root': {
          margin: theme.spacing(0, 0.313)
        }
      },
      '& .phone-verified': {
        color: theme.colors.white,
        '& .MuiTypography-h3': {
          fontWeight: theme.typography.fontWeightBold,
          fontSize: theme.spacing(1.5),
          textTransform: 'capitalize'
        }
      }
    },
    '& .payment-modal-content': {
      '& .payment-modal-text': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        '& .MuiTypography-body1': {
          marginLeft: theme.spacing(0.313),
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.875)
          }
        },
        '& .payment-logo': {
          width: '65px'
        }
      },
      '& .order-summary-card': {
        padding: theme.spacing(1, 4),
        marginTop: theme.spacing(2),
        [theme.breakpoints.down('sm')]: {
          padding: theme.spacing(1, 0)
        },
        '& .MuiTypography-h3': {
          fontSize: theme.spacing(1.875),
          fontWeight: theme.typography.fontWeightBold,
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1.5)
          }
        },
        '& .order-summary-content': {
          margin: theme.spacing(0.625, 0),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          '& .MuiTypography-body1': {
            fontSize: theme.spacing(1.375),
            fontWeight: theme.typography.fontWeightMedium,
            color: theme.colors.themeText,
            textTransform: 'capitalize',
            display: 'flex',
            alignItems: 'center',
            '& a': {
              display: 'flex',
              marginLeft: theme.spacing(0.313)
            },
            '&.order-total': {
              fontWeight: theme.typography.fontWeightBold
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(1)
            }
          }
        },
        '& .order-total-card': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          margin: theme.spacing(1, 0),
          background: theme.colors.paymentTotalBg,
          borderRadius: theme.spacing(1.25),
          padding: theme.spacing(0.625, 3),
          '& .MuiTypography-body1': {
            fontSize: theme.spacing(1.375),
            fontWeight: theme.typography.fontWeightMedium,
            margin: '0',
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(1)
            }
          },
          '& .MuiTypography-h4': {
            fontSize: theme.spacing(2.5),
            background: theme.colors.textGradient,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: theme.typography.fontWeightBold,
            margin: '0',
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(1)
            }
          }
        },
        '& .order-coins-wrap': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          '& img': {
            width: theme.spacing(2),
            height: theme.spacing(2),
            margin: theme.spacing(0, 0.625)
          }
        },
        '& .payment-method-section': {
          marginTop: theme.spacing(2),
          '& > .MuiTypography-body1': {
            textTransform: 'capitalize',
            fontSize: theme.spacing(1.375),
            textAlign: 'center',
            marginBottom: theme.spacing(1),
            color: theme.colors.white
          },

          // "& .fiat-pay-btn" : {
          //     flexDirection : 'row-reverse',
          // },
          '& .payment-method-card': {
            display: 'flex',
            cursor: 'pointer',
            alignItems: 'center',
            justifyContent: 'space-between',
            background: theme.colors.white,
            borderRadius: theme.spacing(1.875),
            padding: theme.spacing(0.2, 1),
            marginBottom: theme.spacing(1),
            position: 'relative',
            minHeight: '55px',
            '& .payment-method-icon': {
              display: 'flex',
              alignItems: 'center',
              '& img': {
                margin: theme.spacing(0, 0.5),
                width: '30px'
              }
            }
          },
          '& .payment-method-right': {
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            '& .MuiTypography-root': {
              color: theme.colors.authCardBg,
              fontWeight: theme.typography.fontWeightSemiBold,
              fontSize: theme.spacing(1),
              marginRight: theme.spacing(0.625)
            }
          },
          '& .payment-btn-wrap': {
            textAlign: 'center',
            margin: theme.spacing(3, 0)
          }
        },
        '& .MuiTypography-h4': {
          fontSize: theme.spacing(1),
          color: theme.colors.accordianIcon,
          marginBottom: theme.spacing(0.625)
        },
        '& .MuiTypography-body1': {
          fontSize: theme.spacing(0.875),
          color: theme.colors.accordianIcon,
          textAlign: 'left'
        }
      },
      '& .MuiOutlinedInput-root': {
        borderRadius: theme.spacing(0.625)
      },
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none'
      }
    },
    '& .payment-status-content': {
      paddingBottom: theme.spacing(3),
      textAlign: 'center',
      '& .payment-status-icon': {
        width: theme.spacing(6),
        height: theme.spacing(6),
        margin: '1rem auto',
        [theme.breakpoints.down('sm')]: {
          width: theme.spacing(4),
          height: theme.spacing(4)
        }
      },
      '& .MuiTypography-h4': {
        color: theme.colors.white,
        fontSize: theme.spacing(1.8),
        fontWeight: theme.typography.fontWeightBold,
        textTransform: 'capitalize',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(1.2)
        }
      },
      '& p': {
        color: theme.colors.white,
        textTransform: 'capitalize'
      }
    },
    '& .auth-link-text': {
      marginTop: theme.spacing(0.625),
      cursor: 'pointer',
      '& .MuiTypography-body1': {
        color: theme.colors.themeText,
        '& a': {
          fontWeight: theme.typography.fontWeightExtraBold,
          background: theme.colors.textGradient,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: theme.colors.themeText,
          textDecoration: 'none',
          paddingLeft: theme.spacing(0.313)
        }
      }
    },
    '& .modal-loader': {
      position: 'absolute',
      width: '100%',
      height: '100%',
      top: '0',
      left: '0',
      background: theme.colors.modalLoaderBg,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: '2',
      backdropFilter: 'blur(20px)',
      fontSize: theme.spacing(2),
      '& p': {
        color: theme.colors.white
      },
      '& .daily-bonus-modal-wrap': {
        minWidth: '900px'
      }
    }
  },
  authInputWrap: {
    position: 'relative',
    marginBottom: theme.spacing(1),
    '& .MuiFormControl-root': {
      width: '100%',
      '& .MuiInputBase-root': {
        background: theme.colors.authCardBg,
        borderRadius: '10px',
        height: '46px',
        '& .MuiInputBase-input': {
          height: '46px',
          padding: theme.spacing(0, 1),
          color: theme.colors.white,
          display: 'flex',
          alignItems: 'center'
        }
      },
      '& .MuiFormLabel-root': {
        top: '-5px',
        color: theme.colors.white
      },
      '& .MuiOutlinedInput-root': {
        '&.Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: theme.colors.secondryBtnBorder
          }
        }
      }
    },
    '& .input-error': {
      color: theme.colors.red,
      fontSize: theme.spacing(0.75),
      fontWeight: theme.typography.fontWeightMedium,
      marginTop: '0',
      // position:"absolute",
      '&.phone-input-error': {
        marginTop: '5px',
        marginBottom: '0',
        marginLeft: 'auto',
        maxWidth: '300px'
      }
    },
    '& .input-inline': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      '& .MuiFormControl-root': {
        width: 'calc(50% - 10px)'
      }
    },
    '& .input-inline-row': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      '& .MuiFormControl-root': {
        width: 'calc(33% - 10px)'
      }
    },
    '& svg': {
      color: theme.colors.white
    },
    '& .MuiTypography-body1': {
      marginBottom: theme.spacing(0.313),
      color: theme.colors.white,
      fontSize: theme.spacing(0.75)
    },
    '& .MuiFormLabel-root': {
      color: theme.colors.themeText,
      fontSize: theme.spacing(0.875),
      fontWeight: theme.typography.fontWeightMedium,
      marginBottom: theme.spacing(0.313),
      display: 'block'
    },
    '& .country-code-select': {
      width: '100px',
      marginRight: theme.spacing(1)
    },
    '& .auth-input-inner': {
      display: 'flex'
    }
  },
  btnWhiteGradient: {
    transition: 'all 0.3s ease 0s',
    '& .btn-gradient': {
      '&.MuiButtonBase-root': {
        background: theme.colors.btnSecondaryBg,
        boxShadow: theme.shadows[1],
        borderRadius: '30px',
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: 'relative',
        overflow: 'hidden',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '&:before': {
          position: 'absolute',
          width: '700px',
          height: '100%',
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: '1rem 1rem'
        },
        '& span': {
          position: 'relative',
          color: theme.colors.authCardBg,
          zIndex: '2',
          fontWeight: theme.typography.fontWeightSemiBold
        },
        '&:hover': {
          background: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
          '& span': {
            color: theme.colors.white
          }
        }
      }
    }
  }
}))
