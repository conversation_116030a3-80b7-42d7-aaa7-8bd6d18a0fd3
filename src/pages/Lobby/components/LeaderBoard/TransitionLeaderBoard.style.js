import { Box } from '@mui/material';
import { styled } from '@mui/system';

import { leaderBoardContainer } from '../../../../MainPage.styles';

export const TransitionLeaderBoard = styled(Box)(({ theme }) => {
  return {
    // "& .leaderBoardContainer": {
    //   "& table": {
    //     [theme.breakpoints.down('sm')]: {
    //       display: 'none',
    //     },
    //     "& thead": {
    //       "& tr": {

    //         "& th": {
    //           color: theme.colors.osloGrey,
    //           fontSize: "10px",
    //           fontStyle: "normal",
    //           fontWeight: 700,
    //           lineHeight: "normal",
    //           borderBottom: "none",
    //           padding:'4px 2px 4px 0',
    //           // width:'90px',
    //           wordWrap:'word-break',
    //           "&:nth-child(2)": {
    //             [theme.breakpoints.down('md')]: {
    //               display: "none",
    //             },
    //             [theme.breakpoints.down('sm')]: {
    //               isplay: "none",
    //             },
    //           },
    //           "&:nth-child(3)": {
    //             [theme.breakpoints.down('sm')]: {
    //               display: "none",
    //             },
    //           },
    //           "&:nth-child(4)": {
    //             [theme.breakpoints.down('sm')]: {
    //               display: "none",
    //             },
    //           }
    //         }
    //       }
    //     },
    //     "& tbody": {
    //       "& tr": {
    //         "&:nth-of-type(odd)": {
    //           background: `${theme.colors.GreenishCyan}`,
    //         },
    //         "& td": {
    //           color: theme.colors.textWhite,
    //           fontSize: "9px",
    //           fontStyle: "normal",
    //           fontWeight: 400,
    //           lineHeight: "normal",
    //           borderBottom: "none",
    //           padding:'4px 2px',
    //           "&:first-child": {
    //              borderRadius: theme.spacing(3.5, 0, 0, 3.5)
    //           },
    //           "&:last-child": {
    //              borderRadius: theme.spacing(0, 3.5, 3.5, 0)
    //           },
    //           "&:nth-child(2)": {
    //             [theme.breakpoints.down('md')]: {
    //               display: "none",
    //             },

    //             [theme.breakpoints.down('sm')]: {
    //               display: "none",
    //             },
    //           },
    //           "&:nth-child(3)": {
    //             [theme.breakpoints.down('sm')]: {
    //               display: "none",
    //             },
    //           },
    //           "&:nth-child(4)": {
    //             [theme.breakpoints.down('sm')]: {
    //               display: "none",
    //             },
    //           }
    //         }
    //       }
    //     }
    //   }

    // },
    leaderBoardContainer: {
      ...leaderBoardContainer(theme),
    },
    "& .parent": {
      margin: theme.spacing(1.85, 0),
    },

    "& .tabsDesign": {
      borderRadius: theme.spacing(2.1875),
      border: `2px solid ${theme.colors.GreenishCyan}`,
      padding: theme.spacing(0.31),
      width: 'fit-content',
      "& .MuiTabs-root": {
        minHeight: 'auto',
      },
      "& .MuiTabs-indicator": {
        display: 'none'
      },
      "& button": {
        color: theme.colors.textWhite,
        cursor: "pointer",
        padding: "0.63rem 1.5rem",
        position: "relative",
        fontSize: "1rem",
        transform: "none",
        transition: "none",
        fontWeight: 'bold',
        minHeight: 'auto !important',
        "&.Mui-selected": {
          background: theme.colors.YellowishOrange,
          fontWeight: '700',
          borderRadius: '4.1875rem',
          color: theme.colors.textBlack
        },
        "&:hover": {

        }
      }
    },
    "& .selectOption": {
      borderRadius: theme.spacing(2.1875),
      background: theme.colors.GreenishCyan,
      padding: theme.spacing(0, 1.5),
      color: theme.colors.textWhite,
      fontSize: "1rem",
      borderRight: `10px solid ${theme.colors.GreenishCyan} !important`,
      border: 'none',
    }

  }
})
