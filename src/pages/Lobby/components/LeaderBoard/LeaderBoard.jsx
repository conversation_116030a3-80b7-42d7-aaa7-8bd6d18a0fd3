import * as React from 'react'
import { Grid, useTheme, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'
import { LeaderBoardContainer } from './LeaderBoard.styles'

function createData(name, calories, fat, carbs, protein, data) {
  return { name, calories, fat, carbs, protein, data }
}

const rows = [
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00'),
  createData('Big Six wheel', 'Sabroon', '02:10 pm', '1x', '4.0', '300.00')
]

export default function LeaderBoard() {
  const theme = useTheme()
  return (
    <LeaderBoardContainer theme={theme}>
      <TableContainer>
        <Grid className='leaderBoardContainer'>
          <Table aria-label='a dense table'>
            <TableHead>
              <TableRow>
                <TableCell style={{ flex: 1 }}>Game</TableCell>
                <TableCell style={{ flex: 1 }}>User</TableCell>
                <TableCell style={{ flex: 1 }}>Time</TableCell>
                <TableCell style={{ flex: 1 }}>Bet Amount</TableCell>
                <TableCell style={{ flex: 1 }}>Multiplier </TableCell>
                <TableCell style={{ flex: 1 }}>Payout </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rows.map((row) => (
                <TableRow key={row.name} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                  <TableCell style={{ flex: 1 }} scope='row'>
                    {row.name}
                  </TableCell>
                  <TableCell style={{ flex: 1 }}>{row.calories}</TableCell>
                  <TableCell style={{ flex: 1 }}>{row.fat}</TableCell>
                  <TableCell style={{ flex: 1 }}>{row.carbs}</TableCell>
                  <TableCell style={{ flex: 1 }}>{row.protein}</TableCell>
                  <TableCell style={{ flex: 1 }}>{row.data}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Grid>
      </TableContainer>
    </LeaderBoardContainer>
  )
}
