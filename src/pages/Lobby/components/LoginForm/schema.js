import * as Yup from 'yup'

export const userLogInSchema = Yup.object().shape({
  email: Yup.string()
    .test('is-email', 'Invalid email address', (value) => {
      if (!value) return true
      const emailRegex =
        /^(([^<>()[\]\\.,+;:\s@"]+(\.[^<>()[\]\\.,+;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return emailRegex.test(value)
    })
    .required('Please enter an email address')
    .max(64, 'Email must not be longer than 64 characters'),
  password: Yup.string()
    .required('Password must contain at least 8 characters')
    .min(8, 'Password must contain at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
})
const onlySpacesRegex = /^\s*$/
const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/
export const userLogInMobileSchema = Yup.object().shape({
  phoneCode: Yup.number().required('Phone code is required'),
  phoneNumber: Yup.string()
    .matches(phoneRegExp, 'Phone number is not valid')
    .required('Phone number is required')
    .min(8)
    .max(10)
    .test('no-only-spaces', 'Phone number cannot contain spaces', (value) => {
      return !onlySpacesRegex.test(value)
    }),
  password: Yup.string()
    .required('Password must contain at least 8 characters')
    .min(8, 'Password must contain at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
})
