import * as Yup from 'yup'
const onlySpacesRegex = /^\s*$/;

export const userUsernameCheckSchema = Yup.object().shape({
    username: Yup.string()
        .required('Username is required')
        .min(5, 'Username cannot be less than 5 characters')
        .max(20, 'Username cannot be more than 20 characters')
        .test('no-only-spaces', 'Username cannot contain only spaces', (value) => {
            return !onlySpacesRegex.test(value);
        }).matches(/^[a-zA-Z][a-zA-Z0-9_.]{2,10}[a-zA-Z0-9_]$/, 'Username cannot contain special character and must be 5 to 20 characters long')

})
