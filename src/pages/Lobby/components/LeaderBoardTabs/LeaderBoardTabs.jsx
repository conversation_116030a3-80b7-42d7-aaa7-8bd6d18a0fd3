import * as React from 'react'
import { Grid, Tab, Tabs, Box, useTheme } from '@mui/material'
import { LeaderBoardContainer } from './LeaderBoardTabs.styles'
import LeaderBoard from '../LeaderBoard/LeaderBoard'

export default function LeaderBoardTabs() {
  const theme = useTheme()
  const [value, setValue] = React.useState(0)
  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  return (
    <LeaderBoardContainer theme={theme}>
      <Grid className='parent'>
        <Grid>
          <Box className='tabsDesign'>
            <Tabs
              value={value}
              onChange={handleChange} // Enables scrolling
            >
              <Tab label='My Plays' />
            </Tabs>
          </Box>
          <select className='selectOption'>
            <option value='10'>10</option>
            <option value='20'>20</option>
            <option value='30'>30</option>
            <option value='40'>40</option>
            <option value='50'>50</option>
            <option value='60'>60</option>
            <option value='70'>70</option>
            <option value='80'>80</option>
            <option value='90'>90</option>
            <option value='100'>100</option>
          </select>
        </Grid>
      </Grid>

      <LeaderBoard />
    </LeaderBoardContainer>
  )
}
