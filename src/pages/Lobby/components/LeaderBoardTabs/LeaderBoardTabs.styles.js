import { Box } from '@mui/material';
import { styled } from '@mui/system';

export const LeaderBoardContainer = styled(Box)(({ theme }) => {
  return {
    "& .parent": {
      margin: theme.spacing(1.85, 0),
    },

    "& .tabsDesign": {
      borderRadius: theme.spacing(2.1875),
      border: `2px solid ${theme.colors.GreenishCyan}`,
      padding: theme.spacing(0.31),
      width: 'fit-content',
      [theme.breakpoints.down('sm')]: {
        width: "100% !important",
      },
      "& .MuiTabs-root": {
        minHeight: 'auto',
      },
      "& .MuiTabs-indicator": {
        display: 'none'
      },
      "& button": {
        color: theme.colors.textWhite,
        cursor: "pointer",
        padding: "0.63rem 1.5rem",
        position: "relative",
        fontSize: "1rem",
        transform: "none",
        transition: "none",
        fontWeight: 'bold',
        minHeight: 'auto !important',
        "&.Mui-selected": {
          background: theme.colors.YellowishOrange,
          fontWeight: '700',
          borderRadius: '4.1875rem',
          color: theme.colors.textBlack
        },
        "&:hover": {

        }
      }
    },
    "& .selectOption": {
      borderRadius: theme.spacing(2.1875),
      background: theme.colors.GreenishCyan,
      padding: theme.spacing(0, 1.5),
      color: theme.colors.textWhite,
      fontSize: "1rem",
      borderRight: `10px solid ${theme.colors.GreenishCyan} !important`,
      border: 'none',
    }

  }
})
