import React, { useEffect } from 'react'
import { loadLottieScript } from '../../../../utils/loadLottieScript'

function TimerAnimation () {
  useEffect(() => {
    loadLottieScript()
  }, [])
  return (
    <>
      <lottie-player
        src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/Gift.json`}
        background='transparent'
        speed='1'
        autoplay
        style={{ width: '100%', height: '100%' }}
      />
    </>
  )
}

export default TimerAnimation
