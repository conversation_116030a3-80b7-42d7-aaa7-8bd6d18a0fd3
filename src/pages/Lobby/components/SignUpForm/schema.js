import * as Yup from 'yup'
const onlySpacesRegex = /^\s*$/
export const userSignUpSchema = Yup.object().shape({
  email: Yup.string()
    .test('is-email', 'Invalid email address', (value) => {
      if (!value) return true
      const emailRegex =
        /^(([^<>()[\]\\.,+;:\s@"]+(\.[^<>()[\]\\.,+;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      // const emailRegex = /^(([^<>()[\]\\.,+;:\s@/\\"]+(\.[^<>()[\]\\.,+;:\s@/\\"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return emailRegex.test(value)
    })
    .required('Please enter an email address')
    .max(64, 'Email must not be longer than 64 characters'),
  password: Yup.string()
    .required('Please enter your password')
    .min(8, 'Password must contain at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~`!@#$%^&*()_\-+={[}\]|:;"'<>,.?/]).{8,20}$/,
      'Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character'
    )
})

const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/

export const userMobileSchema = Yup.object().shape({
  phoneCode: Yup.number().required('Phone code is required'),
  phoneNumber: Yup.string()
    .matches(phoneRegExp, 'Phone number is not valid')
    .required('Phone number is required')
    .min(8)
    .max(10)
    .test('no-only-spaces', 'Phone number cannot contain spaces', (value) => {
      return !onlySpacesRegex.test(value)
    }),
  password: Yup.string()
    .required('Password must contain at least 8 characters')
    .min(8, 'Password must contain at least 8 characters')
    .max(20, 'Password must not contain more than 20 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~`!@#$%^&*()_\-+={[}\]|:;"'<>,.?/]).{8,20}$/,
      'Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character'
    )
})
