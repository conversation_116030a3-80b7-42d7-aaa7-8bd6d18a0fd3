import React from 'react'
import useStyles from '../../../../components/Modal/Signin/Signin.styles'
import CloseIcon from '@mui/icons-material/Close'
import { useUserStore } from '../../../../store/useUserSlice'
import { useResendEmailMutation } from '../../../../reactQuery'
import { usePortalStore } from '../../../../store/userPortalSlice'
import { useNavigate } from 'react-router-dom'
import PropTypes from 'prop-types'
import DialogTitle from '@mui/material/DialogTitle'
import { Button, Grid, Box, IconButton, Typography, DialogContent } from '@mui/material'
import toast from 'react-hot-toast'

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}
const EmailVerificationPopup = ({ userDetails }) => {
  const classes = useStyles()
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)
  const user = useUserStore((state) => state)
  const resendVerificationMail = useResendEmailMutation({
    onSuccess: (res) => {
      toast.success('Verification mail has been sent successfully')
    },
    onError: (err) => {
      if (err?.response?.data?.errors.length > 0) {
        const { errors } = err.response.data
        errors.forEach((error) => {
          if (error?.description) {
            // toast.error(error?.description)
          }
        })
      }
    }
  })
  const handleResendEmail = () => {
    resendVerificationMail.mutate({ email: userDetails?.email })
  }

  const handleClose = () => {
    user.logout()
    navigate('/')
    portalStore.closePortal()
  }

  return (
    <Grid>
      <Grid>
        <DialogContent sx={{ padding: '0' }}>
          <Grid className='modal-section'>
            <Grid sx={{ width: '100%' }}>
              <DialogTitle sx={{ m: 0, p: 2 }} id='customized-dialog-title'>
                <Typography variant='h4'>Action Required</Typography>
              </DialogTitle>
              <IconButton
                aria-label='close'
                onClick={handleClose}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: (theme) => theme.palette.grey[500]
                }}
              >
                <CloseIcon />
              </IconButton>
              <Box sx={{ width: '100%' }} style={{ margin: '10px', color: 'white' }} className={classes.modalWrapper}>
                Your email Id is not verified
              </Box>

              <Box className={classes.bottomSection}>
                <Grid className={classes.submitBtn}>
                  <Button variant='contained' onClick={handleResendEmail}>
                    <span>Send Email</span>
                  </Button>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
      </Grid>
    </Grid>
  )
}

export default EmailVerificationPopup
