import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({

  btnWhiteGradient: {
    transition: "all 0.3s ease 0s",
    "& .btn-gradient": {
      "&.MuiButtonBase-root": {
        background: theme.colors.btnSecondaryBg,
        boxShadow: theme.shadows[1],
        borderRadius: "30px",
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: "relative",
        overflow: "hidden",
        padding: theme.spacing(0.375, 1.5),
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        "&:before": {
          position: "absolute",
          width: "700px",
          height: "100%",
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: "1rem 1rem",
        },
        "& span": {
          position: "relative",
          color: theme.colors.authCardBg,
          zIndex: "2",
          fontWeight: theme.typography.fontWeightSemiBold,
        },
        "&:hover": {
          background: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
          "& span": {
            color: theme.colors.white,
          }
        }
      },
    }
  },
  btnGradientWrap: {
    marginTop: theme.spacing(2),
    "& .btn-gradient": {
      "&.MuiButtonBase-root": {
        background: theme.colors.primaryGradient,
        boxShadow: theme.shadows[1],
        borderRadius: "30px",
        fontSize: theme.spacing(1),
        color: theme.colors.authCardBg,
        position: "relative",
        overflow: "hidden",
        padding: theme.spacing(0.375, 1.25),
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        "&:before": {
          position: "absolute",
          width: "700px",
          height: "100%",
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: "1rem 1rem",
        },
        "& .btn-span": {
          position: "relative",
          color: theme.colors.white,
          zIndex: "2",
          fontWeight: theme.typography.fontWeightSemiBold,
          display: "flex",
        },
        "&:hover": {
          backgroundColor: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
        },
      },
    },
  },
  authModal: {
    background: theme.colors.modalBg,
    backdropFilter: "drop-shadow(2px 4px 6px black)",
    "& .MuiPaper-root": {
      background: "transparent",
    },
    "& .auth-header": {
      background: "transparent",
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      padding: theme.spacing(0.6, 1),
      boxShadow: "none",
      "& .modal-close": {
        "& .MuiButtonBase-root": {
          background: theme.colors.authCardBg,
          padding: "0.625rem",
          borderRadius: theme.spacing(0.625),
          height: theme.spacing(2.5),
          width: theme.spacing(2.5),
          marginRight: theme.spacing(1),
          [theme.breakpoints.down('sm')]: {
            marginRight: "0",
          },
          "& svg": {
            color: theme.colors.themeText
          },
          "&:hover": {
            "& svg": {
              color: theme.colors.white
            },
          }
        }
      },
      "&.MuiPaper-elevation": {
        backdropFilter: "none !important",
        background: "transparent !important"
      },
    },
    "& .authContentWrap": {
      maxWidth: "400px",
      margin: "4rem auto",
      padding: theme.spacing(2, 0),
      position: "relative",
      zIndex: "2",
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      height: "100%",
      color: theme.colors.textWhite,
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(1),
      },
      "& .authThemeCard": {
        background: theme.colors.authCardBg,
        borderRadius: theme.spacing(0.625),
        display: "flex",
        padding: theme.spacing(1.45, 1.5),
        position: "relative",
        marginBottom: theme.spacing(2),
        [theme.breakpoints.down('sm')]: {
          padding: theme.spacing(1),
        },
        "& .MuiTypography-h4": {
          color: theme.colors.white,
          fontWeight: theme.typography.fontWeightMedium,
          fontSize: theme.spacing(0.875),
          maxWidth: "60%",
          [theme.breakpoints.down('sm')]: {
            maxWidth: "50%",
            fontSize: theme.spacing(0.75),
          },
          "& span": {
            fontWeight: theme.typography.fontWeightExtraBold,
            background: theme.colors.textGradient,
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: theme.colors.themeText,
          }
        },
        "& .auth-img": {
          position: "absolute",
          right: "-20px",
          top: "-31px",
          [theme.breakpoints.down('sm')]: {
            right: "0px",
            top: "-12px",
          },
          "& img": {
            width: "200px",
            [theme.breakpoints.down('sm')]: {
              width: "160px",
            },
          }
        },
      }
    },
    "& .MuiPaper-elevation": {
      backdropFilter: "none !important",
      background: "transparent !important",
      borderRadius:'10px',
    }
  },
  authInputWrap: {
    position: "relative",
    marginBottom: theme.spacing(1),
    "& .MuiInputBase-root": {
      width: "100%",
      background: theme.colors.authCardBg,
      borderRadius: theme.spacing(0.625),
      height: "46px",
      paddingRight: "0",
      color: theme.colors.textWhite,

      "& .MuiInputAdornment-root": {
        position: "absolute",
        right: "20px",
        zIndex: "1"
      },
      "& .MuiInputBase-input": {
        padding: theme.spacing(0, 1),
        height: "46px",
        color: theme.colors.textWhite
      },
      "&:hover": {
        "& .MuiOutlinedInput-notchedOutline": {
          borderColor: theme.colors.themeHighlight,
        }
      },
    },
    "& .MuiFormControl-root": {
      width: "100%",
      "& .MuiInputBase-root": {
        background: theme.colors.authCardBg,
        borderRadius: "10px",
        height: "46px",

        "& .MuiInputBase-input": {
          height: "46px",
          padding: theme.spacing(0, 1),
          color: theme.colors.white
        },
        "&:hover": {
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.colors.themeHighlight,
          }
        },
      },

      "& .MuiFormLabel-root": {
        top: "-5px",
        color: theme.colors.white
      },
      "& .MuiOutlinedInput-root": {
        "&.Mui-focused": {
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.colors.secondryBtnBorder,
          }
        }
      }
    },

    "& .input-error": {
      color: theme.colors.error,
      fontSize: `${theme.spacing(0.8)}!important`,
      marginBottom: `${theme.spacing(1.5)} !important`,
      lineHeight: 'normal !important',
      minHeight: '16px',
    },
    "& .input-inline": {
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      "& .MuiFormControl-root": {
        width: "calc(50% - 10px)",
      },
    },
    "& .input-inline-row": {
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      "& .MuiFormControl-root": {
        width: "calc(33% - 10px)",
      },
    },
    "& svg": {
      color: theme.colors.white
    },
    "& .MuiTypography-body1": {
      marginBottom: theme.spacing(0.313),
      color: theme.colors.white,
      fontSize: theme.spacing(0.75)
    },
    "& .MuiOutlinedInput-root": {
      "&.Mui-focused": {
        "& .MuiOutlinedInput-notchedOutline": {
          borderColor: theme.colors.secondryBtnBorder,
        }
      }
    }
  },
  authInputCta: {
    "& .MuiButtonBase-root": {
      width: "100%",
      fontWeight: theme.typography.fontWeightMedium,
      padding: theme.spacing(0.5, 0.625),
    }
  },
  authLinking: {
    textAlign: "center",
    margin: theme.spacing(1, 0),
    "& a": {
      color: theme.colors.themeText,
      textDecoration: "none",
      fontSize: theme.spacing(0.875),
      fontWeight: theme.typography.fontWeightMedium,
      "&:hover": {
        color: theme.colors.themeHighlight
      }
    },

  },
  loginWithSocail: {
    marginTop: theme.spacing(2),
    "& .login-with-heading": {
      marginBottom: theme.spacing(1),
      "& .MuiTypography-body1": {
        textAlign: "center",
        color: theme.colors.themeText,
        "& span": {
          position: "relative",
          "&:before, &:after ": {
            display: "inline-block",
            content: "''",
            borderStyle: "solid",
            borderWidth: "2px 0 0 0",
            borderColor: theme.colors.authCardBg,
            width: theme.spacing(6),
            margin: theme.spacing(0, 1),
            transform: "translateY(-3px)",
            [theme.breakpoints.down('sm')]: {
              width: theme.spacing(4),
            },
          },

        }

      }
    },
    "& .auth-socail-btns": {
      background: theme.colors.authCardBg,
      marginBottom: theme.spacing(1),
      width: "100%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      borderRadius: theme.spacing(0.625),
      minHeight: "46px",
      "& img": {
        marginRight: theme.spacing(0.625),
      },
      "&:hover": {
        background: theme.colors.primaryGradient,
        color: theme.colors.white,
      }
    },
    "& .auth-link-text": {
      "& .MuiTypography-body1": {
        textAlign: "center",
        color: theme.colors.themeText,
        "& a": {
          fontWeight: theme.typography.fontWeightExtraBold,
          background: theme.colors.textGradient,
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: theme.colors.themeText,
          textDecoration: "none",
          paddingLeft: theme.spacing(0.313),
        }
      }
    }
  },
  // SIGNUP STYLE FROM HERE
  signUpWrap: {
    "& .signup-with-social-wrap": {
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      [theme.breakpoints.down('sm')]: {
        flexDirection: "column",
      },
      "& .auth-socail-btns": {
        background: theme.colors.authCardBg,
        marginBottom: theme.spacing(1),
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: theme.spacing(0.625),
        minHeight: "46px",
        [theme.breakpoints.down('sm')]: {
          width: "100%",
        },
        "& img": {
          marginRight: theme.spacing(0.313),
          width: '18px'
        },
        "&:hover": {
          background: theme.colors.primaryGradient,
          color: theme.colors.white,
        }
      },
    },
    "& .auth-link-text": {
      marginTop: theme.spacing(1),
      "& .MuiTypography-body1": {
        textAlign: "center",
        color: theme.colors.themeText,
        "& a": {
          fontWeight: theme.typography.fontWeightExtraBold,
          background: theme.colors.textGradient,
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: theme.colors.themeText,
          textDecoration: "none",
          paddingLeft: theme.spacing(0.313),
        }
      }
    }
  },
  regWithMail: {
    "& .login-with-heading": {
      marginBottom: theme.spacing(1),
      "& .MuiTypography-body1": {
        textAlign: "center",
        color: theme.colors.themeText,
        "& span": {
          position: "relative",
          "&:before, &:after ": {
            display: "inline-block",
            content: "''",
            borderStyle: "solid",
            borderWidth: "2px 0 0 0",
            borderColor: theme.colors.authCardBg,
            width: theme.spacing(6),
            margin: theme.spacing(0, 1),
            transform: "translateY(-3px)",
            [theme.breakpoints.down('xxl')]: {
              width: theme.spacing(4),
            },
            [theme.breakpoints.down('sm')]: {
              width: theme.spacing(3),
            },
          },
        }

      }
    },

  },
  themeCheckBoxWrap: {
    marginBottom: theme.spacing(1),
    "& .MuiTypography-root": {
      color: theme.colors.white,
      fontSize: theme.spacing(0.75)
    },
    "& .MuiCheckbox-root": {
      color: theme.colors.textWhite,
      borderRadius: "5px",
      "&.Mui-checked": {
        color: theme.colors.textWhite,
      }
    },
    "& .input-error": {
      color: theme.colors.error,
      fontSize: `${theme.spacing(0.8)}!important`,
      marginBottom: `${theme.spacing(1.5)} !important`,
      lineHeight: 'normal !important',
      minHeight: '16px',
    }
  }
}))
