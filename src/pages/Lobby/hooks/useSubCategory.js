// import { useState, useEffect, useRef } from 'react'

// import { SubCategoryConstants } from '../../../components/SideBar/constants'
// import { CasinoQuery } from '../../../reactQuery'
// import {
//   useSelectedProviderStore,
//   useSearchDialogStore,
//   useSelectedSubCategoryStore,
//   useSubCategoryOnLoadStore,
//   useFavoriteGamesStore,
//   useGamesStore
// } from '../../../store/store'
// import { getCookie, setCookie } from '../../../utils/cookiesCollection'
// import { dynamicMerge, updateFavoriteGames } from '../../../utils/helpers'

// function useSubCategory () {
//   const onLoadGameApi = getCookie('onloadGameApi')
//   // React Query
//   const { data: providerData } = CasinoQuery.getProviderListQuery()

//   // Zustand Store
//   const selectedSubCat = useSelectedSubCategoryStore((state) => state.selectedSubCat)
//   const selectedProviderStore = useSelectedProviderStore((state) => state)
//   const state = useSubCategoryOnLoadStore((state) => state)
//   const stateFavBySearch = useSearchDialogStore((state) => state)
//   const { setSubCategories } = useSubCategoryOnLoadStore((state) => state)
//   const { favorites } = useFavoriteGamesStore((state) => state)
//   // gamesStore
//   const { setFavoriteGamesList, setRecentGamesList, recentGames } = useGamesStore((state) => state)

//   // Component State
//   const [selectedSubCategory, setSelectedSubcategory] = useState(null)
//   const [limit, setLimit] = useState(21)
//   const [pageNo, setPageNo] = useState(1)
//   const [search, setSearch] = useState('')
//   const [selectedProvider, setSelectedProvider] = useState({ value: '', label: '' })
//   const [options, setOptions] = useState([])
//   const [featuredSubcategory, setFeaturedSubcategory] = useState([])
//   const [subCategoryName, setSubCategoryName] = useState('Lobby')
//   const [selectedSubCategoryId, setSelectedSubCategoryId] = useState()
//   const [gameData, setGameData] = useState([])
//   const [gameDataCount, setGameDataCount] = useState()
//   const [gameDataLoading, setGameDataLoading] = useState(false)
//   const [favoriteIds, setFavoriteIds] = useState({})

//   // Component Ref
//   const previousSubCategoryName = useRef(subCategoryName)
//   const previousSubCategoryId = useRef(selectedSubCategoryId)

//   const featuredSubcategoryNames = featuredSubcategory?.map((x) => x?.name)

//   useEffect(() => {
//     if (
//       previousSubCategoryName.current !== subCategoryName &&
//       subCategoryName !== 'all-games' &&
//       subCategoryName !== SubCategoryConstants.ALL_GAMES
//     ) {
//       setPageNo(1)
//       const subcategoryData = state.subCategories?.filter((x) => x?.name === subCategoryName)[0]?.subCategoryGames
//       const subcategoryId = state.subCategories?.filter((x) => x?.name === subCategoryName)[0]?.masterGameSubCategoryId
//       const totalGames = state.subCategories?.filter((x) => x?.name === subCategoryName)[0]?.totalGames
//       setSelectedSubCategoryId(subcategoryId)
//       setGameData(subcategoryData)
//       setGameDataCount(totalGames)
//       previousSubCategoryName.current = subCategoryName
//       previousSubCategoryId.current = selectedSubCategoryId
//     }
//   }, [subCategoryName])

//   useEffect(() => {
//     if (providerData && providerData.length > 0) {
//       const defaultData = [{ value: '', label: 'All' }]
//       const prodata = providerData?.map((data) => ({
//         value: data?.masterCasinoProviderId,
//         label: data?.name
//       }))
//       setOptions([...defaultData, ...prodata])
//     }
//   }, [providerData])

//   useEffect(() => {
//     if (!stateFavBySearch.isDialog && stateFavBySearch.isFavBySearch) {
//       if (selectedSubCat === SubCategoryConstants.ALL_GAMES) {
//         stateFavBySearch.setFavBySearch(false)
//         subcategoryListMutation.mutate({ subCategoryId: 0, limit: limit, page: pageNo })
//       } else if (selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED) {
//         stateFavBySearch.setFavBySearch(false)
//         recentGameListMutation.mutate({ limit: limit, page: pageNo })
//       } else if (selectedSubCat === SubCategoryConstants.FAVORITE_GAMES) {
//         stateFavBySearch.setFavBySearch(false)
//         favGameListMutation.mutate({ limit: limit, page: pageNo })
//       } else if (featuredSubcategoryNames.includes(selectedSubCat)) {
//         const subId = featuredSubcategory?.find((x) => x?.name === selectedSubCat)?.masterGameSubCategoryId
//         stateFavBySearch.setFavBySearch(false)
//         subcategoryListMutation.mutate({ subCategoryId: subId, limit: limit, page: pageNo })
//       } else if (selectedSubCat === 'Lobby') {
//         stateFavBySearch.setFavBySearch(false)
//         subcategoryListMutation.mutate()
//       }
//     }
//   }, [stateFavBySearch, selectedSubCat])

//   /* This api trigger when subcategory */
//   useEffect(() => {
//     if (
//       selectedSubCat !== SubCategoryConstants.FAVORITE_GAMES &&
//       selectedSubCat !== SubCategoryConstants.RECENTLY_PLAYED
//     ) {
//       if (subCategoryName === SubCategoryConstants.ALL_GAMES) {
//         subcategoryListMutation.mutate({ subCategoryId: 0, limit: limit, page: pageNo })
//       } else if (featuredSubcategoryNames.includes(selectedSubCat)) {
//         const subId = featuredSubcategory?.find((x) => x?.name === selectedSubCat)?.masterGameSubCategoryId
//         subcategoryListMutation.mutate({ subCategoryId: subId, limit: limit, page: pageNo })
//       } else if (subCategoryName === 'Lobby' && !onLoadGameApi) {
//         subcategoryListMutation.mutate()
//       } else {
//         setCookie('onloadGameApi', false)
//       }
//     }
//   }, [subCategoryName, onLoadGameApi])

//   const handleLoadMore = () => {
//     let filterData = {}
//     if (Math.ceil(gameDataCount / limit) > pageNo) {
//       setPageNo(pageNo + 1)
//       if (
//         selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED ||
//         selectedSubCat === SubCategoryConstants.FAVORITE_GAMES
//       ) {
//         filterData = {
//           limit: limit,
//           page: pageNo + 1
//         }
//       } else {
//         filterData = {
//           subCategoryId: subCategoryName !== SubCategoryConstants.ALL_GAMES ? selectedSubCategoryId : 0,
//           limit: limit,
//           page: pageNo + 1
//         }
//       }
//       if (selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED) {
//         recentGameListMutation.mutate(filterData)
//       } else if (selectedSubCat === SubCategoryConstants.FAVORITE_GAMES) {
//         favGameListMutation.mutate(filterData)
//       } else {
//         subcategoryListMutation.mutate(filterData)
//       }
//     } else return undefined
//   }

//   /* This api triggeer only when user select provider button on lobby */
//   const onLobbyProviderChange = (selectedProviderKey) => {
//     selectedProviderStore.setSelectedProviderId(selectedProviderKey)
//     const data = options?.find((x) => x?.value === selectedProviderKey)
//     setSelectedProvider(data)
//   }

//   /* success response of subcategoryListMutation */
//   const successToggler = (data) => {
//     if (subCategoryName === SubCategoryConstants.ALL_GAMES) {
//       const newData = data?.data?.data?.rows
//       const newArray = updateFavoriteGames(favorites, gameData)
//       const updatedGameData = dynamicMerge(newArray || [], newData || [], 'masterCasinoGameId')
//       setGameData(updatedGameData)
//       setGameDataCount(data?.data?.data?.count)
//       // gamesStore
//       // setGamesList(updatedGameData);
//       // setGamesCount(data?.data?.data?.count);
//       setGameDataLoading(false)
//     } else if (subCategoryName === 'Lobby') {
//       const newData = data?.data?.data
//       const updatedFeaturedSubcategory = dynamicMerge(
//         featuredSubcategory || [],
//         newData || [],
//         'masterGameSubCategoryId',
//         'subCategoryGames',
//         'masterCasinoGameId'
//       )
//       setFeaturedSubcategory(updatedFeaturedSubcategory)
//       setSubCategories(updatedFeaturedSubcategory)
//     } else {
//       const newData1 = data?.data?.data[0]?.subCategoryGames
//       const newArray = updateFavoriteGames(favorites, gameData)
//       const updatedGameData = dynamicMerge(newArray || [], newData1 || [], 'masterCasinoGameId')
//       setGameData(updatedGameData)
//       setGameDataCount(data?.data?.data[0]?.totalGames)
//       // gamesStore
//       // setGamesList(updatedGameData);
//       // setGamesCount(data?.data?.data[0]?.totalGames);
//       setGameDataLoading(false)
//     }
//   }

//   /* error response of subcategoryListMutation */
//   const errorToggler = () => {
//     setGameDataLoading(false)
//   }

//   /* subcategoryListMutation api hit  */
//   const subcategoryListMutation = CasinoQuery.useSubcategoryListMutation({
//     successToggler,
//     errorToggler
//   })

//   /* Start of recent games */
//   const recentSuccessToggler = (data) => {
//     const newData = data?.data?.data?.rows
//     if (pageNo >= 2) {
//       const newArray = recentGames
//       const updatedGameData = dynamicMerge(newArray || [], newData || [], 'masterCasinoGameId')
//       setGameData(updatedGameData)
//       setRecentGamesList(updatedGameData)
//     } else {
//       setGameData(newData)
//       setRecentGamesList(newData)
//     }
//     setGameDataCount(data?.data?.data?.count)
//     setGameDataLoading(false)
//   }

//   const recentErrorToggler = () => {
//   }

//   const recentGameListMutation = CasinoQuery.useRecentPlayListMutation({
//     successToggler: recentSuccessToggler,
//     errorToggler: recentErrorToggler
//   })
//   /* End of recent games */

//   /* Start of fav games */
//   const favSuccessToggler = (data) => {
//     const newData = data?.data?.data?.rows
//     if (pageNo >= 2) {
//       const newArray = gameData?.filter((game) => favoriteIds[game?.masterCasinoGameId])
//       const updatedGameData = dynamicMerge(newArray || [], newData || [], 'masterCasinoGameId')
//       setGameData(updatedGameData)
//       setFavoriteGamesList(updatedGameData)
//     } else {
//       setGameData(newData)
//       setFavoriteGamesList(newData)
//     }
//     setGameDataCount(data?.data?.data?.count)
//     // gamesStore
//     // setGamesList(updatedGameData);
//     // setGamesCount(data?.data?.data?.count);
//     setGameDataLoading(false)
//   }

//   const favErrorToggler = () => {
//   }

//   const favGameListMutation = CasinoQuery.useFavGameListMutation({
//     successToggler: favSuccessToggler,
//     errorToggler: favErrorToggler
//   })
//   /* End of fav games */

//   useEffect(() => {
//     let filterData = {}
//     setGameData([])
//     setPageNo(1)
//     if (previousSubCategoryName.current !== selectedSubCat) {
//       filterData = { limit: limit, page: 1 }
//     } else {
//       filterData = { limit: limit, page: pageNo }
//     }
//     if (selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED) {
//       recentGameListMutation.mutate(filterData)
//     } else if (selectedSubCat === SubCategoryConstants.FAVORITE_GAMES) {
//       favGameListMutation.mutate(filterData)
//     }
//   }, [selectedSubCat])

//   useEffect(() => {
//     if (state.subCategories) {
//       setFeaturedSubcategory(state.subCategories?.filter((x) => x?.isFeatured === true))
//     }
//   }, [state.subCategories])

//   const handleSearch = (event) => {
//     setSearch(event.target.value)
//   }

//   return {
//     subCategories: state.subCategories,
//     selectedSubCategory,
//     setSelectedSubcategory,
//     handleSearch,
//     search,
//     setLimit,
//     setPageNo,
//     isLoading: state.isLoading,
//     refetch: state.refetch,
//     selectedProvider,
//     setSelectedProvider,
//     options,
//     featuredSubcategory,
//     setSubCategoryName,
//     subCategoryName,
//     gameData,
//     gameDataCount,
//     limit,
//     pageNo,
//     handleLoadMore,
//     onLobbyProviderChange,
//     gameDataLoading,
//     setGameDataLoading,
//     setGameData,
//     setGameDataCount,
//     favoriteIds,
//     setFavoriteIds
//   }
// }

// export default useSubCategory
