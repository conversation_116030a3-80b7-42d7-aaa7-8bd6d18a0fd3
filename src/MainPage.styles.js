import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  lobbyWrap: {
    display: 'flex',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      display: 'block'
    },
    '&.msg-header': {
      paddingTop: '1.5rem'
    }
  }
}))
export const mirrorText = (theme) => ({
  fontSize: theme.spacing(4.6875),
  padding: theme.spacing(1),
  color: theme.colors.textWhite,
  fontWeight: theme.typography.fontWeightExtraBold,
  position: 'relative'
  // "&:after": {
  //   content: '"Tech"',
  //   display: "flex",
  //   transform: "rotateX(180deg)",
  //   backgroundImage:
  //     "linear-gradient(180deg,rgba(255,255,255,.0) 10%,rgba(255,255,255,.5))",
  //   WebkitBackgroundClip: "text",
  //   color: "transparent"
  // }
})

export const ButtonPrimary = (theme) => ({
  borderRadius: theme.spacing(7.8),
  minWidth: '130px',
  color: theme.colors.textBlack,
  fontWeight: 'bold',
  fontSize: '16px',
  background: theme.colors.YellowishOrange,
  boxShadow: '0px 0px 12px rgba(203, 184, 186, 0.20)',
  [theme.breakpoints.down('md')]: {
    minWidth: '100px',
    marginLeft: '0',
    padding: '2px 16px'
  }
})

export const ButtonSecondary = (theme) => ({
  borderRadius: theme.spacing(7.8),
  border: `1px solid ${theme.colors.YellowishOrange}`,
  minWidth: '130px',
  fontWeight: theme.typography.fontWeightExtraBold,
  color: theme.colors.textWhite,
  fontSize: theme.spacing(1),
  [theme.breakpoints.down('md')]: {
    minWidth: '100px',
    marginLeft: '0',
    padding: '2px 16px'
  }
})

export const Container = (theme) => ({
  maxWidth: '1200px',
  margin: '0 auto',
  width: '100%',
  padding: theme.spacing(1, 0, 0),
  [theme.breakpoints.down('md')]: {
    paddingBottom: theme.spacing(4)
  }
})

export const InnerBanner = (theme) => ({
  backgroundSize: 'cover',
  minHeight: theme.spacing(20),
  padding: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(1),
  borderRadius: theme.spacing(1),

  [theme.breakpoints.down(1399)]: {
    minHeight: theme.spacing(17)
  },
  [theme.breakpoints.down('lg')]: {
    minHeight: theme.spacing(14)
  },
  [theme.breakpoints.down('md')]: {
    minHeight: theme.spacing(11.875),
    backgroundPosition: '70%',
    padding: theme.spacing(1),
    marginBottom: theme.spacing(3)
    // "&:before": {
    //   position: "absolute",
    //   left: "0",
    //   top: "0",
    //   background: theme.colors.overlayBg,
    //   width: "100%",
    //   height: "100%",
    //   content: "''",
    // }
  },

  '& .banner-content': {
    marginLeft: theme.spacing(4.25),
    [theme.breakpoints.down('md')]: {
      textAlign: 'left'
    },
    [theme.breakpoints.down('lg')]: {
      marginLeft: 0
    },
    '& h2': {
      fontSize: theme.spacing(4.375),
      fontWeight: '700',
      [theme.breakpoints.down(1399)]: {
        fontSize: theme.spacing(3)
      },
      [theme.breakpoints.down('lg')]: {
        fontSize: theme.spacing(2)
      },

      '& span': {
        color: theme.colors.YellowishOrange,
        display: 'block'
      }
    },
    '& p': {
      fontWeight: '700',
      color: theme.colors.textWhite,
      fontSize: theme.spacing(1.4375),
      textTransform: 'uppercase',
      maxWidth: theme.spacing(25),
      [theme.breakpoints.down(1399)]: {
        fontSize: theme.spacing(1)
      },
      [theme.breakpoints.down('lg')]: {
        fontSize: theme.spacing(0.875)
      },
      [theme.breakpoints.down('md')]: {
        fontSize: `${theme.spacing(0.75)} !important`,
        maxWidth: `${theme.spacing(12.5)} !important`
      }
    }
  },
  '& .btn-primary': {
    fontSize: theme.spacing(1.25),
    [theme.breakpoints.down('md')]: {
      fontSize: theme.spacing(0.875)
    }
  }
})

export const LobbyRight = (theme) => ({
  width: 'calc(100% - 260px)',
  marginLeft: '250px',
  // minHeight: "calc(100vh - 260px)",
  marginTop: theme.spacing(5),
  padding: theme.spacing(1, 1.5),
  [theme.breakpoints.down('lg')]: {
    width: '100%',
    marginLeft: '0',
    marginTop: theme.spacing(5.5),
    // marginTop: theme.spacing(7.5),
    padding: theme.spacing(0.313, 1),
    minHeight: 'auto'
  },
  [theme.breakpoints.down('md')]: {
    paddingTop: theme.spacing(0),
    marginTop: theme.spacing(5.5)
  },
  [theme.breakpoints.down('sm')]: {
    paddingTop: theme.spacing(0),
    marginTop: theme.spacing(5)
  }
})

export const BonusHeading = (theme) => ({
  margin: '0',
  fontWeight: 'bold',
  fontSize: '35px',
  lineHeight: '39px',
  color: theme.colors.textWhite
})

export const Bonuspara = (theme) => ({
  margin: '0',
  fontWeight: 300,
  fontSize: '21px',
  lineHeight: '25px',
  color: theme.colors.textWhite,
  marginTop: '20px'
})

export const switchTabWrap = (theme) => ({
  '& .MuiTabs-root': {
    display: 'inline-flex',
    alignItems: 'center',
    background: theme.colors.switchTabBg,
    borderRadius: theme.spacing(1.875),
    padding: theme.spacing(0.375, 0.5625),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(0.5, 0.25),
      minHeight: '30px',
      maxHeight: '38px'
    },
    '& .MuiButtonBase-root': {
      display: 'flex',
      alignItems: 'center',
      borderRadius: theme.spacing(1.875),
      minHeight: 'auto',
      fontSize: theme.spacing(1.125),
      fontWeight: '600',
      position: 'relative',
      zIndex: '2',
      gap: theme.spacing(0.625),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(0.75),
        padding: '8px',
        gap: theme.spacing(0.25)
      },
      '& img': {
        [theme.breakpoints.down('md')]: {
          width: '12px'
        }
      },
      '&.gold-coin-tab': {
        color: theme.colors.YellowishOrange
      },
      '&.shweep-coin-tab': {
        color: theme.colors.Promosuccess
      },
      '& .MuiTab-iconWrapper': {
        marginRight: '0'
      }
    },
    '& .MuiTabs-indicator ': {
      height: '100%',
      borderRadius: theme.spacing(1.875),
      background: theme.colors.tabdButtonBg
    }
  }
})
export const leaderBoardContainer = (theme) => ({
  '& table': {
    [theme.breakpoints.down('sm')]: {
      overflowX: 'auto',
      whiteSpace: 'nowrap'
    },
    '& thead': {
      '& tr': {
        '& th': {
          color: theme.colors.osloGrey,
          fontSize: '1rem',
          fontStyle: 'normal',
          fontWeight: 700,
          lineHeight: 'normal',
          borderBottom: 'none',
          '&:first-child': {
            borderRadius: theme.spacing(0.625, 0, 0, 0.625)
          },
          '&:last-child': {
            borderRadius: theme.spacing(0, 0.625, 0.625, 0)
          },
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(0.75),
            padding: '0.5rem'
          }
        }
      }
    },
    '& tbody': {
      '& tr': {
        borderRadius: theme.spacing(0.625),
        '&:nth-child(even)': {
          boxShadow: `0px 0px 2px inset ${theme.colors.inputBorder}`
        },
        '& td': {
          color: theme.colors.textWhite,
          fontSize: '1rem',
          fontStyle: 'normal',
          lineHeight: 'normal',
          borderBottom: 'none',
          fontWeight: theme.typography.fontWeightSemiBold,
          '&:first-child': {
            borderRadius: theme.spacing(3.5, 0, 0, 3.5)
          },
          '&:last-child': {
            borderRadius: theme.spacing(0, 3.5, 3.5, 0)
          },

          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(0.75),
            padding: '0.5rem'
          }
        }
      }
    }
  }
})

export const casinoCard = (theme) => ({
  position: 'relative',
  transition: 'all 200ms ease-in-out',
  lineHeight: '0',
  '& .fav-icon': {
    width: '20px',
    height: '20px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: '10px',
    right: '10px',
    zIndex: '8',
    '& img': {
      width: '20px',
      height: '20px',
      objectFit: 'contain',
      objectPosition: 'center'
    },
    '&:hover': {
      // backgroundColor: theme.colors.textWhite,
      cursor: 'pointer'
    }
  },
  '& .casinoGame-img': {
    width: '100%',
    // aspectRatio: '2/3',
    borderRadius: '8px',
    height: '100%',
    '&:hover': {
      backgroundColor: theme.colors.textWhite,
      cursor: 'pointer'
    }
  },
  '& .casino-img': {
    width: '100%',
    aspectRatio: '1'
  },
  '&:hover': {
    transform: 'translateY(-0.25rem)',
    '& .casino-overlay': {
      display: 'flex',
      opacity: '1',
      transition: 'all 300ms ease-in-out'
    }
  },
  '& .casino-overlay': {
    position: 'absolute',
    opacity: '0',
    display: 'flex',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    margin: '0 auto',
    inset: '0',
    flexDirection: 'column',
    background: 'linear-gradient(180deg, rgba(255,84,37,0.9) 0%, rgba(251,162,83,0.9) 100%)',
    cursor: 'pointer',
    transition: 'all 200ms ease-in-out',
    borderRadius: '8px',
    color: theme.colors.textWhite,
    '& a': {
      color: theme.colors.textWhite,
      textDecoration: 'none'
    },
    '& h6': {
      color: theme.colors.textWhite
      // wordBreak: 'break-all'
    },
    '& .play-img': {
      width: theme.spacing(3.75),
      height: theme.spacing(3.75),
      margin: '1.25rem auto'
    }
  }
})
