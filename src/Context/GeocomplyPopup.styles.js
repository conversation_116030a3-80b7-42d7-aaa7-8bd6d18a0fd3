import { makeStyles } from '@mui/styles'

import { otpBg, LocationBg } from '../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  //  GEOMODAL CSS
  geoModal: {
    '& .MuiPaper-root': {
      borderRadius: `${theme.spacing(0.375)}!important`,
      background: 'transparent !important',
      overflow: 'visible',
      [theme.breakpoints.down('sm')]: {
        minWidth: '98%'
      }
    },
    '& .MuiDialogContent-root': {
      background: `url(${otpBg})`,
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat',
      borderRadius: theme.spacing(0.375),
      minWidth: theme.spacing(25.75),
      textAlign: 'center',
      overflow: 'visible',
      [theme.breakpoints.down('md')]: {
        minWidth: '100%'
      },
      '& .close-btn': {
        position: 'absolute',
        top: theme.spacing(0.625),
        right: theme.spacing(0.625),
        color: theme.colors.textWhite,
        zIndex: 2
      },
      '& .modal-header': {
        position: 'relative',
        '& .geo-logo-wrap': {
          textAlign: 'center',
          position: 'relative',
          // '&:before': {
          //   position: 'absolute',
          //   left: '50%',
          //   top: '30%',
          //   transform: 'translate(-50%, -50%)',
          //   background: theme.colors.modalEffect,
          //   height: theme.spacing(9),
          //   width: theme.spacing(9),
          //   borderRadius: '100%',
          //   filter: 'blur(30px)',
          //   content: "''"
          // },
          '& img': {
            marginTop: theme.spacing(-4)
          }
        }
      },
      '& .geo-modal-content': {
        maxWidth: theme.spacing(18.75),
        margin: '0 auto',
        '& h4': {
          marginBottom: theme.spacing(1),
          fontSize: theme.spacing(2.1875),
          fontWeight: theme.typography.fontWeightBold,
          background: theme.colors.textGradient,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textTransform: 'uppercase',
          borderBottom: `1px solid ${theme.colors.modalTabBtnActive}`,
          paddingBottom: theme.spacing(0.5),
          marginBOttom: theme.spacing(1),
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(2)
          }
        },
        '& p': {
          color: theme.colors.textWhite,
          fontSize: theme.spacing(1.125),
          marginBottom: theme.spacing(0.625),
          fontWeight: theme.typography.fontWeightBold,
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1)
          },
          '& a': {
            color: theme.colors.YellowishOrange
          }
        },
        '& .region-wrap': {
          padding: theme.spacing(1, 0, 1, 0),
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(0.5, 0, 0.5, 0)
          },
          '& h5': {
            color: theme.colors.greenTextModal,
            fontSize: theme.spacing(1.125),
            fontWeight: theme.typography.fontWeightBold,
            [theme.breakpoints.down('md')]: {
              paddingBottom: '0.5rem'
            }
          },
          '& h6': {
            color: theme.colors.textWhite,
            fontSize: theme.spacing(1.125),
            fontWeight: theme.typography.fontWeightBold
          }
        },
        '& .vpn-card-wrap': {
          textAlign: 'center',
          margin: theme.spacing(0.45, 0, 1, 0),
          '& .vpn-card': {
            fontSize: theme.spacing(0.875),
            fontWeight: theme.typography.fontWeightBold,
            color: theme.colors.vpnRed,
            border: `1px solid ${theme.colors.vpnRed}`,
            borderRadius: theme.spacing(0.313),
            padding: theme.spacing(0.1, 1),
            maxWidth: theme.spacing(9.375),
            margin: '0 auto'
          }
        }
      },
      '&.location-modal': {
        backgroundImage: `url(${LocationBg}) !important`,
        minWidth: `${theme.spacing(24.125)}!important`,
        border: '1px solid #646464',

        [theme.breakpoints.down('sm')]: {
          minWidth: '100% !important'
        },
        borderRadius: '10px',
        '& .geo-logo-wrap': {
          '& img': {
            marginTop: `${theme.spacing(0)} !important`,
            [theme.breakpoints.down('md')]: {
              width: theme.spacing(3.75)
            }
          }
        },
        '& .geo-modal-content': {
          maxWidth: '21.75rem',
          '& h4': {
            fontSize: theme.spacing(1.5625),
            fontWeight: theme.typography.fontWeightBoldBlack,
            background: 'none',
            color: theme.colors.YellowishOrange,
            WebkitTextFillColor: 'inherit',
            marginTop: '1.5rem',
            [theme.breakpoints.down('md')]: {
              fontSize: theme.spacing(1.2)
            }
          },
          '& button': {
            padding: '0.5rem 1.875rem',
            fontSize: theme.spacing(1),
            fontWeight: theme.typography.fontWeightBold
          },
          '& .learn-more': {
            paddingTop: '0.375rem',
            '& a': {
              textDecoration: 'underline',
              color: '#42A1FF',
              '&:hover': {
                textDecoration: 'none'
              },
              '&:active': {
                color: '#42A1FF'
              }
            }
          }
        }
      }
    }
  }
}))
