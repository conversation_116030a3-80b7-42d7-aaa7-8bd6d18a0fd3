import React, { useState } from 'react';
import { Dialog, DialogContent, Typography, Button,  Grid } from '@mui/material';

import { LocationIcon } from '../components/ui-kit/icons/webp';
import useStyles from './GeocomplyPopup.styles';
import { usePortalStore } from '../store/userPortalSlice';
import { useNavigate } from 'react-router-dom';

const GeocomplyLocationPopup = ({ open, locationDenied }) => {
  const classes = useStyles();
  const portalStore = usePortalStore((state) => state);
  const [locationStatus, setLocationStatus] = useState('');
  const navigate = useNavigate()

  const goToHome = () => {
    navigate('/')
    handleClose()
  }
  const handleClose = () => {
    portalStore.closePortal();
  };

  const handleGPS = () => {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          goToHome()
          // setLocationStatus('granted'); 
          // handleClose('/')
        },
        (error) => {
          console.error('Error obtaining location:', error);
          if (error.code === error.PERMISSION_DENIED) {
            // handleClose('/')            
            // open hint wala popup 
          }
        }
      );
    } else {
      setLocationStatus('Geolocation is not supported by your browser.');
      // handleClose('/')
    }
  };

  return (
    <Dialog open={open} className={classes.geoModal}>
      <DialogContent className='location-modal'>
        {/* <IconButton onClick={handleClose} className='close-btn'>
          <CloseIcon />
        </IconButton> */}
        <Grid className='modal-header'>
          <Grid className='geo-logo-wrap'>
            <img src={LocationIcon} alt='Location' />
          </Grid>
        </Grid>
        <Grid className='geo-modal-content'>
          {locationDenied ?
            <>
              <Typography>
                Your browser location permission is denied please allow browser location <a onClick={handleGPS} style={{ cursor: "pointer", textDecoration: "underline" }}>See how</a>
              </Typography>
            </> :
            <>
              <Typography variant='h4'>
                Enhance your <br /> Money Factory Experience!
              </Typography>
              <Typography>
                To provide the best experience, we need access to your location for compliance and personalized service.
              </Typography>
            </>}


          {/* {locationStatus === 'granted' ?
            <Button className='btn btn-secondary' onClick={goToHome}>
              Go to Home
            </Button> : */}
          <Button className='btn btn-primary' variant="contained" onClick={handleGPS}>
            Enable Location
          </Button>
          {/* } */}

          <Typography color='error' style={{ marginTop: '16px' }}>
            {locationStatus}
          </Typography>
        </Grid>
      </DialogContent>
    </Dialog>
  );
};

export default GeocomplyLocationPopup;
