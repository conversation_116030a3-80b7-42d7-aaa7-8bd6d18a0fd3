import * as React from 'react'
import { GEOCOMPLY_REASON_CODE } from '../utils/constants'
import { decryptGeoComplyResponse, getAllowedStates, getLicenseString } from '../utils/apiCalls'
import { useUserStore } from '../store/useUserSlice'
import { useLogOutMutation } from '../reactQuery'
import useLogout from '../hooks/useLogout'
import { usePortalStore } from '../store/userPortalSlice'
import { useGeolocationGameplayUpdate } from '../store/store'
import { useNavigate } from 'react-router-dom'
import GeocomplyPopup from './GeocomplyPopup'
import GeocomplyLocationPopup from './GeocomplyLocationPopup'


const GeoLocationContext = React.createContext()

export const useGeolocation = () => React.useContext(GeoLocationContext)

export const GeolocationController = ({ children }) => {
  const [licenseString, setLicenseString] = React.useState('')
  const [isNotAvailable, setIsNotAvailable] = React.useState(false)
  const [isRegionAllowed, setIsRegionAllowed] = React.useState(false)
  const [encryptedResponse, setEncryptedResponse] = React.useState('')
  const [regeolocateTime, setRegeolocateTime] = React.useState(0)
  const [userIp, setUserIp] = React.useState('')
  const user = useUserStore((state) => state)
  const userDetails = useUserStore((state) => state.userDetails)
  const { logoutHandler } = useLogout()
  const portalStore = usePortalStore((state) => state)
  const setGeocomplyErrorMsg = useGeolocationGameplayUpdate((state) => state.setGeocomplyErrorMsg)
  const setErrorTroubleShoot = useGeolocationGameplayUpdate((state) => state.setErrorTroubleShoot)
  const setGeoErrorResponse = useGeolocationGameplayUpdate((state) => state.setGeoErrorResponse)
  const setPreCheckError = useGeolocationGameplayUpdate((state) => state.setPreCheckError)
  const [isLicenseExpired, setIsLicenseExpired] = React.useState(false)
  const [allowedStates, setAllowedStates] = React.useState([])
  const [loginResponse, setLoginResponse] = React.useState([])
  // const checkIfPopupOpen = React.useRef(false);
  const isWorkingFirstTime = React.useRef(true)

  const ref = React.useRef(null)
  const navigate = useNavigate()

  // const geoCookieData = getCookie('geocomply-response')
  // const res = geoCookieData?.trim() ? JSON.parse(geoCookieData) : {};

  const restrictedStates = React.useMemo(() => {
    if (allowedStates?.length) {
      return allowedStates.filter((state) => state?.isAllowed === false)
    } else return []
  }, [allowedStates])

  React.useEffect(() => {
    if (restrictedStates) {
      var precheckClient = GcHtml5.createPrecheckClient()
      precheckClient.setServiceUrl(import.meta.env.VITE_GEOCOMPLY_SERVICE_URL)
      precheckClient.setApiKey(import.meta.env.VITE_GEOCOMPLY_API_KEY)
      precheckClient.setSecretKey(import.meta.env.VITE_GEOCOMPLY_SECRET_KEY)
      precheckClient.check(
        function (data) {
          const stateCodes = restrictedStates.map((item) => item.stateCode)
          if (stateCodes.includes(data?.user_state_code)) {
            // setPreCheckError(data)
            // handleGeocomplyPopup()
          }
        },
        function (code, message) {
          //   navigate('/not-available');
        }
      )
    }
  }, [restrictedStates])

  const getAllowedStateList = React.useCallback(async () => {
    if (!allowedStates.length) {
      const data = await getAllowedStates()
      setAllowedStates(data?.data?.data)
    }
  }, [allowedStates])

  React.useLayoutEffect(() => {
    getAllowedStateList()
  }, [])

  const handleGeocomplyPopup = () => {
    portalStore.openPortal(() => <GeocomplyPopup open={true} />, 'tournamentEndPopup')
  }

  const handleLocationPopup = () => {
    portalStore.openPortal(() => <GeocomplyLocationPopup open={true} />, 'tournamentEndPopup')
  }

  const logOutMutation = useLogOutMutation({
    onSuccess: (res) => {
      logoutHandler()
      // portalStore.closePortal()
    },
    onError: (error) => {
      console.error('error', error)
    }
  })

  const handleLogout = async () => {
    try {
      if (typeof window.cioLogoutUser === 'function') {
        await window.cioLogoutUser()
      } else {
        console.error('cioLogoutUser is not defined')
      }
      await logOutMutation.mutateAsync()
      await user.logout()
    } catch (error) {
      console.error('Error in handleLogout:', error)
    }
  }

  React.useEffect(() => {
    if (userDetails?.username && regeolocateTime > 0) {
      setTimeout(() => {
        fetchEncryptedLocation(userDetails?.userId, GEOCOMPLY_REASON_CODE.INTERVAL)
      }, regeolocateTime * 1000)
    }
  }, [userDetails?.username, regeolocateTime])

  React.useEffect(() => {
    const fetchLicenseAndInitializeClient = async () => {
      try {
        const licenseData = await getLicenseString({ isExpired: isLicenseExpired })

        const regex = /<license[^>]*>(.*?)<\/license>/
        const match = licenseData?.data?.data?.match(regex)
        const licenseStringValue = match ? match[1] : ''

        setLicenseString(licenseStringValue)

        const client = GcHtml5.createClient()
        client.setLicense(licenseStringValue)
        client.events
          .on('hint', (reason, data) => {
            console.log('client.events hint description', data, reason)
            if (reason === client.HintReasons.BROWSER_GEOLOCATION_DENIED) {
              var description = data.description,
                messages = data.messages || []
              console.log('hint description', data, reason, description, messages)
            }
          })
          .on('success', (res) => {
            setEncryptedResponse(res)
          })
          .on('failed', (code, message) => {
            console.error('client.events Error in geocomply:', code, message)
            if (code === 608) {
              setIsLicenseExpired(true)
            } else if (code == 605) {
              // handleLogout()
              handleLocationPopup()
              setGeocomplyErrorMsg(message)
            } else if (code !== 614 || code !== 700 || code !== 707) {
              // handleLogout()
              handleGeocomplyPopup()
              setGeocomplyErrorMsg(message)
            }
          })

        GcHtml5.onMyIpSuccess = (IP) => {
          if (IP !== userIp) {
            setUserIp(IP)
            fetchEncryptedLocation(userDetails?.userId, GEOCOMPLY_REASON_CODE.IPCHANGE)
          }
          GcHtml5.ackMyIpSuccess()
        }

        GcHtml5.onMyIpFailure = (code, message) => {
          console.error('Error in geocomply:', code, message)
          if (code !== 700) {
            // handleLogout()
            handleLocationPopup()
            setGeocomplyErrorMsg(message)
          }
        }

        // client.allowHint = false
        client.allowHint(false)
        isWorkingFirstTime.current = false

        // if (isWorkingFirstTime) {
        //     // Get stored geocomply response
        //     const geoCookieData = getCookie('geocomply-response')
        //     const res = geoCookieData?.trim() ? JSON.parse(geoCookieData) : {};
        //     console.log('geoResponse app', res?.error_code?.[0] === '1', !!getLoginToken(), geoCookieData, typeof geoCookieData, geoCookieData !== '', res);

        //     isWorkingFirstTime.current = false;

        //     if (res || res === 'location_denied') {
        //         if (fetchEncryptedLocation) {
        //             console.log('geoResponse res>>>', res);
        //             fetchEncryptedLocation(res?.user_id, res?.reason)
        //         } else {
        //             console.warn("fetchEncryptedLocation is undefined");
        //         }
        //     }

        //     if ((geoCookieData || geoCookieData !== '') && !!getLoginToken() && res?.error_code?.[0] === '1') {
        //         portalStore.openPortal(() => <GeocomplyPopup open={true} geoCookieData={geoCookieData} />, 'tournamentEndPopup')
        //         checkIfPopupOpen.current = true;
        //     }
        // }

        if (!ref.current) ref.current = client
      } catch (error) {
        console.error('Failed to get license or initialize client', error)
      }
    }

    //    if(import.meta.env.VITE_NODE_ENV ==='staging' || import.meta.env.VITE_NODE_ENV==='production'){
    fetchLicenseAndInitializeClient()
    //    }
  }, [isLicenseExpired, userDetails?.username])

  React.useEffect(() => {
    const getDecryptedData = async () => {
      try {
        if (encryptedResponse) {
          const data = await decryptGeoComplyResponse({
            token: encryptedResponse
          })
          const geoLocateIn = parseInt(data?.data?.data?.nodes?.geolocate_in[0]) || 0
          const bufferTime = parseInt(data?.data?.data?.nodes?.buffer_time[0]) || 0

          const regeolocateTime = geoLocateIn - bufferTime
          setRegeolocateTime(regeolocateTime)

          const errorDecrypt = data?.data?.data?.nodes
          const troubleshooterMessages = errorDecrypt?.troubleshooter?.[0].message.map((msg) => msg._)
          setGeoErrorResponse(errorDecrypt)

          // setCookie('geocomply-response', JSON.stringify(errorDecrypt))
          if (errorDecrypt?.error_code?.[0] && errorDecrypt?.error_code?.[0] !== '0') {
            console.log('error', errorDecrypt?.error_details[0])
            console.log('troubleShoot', errorDecrypt?.troubleshooter?.[0].message[0])

            if (
              errorDecrypt?.error_code &&
              errorDecrypt?.error_code.length === 1 &&
              errorDecrypt?.error_details?.[0]?.boundary?.[0]?.$?.description
            ) {
              setGeocomplyErrorMsg(errorDecrypt?.error_details?.[0]?.boundary?.[0]?.$?.description)
              setErrorTroubleShoot(troubleshooterMessages)
              // handleLogout()
            }
            if (errorDecrypt?.troubleshooter?.[0]?.message) {
              setGeocomplyErrorMsg(errorDecrypt?.error_details?.[0]?.boundary?.[0]?.$?.description)
              setErrorTroubleShoot(troubleshooterMessages)
              // handleLogout()
            }
            // portalStore.closePortal()
            // navigate('/not-available')
            handleGeocomplyPopup()
            setIsRegionAllowed(false)

            // if (!checkIfPopupOpen.current
            // ) {
            //     handleGeocomplyPopup()
            // }
            // setIsRegionAllowed(false)
          } else if (errorDecrypt?.error_code?.[0] && errorDecrypt?.error_code?.[0] === '0') {
            // alert("else if")
            // console.log("55555",isWorkingFirstTime.current);

            if (!isWorkingFirstTime.current) {
              setIsRegionAllowed(true)
            }
            // if (!checkIfPopupOpen.current
            // ) {
            //     portalStore.closePortal()
            // }
          } else {
            // alert("else")
            // deleteCookie('geocomply-response')
            // if (!checkIfPopupOpen.current
            // ) {
            //     portalStore.closePortal()
            // }
          }
          // console.log(data, data?.data?.data?.nodes?.error_code[0] === '0')
        }
      } catch (error) {
        console.error('Failed to decrypt geocomply response', error)
      }
    }

    if (encryptedResponse) getDecryptedData()
  }, [encryptedResponse])

  const startIpService = () => {
    try {
      GcHtml5?.startMyIpService({
        license: licenseString,
        resumable: true
      })
      console.log('IP service started')
    } catch (e) {
      console.log(e, 'Error starting IP service')
      setIsNotAvailable(true)
      // handleLogout()
      handleLocationPopup()
    }
  }

  const stopIpService = () => {
    GcHtml5?.stopMyIpService()
    console.log('IP service stop')
  }

  const fetchEncryptedLocation = async (userName, reason) => {
    try {
      if (ref.current) {
        ref.current.setUserId(userName || userDetails?.username)
        ref.current.setReason(reason)
        ref.current.request()
        if (reason === GEOCOMPLY_REASON_CODE.LOGIN) {
          startIpService()
        }
      } else {
        console.warn('GeoComply client not initialized')
      }
    } catch (error) {
      console.error('Failed to fetch encrypted location', error)
    }
  }

  return (
    <GeoLocationContext.Provider
      value={{
        startIpService,
        stopIpService,
        fetchEncryptedLocation,
        ref,
        licenseString,
        isNotAvailable,
        isRegionAllowed,
        loginResponse,
        setLoginResponse,
        setIsRegionAllowed
      }}
    >
      {children}
    </GeoLocationContext.Provider>
  )
}
