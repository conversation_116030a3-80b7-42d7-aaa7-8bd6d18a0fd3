import React from 'react'
import { Box } from '@mui/material'
import { AboutIcon } from './iconComponents/AboutIcon'
import { AddressIcon } from './iconComponents/AddressIcon'
import { AdulltIcon } from './iconComponents/AdulltIcon'
import { AnnouncementIcon } from './iconComponents/AnnouncementIcon'
import { ArcadeIcon } from './iconComponents/ArcadeIcon'
import { BonusBagIcon } from './iconComponents/BonusBagIcon'
import { BonusIcon } from './iconComponents/BonusIcon'
import { BrandLogoIcon } from './iconComponents/BrandLogoIcon'
import { CoinsIcon } from './iconComponents/CoinsIcon'
import { DashboardIcon } from './iconComponents/DashboardIcon'
import { DoubleArrowIcon } from './iconComponents/DoubleArrowIcon'
import { DownArrowWhiteIcon } from './iconComponents/DownArrowWhiteIcon'
import { DownArrowIcon } from './iconComponents/DownArrowIcon'
import { EnIcon } from './iconComponents/EnIcon'
import { EnglishIcon } from './iconComponents/EnglishIcon'
import { EsportsIcon } from './iconComponents/EsportsIcon'
import { FilterSlotIcon } from './iconComponents/FilterSlotIcon'
import { FilterIcon } from './iconComponents/FilterIcon'
import { FrenchIcon } from './iconComponents/FrenchIcon'
import { GamblersIcon } from './iconComponents/GamblersIcon'
import { GenralDetailsIcon } from './iconComponents/GenralDetailsIcon'
import { HeadingJackpotsIcon } from './iconComponents/HeadingJackpotsIcon'
import { HotgamesIcon } from './iconComponents/HotgamesIcon'
import { InfoIcon } from './iconComponents/InfoIcon'
import { JackpotsIcon } from './iconComponents/JackpotsIcon'
import { JogosIcon } from './iconComponents/JogosIcon'
import { MegawaysIcon } from './iconComponents/MegawaysIcon'
import { MenuIconIcon } from './iconComponents/MenuIconIcon'
import { MicrogamingIcon } from './iconComponents/MicrogamingIcon'
import { MobBrandLogoIcon } from './iconComponents/MobBrandLogoIcon'
import { MoneyBagIcon } from './iconComponents/MoneyBagIcon'
import { NewIcon } from './iconComponents/NewIcon'
import { PlayButtonIcon } from './iconComponents/PlayButtonIcon'
import { ProfileUserIcon } from './iconComponents/ProfileUserIcon'
import { ProvidersIcon } from './iconComponents/ProvidersIcon'
import { RecomendadoIcon } from './iconComponents/RecomendadoIcon'
import { ReloadIcon } from './iconComponents/ReloadIcon'
import { ResponsibleGamblingIcon } from './iconComponents/ResponsibleGamblingIcon'
import { SearchIcon } from './iconComponents/SearchIcon'
import { SidebarArrowIcon } from './iconComponents/SidebarArrowIcon'
import { SignupCheckMarkIcon } from './iconComponents/SignupCheckMarkIcon'
import { SorteIcon } from './iconComponents/SorteIcon'
import { StatusIcon } from './iconComponents/StatusIcon'
import { SupportIcon } from './iconComponents/SupportIcon'
import { TableGamesIcon } from './iconComponents/TableGamesIcon'
import { TurbogamesIcon } from './iconComponents/TurbogamesIcon'
import { UserBalanceIcon } from './iconComponents/UserBalanceIcon'

export default {
  title: 'atoms/icons'
}

const Row = props => <Box p={2} {...props} />
const Cell = props => <Box display='inline-block' m={1} {...props} style={{ cursor: 'pointer' }} />

export const icons = () => (
  <Box>
    <Row>
      <Cell><AboutIcon title='AboutIcon' width={40} height={40} /></Cell>

      <Cell><AddressIcon title='AddressIcon' width={40} height={40} /></Cell>

      <Cell><AdulltIcon title='AdulltIcon' width={40} height={40} /></Cell>

      <Cell><AnnouncementIcon title='AnnouncementIcon' width={40} height={40} /></Cell>

      <Cell><ArcadeIcon title='ArcadeIcon' width={40} height={40} /></Cell>

      <Cell><BonusBagIcon title='BonusBagIcon' width={40} height={40} /></Cell>

      <Cell><BonusIcon title='BonusIcon' width={40} height={40} /></Cell>

      <Cell><BrandLogoIcon title='BrandLogoIcon' width={40} height={40} /></Cell>

      <Cell><CoinsIcon title='CoinsIcon' width={40} height={40} /></Cell>

      <Cell><DashboardIcon title='DashboardIcon' width={40} height={40} /></Cell>

      <Cell><DoubleArrowIcon title='DoubleArrowIcon' width={40} height={40} /></Cell>

      <Cell><DownArrowWhiteIcon title='DownArrowWhiteIcon' width={40} height={40} /></Cell>

      <Cell><DownArrowIcon title='DownArrowIcon' width={40} height={40} /></Cell>

      <Cell><EnIcon title='EnIcon' width={40} height={40} /></Cell>

      <Cell><EnglishIcon title='EnglishIcon' width={40} height={40} /></Cell>

      <Cell><EsportsIcon title='EsportsIcon' width={40} height={40} /></Cell>

      <Cell><FilterSlotIcon title='FilterSlotIcon' width={40} height={40} /></Cell>

      <Cell><FilterIcon title='FilterIcon' width={40} height={40} /></Cell>

      <Cell><FrenchIcon title='FrenchIcon' width={40} height={40} /></Cell>

      <Cell><GamblersIcon title='GamblersIcon' width={40} height={40} /></Cell>

      <Cell><GenralDetailsIcon title='GenralDetailsIcon' width={40} height={40} /></Cell>

      <Cell><HeadingJackpotsIcon title='HeadingJackpotsIcon' width={40} height={40} /></Cell>

      <Cell><HotgamesIcon title='HotgamesIcon' width={40} height={40} /></Cell>

      <Cell><InfoIcon title='InfoIcon' width={40} height={40} /></Cell>

      <Cell><JackpotsIcon title='JackpotsIcon' width={40} height={40} /></Cell>

      <Cell><JogosIcon title='JogosIcon' width={40} height={40} /></Cell>

      <Cell><MegawaysIcon title='MegawaysIcon' width={40} height={40} /></Cell>

      <Cell><MenuIconIcon title='MenuIconIcon' width={40} height={40} /></Cell>

      <Cell><MicrogamingIcon title='MicrogamingIcon' width={40} height={40} /></Cell>

      <Cell><MobBrandLogoIcon title='MobBrandLogoIcon' width={40} height={40} /></Cell>

      <Cell><MoneyBagIcon title='MoneyBagIcon' width={40} height={40} /></Cell>

      <Cell><NewIcon title='NewIcon' width={40} height={40} /></Cell>

      <Cell><PlayButtonIcon title='PlayButtonIcon' width={40} height={40} /></Cell>

      <Cell><ProfileUserIcon title='ProfileUserIcon' width={40} height={40} /></Cell>

      <Cell><ProvidersIcon title='ProvidersIcon' width={40} height={40} /></Cell>

      <Cell><RecomendadoIcon title='RecomendadoIcon' width={40} height={40} /></Cell>

      <Cell><ReloadIcon title='ReloadIcon' width={40} height={40} /></Cell>

      <Cell><ResponsibleGamblingIcon title='ResponsibleGamblingIcon' width={40} height={40} /></Cell>

      <Cell><SearchIcon title='SearchIcon' width={40} height={40} /></Cell>

      <Cell><SidebarArrowIcon title='SidebarArrowIcon' width={40} height={40} /></Cell>

      <Cell><SignupCheckMarkIcon title='SignupCheckMarkIcon' width={40} height={40} /></Cell>

      <Cell><SorteIcon title='SorteIcon' width={40} height={40} /></Cell>

      <Cell><StatusIcon title='StatusIcon' width={40} height={40} /></Cell>

      <Cell><SupportIcon title='SupportIcon' width={40} height={40} /></Cell>

      <Cell><TableGamesIcon title='TableGamesIcon' width={40} height={40} /></Cell>

      <Cell><TurbogamesIcon title='TurbogamesIcon' width={40} height={40} /></Cell>

      <Cell><UserBalanceIcon title='UserBalanceIcon' width={40} height={40} /></Cell>
    </Row>
  </Box>
)
