<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 16.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="23px" height="23px" viewBox="0 0 23 23" enable-background="new 0 0 23 23" xml:space="preserve">
<g>
	<g>
		<defs>
			<rect id="SVGID_1_" x="0.8" y="0.5" width="22" height="22"/>
		</defs>
		<clipPath id="SVGID_2_">
			<use xlink:href="#SVGID_1_"  overflow="visible"/>
		</clipPath>
		<g clip-path="url(#SVGID_2_)">
			<defs>
				<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="0.771" y="4.638" width="16.411" height="17.197">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="0.771" y="4.638" width="16.411" height="17.197" id="mask0_42_1019">
				<path fill="#FFFFFF" filter="url(#Adobe_OpacityMaskFilter)" d="M0.8,0.5h22v22h-22V0.5z"/>
			</mask>
			<g mask="url(#mask0_42_1019)">
				
					<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
					M3.817,12.852L1.122,9.937l3.212-4.948h1.91"/>
				
					<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
					M4.911,14.034l6.889,7.449l5.03-5.438"/>
			</g>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M19.971,12.647l-2.057,2.225"/>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="14.421" y="4.638" width="8.408" height="7.258">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="14.421" y="4.638" width="8.408" height="7.258" id="mask1_42_1019">
				<path fill="#FFFFFF" filter="url(#Adobe_OpacityMaskFilter_1_)" d="M0.8,0.5h22v22h-22V0.5z"/>
			</mask>
			<g mask="url(#mask1_42_1019)">
				
					<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
					M14.772,4.989h4.493l3.212,4.948l-1.485,1.607"/>
			</g>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M13.268,4.989H8.848"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M5.477,6.75l-1.143-1.76"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M6.295,8.011l1.251,1.927L11.8,4.989l4.254,4.948l3.212-4.948"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M22.478,9.938h-6.424L11.8,21.483"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M1.122,9.938h6.424L11.8,21.483"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M7.546,9.938h8.507"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M5.254,13.757l0.317-0.194l-0.317-0.195c-0.317-0.195-0.563-0.488-0.702-0.834l-0.283-0.708l-0.283,0.708
				c-0.139,0.346-0.385,0.639-0.702,0.834l-0.317,0.195l0.317,0.194c0.317,0.195,0.563,0.487,0.702,0.834l0.283,0.707l0.283-0.707
				C4.69,14.244,4.937,13.952,5.254,13.757z"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M16.414,15.688l-0.317-0.195l0.317-0.195c0.317-0.195,0.563-0.487,0.702-0.833l0.282-0.708l0.283,0.708
				c0.139,0.346,0.385,0.638,0.702,0.833l0.317,0.195l-0.317,0.195c-0.317,0.195-0.563,0.487-0.702,0.834l-0.283,0.707l-0.282-0.707
				C16.978,16.176,16.731,15.884,16.414,15.688z"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M8.531,5.185l0.317-0.195L8.531,4.794C8.213,4.599,7.967,4.306,7.829,3.96L7.546,3.253L7.263,3.96
				C7.125,4.306,6.878,4.599,6.561,4.794L6.244,4.989l0.317,0.195C6.878,5.38,7.125,5.672,7.263,6.019l0.283,0.707l0.283-0.707
				C7.967,5.672,8.213,5.38,8.531,5.185z"/>
			
				<path fill="none" stroke="#858585" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M17.342,3.448l0.318-0.195l-0.318-0.195c-0.316-0.195-0.563-0.487-0.701-0.833l-0.283-0.708l-0.283,0.708
				c-0.139,0.346-0.385,0.638-0.701,0.833l-0.318,0.195l0.318,0.195c0.316,0.195,0.563,0.488,0.701,0.834l0.283,0.707l0.283-0.707
				C16.779,3.936,17.025,3.644,17.342,3.448z"/>
		</g>
	</g>
</g>
<g>
	<g>
		<defs>
			<rect id="SVGID_3_" x="0.799" y="0.5" width="22" height="22"/>
		</defs>
		<clipPath id="SVGID_4_">
			<use xlink:href="#SVGID_3_"  overflow="visible"/>
		</clipPath>
		<g clip-path="url(#SVGID_4_)">
			<defs>
				<filter id="Adobe_OpacityMaskFilter_2_" filterUnits="userSpaceOnUse" x="0.77" y="4.638" width="16.411" height="17.196">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="0.77" y="4.638" width="16.411" height="17.196" id="mask0_42_1018_2_">
				<path fill="#FFFFFF" filter="url(#Adobe_OpacityMaskFilter_2_)" d="M0.799,0.5h22v22h-22V0.5z"/>
			</mask>
			<g mask="url(#mask0_42_1018_2_)">
				
					<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="3.6826" y1="783.2793" x2="3.6826" y2="774.2611" gradientTransform="matrix(1 0 0 1 0 -768)">
					<stop  offset="0" style="stop-color:#FFD958"/>
					<stop  offset="1" style="stop-color:#FF964E"/>
				</linearGradient>
				
					<path fill="none" stroke="url(#SVGID_5_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
					M3.816,12.852L1.122,9.938l3.212-4.948h1.91"/>
				
					<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="10.8706" y1="791.7832" x2="10.8706" y2="783.2397" gradientTransform="matrix(1 0 0 1 0 -768)">
					<stop  offset="0" style="stop-color:#FFD958"/>
					<stop  offset="1" style="stop-color:#FF964E"/>
				</linearGradient>
				
					<path fill="none" stroke="url(#SVGID_6_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
					M4.911,14.034l6.889,7.448l5.031-5.438"/>
			</g>
			
				<linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="18.9424" y1="783.5596" x2="18.9424" y2="781.0071" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_7_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M19.971,12.646l-2.057,2.226"/>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_3_" filterUnits="userSpaceOnUse" x="14.42" y="4.638" width="8.407" height="7.258">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="14.42" y="4.638" width="8.407" height="7.258" id="mask1_42_1018_2_">
				<path fill="#FFFFFF" filter="url(#Adobe_OpacityMaskFilter_3_)" d="M0.799,0.5h22v22h-22V0.5z"/>
			</mask>
			<g mask="url(#mask1_42_1018_2_)">
				
					<linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="18.624" y1="781.5684" x2="18.624" y2="774.05" gradientTransform="matrix(1 0 0 1 0 -768)">
					<stop  offset="0" style="stop-color:#FFD958"/>
					<stop  offset="1" style="stop-color:#FF964E"/>
				</linearGradient>
				
					<path fill="none" stroke="url(#SVGID_8_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
					M14.771,4.989h4.492l3.213,4.948l-1.486,1.606"/>
			</g>
			
				<linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="11.0576" y1="773.2979" x2="11.0576" y2="772.1508" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_9_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M13.268,4.989h-4.42"/>
			
				<linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="4.9048" y1="775.293" x2="4.9048" y2="773.274" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_10_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M5.476,6.75L4.333,4.99"/>
			
				<linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="12.7793" y1="779.4639" x2="12.7793" y2="773.7894" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_11_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M6.294,8.011l1.251,1.927l4.254-4.948l4.253,4.948l3.213-4.948"/>
			
				<linearGradient id="SVGID_12_" gradientUnits="userSpaceOnUse" x1="17.1377" y1="793.0479" x2="17.1377" y2="779.8036" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_12_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M22.477,9.938h-6.424l-4.253,11.545"/>
			
				<linearGradient id="SVGID_13_" gradientUnits="userSpaceOnUse" x1="6.4604" y1="793.0479" x2="6.4604" y2="779.8036" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_13_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M1.122,9.938h6.424l4.254,11.545"/>
			
				<linearGradient id="SVGID_14_" gradientUnits="userSpaceOnUse" x1="11.7993" y1="778.2451" x2="11.7993" y2="777.098" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_14_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M7.545,9.938h8.507"/>
			
				<linearGradient id="SVGID_15_" gradientUnits="userSpaceOnUse" x1="4.2681" y1="784.3701" x2="4.2681" y2="780.3866" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_15_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M5.253,13.757l0.316-0.194l-0.316-0.194c-0.317-0.195-0.563-0.488-0.702-0.834l-0.283-0.708l-0.283,0.708
				C3.846,12.88,3.6,13.173,3.284,13.368l-0.317,0.194l0.317,0.194c0.316,0.195,0.563,0.487,0.701,0.834l0.283,0.707l0.283-0.707
				C4.689,14.244,4.936,13.952,5.253,13.757z"/>
			
				<linearGradient id="SVGID_16_" gradientUnits="userSpaceOnUse" x1="17.3984" y1="786.3008" x2="17.3984" y2="782.3186" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_16_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M16.414,15.688l-0.318-0.194l0.318-0.195c0.316-0.194,0.563-0.487,0.701-0.833l0.283-0.708l0.283,0.708
				c0.139,0.346,0.385,0.639,0.701,0.833l0.318,0.195l-0.318,0.194c-0.316,0.195-0.563,0.487-0.701,0.834l-0.283,0.707l-0.283-0.707
				C16.977,16.176,16.73,15.884,16.414,15.688z"/>
			
				<linearGradient id="SVGID_17_" gradientUnits="userSpaceOnUse" x1="7.5454" y1="775.7979" x2="7.5454" y2="771.8148" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_17_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M8.53,5.185L8.847,4.99L8.53,4.794C8.212,4.599,7.966,4.306,7.829,3.96L7.545,3.253L7.262,3.96
				C7.125,4.306,6.877,4.599,6.56,4.794L6.244,4.989L6.56,5.184C6.877,5.38,7.125,5.672,7.262,6.019l0.283,0.707l0.283-0.707
				C7.966,5.672,8.212,5.38,8.53,5.185z"/>
			
				<linearGradient id="SVGID_18_" gradientUnits="userSpaceOnUse" x1="16.3564" y1="774.0615" x2="16.3564" y2="770.0784" gradientTransform="matrix(1 0 0 1 0 -768)">
				<stop  offset="0" style="stop-color:#FFD958"/>
				<stop  offset="1" style="stop-color:#FF964E"/>
			</linearGradient>
			
				<path fill="none" stroke="url(#SVGID_18_)" stroke-width="0.7031" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
				M17.342,3.448l0.316-0.195l-0.316-0.195c-0.318-0.195-0.564-0.487-0.703-0.833l-0.283-0.708l-0.281,0.708
				c-0.141,0.346-0.387,0.638-0.703,0.833l-0.316,0.195l0.316,0.195c0.316,0.195,0.563,0.488,0.703,0.834l0.281,0.707l0.283-0.707
				C16.777,3.936,17.023,3.644,17.342,3.448z"/>
		</g>
	</g>
</g>
</svg>
