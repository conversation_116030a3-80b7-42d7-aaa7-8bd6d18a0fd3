<svg width="221" height="221" viewBox="0 0 221 221" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M110.657 220.657C171.408 220.657 220.657 171.408 220.657 110.657C220.657 49.9054 171.408 0.656677 110.657 0.656677C49.9054 0.656677 0.656738 49.9054 0.656738 110.657C0.656738 171.408 49.9054 220.657 110.657 220.657Z" fill="url(#paint0_linear_913_21889)"/>
<path style="mix-blend-mode:screen" d="M110.658 220.145C171.127 220.145 220.147 171.126 220.147 110.657C220.147 50.1882 171.127 1.16861 110.658 1.16861C50.1895 1.16861 1.16992 50.1882 1.16992 110.657C1.16992 171.126 50.1895 220.145 110.658 220.145Z" fill="url(#paint1_radial_913_21889)"/>
<path opacity="0.3" d="M110.657 217.348C50.4557 217.348 1.56679 168.994 0.679966 109.008C0.679966 109.554 0.657227 110.111 0.657227 110.657C0.657227 171.404 49.9099 220.657 110.657 220.657C171.405 220.657 220.657 171.404 220.657 110.657C220.657 110.1 220.646 109.554 220.634 109.008C219.748 168.994 170.859 217.36 110.657 217.36V217.348Z" fill="#FFEA83"/>
<path d="M110.657 4.79518C170.722 4.79518 219.531 52.9337 220.634 112.726C220.645 112.032 220.657 111.35 220.657 110.657C220.657 49.9094 171.404 0.656677 110.657 0.656677C49.9095 0.656677 0.656738 49.9094 0.656738 110.657C0.656738 111.35 0.668108 112.032 0.679477 112.726C1.78232 52.9337 50.5916 4.79518 110.657 4.79518Z" fill="#FFEA83"/>
<path style="mix-blend-mode:screen" opacity="0.5" d="M110.657 1.06607C51.1607 1.06607 2.73794 48.511 1.16895 107.632C13.2775 114.807 32.3214 109.065 44.771 102.835C55.4811 97.8888 68.6583 99.5374 80.187 100.367C109.009 102.835 137.012 115.193 165.834 107.78C171.598 106.132 179.829 98.7188 185.594 103.665C195.747 112.123 209.39 112.862 220.146 107.712C218.611 48.5565 170.177 1.06607 110.657 1.06607Z" fill="url(#paint2_linear_913_21889)"/>
<path d="M110.657 195.848C157.707 195.848 195.849 157.707 195.849 110.657C195.849 63.6065 157.707 25.4649 110.657 25.4649C63.607 25.4649 25.4653 63.6065 25.4653 110.657C25.4653 157.707 63.607 195.848 110.657 195.848Z" fill="url(#paint3_linear_913_21889)"/>
<path d="M110.657 191.71C155.421 191.71 191.71 155.421 191.71 110.657C191.71 65.8922 155.421 29.6034 110.657 29.6034C65.8923 29.6034 29.6035 65.8922 29.6035 110.657C29.6035 155.421 65.8923 191.71 110.657 191.71Z" fill="#D68738"/>
<path d="M110.657 188.401C153.595 188.401 188.402 153.594 188.402 110.657C188.402 67.7195 153.595 32.912 110.657 32.912C67.7201 32.912 32.9126 67.7195 32.9126 110.657C32.9126 153.594 67.7201 188.401 110.657 188.401Z" fill="url(#paint4_radial_913_21889)"/>
<g filter="url(#filter0_d_913_21889)">
<path d="M71.3981 141.939C65.3869 141.939 60.1983 140.959 55.8323 138.997C51.5296 136.972 48.176 134.061 45.7715 130.265C43.4303 126.405 42.1332 121.723 41.8801 116.218C41.8168 113.497 41.7851 110.618 41.7851 107.581C41.7851 104.48 41.8168 101.538 41.8801 98.7537C42.1332 93.3753 43.4619 88.8195 45.8664 85.0862C48.2709 81.3529 51.6561 78.5055 56.0221 76.544C60.3882 74.5825 65.5135 73.6017 71.3981 73.6017C76.1438 73.6017 80.3516 74.2028 84.0216 75.405C87.6916 76.544 90.7604 78.0942 93.2282 80.0558C95.7592 82.0173 97.6575 84.1687 98.923 86.5099C100.252 88.8511 100.948 91.1607 101.011 93.4386C101.074 94.008 100.885 94.4826 100.442 94.8623C100.062 95.2419 99.5874 95.4317 99.0179 95.4317H85.1605C84.4645 95.4317 83.9267 95.3368 83.547 95.147C83.2306 94.8939 82.9459 94.5459 82.6928 94.103C82.2499 93.1538 81.5855 92.1731 80.6996 91.1606C79.877 90.085 78.7064 89.1675 77.1878 88.4082C75.7325 87.6489 73.8026 87.2692 71.3981 87.2692C67.7914 87.2692 64.944 88.2183 62.8559 90.1166C60.8311 92.0149 59.7238 95.0521 59.5339 99.2283C59.3441 104.67 59.3441 110.175 59.5339 115.743C59.7238 120.172 60.8944 123.368 63.0457 125.329C65.1971 127.291 68.0445 128.272 71.5879 128.272C73.9924 128.272 76.1121 127.86 77.9471 127.038C79.8454 126.215 81.3324 124.95 82.408 123.241C83.4837 121.47 84.0216 119.223 84.0216 116.503V114.699H75.0997C74.4037 114.699 73.8026 114.478 73.2964 114.035C72.8534 113.529 72.632 112.927 72.632 112.231V105.208C72.632 104.512 72.8534 103.942 73.2964 103.499C73.8026 102.993 74.4037 102.74 75.0997 102.74H99.0179C99.7139 102.74 100.283 102.993 100.726 103.499C101.169 103.942 101.391 104.512 101.391 105.208V116.028C101.391 121.47 100.157 126.12 97.6891 129.98C95.2213 133.84 91.7095 136.814 87.1537 138.902C82.6611 140.927 77.4093 141.939 71.3981 141.939ZM139.52 141.939C133.509 141.939 128.352 140.959 124.049 138.997C119.81 137.035 116.488 134.156 114.083 130.36C111.742 126.563 110.445 121.849 110.192 116.218C110.128 113.623 110.097 110.839 110.097 107.865C110.097 104.891 110.128 102.044 110.192 99.3232C110.445 93.8182 111.774 89.1675 114.178 85.3709C116.583 81.5111 119.936 78.6005 124.239 76.6389C128.542 74.6141 133.635 73.6017 139.52 73.6017C143.696 73.6017 147.556 74.1395 151.099 75.2152C154.643 76.2276 157.743 77.7462 160.401 79.7711C163.058 81.7326 165.115 84.1371 166.57 86.9845C168.089 89.7686 168.88 92.9324 168.943 96.4758C169.006 97.0453 168.816 97.5198 168.374 97.8995C167.994 98.2791 167.519 98.469 166.95 98.469H154.137C153.314 98.469 152.681 98.3108 152.238 97.9944C151.795 97.6148 151.416 96.9504 151.099 96.0012C150.213 92.6476 148.758 90.3697 146.733 89.1675C144.772 87.902 142.336 87.2692 139.425 87.2692C135.945 87.2692 133.192 88.25 131.168 90.2115C129.143 92.1098 128.035 95.3052 127.846 99.7978C127.656 104.986 127.656 110.302 127.846 115.743C128.035 120.236 129.143 123.463 131.168 125.424C133.192 127.323 135.945 128.272 139.425 128.272C142.336 128.272 144.803 127.639 146.828 126.373C148.853 125.108 150.277 122.83 151.099 119.54C151.352 118.591 151.7 117.958 152.143 117.641C152.65 117.262 153.314 117.072 154.137 117.072H166.95C167.519 117.072 167.994 117.262 168.374 117.641C168.816 118.021 169.006 118.496 168.943 119.065C168.88 122.609 168.089 125.804 166.57 128.651C165.115 131.436 163.058 133.84 160.401 135.865C157.743 137.826 154.643 139.345 151.099 140.421C147.556 141.433 143.696 141.939 139.52 141.939Z" fill="white"/>
</g>
<path opacity="0.2" d="M110.655 182.614C68.4062 182.614 34.0362 150.575 32.9334 108.587C32.9106 109.269 32.9106 109.963 32.9106 110.656C32.9106 153.599 67.7127 188.401 110.655 188.401C153.598 188.401 188.4 153.599 188.4 110.656C188.4 109.963 188.4 109.281 188.377 108.587C187.286 150.563 152.904 182.614 110.655 182.614Z" fill="#FFEA83"/>
<defs>
<filter id="filter0_d_913_21889" x="41.7852" y="73.6017" width="127.253" height="75.1171" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6.77952"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.825 0 0 0 0 0.571405 0 0 0 0 0.189063 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_913_21889"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_913_21889" result="shape"/>
</filter>
<linearGradient id="paint0_linear_913_21889" x1="110.657" y1="0.656677" x2="110.657" y2="220.657" gradientUnits="userSpaceOnUse">
<stop stop-color="#F8BE4F"/>
<stop offset="0.11" stop-color="#FBC450"/>
<stop offset="0.3" stop-color="#FFCB52"/>
<stop offset="1" stop-color="#D17D3D"/>
</linearGradient>
<radialGradient id="paint1_radial_913_21889" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(109.555 -14.4758) scale(87.6021)">
<stop stop-color="#FFDF7D"/>
<stop offset="1"/>
</radialGradient>
<linearGradient id="paint2_linear_913_21889" x1="110.657" y1="218.713" x2="110.657" y2="59.0847" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCA85"/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="paint3_linear_913_21889" x1="110.657" y1="195.848" x2="110.657" y2="-13.4757" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBC450"/>
<stop offset="1" stop-color="#E09744"/>
</linearGradient>
<radialGradient id="paint4_radial_913_21889" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(109.975 66.9978) scale(151.442)">
<stop offset="0.3" stop-color="#FFCB52"/>
<stop offset="1" stop-color="#D17D3D"/>
</radialGradient>
</defs>
</svg>
