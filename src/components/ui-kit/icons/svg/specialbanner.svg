<svg width="222" height="213" viewBox="0 0 222 213" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M25.5 26.5H0L25.5 4V26.5Z" fill="#890202"/>
<path d="M195.5 26H221.5L195.5 3V26Z" fill="#890202"/>
<g filter="url(#filter0_d_2603_34904)">
<path d="M195.5 3V137L114.5 204L25 137L25.5 3.5L195.5 3Z" fill="url(#paint0_linear_2603_34904)"/>
</g>
<g filter="url(#filter1_d_2603_34904)">
<path d="M178.422 11.0223L183.474 11.0074L183.476 11.5074L185.5 11.5015V13.4861H186V18.4583H185.5V23.4306H186V28.4028H185.5V33.375H186V38.3472H185.5V43.3195H186V48.2917H185.5V53.2639H186V58.2361H185.5V63.2083H186V68.1806H185.5V73.1528H186V78.125H185.5V83.0972H186V88.0695H185.5V93.0417H186V98.0139H185.5V102.986H186V107.958H185.5V112.931H186V117.903H185.5V122.875H186V127.847H185.5V130.098L183.676 131.605L183.994 131.991L179.982 135.306L179.664 134.92L175.652 138.235L175.971 138.62L171.959 141.935L171.64 141.55L167.629 144.865L167.947 145.25L163.935 148.565L163.617 148.179L159.605 151.494L159.924 151.88L155.912 155.194L155.594 154.809L151.582 158.124L151.9 158.509L147.889 161.824L147.57 161.439L143.558 164.753L143.877 165.139L139.865 168.454L139.547 168.068L135.535 171.383L135.853 171.769L131.842 175.083L131.523 174.698L127.511 178.013L127.83 178.398L123.818 181.713L123.5 181.328L119.488 184.642L119.806 185.028L115.795 188.343L115.476 187.957L113.773 189.364L112.094 188.108L111.794 188.508L107.805 185.525L108.104 185.125L104.115 182.141L103.815 182.542L99.8258 179.558L100.125 179.158L96.1358 176.175L95.8364 176.575L91.8469 173.592L92.1464 173.191L88.1569 170.208L87.8575 170.608L83.868 167.625L84.1675 167.225L80.178 164.241L79.8786 164.642L75.8892 161.658L76.1886 161.258L72.1991 158.275L71.8997 158.675L67.9103 155.692L68.2097 155.291L64.2203 152.308L63.9208 152.708L59.9314 149.725L60.2308 149.325L56.2414 146.341L55.9419 146.742L51.9525 143.758L52.2519 143.358L48.2625 140.375L47.963 140.775L43.9736 137.792L44.273 137.391L40.2836 134.408L39.9842 134.808L35.9947 131.825L36.2942 131.425L34.5009 130.084L34.5093 127.858L34.0093 127.857L34.0279 122.903L34.5279 122.905L34.5464 117.951L34.0464 117.949L34.065 112.995L34.565 112.997L34.5836 108.044L34.0836 108.042L34.1022 103.088L34.6021 103.09L34.6207 98.1364L34.1207 98.1345L34.1393 93.1808L34.6393 93.1827L34.6579 88.229L34.1579 88.2272L34.1764 83.2735L34.6764 83.2754L34.695 78.3217L34.195 78.3198L34.2136 73.3661L34.7136 73.368L34.7322 68.4143L34.2322 68.4125L34.2507 63.4588L34.7507 63.4607L34.7693 58.507L34.2693 58.5051L34.2879 53.5515L34.7879 53.5533L34.8064 48.5997L34.3065 48.5978L34.325 43.6441L34.825 43.646L34.8436 38.6923L34.3436 38.6905L34.3622 33.7368L34.8622 33.7387L34.8807 28.785L34.3807 28.7831L34.3993 23.8294L34.8993 23.8313L34.9179 18.8777L34.4179 18.8758L34.4365 13.9221L34.9365 13.924L34.9439 11.9438L36.9731 11.9379L36.9717 11.4379L42.0235 11.423L42.025 11.923L47.0768 11.9082L47.0753 11.4082L52.1271 11.3933L52.1286 11.8933L57.1804 11.8785L57.1789 11.3785L62.2307 11.3636L62.2322 11.8636L67.284 11.8488L67.2825 11.3488L72.3343 11.334L72.3358 11.834L77.3876 11.8191L77.3862 11.3191L82.438 11.3043L82.4394 11.8043L87.4912 11.7894L87.4898 11.2894L92.5416 11.2746L92.543 11.7746L97.5948 11.7597L97.5934 11.2597L102.645 11.2449L102.647 11.7449L107.698 11.7301L107.697 11.2301L112.749 11.2152L112.75 11.7152L117.802 11.7004L117.801 11.2004L122.852 11.1855L122.854 11.6855L127.906 11.6707L127.904 11.1707L132.956 11.1558L132.957 11.6558L138.009 11.641L138.008 11.141L143.06 11.1262L143.061 11.6262L148.113 11.6113L148.111 11.1113L153.163 11.0965L153.165 11.5965L158.217 11.5816L158.215 11.0816L163.267 11.0668L163.268 11.5668L168.32 11.5519L168.319 11.0519L173.37 11.0371L173.372 11.5371L178.424 11.5223L178.422 11.0223Z" stroke="#CDCDCD" stroke-dasharray="5 5" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_2603_34904" x="19" y="0" width="182.5" height="213" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2603_34904"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2603_34904" result="shape"/>
</filter>
<filter id="filter1_d_2603_34904" x="28" y="8" width="164" height="191" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2603_34904"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2603_34904" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2603_34904" x1="147.5" y1="184.5" x2="58.292" y2="2.12347" gradientUnits="userSpaceOnUse">
<stop offset="0.161" stop-color="#A60505"/>
<stop offset="0.591" stop-color="#C12E2E"/>
<stop offset="0.836" stop-color="#FF4949"/>
<stop offset="1" stop-color="#FF4949"/>
</linearGradient>
</defs>
</svg>
