import React from 'react'
import {
  Grid,
  IconButton,
  Typography,
  DialogContent
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import { Success } from '../ui-kit/icons/utils'


const PaymentInitiated = ({type}) => {
  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <>
      <Grid className='inner-modal-header'>
        <Typography variant='h4'>{type ==='deposit' ? 'Payment Processing' : 'Redeem Request'} </Typography>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
      <DialogContent className='payment-status-modal'>
        <Grid className='payment-status-content'>
          <img src={Success} alt='Success' className='payment-status-icon' />
          <Typography variant='h4'>{type === 'deposit' ? 'Payment Initiated !' : 'Redeem Request Submitted !' }</Typography>
          <Typography>{type ==='deposit' ? 'Please wait while we redirect you to the next page.' : 'Your wallet will be credited shortly' }</Typography>
        </Grid>
      </DialogContent>
    </>
  )
}

export default PaymentInitiated
