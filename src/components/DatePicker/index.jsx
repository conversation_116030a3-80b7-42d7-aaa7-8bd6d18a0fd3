import React, { useState } from 'react'
import moment from 'moment'
import Select from 'react-select'
import useStyles from '../../pages/Accounts/bets.styles'
const dateConstants = [
  { label: 'Today', value: 'today' },
  { label: 'Yesterday', value: 'yesterday' },
  { label: 'Last 7 Days', value: 'last7days' },
  { label: 'Last 30 Days', value: 'last30days' },
  { label: 'Last 90 Days', value: 'last90days' }
]

const getDateRange = (option) => {
  const today = moment()
  let start = null
  let end = today.endOf('day').format('YYYY-MM-DD HH:mm:ss')

  switch (option) {
    case 'today':
      start = today.startOf('day').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'yesterday':
      start = today.subtract(1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
      end = today.endOf('day').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'last7days':
      start = today.subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'last30days':
      start = today.subtract(29, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'last90days':
      start = today.subtract(89, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      start = today.subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss') // Default to last 7 days
      break
  }

  return { start, end }
}

const DatePicker = ({ onStartDateChange, onEndDateChange }) => {
  const classes = useStyles()

  const customStyles = {
    input: (provided) => ({
      ...provided,
      pointerEvents: 'none'
    })
  }
  const [selectedOption, setSelectedOption] = useState(dateConstants[2])
  const [dateRange, setDateRange] = useState(getDateRange('last7days')) // eslint-disable-line

  // Function to handle date change
  const handleDateChange = (e) => {
    const value = e.value
    setSelectedOption(e)
    const { start, end } = getDateRange(value)
    setDateRange({ start, end })

    // Call callback functions
    onStartDateChange(new Date(start))
    onEndDateChange(new Date(end))
  }

  return (
    <div>
      {/* <select
        value={selectedOption}
        className="form-select ms-2"
        //onChange={handleDateChange}
      >
        {dateConstants.map((item) => (
          <option value={item.value} key={item.value}>
            {item.label}
          </option>
        ))}
      </select> */}

      <Select
        value={selectedOption} // defaultMenuIsOpen
        onChange={handleDateChange}
        options={dateConstants}
        className={classes.reactCoinSelect}
        classNamePrefix='reactInnerCoinSelect'
        placeholder='Dates'
        styles={customStyles}
        isSearchable={false}
        components={{
          Input: (props) => <components.Input {...props} readOnly /> // eslint-disable-line
        }}
      />

      {/* <div>
        <p>Start Date: {dateRange.start}</p>
        <p>End Date: {dateRange.end}</p>
      </div> */}
    </div>
  )
}

export default DatePicker
