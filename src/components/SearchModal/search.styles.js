import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  leftSearchModal: {
    "& .MuiInputBase-formControl": {
      borderRadius: theme.spacing(0.5),
    }
  },

  lobbySearchWrap: {
    marginBottom: theme.spacing(3.5),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      display: "none",
      // marginTop: theme.spacing(6.25),
    },
    "& .search-icon": {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1.25),
    },
    "& .MuiTextField-root": {
      width: "100%",
      "& .MuiInputBase-formControl": {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.Pastel}`,
        "& fieldset": {
          border: '0',
        },

      },
      "& input": {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        "&::placeholder": {
          color: `${theme.colors.placeHolderText} !important`,
          fontSize: `${theme.spacing(1)} !important`,
          fontWeight: "500 !important",
        },

        "&:focus": {
          borderColor: "rgb(255, 255, 193)",
          boxShadow: "rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px",
          borderRadius: theme.spacing(2.18),
        },
      }
    }
  },
}))
