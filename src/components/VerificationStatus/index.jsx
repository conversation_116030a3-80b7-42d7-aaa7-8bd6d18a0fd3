import React from 'react'
import useStyles from '../../components/Modal/Signin/Signin.styles'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import  VerificationFailed  from '../ui-kit/icons/svg/verified-failed.svg'
import PropTypes from 'prop-types'
import DialogTitle from '@mui/material/DialogTitle'
import { Grid, Box, IconButton, Typography, DialogContent } from '@mui/material'


function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};
const VerificationStatus = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <>
      <Grid>
        <Grid>
          <DialogContent sx={{ padding: "0" }}>
            <Grid className='modal-section'>
              <Grid sx={{ width: "100%" }}>
                <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
                  <Typography variant='h4'>Verification Status</Typography>
                </DialogTitle>
                <IconButton
                  aria-label="close"
                  onClick={handleClose}
                  sx={{
                    position: 'absolute',
                    right: 8,
                    top: 8,
                    color: (theme) => theme.palette.grey[500],
                  }}
                >
                  <CloseIcon />
                </IconButton>
                <Box sx={{ width: '100%' }} style={{ margin: '10px', color: 'white' }} className={classes.modalWrapper}>
                  <img src={VerificationFailed} alt='Success' className='payment-status-icon' style={{ width: '30%', height: '30%' }} />
                  <Typography variant='h4'>Your Verification Has Been failed!</Typography>
                  <Typography>Please Contact our support team.</Typography>
                </Box>
              </Grid>
            </Grid>
          </DialogContent>
        </Grid>
      </Grid>
    </>
  )
}

export default VerificationStatus
