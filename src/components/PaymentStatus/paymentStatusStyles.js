import { makeStyles } from '@mui/styles'


const useStyles = makeStyles((theme) => ({
  root: {
    color: 'white !important',
    background: 'transparent !important',
    '& .MuiPaper-root': {
      background: 'transparent !important'
    },
    '& .MuiBackdrop-root': {
      background: 'rgba(0, 0, 0, 0.3) !important',
      backdropFilter: 'blur(3px) !important'
    },
    '& .payment-status-container': {
      height: '100%',
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      marginTop: '10rem',
      // backgroundImage: `url(${paymentBg})`,\
      background: '#24242480',
      backgroundSize: 'cover',
      alignItems: 'center',
      border: '1px solid #707070',
      borderRadius: '10px',
      marginBottom: '2rem',
      overflow: 'visible !important',
      '& .inner-modal-header': {
        color: 'white !important',
        display: 'none',
        width: '100%',
        // display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: theme.spacing(2),
        borderBottom: `1px solid ${theme.palette.divider}`,
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(2)
        },
        [theme.breakpoints.down('sm')]: {
          padding: theme.spacing(1)
        },
        '& .status-title': {
          fontSize: '26px',
          fontWeight: '700',
          color: 'white !important',
          [theme.breakpoints.down('sm')]: {
            fontSize: '18px'
          }
        }
      },
      '& .modal-close': {
        display: 'flex',
        alignItems: 'center'
      },

      '& .payment-status-modal': {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        textAlign: 'center',
        maxWidth: '25rem',
        position: 'relative',
        // overflow: 'hidden',
        padding: theme.spacing(2),
        marginTop: '3rem',

        [theme.breakpoints.down('sm')]: {
          padding: theme.spacing(2)
        },
        '& button': {
          marginTop: '1rem',
          display: 'none'
        },
        '&:after': {
          position: 'absolute',
          width: '90%',
          content: '""',
          height: '8px',
          background: '#00A30B',
          borderRadius: '0 0 10px 10px',
          bottom: '-8px'
        },
        '&.failed': {
          '&:after': {
            position: 'absolute',
            width: '90%',
            content: '""',
            height: '8px',
            background: '#D60000',
            borderRadius: '0 0 10px 10px',
            bottom: '-8px'
          }
        },
        '&.processing': {
          '&:after': {
            position: 'absolute',
            width: '90%',
            content: '""',
            height: '8px',
            background: '#FDB72E',
            borderRadius: '0 0 10px 10px',
            bottom: '-8px'
          }
        }
      },

      '& .payment-status-content': {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: theme.spacing(1),
        color: 'white !important',
        [theme.breakpoints.down('sm')]: {
          gap: theme.spacing(1)
        },
        '& h4': {
          fontWeight: '700',
          fontSize: '3rem',
          textTransform: 'uppercase',
          lineHeight: '1',
          paddingTop: '1rem',
          color: '#00A30B',
          [theme.breakpoints.down('sm')]: {
            fontSize: '2.5rem'
          },
          '&.failed': {
            color: '#D60000'
          },
          '&.processing': {
            color: '#FDB72E'
          }
        },
        '& p': {
          fontWeight: '500',
          fontSize: '1.5rem',
          // marginTop: '0.5rem',
          paddingTop: '0.5rem',
          lineHeight: '1',
          borderTop: '1px solid #494949',
          [theme.breakpoints.down('sm')]: {
            fontSize: '1rem'
          }
        },
        '& .success-animation': {
          background: '#D9D9D9',
          borderRadius: '100%',
          boxShadow: '0px 0px 10px 0px #000000 inset',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: '-12rem',
          [theme.breakpoints.down('sm')]: {
            height: '140px',
            width: '140px',
            marginTop: '-10rem'
          },
          '& svg': {
            width: '200px !important',
            transform: 'scale(1.8) !important',
            [theme.breakpoints.down('sm')]: {
              transform: 'scale(1.2) !important'
            }
          },
          '&:before': {
            content: '""',
            animation: 'mymove 2s infinite',
            position: 'absolute',
            height: '140px',
            width: '140px',
            borderRadius: '100%',
            backgroundColor: '#00FF00',
            [theme.breakpoints.down('sm')]: {
              height: '100px',
              width: '100px'
            }
          }
        },
        '& .failed-animation': {
          background: '#D9D9D9',
          borderRadius: '100%',
          boxShadow: '0px 0px 10px 0px #000000 inset',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: '-10.5rem',
          width: '170px',
          height: '170px',
          [theme.breakpoints.down('sm')]: {
            // height: '130px',
            marginTop: '-9rem',
            width: '100px',
            height: '100px'
          },
          '& svg': {
            width: '250px !important',
            transform: 'scale(1.5) !important',
            [theme.breakpoints.down('sm')]: {
              transform: 'scale(1) !important'
            }
          },
          '&:before': {
            content: '""',
            animation: 'mymove 2s infinite',
            position: 'absolute',
            height: '140px',
            width: '140px',
            borderRadius: '100%',
            backgroundColor: '#ff4e4e',
            [theme.breakpoints.down('sm')]: {
              height: '100px',
              width: '100px'
            }
          }
        },
        '& .processing-animation': {
          background: '#D9D9D9',
          borderRadius: '100%',
          boxShadow: '0px 0px 10px 0px #000000 inset',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: '-10.5rem',
          width: '170px',
          height: '170px',
          [theme.breakpoints.down('sm')]: {
            // height: '130px',
            marginTop: '-9rem',
            width: '100px',
            height: '100px'
          },
          '& svg': {
            width: '250px !important',
            transform: 'scale(1.1) !important',
            [theme.breakpoints.down('sm')]: {
              transform: 'scale(0.6) !important'
            }
          },
          '&:before': {
            content: '""',
            animation: 'mymove 2s infinite',
            position: 'absolute',
            height: '140px',
            width: '140px',
            borderRadius: '100%',
            backgroundColor: '#FDB72E50',
            [theme.breakpoints.down('sm')]: {
              height: '100px',
              width: '100px'
            }
          }
        },
        '& .amount-wrap': {
          borderTop: '1px solid #494949',
          '& h4': {
            fontSize: '1.5rem',
            fontWeight: '700',
            marginBottom: '0.5rem',
            '& span': {
              color: theme.colors.YellowishOrange
            }
          },
          '& h5': {
            fontSize: '1rem',
            fontWeight: '700',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            marginTop: '0.5rem'
          },
          '& .amount-text': {
            display: 'flex',
            justifyContent: 'space-between',
            // gap: '8px',
            flexDirection: 'column',
            alignItems: 'center',
            '& img': {
              width: '25px'
            },
            '& .bonus-text': {
              color: '#00A30B',
              fontWeight: '700',
              fontSize: '1rem'
            }
          }
        }
      },

      '& .payment-status-icon': {
        height: 80,
        width: 80,
        [theme.breakpoints.down('sm')]: {
          height: 60,
          width: 60
        }
      }
    }
  }
}))

export default useStyles
