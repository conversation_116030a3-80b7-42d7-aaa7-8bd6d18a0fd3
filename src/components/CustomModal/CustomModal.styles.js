import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  loginModal: {
    width: '800px',
    minWidth: '800px',
    maxWidth: '800px',
    margin: '0 auto',
    [theme.breakpoints.down('sm')]: {
      width: '100%',
      minWidth: '100%',
      maxWidth: '100%'
    },

    '& h2': {
      textAlign: 'center',
      color: theme.colors.textWhite,
      padding: '0',
      marginTop: '10px',
      fontSize: theme.spacing(2.0625),
      fontStyle: 'normal',
      fontWeight: '700',
      lineHeight: '86.768%',
      textTransform: 'uppercase',
      paddingBottom: theme.spacing(1)
    },
    '& .MuiDialog-scrollPaper': {
      width: '100%',
      minHeight: '100%',
      '& .MuiPaper-elevation': {
        margin: '0',
        width: '100%',
        backgroundColor: '#0C0A0E',
        borderRadius: '10px',
        boxShadow: 'none',
        [theme.breakpoints.down('sm')]: {
        }
      }
    },
    '& .MuiDialogContent-root': {
      padding: '0 !important',
      [theme.breakpoints.down('sm')]: {
        display: 'block'
        // alignItems: 'center',
        // justifyContent: 'center',
        // flexDirection: 'column',
      }
    },
    '& .modal-section': {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing(1),
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(1),
        display: 'block'
      }
    }

  },

  tabSection: {
    borderBottom: 1,
    borderColor: 'divider',
    width: '75%',
    margin: '0 auto',
    justifyContent: 'space-between',
    '& .MuiTabs-flexContainer': {
      justifyContent: 'space-between',
      borderRadius: theme.spacing(5),
      border: `1px solid ${theme.colors.YellowishOrange} !important`,
      padding: '3px',
      margin: theme.spacing(1.94, 0),
      '& button': {
        flexGrow: '1',
        color: theme.colors.warmGrey,
        fontSize: '0.9375rem',
        fontStyle: 'normal',
        fontWeight: 700,
        lineHeight: 'normal',
        minHeight: '40px',
        '&.Mui-selected': {
          borderRadius: theme.spacing(4.375),
          background: theme.colors.YellowishOrange,
          color: theme.colors.textWhite
        }
      }
    },
    '& .MuiTabs-indicator': {
      display: 'none'
    },
    '& button': {
      color: theme.colors.textWhite
    }
  },

  inputLabel: {
    color: theme.colors.textWhite,
    paddingBottom: '10px'
  },

  errorLabel: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    marginBottom: `${theme.spacing(1.5)} !important`,
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600 !important',
    textAlign: 'left'
  },

  countryCodeSelect: {
    width: '60px !important',
    position: 'absolute !important',
    zIndex: '9999',
    '& .MuiSelect-select': {
      padding: '10px',
      height: '42px',
      width: '60px',
      color: theme.colors.textWhite,
      overflow: 'visible !important'
    },
    '& fieldset': {
      borderRight: '0 !important',
      borderRadius: `${theme.spacing(0.25, 0, 0, 0.25)} !important`,
      borderColor: `${theme.colors.YellowishOrange} !important`,
      borderWidth: '1px',
      '&:focus': {
        borderWidth: '1px',
        borderColor: `${theme.colors.YellowishOrange} !important`
      },
      '& .MuiOutlinedInput-notchedOutline': {
        borderWidth: '1px',
        borderColor: `${theme.colors.YellowishOrange} !important`
      }
    },
    '& .MuiSvgIcon-root': {
      color: theme.colors.textWhite
    }
  },

  '& .Mui-focused': {
    '& .MuiOutlinedInput-notchedOutline': {
      borderWidth: '1px',
      borderColor: `${theme.colors.YellowishOrange} !important`
    }
  },

  inputParent: {
    position: 'relative',
    '& .phoneNumber': {
      '& input': {
        paddingLeft: '80px'
      }
    },
    '& .MuiInputAdornment-root': {
      position: 'absolute',
      right: '20px',
      zIndex: '1'
    },
    '& .MuiInputBase-root': {
      paddingRight: '0'
    },
    '& input': {
      color: `${theme.colors.textWhite}!important`,
      padding: '10px 14px',
      borderRadius: theme.spacing(0.25),
      border: `1px solid ${theme.colors.inputBorder} !important`,
      '&:focus': {
        borderRadius: theme.spacing(0.25),
        border: `1px solid ${theme.colors.YellowishOrange} !important`
      }
    },
    '& fieldset': {
      display: 'none'
    },
    '& .MuiFormLabel-root': {
      color: theme.colors.themeText,
      fontSize: theme.spacing(0.875),
      fontWeight: theme.typography.fontWeightMedium,
      marginBottom: theme.spacing(0.313),
      display: 'block'
    },
    '& .country-code-select': {
      width: '100px',
      color: 'white',
      marginRight: theme.spacing(1)
    },
    '& .auth-input-inner': {
      display: 'flex'
    }
  },

  loginWith: {
    color: theme.colors.quillGrey,
    textAlign: 'center',
    fontSize: '0.875rem',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: 'normal',
    padding: theme.spacing(1.25, 0),
    '& .MuiTypography-root': {
      lineHeight: 'normal'
    }
  },

  submitBtn: {
    '& button': {
      width: '100%',
      borderRadius: theme.spacing(23.25),
      background: theme.colors.YellowishOrange,
      color: theme.colors.textWhite,
      fontSize: '1rem',
      fontWeight: 700,
      marginBottom: '1.5rem',
      lineHeight: theme.spacing(1.8),
      boxShadow: 'none',
      '&:hover': {
        background: theme.colors.YellowishOrange,
        boxShadow: 'none'
      },
      '&.Mui-disabled': {
        color: 'rgb(255 255 255 / 70%)',
        boxShadow: 'none',
        backgroundColor: 'rgb(253 183 46 / 70%)'
      }
    }
  },

  forgetLink: {
    textAlign: 'right',
    padding: theme.spacing(1, 0, 0, 0),
    '& a': {
      color: theme.colors.quillGrey,
      textAlign: 'right',
      fontSize: '0.875rem',
      fontStyle: 'normal',
      fontWeight: 400,
      lineHeight: 'normal',
      textDecorationLine: 'underline'
    }
  },

  dontAccount: {
    '& p': {
      color: theme.colors.quillGrey,
      textAlign: 'center',
      fontSize: '0.875rem',
      fontStyle: 'normal',
      fontWeight: 400,
      padding: theme.spacing(1.25, 0),
      '& a': {
        color: theme.colors.quillGrey,
        fontSize: '0.875rem',
        fontWeight: 600,
        lineHeight: 'normal',
        textDecorationLine: 'underline'
      }
    }
  },

  buttonGrp: {
    '& button': {
      border: '1px solid transparent',
      boxShadow: 'none',
      backgroundColor: 'transparent',
      padding: '0',
      gap: theme.spacing(1),
      '&:hover': {
        border: '1px solid transparent',
        boxShadow: 'none',
        backgroundColor: 'transparent',
        padding: '0'
      },
      '& svg': {
        fontSize: theme.spacing(0.8)
      }
    }
  },

  modalWrapper: {
    display: 'inline-block',
    margin: '0 1.5rem',
    width: 'calc(100% - 3rem) !important',
    '& .MuiBox-root': {
      padding: '0'
    }
  },

  bottomSection: {
    margin: '0 1.5rem',
    width: 'calc(100% - 3rem) !important'
  },

  termCondition: {
    '& svg': {
      color: theme.colors.quillGrey
    },
    '& span': {
      color: theme.colors.quillGrey,
      fontSize: '0.875rem',
      fontStyle: 'normal',
      fontWeight: 400,
      lineHeight: 'normal'
    },
    '& .conditionText': {
      textDecoration: 'underline',
      color: theme.colors.quillGrey
    }
  },
  promoCodeParent: {
    '& .MuiAccordionSummary-gutters': {
      padding: '0',
      color: theme.colors.quillGrey
    },
    '& .MuiSvgIcon-root': {
      color: theme.colors.textWhite
    },
    '& .MuiAccordionSummary-root': {
      '&.Mui-expanded': {
        margin: '0'
      }
    },
    '.accordionSummary': {
      '.promoCode': {
        minHeight: 'auto',
        '& .MuiAccordionSummary-content': {
          '& img': {
            marginRight: '15px'
          }
        }
      },
      accordionDetails: {
        padding: '0',
        color: theme.colors.textWhite
      }
    }
  },
  buttonDisable: {
    opacity: '0.5'
  }
}))
