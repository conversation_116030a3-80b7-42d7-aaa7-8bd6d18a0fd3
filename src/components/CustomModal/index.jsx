import React from 'react'
import { Grid, <PERSON>, DialogContent, IconButton, Typography } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './CustomModal.styles'
import CloseIcon from '@mui/icons-material/Close'

function CustomTabPanel (props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  )
}

const CustomModal = ({ children }) => {
  const classes = useStyles()

  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <Grid>
      <Grid>

        <DialogContent sx={{ padding: '0' }}>
          <Grid className='modal-section'>
            <Grid sx={{ width: '100%' }}>
              <IconButton
                aria-label='close'
                onClick={handleClose}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: (theme) => theme.palette.grey[500]
                }}
              >
                <CloseIcon />
              </IconButton>
            </Grid>
          </Grid>
          <Box sx={{ width: '100%' }} style={{ padding: '0' }} className={classes.modalWrapper}>
            <CustomTabPanel value={0} index={0} style={{ padding: '0' }}>
              {children}
            </CustomTabPanel>
          </Box>
        </DialogContent>
      </Grid>
    </Grid>
  )
}

export default CustomModal
