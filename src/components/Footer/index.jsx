import { Grid, <PERSON>, Typography } from '@mui/material'
import React from 'react'
import useStyles from './Footer.styles'
import { FooterQuerys } from '../../reactQuery'
import { useNavigate } from 'react-router-dom'
import { payment1, payment2, payment3, payment4, payment5, payment6 } from '../ui-kit/icons/footer'
import { Age } from '../ui-kit/icons/svg'
import { getLoginToken } from '../../utils/storageUtils'
import { useUserStore } from '../../store/useUserSlice'
import LazyLoad from 'react-lazy-load'
import { currectYear } from '../../utils/helpers'
import LazyImage from '../../utils/lazyImage'

const Footer = () => {
  const classes = useStyles()
  const auth = useUserStore((state) => state)
  const { data: cmsLinks } = FooterQuerys.getCmsQuery()

  let cmsData = []
  if (!!getLoginToken() || auth.isAuthenticate) {
    cmsData = cmsLinks?.length > 0 ? cmsLinks?.filter((info) => !info?.isHidden) : []
  } else {
    cmsData = cmsLinks?.length > 0 ? cmsLinks?.filter((info) => !info?.isHidden && info?.slug !== 'responsible-sweepstake-rules') : []
  }

  const navigate = useNavigate()

  return (
    <Grid className={classes.lobbyRight}>
      <Grid className={classes.wrapper}>
        <Grid className='footer-content-wrap'>
          <Grid className='footer-content-age-wrap'>
            <Grid container spacing={0.5}>
              <Grid item xs={12} sm={12} lg={12}>
                <Grid className={classes.contentSection} flexDirection='column' display='flex' justifyContent='space-around'>
                  <Grid className='footer-cms-wrap' style={{ minHeight: '30px' }}>
                    {(cmsData && cmsData?.length > 0)
                      ? (
                          cmsData?.map((data, index) => {
                            return (
                              <Link
                                className='footerLink'
                                onClick={() => navigate(`/cms/${data?.slug}`)} key={index}
                              >
                                {data?.title?.EN}
                              </Link>
                            )
                          }))
                      : (
                        <></>
                        )}
                  </Grid>
                </Grid>
                <Grid className='about-footer'>
                  <LazyLoad>
                    <Typography>NO PURCHASE IS NECESSARY to enter free game promotion (“promotional games”). PROMOTIONAL GAMES ARE VOID WHERE PROHIBITED BY LAW. For
                      detailed rules, see Terms and conditions. The Money Factory is a play-for-fun website intended for amusement purposes only. The Money Factory does not
                      offer “real-money gambling” or opportunity to win real money based on a gameplay. The Money Factory is only open to Eligible Participants, who are at
                      least eighteen (18) years old or the age of majority in their jurisdiction (whichever occurs later) at the time of entry.
                    </Typography>
                  </LazyLoad>
                </Grid>
                <Grid className='footer-content'>
                  <Grid className='payment-options'>
                    {/* <LazyImage
                      src={payment1}
                      alt='Payment1'
                      lazy
                      style={{ width: '100%', height: '100%' }}
                    />
                    <LazyImage
                      src={payment2}
                      alt='Payment2'
                      lazy
                      style={{ width: '100%', height: '100%' }}
                    />
                    <LazyImage
                      src={payment3}
                      alt='Payment3'
                      lazy
                      style={{ width: '100%', height: '100%' }}
                    />
                    <LazyImage
                      src={payment4}
                      alt='Payment4'
                      lazy
                      style={{ width: '100%', height: '100%' }}
                    />
                    <LazyImage
                      src={payment5}
                      alt='Payment5'
                      lazy
                      style={{ width: '100%', height: '100%' }}
                    />
                    <LazyImage
                      src={payment6}
                      alt='Payment6'
                      lazy
                      style={{ width: '100%', height: '100%' }}
                    /> */}
                    <img src={payment1} loading='lazy' alt='Payment' width='100%' height='100%' />
                    <img src={payment2} loading='lazy' alt='Payment' width='100%' height='100%' />
                    <img src={payment3} loading='lazy' alt='Payment' width='100%' height='100%' />
                    <img src={payment4} loading='lazy' alt='Payment' width='100%' height='100%' />
                    <img src={payment5} loading='lazy' alt='Payment' width='100%' height='100%' />
                    <img src={payment6} loading='lazy' alt='Payment' width='100%' height='100%' />
                  </Grid>
                </Grid>
                <Grid className='footer-instructions'>
                  <img src={Age} alt='adult' height='100%' width='100%' />
                  <Typography>@ {currectYear} themoneyfactory.com I All rights reserved.</Typography>
                </Grid>
              </Grid>

            </Grid>

          </Grid>

        </Grid>

      </Grid>
    </Grid>
  )
}

export default Footer
