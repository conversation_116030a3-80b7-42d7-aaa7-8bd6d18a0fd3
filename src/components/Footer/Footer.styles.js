import { makeStyles } from '@mui/styles'

import { LobbyRight, Container } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    marginTop: theme.spacing(0.625),
    // minHeight: 'auto',
    [theme.breakpoints.down('sm')]: {
      marginTop: '0'
    }
  },

  
  wrapper: {
    // backgroundImage: `url(${FooterBg})`,
    // backgroundSize: "cover",
    marginBottom: '0',
    '& .border-bottom': {
      borderBottom: '1px solid #545454'
    },
    '& .border': {
      // borderTop: '1px solid #545454',
      // borderBottom: '1px solid #545454',
      marginLeft: theme.spacing(0),
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        border: 'none',
        padding: theme.spacing(0)
      }
    },

    '& .footer-content-wrap': {
      ...Container(theme),
      // [theme.breakpoints.down('md')]: {
      //   padding:theme.spacing(0, 1)
      // },
      '& .footer-container': {
        alignItems: 'center'
      },
      '& .payment-options': {
        display: 'flex',
        justifyContent: 'center',
        marginTop: theme.spacing(1),
        gap: theme.spacing(3),
        marginBottom: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          marginTop: theme.spacing(1)
        },
        [theme.breakpoints.down('sm')]: {
          gap: theme.spacing(0.2),
          marginBottom: theme.spacing(0.313),
          justifyContent: 'center'
        },
        '& img': {
          width: theme.spacing(6.5),
          [theme.breakpoints.down('lg')]: {
            width: theme.spacing(3)
          },
          [theme.breakpoints.down('md')]: {
            width: theme.spacing(3.5)
          },
          [theme.breakpoints.down(360)]: {
            width: theme.spacing(2.5)
          }
        }
      },
      '& .footer-content-age-wrap': {
        display: 'flex',
        alignItems: 'center',
        [theme.breakpoints.down('lg')]: {
          paddingBottom: theme.spacing(4)
        },
        [theme.breakpoints.down('md')]: {
          paddingBottom: theme.spacing(1)
        },
        '& .MuiGrid-container': {
          alignItems: 'flex-end'
        }
      }
    },
    '& .footer-instructions': {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing(1),
      justifyContent: 'center',
      marginTop: theme.spacing(0.625),
      [theme.breakpoints.down('md')]: {
        flexDirection: 'column'
      },
      '& img': {
        width: theme.spacing(2.0625),
        height: theme.spacing(2.0625),
        marginBottom: theme.spacing(0.625),
        [theme.breakpoints.down('lg')]: {
          width: theme.spacing(3.0625),
          height: theme.spacing(3.0625),
          marginBottom: 0
        }
      },
      '& p': {
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(0.875)
        },
        [theme.breakpoints.down('md')]: {
          textAlign: 'center'
        }
      }
    },
    '& .footerBottomDetail': {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
      [theme.breakpoints.down('sm')]: {
        alignItems: 'center'
      },
      color: '#fff',
      [theme.breakpoints.down('md')]: {
        marginBottom: '10px',
        padding: '5px 0'
      }
    },
    '& .site-map-wrap': {
      background: theme.colors.coinBundle,
      borderRadius: theme.spacing(0.625),
      minHeight: theme.spacing(19.125),
      padding: theme.spacing(1, 1.5),
      maxWidth: theme.spacing(79.1875),
      margin: '0 auto',
      display: 'flex',

      '& .site-map-grid': {
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: theme.spacing(1.25),
        [theme.breakpoints.down(375.99)]: {
          gridTemplateColumns: 'repeat(2, 1fr)'
        },
        '& .site-map-grid-card': {
          '& ul': {
            listStyle: 'none',
            paddingLeft: theme.spacing(1),
            '& li': {
              position: 'relative',
              cursor: 'pointer',
              '&:before': {
                position: 'absolute',
                left: theme.spacing(-1.25),
                top: theme.spacing(1.3125),
                background: theme.colors.sitemapDot,
                height: theme.spacing(0.313),
                width: theme.spacing(0.313),
                borderRadius: '100%',
                content: "''",
                [theme.breakpoints.down('lg')]: {
                  top: theme.spacing(0.625)
                }
                // [theme.breakpoints.down('md')]: {
                //   top: theme.spacing(0.625),
                // },
              },
              '& a': {
                fontSize: theme.spacing(1),
                fontWeight: '500',
                padding: theme.spacing(0.75, 0),
                lineHeight: theme.spacing(1.595),
                display: 'block',
                [theme.breakpoints.down('lg')]: {
                  fontSize: theme.spacing(0.875),
                  lineHeight: theme.spacing(1),
                  padding: theme.spacing(0.313, 0)
                },
                [theme.breakpoints.down('md')]: {
                  fontSize: theme.spacing(0.75),
                  lineHeight: theme.spacing(1),
                  padding: theme.spacing(0.313, 0)
                }
              },
              '&.active, &:hover': {
                '& a': {
                  color: theme.colors.YellowishOrange,
                  cursor: 'pointer'
                }
              }
            }
          }
        }
      },
      '& .MuiGrid-container': {
        alignItems: 'center'
      }
    },
    '& .social-links': {
      [theme.breakpoints.down('md')]: {
        textAlign: 'center'
      },
      '& a': {
        display: 'inline-block',
        padding: theme.spacing(0.313),
        '& img': {
          width: theme.spacing(3.5),
          [theme.breakpoints.down(1400)]: {
            width: theme.spacing(1.6875)
          }
        }
      }
    },

    '& a': {
      color: theme.colors.textWhite,
      textDecoration: 'none'
    },
    '& .footerDropdownParent': {
      [theme.breakpoints.down('md')]: {
        flexDirection: 'column'
      }
    },
    '& .about-footer': {
      padding: theme.spacing(0, 0, 0),

      '& p': {
        fontDisplay: 'swap',
        fontSize: theme.spacing(1),
        fontWeight: '500',
        color: theme.colors.landingFooterText,
        marginTop: theme.spacing(1.375),
        textAlign: 'center',
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(0.875)
        },
        [theme.breakpoints.down('md')]: {
          textAlign: 'center'
        },
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75),
          marginTop: 0,
          padding: theme.spacing(0.625, 0),
          textAlign: 'center'
        }
      }
    },
    '& .support-link': {
      textAlign: 'center',

      fontSize: theme.spacing(1.125),
      position: 'relative',
      '&:before, &:after': {
        position: 'absolute',
        width: '1px',
        top: '50%',
        left: theme.spacing(-1),
        transform: 'translate(-50%, -50%)',
        content: "''",
        height: theme.spacing(6.875),
        background: theme.colors.footerBorder
      },
      '&:after': {
        left: 'auto',
        right: theme.spacing(-1)
      }
    },
    '& .mob-age': {
      textAlign: 'center',
      [theme.breakpoints.down('sm')]: {
        display: 'none !important'
      },
      '& a': {
        wordBreak: 'break-all',
        display: 'none',

        fontSize: theme.spacing(1),
        fontWeight: '500',
        [theme.breakpoints.down('md')]: {
          display: 'none',
          fontSize: theme.spacing(0.7)
        }
      },
      '& img': {
        width: theme.spacing(6.25),
        [theme.breakpoints.down('md')]: {
          width: theme.spacing(3.25)
        }
      }
    },
    '& .mob-adult-wrap': {
      display: 'none',
      [theme.breakpoints.down('md')]: {
        display: 'block'
      },
      '& .mob-adult': {
        width: '100%',
        textAlign: 'center',
        borderBottom: `1px solid ${theme.colors.footerBorder}`,
        padding: theme.spacing(1.375, 0),
        '& img': {
          width: theme.spacing(5),
          height: theme.spacing(5),
          margin: '0 auto'
        }
      }
    },
    '& .web-age-instructions': {
      [theme.breakpoints.down('sm')]: {
        display: 'none !important'
      }
    },
    '& .support-link-wrap': {
      [theme.breakpoints.down('md')]: {
        display: 'none'
      }
    }
  },

  footerLogo: {
    [theme.breakpoints.down('sm')]: {
      paddingLeft: '0 !important'
    },

    [theme.breakpoints.down('md')]: {
      textAlign: 'center',
      justifyContent: 'center'
    },
    '& img': {
      width: theme.spacing(12),
      [theme.breakpoints.down('lg')]: {
        width: theme.spacing(7)
      },
      [theme.breakpoints.down('md')]: {
        width: theme.spacing(10)
      }
    },
    '& .footer-logo-content': {
      display: 'flex',
      alignItems: 'center',
      flexDirection: 'column',
      fontWeight: '500',
      position: 'relative',
      [theme.breakpoints.down('sm')]: {
        marginTop: theme.spacing(0)
        // "&:before": {
        //   position: "absolute",
        //   right: theme.spacing(-1.375),
        //   top: "0",
        //   content: "''",
        //   width: "1px",
        //   height: "100%",
        //   background: theme.colors.footerBorder,
        // }
      },

      '& img': {
        // margin:theme.spacing(1),
      },
      '& .MuiLink-root': {
        margin: theme.spacing(1, 0),
        [theme.breakpoints.down(1400)]: {
          margin: theme.spacing(1, 0),
          fontSize: theme.spacing(0.75)
        }
      },
      '& .mob-age-wrap': {
        textAlign: 'center',
        [theme.breakpoints.down('md')]: {
          display: 'none'
        }
      }
    }
  },

  contentSection: {
    [theme.breakpoints.down('sm')]: {
      paddingLeft: '0 !important'
    },

    '& .footerLink': {
      color: theme.colors.textWhite,
      textDecoration: 'none',
      fontSize: theme.spacing(1),
      padding: theme.spacing(0.5, 0),
      cursor: 'pointer',
      textTransform: 'capitalize',
      wordWrap: 'break-word',
      fontWeight: '600',
      [theme.breakpoints.down(1400)]: {
        fontSize: `${theme.spacing(0.75)} !important`
      },
      [theme.breakpoints.down('sm')]: {
        textAlign: 'center'
      },
      '&:hover': {
        color: theme.colors.YellowishOrange
      }
    },
    '& .footer-cms-wrap': {
      display: 'flex',
      maxWidth: theme.spacing(50),
      margin: '0 auto',
      flexWrap: 'wrap',
      gap: '15px',
      justifyContent: 'space-between',
      // margin: "3rem auto 1rem",
      width: '100%',
      paddingLeft: theme.spacing(1),
      [theme.breakpoints.down('sm')]: {
        gap: '0px'
      },

      '& .footerLink': {
        position: 'relative',
        fontSize: theme.spacing(1.125),
        fontWeight: '500',
        // "&:before": {
        //   position: "absolute",
        //   width: theme.spacing(0.313),
        //   height: theme.spacing(0.313),
        //   borderRadius: "100%",
        //   background: theme.colors.textWhite,
        //   content: "''",
        //   left: theme.spacing(-1),
        //   top: theme.spacing(1),
        //   [theme.breakpoints.down("md")]: {
        //     display: "none"
        //   },
        // },
        [theme.breakpoints.down(1400)]: {
          fontSize: theme.spacing(0.75)
        },
        [theme.breakpoints.down('sm')]: {
          width: '100%',
          textAlign: 'center',
          fontSize: theme.spacing(0.75)
        }
      }
    },

    '& .footerDropdown': {
      '& em, & svg': {
        color: theme.colors.textWhite,
        position: 'static'
      },
      '& .MuiSelect-select': {
        minWidth: 'auto',
        overflow: 'visible !important',
        padding: '0',
        color: theme.colors.textWhite
      },
      '& .MuiFormControl-root': {
        padding: '0',
        margin: '0'
      },
      '& fieldset': {
        border: '0'
      }
    }
  }
}))
