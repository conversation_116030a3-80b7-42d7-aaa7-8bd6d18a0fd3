import { makeStyles } from '@mui/styles'

import spinBg from '../../components/ui-kit/icons/webp/spin-modal-bg.webp'

export default makeStyles(() => ({
  freeSpinModal: {
    '& .freeSpinModalBox': {
      maxWidth: 350,
      width: '100%',
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%,-50%)',
      backgroundSize: 'cover',
      borderRadius: '10px',
      backgroundImage: `url(${spinBg})`,
      backgroundPosition: 'center',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: ' 1rem',
      '& img': {
        width: '100%'
      },

      '& h3': {
        background: 'linear-gradient(171.6deg, #FFC538 6.97%, #E37A34 68.29%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        fontWeight: '700',
        fontSize: '2rem'
      },
      '& h5': {
        color: '#fff',
        fontWeight: '600',
        fontSize: '1.25rem'
      },
      '& p': {
        fontSize: '1rem',
        fontWeight: '600',
        lineHeight: '1.1',
        textAlign: 'center',
        color: '#fff',
        marginBottom: '12px'
      },
      '& .close-icon': {
        position: 'absolute',
        top: '1rem',
        right: '1rem',
        cursor: 'pointer',
        width: '0.875rem'
      },
      '& .game-card': {
        borderRadius: '12px',
        maxWidth: '200px',
        margin: '10px auto',
        display: 'flex',
        filter: 'drop-shadow(0 0 30px #7C0C92)',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        padding: '2px',
        background: 'linear-gradient(171.6deg, #FFC538 6.97%, #E37A34 68.29%)',
        '& img': {
          width: '100%',
          height: '100%',
          borderRadius: '13px'
        },
        '& .ribbon': {
          height: 'auto',
          width: '220px',
          bottom: '-45px',
          '& h5': {
            position: 'absolute',
            top: '50%',
            left: '50%',
            fontWeight: '700',
            transform: 'translate(-50%,-50%)'
          }
        }
      },
      '& button': {
        marginTop: '26px'
      }
    }
  }
}))
