import React, { useCallback, useEffect, useState } from 'react'
import { Box, Typography } from '@mui/material'
import { useCoinStore, usePortalStore, useUserStore } from '../../store/store'
import { Button, Grid, DialogContent, IconButton, CircularProgress } from '@mui/material'
import { errorHandler, useGetGameLink, useGetProfileMutation } from '../../reactQuery'
import { useClaimFreeSpinBonusMutation } from '../../reactQuery/bonusQuery'
import useStyles from './styles.js'

import ribbon from '../../components/ui-kit/icons/webp/spin-ribbon.webp'
import LazyImage from '../../utils/lazyImage.jsx'
import DefaultImg from '../../../src/components/ui-kit/icons/utils/casinoGames.webp'

import { useNavigate } from 'react-router-dom'
import { PlayerRoutes } from '../../routes.js'

import closeIcon from '../../../src/components/ui-kit/icons/png/sidebar-cross.png'
import toast from 'react-hot-toast'

export default function FreeSpinModal({ data }) {
  const freeSpinData = data[0]

  const classes = useStyles()
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const [isLoading, setIsLoading] = useState(false)
  const [coinType, setCoinType] = useState(freeSpinData?.coinType )
   const coin = useCoinStore ((state) => {
      return state.coinType
    })

   
  const setCoin = useCoinStore((state) => {
      return state.setCoinType
    })

   const updateCoinType = () => {
      if (coinType !== coin) {
        setCoin(coinType)
        toast(`You've successfully switched to ${coinType} coin!`)
      }
    }


const handlePlayNow = () => {

      if (coin == 'SC' ) {
        updateCoinType()
     
        navigate(`${PlayerRoutes.GamePlay.split(':').shift()}${freeSpinData?.masterCasinoGameId}`)
      } else if (coin == 'GC' ) {
        updateCoinType()
 
         navigate(`${PlayerRoutes.GamePlay.split(':').shift()}${freeSpinData?.masterCasinoGameId}`)
      } 
    
  }

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      portalStore.closePortal()
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const handleClose = () => {
    portalStore.closePortal()
    getProfileMutation.mutate()
  }
  const mutationClaimFreeSpinBonus = useClaimFreeSpinBonusMutation({
    onSuccess: (res) => {
      setIsLoading(false)
     
      getProfileMutation.mutate()
      portalStore.closePortal()
      if(!res?.data?.success){
        toast.error("An issue occurred with the third-party service. Please try again later.");
      }
      handlePlayNow()
    },
    onError: (error) => {
      errorHandler(error)
      portalStore.closePortal()
    }
  })
  const claimfreeSpinBonus = () => {
    setIsLoading(true)
    mutationClaimFreeSpinBonus.mutate({ userBonusId: freeSpinData?.userBonusId })
  }

  return (
    <Box className='freeSpinModalBox'>
      <img className='close-icon' onClick={handleClose} src={closeIcon} alt='Close' />
      <Typography variant='h3'>Congratulations!</Typography>
      <Typography variant='h5'>You’ve Unlock Free Spin!</Typography>
      <Typography variant='body1'>Claim your reward and spin for big wins.</Typography>
      <Typography variant='h5'>{freeSpinData?.gameName}</Typography>
      <Box className='game-card'>
        <LazyImage
          src={freeSpinData?.gameImageUrl || DefaultImg}
          className='casinoGame-img'
          width='100%'
          height='auto'
        />
        <Box className='ribbon'>
          <img src={ribbon} />
          <Typography variant='h5'>{freeSpinData?.freeSpinRound} free Spins</Typography>
        </Box>
      </Box>
      <Box>
 <Typography variant='h5' style={{top:"20px"}}> Amount : {freeSpinData?.freeSpinAmount} {freeSpinData?.coinType}</Typography>
      </Box>
       
      <Button variant='contained' className='btn-primary' onClick={claimfreeSpinBonus} disabled={isLoading}>
        Claim Free Spin
        {isLoading && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
      </Button>
    </Box>
  )
}
