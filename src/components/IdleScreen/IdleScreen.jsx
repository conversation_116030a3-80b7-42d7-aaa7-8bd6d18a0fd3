import React from 'react'
import { <PERSON>rid, Box, DialogContent, Typography, Button } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './IdleScreen.styles'
import Signin from '../Modal/Signin'
import { idlePopupBg } from '../ui-kit/icons/opImages'


const IdleScreen = (props) => {
  const classes = useStyles()

  const portalStore = usePortalStore((state) => state)

  const handleLoginOpen = () => {
    portalStore.openPortal(() => <Signin />, 'loginModal')
  }

  return (
    <Grid className={classes.idleModal}>
      <DialogContent sx={{ padding: '0' }}>
        <Box sx={{ width: '100%' }} style={{ padding: '0' }} className={classes.modalWrapper}>
          <img className='idle-img' src={idlePopupBg} alt='idle-bg' />
          <Typography className='idle-head' variant='h4'>
            Session Timeout
          </Typography>
          {props?.message
            ? (
              <Box className='idle-body'>
                <Typography sx={{ color: '#FFFFFF' }} className='idle-text idle-text2' variant='h6'>
                  {props?.message}
                </Typography>
                <Button variant='outlined' onClick={handleLoginOpen} className='login'>
                  Login
                </Button>
              </Box>)
            : (
              <Box className='idle-body'>
                <Typography sx={{ color: '#FFFFFF' }} className='idle-text idle-text2 ' variant='h6'>
                  You've been away for a while, so we ended your session to protect your account.  Log back in to get started again!
                  {/* Moving to another device, are we? You're logged out here to keep your account safe */}
                </Typography>
                <Button variant='outlined' onClick={handleLoginOpen} className='login'>
                  Login
                </Button>
              </Box>)}
        </Box>
      </DialogContent>
    </Grid>
  )
}

export default IdleScreen
