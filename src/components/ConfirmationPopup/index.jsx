import React from 'react'
import {
  <PERSON>rid,
  IconButton,
  Typo<PERSON>, Button,
  DialogContent
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
const ConfirmationPopup = ({ message, handleConfirm }) => {
  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <>
      <Grid className='inner-modal-header'>
        <Typography variant='h4'> Confirmation </Typography>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
      <DialogContent className='payment-status-modal'>
        <Grid className='payment-status-content'>
          <Typography>{message}</Typography>
          <Button onClick={() => handleConfirm('yes')}>Yes</Button>
          <Button onClick={handleClose}>No</Button>
        </Grid>
      </DialogContent>
    </>
  )
}

export default ConfirmationPopup
