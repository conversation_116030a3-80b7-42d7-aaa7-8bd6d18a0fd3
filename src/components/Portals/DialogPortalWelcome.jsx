import React from 'react'
import { userPortalSliceWelcome } from '../../store/store'
import { Dialog, Slide } from '@mui/material'
import useStyles from '../../pages/Lobby/components/UsernamePopup/Username.styles'
import useAuthStyles from '../../pages/Lobby/CommonStyles/Auth.styles'
import useAccountStyles from '../../pages/Accounts/Accounts.styles'
import usePopStyles from '../WelcomeBonus/WelcomeBonus.styles'
import usePopImgStyles from '../BonusModal/ImgModal.styles'
import useDailyBonusStyles from '../SideBar/sidebar.styles'

export const DialogPortalWelcome = () => {
  const portalStore = userPortalSliceWelcome((state) => state)
  const classes = useStyles()
  const style = useAuthStyles()
  const accountClasses = useAccountStyles()
  const popStyles = usePopStyles()
  const imgStyles = usePopImgStyles()
  const dailyBonusStyles = useDailyBonusStyles()

  const { Component, theme } = portalStore
  const children = Component ? <Component /> : null

  const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction='up' ref={ref} {...props} />
  })

  const themeToClasses = {
    authModal: style.authModal,
    innerModal: classes.innerModal,
    accountModal: accountClasses.innerModal,
    popModal: popStyles.popModal,
    imgModal: imgStyles.imgModal,
    dailyBonusModal: dailyBonusStyles.rewardStepModal
  };

  return (
    <>
    {portalStore.isVisible && theme && (

    <Dialog
      open={portalStore.isVisible}
      className={themeToClasses[theme]}
      aria-labelledby='alert-dialog-title'
      aria-describedby='alert-dialog-description'
      TransitionComponent={theme === 'authModal' ? Transition : undefined}
      fullScreen={theme === 'authModal'}
    >
      {portalStore.isVisible && children}
    </Dialog> )
    }
       </>
  )
}
