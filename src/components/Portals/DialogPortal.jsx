import React, { useEffect } from 'react'
import { Dialog, Slide } from '@mui/material'
import useStyles from '../../pages/Lobby/components/UsernamePopup/Username.styles'
import useAuthStyles from '../../pages/Lobby/CommonStyles/Auth.styles'
import useAccountStyles from '../../pages/Accounts/Accounts.styles'
import useBonusStyles from '../../components/DailyBonus/Bonus.styles'
import useTrustlyStyles from '../../pages/Store/PaymentModal/components/TrustlyPopup.styles.js'
import useBonusStreakStyles from '../../components/DailyBonus/BonusStreak.styles'
import usePopStyles from '../../components/WelcomeBonus/WelcomeBonus.styles'
import usePopImgStyles from '../BonusModal/ImgModal.styles'
import useDailyBonusStyles from '../../components/SideBar/sidebar.styles'
import useLoginStyle from '../../components/Modal/Signin/Signin.styles'
import useTermsNConditionStyle from '../../components/Modal/TermAndCondition/TermAndCondition.styles'
import useValutStyle from '../../pages/Vault/style'
import useWithdrawlStyle from '../../pages/Accounts/components/Withdrawl.styles'
import useCmsModalStyle from '../CmsModal/styles'
import usePurchasePopupStyle from '../../pages/GamePlay/insufficient.style'
import useVipStyle from '../../../src/pages/Accounts/components/DynamicVipForm/vipmodal.styles.js'
import useFreeSpinStyle from '../FreeSpinModal/styles.js'
import { usePortalStore } from '../../store/userPortalSlice'

export const DialogPortal = () => {
  const portalStore = usePortalStore((state) => state)
  const classes = useStyles()
  const style = useAuthStyles()
  const accountClasses = useAccountStyles()
  const popStyles = usePopStyles()
  const imgStyles = usePopImgStyles()
  const dailyBonusStyles = useDailyBonusStyles()
  const bonusClasses = useBonusStyles()
  const trustlyClasses = useTrustlyStyles()
  const loginClasses = useLoginStyle()
  const termsNConditionClasses = useTermsNConditionStyle()
  const valutModalClasses = useValutStyle()
  const withdrawModalClasses = useWithdrawlStyle()
  const cmsModalClasses = useCmsModalStyle()
  const bonusStreakClasses = useBonusStreakStyles()
  const purchasePopupClasses = usePurchasePopupStyle()
  const useVipStyleClasses = useVipStyle()
  const freeSpinClasses = useFreeSpinStyle()

  const { Component, theme } = portalStore
  const children = Component ? <Component /> : null

  const Transition = React.forwardRef(function Transition (props, ref) {
    return <Slide direction='up' ref={ref} {...props} />
  })

  const themeToClasses = {
    authModal: style.authModal,
    innerModal: classes.innerModal,
    accountModal: accountClasses.innerModal,
    trustlyConnection: trustlyClasses.trustlyConnection,
    popModal: popStyles.popModal,
    imgModal: imgStyles.imgModal,
    dailyBonusModal: dailyBonusStyles.rewardStepModal,
    bonusModal: bonusClasses.bonusModal,
    loginModal: loginClasses.loginModal,
    signupModal: loginClasses.loginModal,
    termsNConditionModal: termsNConditionClasses.termsNConditionModal,
    valutModal: valutModalClasses.valutFromModal,
    withdrawModal: withdrawModalClasses.withdrawModal,
    cmsModal: cmsModalClasses.cmsModalWrap,
    welcomeBonusModal: bonusClasses.bonusModal,
    bonusStreak: bonusStreakClasses.bonusModalWrap,
    purchasePopupModal: purchasePopupClasses.purchasePopupModal,
    vipModal: useVipStyleClasses.vipCompletionPopUp,
    freeSpinModal: freeSpinClasses.freeSpinModal
  }

  const dataTracking = {
    bonusModal: 'Play.Bonus.Claim.Dialog',
    termsNConditionModal: 'AfterLogin.ImportantUpdate.Dialog',
    welcomeBonusModal: 'Play.Bonus.WecomeBonus.Dialog',
    loginModal: 'Login.Dialog',
    signupModal: 'Signup.Dialog'
  }
  useEffect(() => {
    if (portalStore.isVisible) {
      document.documentElement.classList.add('modal-open')
    } else {
      document.documentElement.classList.remove('modal-open')
    }

    return () => {
      document.documentElement.classList.remove('modal-open')
    }
  }, [portalStore.isVisible])

  return (
    <>
      {portalStore.isVisible && theme && (
        <Dialog
          open={portalStore.isVisible}
          className={themeToClasses[theme]}
          aria-labelledby='alert-dialog-title'
          aria-describedby='alert-dialog-description'
          TransitionComponent={theme === 'authModal' ? Transition : undefined}
          fullScreen={theme === 'authModal'}
          PaperProps={{
            'data-tracking': dataTracking[theme]
          }}
        >
          {portalStore.isVisible && children}
        </Dialog>
      )}
    </>
  )
}
