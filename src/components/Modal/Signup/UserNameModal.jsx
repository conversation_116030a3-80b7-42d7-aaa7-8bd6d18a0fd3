import React, { useEffect, useState } from 'react'
import useStyles from '../Signin/Signin.styles'
import { Button, Grid, Box, TextField, Typography, DialogContent, CircularProgress, DialogTitle } from '@mui/material'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { userUsernameCheckSchema } from './SignUpSchema'
import { useDynamoKey, useGetProfileMutation, useUserNameExistMutation, useUserNameMutation } from '../../../reactQuery'
import { toast } from 'react-hot-toast'
import { usePortalStore } from '../../../store/userPortalSlice'
import PropTypes from 'prop-types'
import { useUserStore } from '../../../store/useUserSlice'
import DailyBonus from '../../DailyBonus'
import {
  useGetDailyBonusMutation,
  useGetPromotionBonusMutation,
  useGetWelcomeBonusMutation
} from '../../../reactQuery/bonusQuery'
import WelcomeBonus from '../../WelcomeBonus'
import PromotionBonus from '../../DailyBonus/promotionBonus'
import { useNavigate } from 'react-router-dom'
import TermAndCondition from '../TermAndCondition/TermAndCondition'
import { setItem } from '../../../utils/storageUtils'
import TagManager from 'react-gtm-module'
import { deleteCookie, getCookie } from '../../../utils/cookiesCollection'
// import { useGeolocation } from "../../../Context";
// import { GEOCOMPLY_REASON_CODE } from "../../../utils/constants";

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

const UserNameModal = () => {
  const classes = useStyles()
  const [username, setUsername] = useState(false)
  const [isUsernameProfane, setUsernameProfane] = useState(true)
  const [isFormSubmitting, setIsFormSubmitting] = useState(false)
  // const [isFormSubmitted, setIsFormSubmitted] = useState(false)
  const portalStore = usePortalStore((state) => state)
  const userDetails = useUserStore((state) => state)
  const user = useUserStore((state) => state)
  const navigate = useNavigate()
  // const { isNotAvailable, fetchEncryptedLocation, isRegionAllowed, setIsRegionAllowed } = useGeolocation()
  let dynamoKey = getCookie('dynamoKey')
  const {
    register,
    formState: { errors, isValid },
    handleSubmit: handleUsernameSubmit,
    setError,
    watch
  } = useForm({ resolver: yupResolver(userUsernameCheckSchema), mode: 'onChange' })

  const UpdatedUsername = watch('username')

  React.useEffect(() => {
    const getData = setTimeout(() => {
      if (UpdatedUsername?.length > 4 && !errors.username) {
        setIsFormSubmitting(true)
        checkUsernameExistMutation.mutate({ username: UpdatedUsername })
      }
    }, 1000)

    return () => clearTimeout(getData)
  }, [UpdatedUsername])

  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (res?.data?.data) {
        let resetData = res?.data?.data?.remainingTime
        portalStore.openPortal(() => <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />, 'bonusStreak')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {}
  })

  const mutationGetWelcomeBonus = useGetWelcomeBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(() => <WelcomeBonus welcomeBonusData={res?.data?.data} />, 'bonusModal')
      }
      //   else {
      //     handleClose();
      //   }
    },
    onError: (error) => {
      console.log(error)
    }
  })
  const mutationGetPromotionBonus = useGetPromotionBonusMutation({
    onSuccess: (res) => {
      if (res?.data?.data?.scAmount || res?.data?.data?.gcAmount) {
        portalStore.openPortal(() => <PromotionBonus promotionBonusData={res?.data?.data} />, 'bonusModal')
      } else {
        handleClose()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })


  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      const userData = res?.data?.data
      if (userData) {
        user.setUserDetails(userData)
        user.setIsAuthenticate(true)

        if (!userData.isEmailVerified && !userData.phoneVerified) {
          user.logout()
          navigate('/')
        }
        if (userData.isPromotionBonusAllowed && !userData.promotionBonusClaimedAt) {
          mutationGetPromotionBonus.mutate()
        } else if (!userData.isTermsAccepted) {
          portalStore.openPortal(() => <TermAndCondition />, 'termsNConditionModal')
        } else if (!userData.isWelcomeBonusClaimed) {
          mutationGetWelcomeBonus.mutate()
        } else if (!userData.isDailyBonusClaimed) {
          mutationGetDailyBonus.mutate()
        } 
      }
      setItem('special_package', true)
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  // useEffect(() => {
  //   console.log("isRegionAllowed Username",isRegionAllowed);

  //   if (isRegionAllowed !== null && isRegionAllowed) {
  //     // alert("after username success")
  //     setIsRegionAllowed(false)
  //     if (setUsername !== false) {
  //       // alert("after username set")
  //       getProfileMutation.mutate()
  //       portalStore.closePortal()
  //       navigate('/')
  //     }

  //   }
  // }, [isRegionAllowed]);

  // console.log("isRegionAllowed outer Username",isRegionAllowed);
  const dynamoKeySend = useDynamoKey({
    onSuccess: (res) => {
      if (res.data.success) {
        toast.success('Message Sent')
        deleteCookie('dynamoKey')
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const userNameMutate = useUserNameMutation({
    onSuccess: (res) => {
      setIsFormSubmitting(false)
      // setIsFormSubmitted(true);
      localStorage.setItem('username', res?.data?.username)
      const userData = userDetails?.userDetails
      userDetails.setUserDetails({ ...userData, username: username })
      toast.success('UserName created successfully')
      if(dynamoKey !== ''){
        dynamoKeySend.mutate({ d10x_link_id: dynamoKey})
      }
      portalStore.closePortal()
      getProfileMutation.mutate()
      // portalStore.closePortal()
      // getProfileMutation.mutate()

      // if (import.meta.env.VITE_NODE_ENV ==='staging' || import.meta.env.VITE_NODE_ENV==='production' ){
      // fetchEncryptedLocation(userData?.userId, GEOCOMPLY_REASON_CODE.REGISTRATION)
      // }

      TagManager.dataLayer({
        dataLayer: {
          event: "create account",
          user_status: "username_added",
          user_name: res?.data?.username,
          user_id: userData?.userId,
          email: userData?.email
        }
      })
      var parameters = {
        user_name: res?.data?.username,
        email: userData?.email
      }
      try {
        if (typeof optimoveSDK !== 'undefined' && optimoveSDK.API) {
          optimoveSDK.API.reportEvent('registration', parameters, null, userData?.userId.toString())
        } else {
          console.log('OPTIMOVE ERROR: SDK or API is not defined for customEvent')
        }
      } catch (error) {
        console.error('OPTIMOVE ERROR in customEvent:', error)
      }
      portalStore.closePortal()
      navigate('/')
    },
    onError: (err) => {
      setIsFormSubmitting(false)
      const { errors } = err?.response?.data || []
      if (errors.length > 0) {
        errors.forEach(({ description }) => {
          if (description) {
            // toast.error(description)
          }
        })
      }
    }
  })

  const checkUsernameExistMutation = useUserNameExistMutation({
    onSuccess: (res) => {
      if (res?.data?.user) {
        if (res?.data?.user?.isUserNameExist) {
          setIsFormSubmitting(false)
          setError('username', { type: 'focus', message: 'Username already exists.' }, { shouldFocus: true })
        } else {
          setIsFormSubmitting(false)
          setUsernameProfane(false)
        }
      }
    },
    onError: (err) => {
      setIsFormSubmitting(false)
      const errors = err?.response?.data?.errors
      if (errors?.length > 0) {
        if (errors[0].name === 'ProfaneUserNameError') {
          setUsernameProfane(true)
        }
        errors.forEach(({ description }) => {
          if (description) {
            // toast.error(error?.description)
          }
        })
      }
    }
  })

  const usernameExistMutation = useUserNameExistMutation({
    onSuccess: (res) => {
      if (res?.data?.user) {
        if (res?.data?.user?.isUserNameExist) {
          setIsFormSubmitting(false)
          setError('username', { type: 'focus', message: 'Username already exists.' }, { shouldFocus: true })
        } else {
          userNameMutate.mutate({ username: username })
        }
      }
    },
    onError: (err) => {
      const { errors } = err?.response?.data || []
      if (errors.length > 0) {
        errors.forEach(({ description }) => {
          if (description) {
            // toast.error(description)
          }
        })
      }
    }
  })

  const onUsernameSubmit = (data) => {
    if (data.username !== ' ') {
      setIsFormSubmitting(true)
      setUsername(data.username)
      usernameExistMutation.mutate({ username: data.username })
    }
  }

  const handleClose = () => {
    portalStore.closePortal()
  }

  const handleUsernameChange = (e) => {
    const username = e.target.value
    setUsernameProfane(true)
    // Call the form's onChange handler
    register('username').onChange(e)
  }

  return (
    <Grid>
      <Grid>
        <DialogContent sx={{ padding: '0' }}>
          <Grid className='modal-section'>
            <Grid sx={{ width: '100%' }}>
              <DialogTitle sx={{ m: 0, p: 2 }} id='customized-dialog-title'>
                Create your username
              </DialogTitle>

              <form onSubmit={handleUsernameSubmit(onUsernameSubmit)}>
                <Box sx={{ width: '100%' }} style={{ marginTop: '20px' }} className={classes.modalWrapper}>
                  <Grid display='flex' sx={{ flexDirection: 'column' }} className={classes.inputParent}>
                    <TextField
                      id='outlined-basic'
                      label=''
                      variant='outlined'
                      placeholder='Enter a Username'
                      {...register('username')}
                      autoComplete='off'
                      onChange={handleUsernameChange}
                    />
                    <Typography className={classes.errorLabel} style={{ marginBottom: '0px !important' }}>
                      {errors?.username && errors?.username?.message}
                    </Typography>
                  </Grid>
                </Box>
                <Box className={classes.bottomSection}>
                  <Grid className={classes.submitBtn}>
                    <Button
                      variant='contained'
                      type='submit'
                      disabled={isFormSubmitting || !isValid || isUsernameProfane}
                      style={{ opacity: isFormSubmitting || isUsernameProfane || !isValid ? 0.5 : 1 }}
                    >
                      <span>Continue</span>
                      {isFormSubmitting && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
                    </Button>
                  </Grid>
                </Box>
              </form>
            </Grid>
          </Grid>
        </DialogContent>
      </Grid>
    </Grid>
  )
}

export default UserNameModal
