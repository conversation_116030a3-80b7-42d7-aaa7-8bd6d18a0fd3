import React, { useState, useEffect } from 'react'
import useStyles from '../Signin/Signin.styles'
import { Button, Grid, Box, Typography, CircularProgress } from '@mui/material'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { useResendEmailOTPMutation, useVerifyEmailOTPMutation } from '../../../reactQuery'
import { useUserStore } from '../../../store/useUserSlice'
import { useNavigate } from 'react-router-dom'
import OtpInput from 'react-otp-input'
import { useCoinStore } from '../../../store/store'
import TagManager from 'react-gtm-module'

const OtpVerificationUI = ({ userEmail, handleClose }) => {
  const classes = useStyles()
  const setCoin = useCoinStore((state) => state.setCoinType)
  const userStore = useUserStore((state) => state)

  const [otp, setOtp] = useState('')

  const [timer, setTimer] = useState(59) // Initial timer value

  const [disabled, setDisabled] = useState(true)

  const [isFormSubmitting, setIsFormSubmitting] = useState(false)

  const [startTimerAgain, setStartTimerAgain] = useState(true)

  const { setValue, handleSubmit: handleOtpSubmit } = useForm({
    defaultValues: {
      otp: ''
    },
    mode: 'onChange'
  })

  useEffect(() => {
    let interval

    // Function to start the countdown
    const startTimer = () => {
      interval = setInterval(() => {
        setTimer((prevTimer) => {
          if (prevTimer === 0) {
            clearInterval(interval)
            setDisabled(false) // Enable the button when timer reaches 0
            setIsFormSubmitting(false)
            return 0
          } else {
            return prevTimer - 1
          }
        })
      }, 1000)
    }

    // Start the countdown when component mounts
    startTimer()

    // Clean up the interval when component unmounts
    return () => clearInterval(interval)
  }, [startTimerAgain])

  const navigate = useNavigate()

  const verifyEmailMutation = useVerifyEmailOTPMutation({
    onSuccess: (res) => {
      const userData = res?.data?.user
      if (userData) {
        setIsFormSubmitting(false)
        localStorage.setItem('username', userData?.username)
        setCoin('SC')
        toast.success('Email Verified Successfully')
        userStore.setUserDetails(userData)
        userStore.setIsAuthenticate(true)
        TagManager.dataLayer({
          dataLayer: {
            event: 'signup_email_confirm',
            user_status: 'email_verified',
            user_id: userData?.userId,
            email: userData?.email
          }
        })
        var parameters = {
          email: userData?.email
        }
        try {
          if (typeof optimoveSDK !== 'undefined' && optimoveSDK.API) {
            optimoveSDK.API.reportEvent('email_verified', parameters, null, res?.data?.user?.userId.toString())
          } else {
            console.log('OPTIMOVE ERROR: SDK or API is not defined for customEvent')
          }
        } catch (error) {
          console.error('OPTIMOVE ERROR in customEvent:', error)
        }
        handleClose()
        navigate('/')
      }
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        setIsFormSubmitting(false)
        errors.forEach(({ description }) => {
          if (description) {
            TagManager.dataLayer({
              dataLayer: {
                event: 'signup_email_confirm_failed',
                email: userEmail,
                error: description
              }
            })
            // toast.error(description);
            handleClose()
            navigate('/')
          }
        })
      }
    }
  })

  const onOtpSubmit = (data) => {
    setIsFormSubmitting(true)
    const newData = {
      otp: String(otp),
      email: userEmail
    }
    verifyEmailMutation.mutate(newData)
  }

  const resendVerificationMail = useResendEmailOTPMutation({
    onSuccess: () => {
      toast.success('OTP has been sent successfully to your email')
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      if (errors.length > 0) {
        errors.forEach(({ description }) => {
          if (description) {
            // toast.error(description);
          }
        })
      }
    }
  })

  const handleResendOTP = () => {
    if (!disabled) {
      resendVerificationMail.mutate({ email: userEmail })
      setTimer(59)
      setDisabled(true)
      setStartTimerAgain(!startTimerAgain)
    }
  }

  const handleOtp = (otp) => {
    setOtp(otp)
    setValue('otp', otp)
  }

  return (
    <form onSubmit={handleOtpSubmit(onOtpSubmit)}>
      <Box
        sx={{ width: '100%' }}
        style={{ marginTop: '15px' }}
        className={classes.modalWrapper}
        data-tracking='VerifyEmail.EnterCode'
      >
        <Grid className='OtpModelContent'>
          <Typography className='topText'>
            One time password send to your registered email
            <br />
            <span style={{ fontWeight: 700 }}>{userEmail} </span>
          </Typography>

          <Grid className='inputOtp'>
            <Grid style={{ width: '100%' }}>
              <OtpInput
                value={otp}
                inputType='number'
                onChange={handleOtp}
                numInputs={6}
                renderSeparator={<span>-</span>}
                className='otpNumber'
                inputStyle={{ userSelect: 'none' }} // Prevents user selection
                containerStyle={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  userSelect: 'none'
                }}
                renderInput={(props) => <input {...props} data-tracking='VerifyEmail.EnterCode.Fld' />}
              />
            </Grid>

            <Grid className='resend-text'>
              <Typography className='middleText'>{timer !== 0 ? ` Time Remaining : ${timer} sec` : ''}</Typography>

            
              {timer === 0 && (
  <Button
  disabled={resendVerificationMail.isLoading || disabled}
  onClick={handleResendOTP}
    style={{
      
      color: '#fdb72e',
      fontSize: '14px',
    
      border: 'none',
      borderRadius: '8px',
      boxShadow: '0 2px 6px rgba(0,0,0,0.2)',
      fontWeight: '500',
    }}
  >
    Resend OTP
  </Button>
)}
            </Grid>
          </Grid>

          <Grid
            style={{ display: 'flex', justifyContent: 'center', gap: '20px', margin: '15px 0', alignItems: 'center' }}
          >
            <Box className={classes.bottomSection}>
              <Grid className={classes.submitBtn}>
                <Button
                  variant='contained'
                  type='submit'
                  disabled={isFormSubmitting || otp.length !== 6}
                  style={{ opacity: otp.length !== 6 || isFormSubmitting ? 0.7 : 1 }}
                  className='otpRendEmail'
                  data-tracking='VerifyEmail.VerifyEmail.Btn'
                >
                  <span>Verify</span>
                  {isFormSubmitting && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
                </Button>
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </form>
  )
}

export default OtpVerificationUI
