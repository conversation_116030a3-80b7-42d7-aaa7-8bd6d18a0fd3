import * as Yup from 'yup'
const onlySpacesRegex = /^\s*$/

export const userUsernameCheckSchema = Yup.object().shape({
  username: Yup.string()
    .required('Username is required')
    .min(5, 'Username cannot be less than 5 characters')
    .max(20, 'Username cannot be more than 20 characters')
    .test('no-only-spaces', 'Username cannot contain only spaces', (value) => {
      return !onlySpacesRegex.test(value)
    })
    .matches(
      /^(?=.*[a-z])[A-Za-z\d_]{4,20}$/,
      'Username must be 5-20 characters long, contain at least one lowercase letter, and consist only of letters, digits, and underscores'
    )
})

export const termAndConditionCheckSchema = Yup.object().shape({
  tandc: Yup.bool()
    .oneOf([true], 'You must accept the terms and conditions')
    .required('You must accept the terms and conditions')
})

export const userOtpSchema = Yup.object().shape({
  otp: Yup.string()
    .required('OTP is required')
    .matches(/^\d{6}$/, 'OTP must be a 6-digit number')
    .max(6, 'OTP must be at most 6 characters long')
})
