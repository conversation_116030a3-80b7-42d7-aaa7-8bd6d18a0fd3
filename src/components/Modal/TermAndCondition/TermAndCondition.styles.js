import { makeStyles } from '@mui/styles'

import { Container, ButtonSecondary, ButtonPrimary, LobbyRight } from '../../../MainPage.styles'

export default makeStyles((theme) => ({
  termsNConditionModal: {
    width: '869px',
    minWidth: '869px',
    maxWidth: '869px',
    // maxHeight:'469px',
    //height:'100%',
    margin: '0 auto',
    [theme.breakpoints.down('sm')]: {
      width: '100%',
      minWidth: '100%',
      maxWidth: '100%',
    },
    "& h2": {
      textAlign: 'center',
      color: theme.colors.textWhite,
      padding: '0',
      fontSize: theme.spacing(2.0625),
      fontStyle: "normal",
      fontWeight: '700',
      lineHeight: "86.768%",
      textTransform: 'uppercase',
    },
    "& .MuiDialog-scrollPaper": {
      width: '100%',
      minHeight: '100%',
      maxWidth: '990px',
      "& .MuiPaper-elevation": {
        margin: "0",
        width: "100%",
        borderRadius: '10px',
        backgroundColor: '#0C0A0E',
        position: "relative",
        [theme.breakpoints.down('sm')]: {
          maxHeight: "100%",
          height: "auto",
        },
      },
      "& .modal-close-btn": {
        position: "absolute",
        right: theme.spacing(-2),
        top: theme.spacing(-1.5),
        color: theme.colors.textWhite,
        [theme.breakpoints.down('sm')]: {
          right: theme.spacing(-1),
          top: theme.spacing(-1),
        },
      },
    },
    "& .MuiDialogContent-root": {
      padding: "0 !important",
      overflow: 'hidden',
      [theme.breakpoints.down('sm')]: {
        display: "block",
        width: "100%",
      },
    },
    "& .MuiDialog-paper": {
      minWidth: '869px',

      [theme.breakpoints.down('sm')]: {
        minWidth: '350px',
      }
    },
    "& .modal-section": {
      display: 'flex',
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing(2.38),
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(1),
        display: "block",
        width: "100%",
      }

    }
  },
  termsandcondition: {
    width: "100%",
    maxWidth: '869px !important',
    padding: theme.spacing(15)
  },

  lobbyRight: {
    ...LobbyRight(theme),
  },
  profileTabsDesign: {
    "& .headingLogout": {
      display: 'flex',
      marginTop: '65px',
      justifyContent: 'space-between',
      alignItems: 'center',
      [theme.breakpoints.down('md')]: {
        marginTop: '20px',
      },
    },
    "& h2": {
      fontSize: '25px',
      fontWeight: '500',
      [theme.breakpoints.down('md')]: {
        width: "100%",
        marginLeft: "0",
      },
    },

    "& .MuiTabs-root": {
      width: 'fit-content',
      [theme.breakpoints.down('md')]: {
        width: "100%",
      },
      '& .MuiTabs-scroller': {
        maxWidth: "100%",
        display: "flex",
        whiteSpace: "nowrap",
        flexWrap: "nowrap",
        overflowX: "auto",
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        padding: theme.spacing(0.31),
        margin: '38px 0 68px 0 !important',
        [theme.breakpoints.down('md')]: {
          margin: '20px 0 20px 0 !important',
        },
        "& .MuiTabs-indicator": {
          borderRadius: theme.spacing(4.1875),
          background: theme.colors.YellowishOrange,
          fontWeight: theme.typography.fontWeightExtraBold,
          color: theme.colors.textBlack,
          height: 'calc(100% - 0.62rem)',
          top: '0.31rem',
        },

        "& .MuiTab-iconWrapper": {
          margin: '0'
        },

        "& button": {
          color: theme.colors.textWhite,
          position: "relative",
          marginRight: theme.spacing(0.5),
          fontWeight: theme.typography.fontWeightExtraBold,
          cursor: "pointer",
          fontSize: theme.spacing(1),
          transition: 'none',
          transform: 'none',
          minWidth: 'auto',
          zIndex: '1',
          minHeight: 'auto',
          padding: '13px 20px',

          "&:hover .MuiTab-iconWrapper": {
            "& .image2": {
              display: "block",
            },
            "& .image1": {
              display: "none",
            },
          },
          "& .MuiTab-iconWrapper": {
            "& .image2": {
              display: "none",
            },
            "& .image1": {
              display: "block",
            },
          },
          "&.Mui-selected": {
            color: theme.colors.textBlack,
            "& .MuiTab-iconWrapper": {
              "& .image1": {
                display: "none",
              },
              "& .image2": {
                display: "block !important",
              }
            },
          },

          '&:hover': {
            borderRadius: theme.spacing(4.1875),
            background: theme.colors.YellowishOrange,
            transition: 'none',
            transform: 'none',
            color: theme.colors.textBlack,
            border: '0'
          },
          "& img": {
            marginRight: theme.spacing(0.62),
            width: '25px',
            aspectRatio: '1'
          },
        }
      },
    },

    "& .profile-section-detail": {
      "& .profile": {
        display: 'flex',
        gap: '30px',
        flexWrap: 'wrap',
      },
      "& .MuiSelect-select, & .MuiTextField-root": {
        width: '100%',
        border: `1px solid ${theme.colors.inputBorder}`,
        borderRadius: "0.25rem",
        height: "43px",
        "&:focus": {
          border: `1px solid ${theme.colors.YellowishOrange}`,
        }
      },
      "& .uppercaseInput": {
        textTransform: "uppercase"
      },
      "& .inputSelect": {
        width: '-webkit-fill-available',
        border: `1px solid ${theme.colors.inputBorder}`,
        // borderColor: theme.colors.YellowishOrange,
        borderRadius: "0.25rem",
        color: `${theme.colors.textWhite}!important`,
        padding: "12px 14px",
        backgroundColor: 'transparent',
        '&:focus': {
          border: `1px solid ${theme.colors.YellowishOrange}`,
        },
        "& option": {
          color: theme.colors.textBlack,
        },
        "&:focus-visible": {
          outline: 'none',
        },
        "&:disabled": {
          color: 'rgba(255, 255, 255, 0.6) !important',
        },
        '&::-webkit-inner-spin-button, &::-webkit-outer-spin-button': {
          '-webkit-appearance': 'none',
          margin: 0,
        },
      },

      "& .datePicker": {
        "& .MuiStack-root": {
          padding: "0 !important",
          width: "100% !important"
        },
        "& .MuiInputBase-root": {
          padding: "0 !important",
          height: "100% !important",
          "& input": {
            padding: "0 0 0 15px !important",
            height: "100% !important",
            color: theme.colors.textWhite,
            "&:disabled": {
              WebkitTextFillColor: theme.colors.textWhite,
            },
          },

        },
        "& .MuiInputAdornment-positionEnd": {
          marginLeft: "0 !important",
          "& button": {
            margin: "0 !important",
            "& svg": {
              color: theme.colors.textWhite,
            }
          }
        },
        "& .Mui-focused": {
          "& .MuiOutlinedInput-notchedOutline": {
            border: "0",
            borderColor: 'transparent',
          },
        },
      },
    },

    "& .saveBtn": {
      ...ButtonPrimary(theme),
      "&:hover": {
        ...ButtonPrimary(theme),
      }
    },

    "& .cancelBtn": {
      ...ButtonSecondary(theme),
      "&:hover": {
        ...ButtonSecondary(theme),
      }
    },

    "& .gridHeading": {
      marginBottom: "30px",
      "& h3": {
        fontSize: "25px",
        marginBottom: "15px",
        fontWeight: "500",
      }
    }
  },

  container: {
    ...Container(theme),
    [theme.breakpoints.down('md')]: {
      marginBottom: '20px',
    },
  },

  logOutBtn: {
    color: "#000 !important",
    fontSize: "1rem !important",
    minWidth: "130px !important",
    background: `${theme.colors.YellowishOrange} !important`,
    boxShadow:
      "0px 0.637px 1.401px 0px #EAB647, 0px 1.932px 4.25px 0px rgba(234, 182, 71, 0.04), 0px 5.106px 11.233px 0px rgba(234, 182, 71, 0.09), 0px 16px 35.2px 0px rgba(234, 182, 71, 0.30) !important",
    fontWeight: '700 !important',
    borderRadius: "7.8rem !important",
    "& img": {
      filter: 'invert(1)',
      paddingRight: "10px",
      transform: 'rotate(180deg)',
      marginRight: '10px',
    }
  },

  userImgWrap: {
    position: "relative",
  },

  userImg: {
    width: '120px',
    height: '120px',
    [theme.breakpoints.down('sm')]: {
      width: '80px',
      height: '80px',
    },
    "& img": {
      width: '100%',
      borderRadius: '50%',
      objectFit: 'cover',
    },
  },

  uploadImgBtn: {
    top: "0",
    right: "0",
    position: "absolute !important",
    borderRadius: "50% !important",
    minHeight: "30px !important",
    minWidth: "30px !important",
    width: "30px !important",
    background: `${theme.colors.YellowishOrange} !important`,
    "& img": {
      width: "13px",
    }
  },
  errorLabel: {
    marginBottom: `${theme.spacing(1.5)} !important`,
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600 !important',
  },

  changePWD: {
    "& .MuiOutlinedInput-root": {
      padding: '0',
      height: '43px',
      width: '100%',
    },
    "& input": {
      padding: "0",
      color: theme.colors.textWhite,
      height: "100%",
      paddingLeft: "15px",
      maxWidth: "350px",
      width: '100%',
      border: `1px solid ${theme.colors.inputBorder}`,
      borderRadius: theme.spacing(0.25),
      "&:focus": {
        border: `1px solid ${theme.colors.YellowishOrange}`,
      },
    },
    "& .MuiInputAdornment-positionEnd": {
      marginLeft: "-60px",
      "& svg": {
        color: theme.colors.textWhite
      }
    },
    "& fieldset": {
      border: "none"
    },
  },

  "& .Mui-focused": {
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: '0px',
      borderColor: 'transparent !important',
    },
  },

  inputError: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    margin: "0 !important",
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600',
  },
  inputDetail: {
    [theme.breakpoints.down('xs')]: {
      flexDirection: 'column !important',
    }

  },
  profileBottom: {
    display: 'flex', gap: '15px', marginTop: '38px',
    [theme.breakpoints.down('md')]: {
      marginTop: '0px',
      marginBottom: '0',
    },
  },

  userKyc: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
    [theme.breakpoints.down('lg')]: {
      flexDirection: 'column !important', gap: '20px',
    }
  },

  kycAndRefferal: {
    display: 'flex',
    gap: '20px',
    width: '100%',
    "& .MuiTouchRipple-root": {
      display: 'none',
    },
    "& .variant1,  & .variant2": {
      color: theme.colors.Pastel,
      position: "relative",
      fontSize: "1rem",
      textAlign: "center",
      border: `2px solid ${theme.colors.Pastel}`,
      padding: theme.spacing(0.8, 5),
      borderRadius: "50px",
      fontWeight: 'bold',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 2.5),
      },
      "&:hover": {
        color: theme.colors.Pastel,
        position: "relative",
        fontSize: "1rem",
        textAlign: "center",
        border: `2px solid ${theme.colors.Pastel}`,
        padding: theme.spacing(0.8, 5),
        borderRadius: "50px",
        fontWeight: 'bold',
      },
      "& span": {
        left: "0",
        color: theme.colors.textWhite,
        right: "0",
        bottom: "-18px",
        position: "absolute",
        fontSize: "0.8rem",
        fontWeight: 500,
        backgroundColor: "#1B181E",
        borderRadius: "10px",
        width: "fit-content",
        padding: "5px 14px",
        margin: "0 auto"
      }
    },
    "& .variant2": {
      border: '2px solid transparent',
      backgroundColor: theme.colors.YellowishOrange,
      color: '#05051C',
      "&:hover": {
        border: '2px solid transparent !important',
        backgroundColor: `${theme.colors.YellowishOrange} !important`,
        color: '#05051C',
      }
    },
    "& .variant3": {
      fontSize: "1rem",
      textAlign: "center",
      border: 'none',
      backgroundColor: theme.colors.YellowishOrange,
      color: '#05051C',
      padding: theme.spacing(0.8, 2),
      borderRadius: "50px",
      fontWeight: 'bold',
      display: 'flex',
      gap: '10px',
      whiteSpace: 'nowrap',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 1),
      },
      "&:hover": {
        fontSize: "1rem",
        textAlign: "center",
        border: 'none',
        backgroundColor: theme.colors.YellowishOrange,
        color: '#05051C',
        padding: "0.8rem 2rem",
        borderRadius: "50px",
        fontWeight: 'bold',
        display: 'flex',
        gap: '10px',
        whiteSpace: 'nowrap',
      }
    },
    "& .referalBtn": {
      fontSize: "1rem",
      textAlign: "center",
      border: `2px solid ${theme.colors.textWhite}`,
      color: theme.colors.textWhite,
      padding: theme.spacing(0.8, 2),
      borderRadius: "50px",
      fontWeight: 'bold',
      display: 'flex',
      gap: '10px',
      whiteSpace: 'nowrap',
      width: '100%',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.8rem',
        padding: theme.spacing(0.8, 1),
      },
    }
  },

  userDetailsRight: {
    width: '100%',
    "& h4": {
      fontWeight: '900',
      [theme.breakpoints.down('md')]: {
        fontSize: `${theme.spacing(1.5)}!important`,
      },
    },
    "& p": {
      [theme.breakpoints.down('md')]: {
        fontSize: `${theme.spacing(0.8)}!important`,
      },
    }
  },
  termsAndConditionMainContainer: {
    display: "flex",
    // padding: '15px 20px 15px 5px',
    width: '100%'
  },
  redeemModalContainer: {
    padding: "20px",
    gap: "52px",
    display: "flex",
    flexDirection: "column",
    "& .Arrow-right-icon": {
      fill: "#fff",
      fontSize: '30px'
    }
  },
  redeemModalContainer2: {
    padding: "20px",
    gap: "20px",
    display: "flex",
    flexDirection: "column",
    "& .Arrow-right-icon": {
      fill: "#fff",
      fontSize: '30px'
    }
  },
  contentSectionList: {
    display: "flex",
    flexDirection: "column",
    // gap: "8px",
    "& .linkListItems": {
      display: "flex",
      alignItems: "center",
      gap: "7px",
      color: "#fff",
      fontSize: "16px",
      fontWeight: 500,
      cursor: "pointer",
      textDecoration: "none",
      "& .Arrow-right-icon": {
        fill: "#fff",
      }
    }
  },
  customDialogPaper: {
    width: '80%', // Adjust the width as needed
    maxWidth: 'none', // Disable the default maxWidth behavior
  },
  redeemModal: {
    "& .terms-image": {
      [theme.breakpoints.down('md')]: {
        display: 'none'
      }
    },
    "& .MuiFormControlLabel-label": {
      fontSize: '0.7rem',
    },
    "& .leftSection": {
      flexGrow: 1,
      gap: theme.spacing(1.5),
      display: "flex",
      flexDirection: "column",
      [theme.breakpoints.down('sm')]: {
        width: '100%'
      }
    },
    "& .inputWrapContainer": {
      backgroundColor: '#232323',
      padding: theme.spacing(1),
      gap: theme.spacing(0.5),
      maxWidth: "485px",
      minWidth: "100%",
      width: "100%",
      borderRadius: theme.spacing(0.25)
    },
    "& .inputWrap": {
      marginBottom: theme.spacing(1),
      marginTop: theme.spacing(0.5),
      '& .textAmount': {
        // '&:hover':{
        color: 'yellow'
        // }
      },

    },
    "& .MuiTypography-heading": {
      color: theme.colors.textWhite,
      fontWeight: 'bold',
      fontSize: theme.spacing(2),
    },
    "& .MuiFormLabel-root": {
      color: theme.colors.textWhite,
      fontWeight: "600",
    },
    "& .redeemImg": {
      width: '40%',
      textAlign: 'right',
      padding: '1rem',
      "& img": {
        width: '100%',
      },
      [theme.breakpoints.down('sm')]: {
        display: 'none',
      }
    },
    "& .MuiTextField-root, & .MuiOutlinedInput-root": {
      width: '100%',
      "& input": {
        color: "#fff !important",
        width: "-webkit-fill-available",
        border: "1px solid #293937",
        padding: "0px 14px",
        borderRadius: "0.25rem",
        backgroundColor: "transparent",
        height: '41px',

        '&.Mui-disabled': {
          background: theme.colors.disabledInput,
          textFillColor: theme.colors.textWhite,
        }
      },
    },
    '& .MuiSvgIcon-root': {
      color: '#293937',
    },
    "& .btn-wrap": {
      "& button": {
        color: theme.colors.textBlack,
        padding: "0.625rem 1.25rem",
        fontSize: "1rem",
        minWidth: "115px",
        background: "#FDB72E",
        fontWeight: 700,
        borderRadius: "7.8rem",
        textTransform: "uppercase",
        width: "65%",
      },
      "& .cancel-redeem-btn": {
        width: "35%",
        [theme.breakpoints.down('sm')]: {
          width: '60%',
        },
      },
    },
    "& .leftText": {
      flexGrow: "1",
      [theme.breakpoints.down('sm')]: {
        width: "100%",
      },
      "& h4": {
        fontWeight: 'bold',
        fontSize: theme.spacing(1.8),
      }
    },
    "& .rightImage": {
      width: '100%',
      textAlign: 'right',
      [theme.breakpoints.down('sm')]: {
        display: 'none',
      },
      "& img": {
        width: '100%',
        paddingLeft: '1rem'
      },
    },
    '& .input-grp': {
      display: 'flex',
      "& .MuiTextField-root, & .MuiOutlinedInput-root": {
        '& input': {
          borderTopRightRadius: '0 !important',
          borderBottomRightRadius: '0 !important',
        }
      },
      '& .inner-btn': {
        minWidth: 'unset',
        color: theme.colors.textBlack,
        padding: "0.375rem 0.75rem",
        fontSize: "1rem",
        background: "#FDB72E",
        fontWeight: 700,
        borderRadius: "0 0.25rem 0.25rem 0",
        textTransform: "uppercase",

      }
    },
    '& .themeCheckBoxWrap': {
      '& label': {

        '& .MuiFormControlLabel-label': {
          fontSize: theme.spacing(0.9),
        }

      }
    }
  },
  TermsAndConditionContent: {
    color: "#D3D3D3",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    fontSize: `${theme.spacing(1.313)} !important`,
    fontWeight: "600 !important",
  },
  TermsAndConditionContentArea: {
    padding: theme.spacing(1, 1.688, 1, 1.688),
    gap: "8px",
    color: "#fff",
    borderRadius: "6px 0px 0px 0px",
    background: "#211F1F",
    "& .termsAndCondition-tag": {
      fontSize: theme.spacing(1),
      fontWeight: "600",
    },
    "& .termsAndCondition-content": {
      height: "100%",
      maxHeight: "350px",
      overflowY: "scroll"
    }
  },
  lobbySearchWrap: {
    position: 'relative',
    minWidth: theme.spacing(18.75),
    "& .search-icon": {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1),
    },
    "& .inputSearch": {
      width: '-webkit-fill-available',
      border: `1px solid ${theme.colors.inputBorder}`,
      borderRadius: "10rem",
      color: `${theme.colors.textWhite}!important`,
      padding: "12px 14px 15px 50px",
      backgroundColor: 'transparent',
    },
    "& .MuiTextField-root": {
      width: "100%",
      "& .MuiInputBase-formControl": {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        "& fieldset": {
          border: '0',
        },

      },
      "& input": {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        "&::placeholder": {
          color: 'red',
        },
        "&::-ms-input-placeholder": {
          color: 'red',
        },
        "&:focus": {
          borderColor: "rgb(255, 255, 193)",
          boxShadow: "rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px",
          borderRadius: theme.spacing(2.18),
        },
      }
    }
  },
}))
