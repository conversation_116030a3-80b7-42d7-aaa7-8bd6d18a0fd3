// import { useEffect, useState } from "react";

// const useSeon = () => {
//   const [sessionLoginKey, setSessionLoginKey] = useState("");

//   useEffect(() => {
//     const initSeon = async (config) => {
//       seon.init();
//       try {
//         const session = await seon.getSession(config);
//         setSessionLoginKey(session);
//       } catch (error) {
//         console.error('Error initializing Seon session:', error);
//       }
//     };

//     if ('geolocation' in navigator) {
//       navigator.permissions.query({ name: 'geolocation' }).then((permissionStatus) => {
//         if (permissionStatus.state === 'granted') {
//           initSeon({
//             geolocation: {
//               canPrompt: true,
//               enabled: true,
//               maxAgeSeconds: 0
//             },
//             networkTimeoutMs: 2000,
//             fieldTimeoutMs: 2000,
//             region: 'eu',
//             silentMode: true
//           });
//         } else {
//           initSeon({
//             geolocation: {
//               canPrompt: false
//             },
//             networkTimeoutMs: 2000,
//             fieldTimeoutMs: 2000,
//             region: 'eu',
//             silentMode: true
//           });
//         }
//       });
//     } else {
//       initSeon({
//         geolocation: {
//           canPrompt: false
//         },
//         networkTimeoutMs: 2000,
//         fieldTimeoutMs: 2000,
//         region: 'eu',
//         silentMode: true
//       });
//     }
//   }, []);

//   return sessionLoginKey;
// };

// export default useSeon;
