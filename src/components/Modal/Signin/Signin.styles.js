import { makeStyles } from '@mui/styles'

import { ButtonPrimary } from '../../../MainPage.styles'

export default makeStyles((theme) => ({
  loginModal: {
    width: '869px',
    minWidth: '500px',
    maxWidth: '500px',
    margin: '0 auto',
    [theme.breakpoints.down('sm')]: {
      width: '100%',
      minWidth: '100%',
      maxWidth: '100%'
    },
    '&.OtpModal': {
      width: '800px',
      minWidth: '800px',
      maxWidth: '800px',
      [theme.breakpoints.down('md')]: {
        width: '100%',
        minWidth: '100%',
        maxWidth: '100%'
      },
      '& .OtpModelContent': {
        '& .otpRendEmail': {
          ...ButtonPrimary(theme),
          width: '35%',
          margin: '0 auto',
          [theme.breakpoints.down('md')]: {
            width: '35%',
            margin: '0 auto'
          }
        },
        display: 'flex',
        flexDirection: 'column',
        gap: '30px',
        [theme.breakpoints.down('md')]: {
          gap: '15px'
        },
        '& .topText': {
          color: theme.colors.textWhite,
          fontSize: theme.spacing(1.25),
          textAlign: 'center',
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1)
          }
        },
        '& .middleText': {
          color: theme.colors.textWhite,
          fontSize: theme.spacing(1.25),
          flexGrow: 1,
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1)
          }
        },
        '& .bottomText': {
          color: theme.colors.Grey,
          fontSize: theme.spacing(1),
          textAlign: 'center'
        }
      }
    },
    '& h2': {
      textAlign: 'center',
      color: theme.colors.textWhite,
      padding: '0',
      fontSize: theme.spacing(2.0625),
      fontStyle: 'normal',
      fontWeight: '700',
      lineHeight: '86.768%',
      textTransform: 'uppercase',
      marginBottom: theme.spacing(1),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.2)
      }
    },
    '& .MuiDialog-scrollPaper': {
      width: '100%',
      minHeight: '100%',
      '& .MuiPaper-elevation': {
        margin: '0',
        width: '100%',
        borderRadius: '10px',
        backgroundColor: '#0C0A0E',
        position: 'relative',
        [theme.breakpoints.down('sm')]: {
          maxHeight: '100%',
          height: 'auto'
        }
      },
      '& .modal-close-btn': {
        position: 'absolute',
        right: theme.spacing(-2),
        top: theme.spacing(-1.5),
        color: theme.colors.textWhite,
        [theme.breakpoints.down('sm')]: {
          right: theme.spacing(-1),
          top: theme.spacing(-1)
        }
      }
    },
    '& .MuiDialogContent-root': {
      padding: '0 !important',
      [theme.breakpoints.down('sm')]: {
        display: 'block',
        width: '100%'
      }
    },
    '& .modal-section': {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      color: theme.colors.textWhite,
      textAlign: 'center',
      padding: theme.spacing(2.38),
      [theme.breakpoints.down('sm')]: {
        // padding: theme.spacing(1),
        display: 'block',
        width: '100%'
      }
    },
    '& .inputOtp': {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
      margin: '1rem auto 0',
      [theme.breakpoints.down('sm')]: {
        gap: '10px'
      },
      '& input': {
        color: theme.colors.textWhite,
        border: `1px solid ${theme.colors.Pastel}`,
        padding: '0',
        borderRadius: '0.25rem',
        backgroundColor: 'transparent',
        width: `${theme.spacing(3.4375)} !important`,
        height: theme.spacing(3.4375),
        fontWeight: '700',
        WebkitAppearance: 'none',
        MozAppearance: 'none',
        appearance: 'none',
        fontSize: theme.spacing(1.4),
        [theme.breakpoints.down('sm')]: {
          padding: '6px',
          width: `${theme.spacing(2.5)} !important`,
          height: theme.spacing(2.5),
          fontSize: theme.spacing(0.875)
        },
        '&:focus': {
          outline: 'none',
          border: `1px solid ${theme.colors.YellowishOrange} !important`
        }
      },
      '& input::-webkit-inner-spin-button': {
        display: 'none'
      },
      '& span': {
        display: 'none'
      }
    },
    '& .resend-text': {
      textAlign: 'center',
      marginTop: theme.spacing(1)
    },
    '& > p': {
      color: theme.colors.textWhite,
      textAlign: 'center'
    }
  },

  tabSection: {
    borderBottom: 1,
    borderColor: 'divider',
    width: '75%',
    margin: '0 auto',
    justifyContent: 'space-between',
    '& .MuiTabs-flexContainer': {
      justifyContent: 'space-between',
      borderRadius: theme.spacing(5),
      border: `1px solid ${theme.colors.YellowishOrange}`,
      padding: '3px',
      margin: theme.spacing(1.94, 0),
      '& button': {
        flexGrow: '1',
        color: theme.colors.warmGrey,
        fontSize: '0.9375rem',
        fontStyle: 'normal',
        fontWeight: 700,
        lineHeight: 'normal',
        minHeight: '40px',
        '&.Mui-selected': {
          borderRadius: theme.spacing(4.375),
          background: theme.colors.YellowishOrange,
          color: theme.colors.textWhite
        }
      }
    },
    '& .MuiTabs-indicator': {
      display: 'none'
    },
    '& button': {
      color: theme.colors.textWhite
    }
  },

  inputLabel: {
    color: theme.colors.textWhite,
    paddingBottom: '10px'
  },

  errorLabel: {
    marginBottom: `${theme.spacing(0.5)} !important`,
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600 !important',
    textAlign: 'left'
  },
  countryCodeSelect: {
    // marginRight: '-10px !important',
    width: '60px !important',
    position: 'absolute !important',
    zIndex: '9999',
    '& .MuiSelect-select': {
      padding: '10px',
      height: '42px',
      width: '60px',
      overflow: 'visible !important',
      color: theme.colors.textWhite
    },
    '& fieldset': {
      borderRight: '0 !important',
      borderRadius: `${theme.spacing(0.25, 0, 0, 0.25)} !important`,
      borderColor: `${theme.colors.YellowishOrange} !important`,
      borderWidth: '1px',
      '&:focus': {
        borderWidth: '1px',
        borderColor: `${theme.colors.YellowishOrange} !important`
      }
    },
    '& .MuiSvgIcon-root': {
      color: theme.colors.textWhite
    }
  },

  '& .Mui-focused': {
    '& .MuiOutlinedInput-notchedOutline': {
      borderWidth: '1px',
      borderColor: `${theme.colors.YellowishOrange} !important`
    }
  },

  inputParent: {
    position: 'relative',
    '& .phoneNumber': {
      '& input': {
        paddingLeft: '80px'
      }
    },
    '& .MuiInputAdornment-root': {
      position: 'absolute',
      right: '0',
      zIndex: '1'
    },
    '& .MuiInputBase-root': {
      paddingRight: '0'
    },
    '& input': {
      color: `${theme.colors.textWhite}!important`,
      padding: '10px 14px',
      border: `1px solid ${theme.colors.inputBorder}`,
      borderRadius: theme.spacing(0.25),
      '&::-webkit-inner-spin-button, &::-webkit-outer-spin-button': {
        '-webkit-appearance': 'none',
        margin: 0
      },
      '&:focus': {
        border: `1px solid ${theme.colors.YellowishOrange}`
      }
    },
    '& fieldset': {
      display: 'none'
    }
  },

  loginWith: {
    color: theme.colors.quillGrey,
    textAlign: 'center',
    fontSize: '0.875rem',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: 'normal',
    padding: theme.spacing(1.25, 0),
    '& .MuiTypography-root': {
      lineHeight: 'normal'
    }
  },

  resendLink: {
    '& span': {
      color: theme.colors.YellowishOrange,
      fontSize: '1rem',
      fontWeight: 700,
      lineHeight: theme.spacing(1.8),
      boxShadow: 'none',
      '&:hover': {
        textDecoration: 'underline',
        cursor: 'pointer'
      }
    },
    '& span[disabled]': {
      color: 'rgb(253, 183, 46, 0.7)', // Adjusted for syntax
      pointerEvents: 'none' // Disable pointer events when disabled
    }
  },

  submitBtn: {
    '& button': {
      width: '100%',
      borderRadius: theme.spacing(23.25),
      background: theme.colors.YellowishOrange,
      color: theme.colors.textBlack,
      fontSize: '1rem',
      fontWeight: 700,
      lineHeight: theme.spacing(1.8),
      boxShadow: 'none',
      '&:hover': {
        background: theme.colors.YellowishOrange,
        boxShadow: 'none'
      },
      '&.Mui-disabled': {
        color: 'rgb(*********** / 70%)',
        boxShadow: 'none',
        backgroundColor: 'rgb(253 183 46 / 70%)'
      }
    }
  },

  forgetLink: {
    textAlign: 'right',
    padding: theme.spacing(0, 0, 1, 0),
    marginTop: theme.spacing(1),
    '& a': {
      color: theme.colors.quillGrey,
      textAlign: 'right',
      fontSize: '0.875rem',
      fontStyle: 'normal',
      fontWeight: 400,
      lineHeight: 'normal',
      textDecorationLine: 'underline'
    }
  },

  dontAccount: {
    '& p': {
      color: theme.colors.quillGrey,
      textAlign: 'center',
      fontSize: '0.875rem',
      fontStyle: 'normal',
      fontWeight: 400,
      padding: theme.spacing(1.25, 0),
      '& a': {
        color: theme.colors.YellowishOrange,
        fontSize: '0.875rem',
        fontWeight: 600,
        lineHeight: 'normal',
        textDecoration: 'underline'
      }
    }
  },

  buttonGrp: {
    '& button': {
      border: '1px solid transparent',
      boxShadow: 'none',
      backgroundColor: 'transparent',
      padding: '0',
      gap: theme.spacing(1),
      '&:hover': {
        border: '1px solid transparent',
        boxShadow: 'none',
        backgroundColor: 'transparent',
        padding: '0'
      },
      '& svg': {
        fontSize: theme.spacing(0.8)
      }
    }
  },

  modalWrapper: {
    display: 'inline-block',
    '& .MuiBox-root': {
      padding: '0'
    }
  },

  buttonDisable: {
    opacity: '0.5'
  },

  passEyeIcon: {
    // '& .MuiInputBase-input':{
    //   border:'none !important',
    // },
    '& input': {
      paddingRight: '40px'
    },
    '& input[type="password"]': {
      WebkitAppearance: 'none',
      appearance: 'none'
    }
  },
  passCheckIcon: {
    '& input': {
      paddingRight: '50px !important '
    }
  }
}))
