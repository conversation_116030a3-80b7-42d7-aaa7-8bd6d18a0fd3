import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import { FreeMode, Navigation } from 'swiper/modules';
import 'swiper/css/navigation';
import { Grid, Typography, useTheme } from '@mui/material';
import { LatestWinnerWrapper } from './Latestwinners.styles';
import { PreviousWhiteIcon, NextWhiteIcon, winIcon, chipCoinIcon } from '../../components/ui-kit/icons/svg';

const LatesWinner = (props) => {
  const theme = useTheme();


  return (
    <div key={`sliderGrid-${props.subCategory?.name?.EN}-${props.index}`}>
      <LatestWinnerWrapper theme={theme} >
        <Grid className='gameHeading'>
          <Grid className='heading'>
            <img src={winIcon} alt='winIcon' />
            <Typography>
              Latest Winner
            </Typography>
          </Grid>

          <Grid>
            <button id={`swiper-button-next`} className="swiper-button-next">
              <img src={NextWhiteIcon} alt='Next Icon' />
            </button>

            <button id={`swiper-button-prev`} className="swiper-button-prev">
              <img src={PreviousWhiteIcon} alt='Prev Icon' />
            </button>
          </Grid>
        </Grid>

        <Swiper
          spaceBetween={10}
          freeMode={true}
          navigation={{
            nextEl: `#swiper-button-next`,
            prevEl: `#swiper-button-prev`,
          }}
          modules={[FreeMode, Navigation]}
          className="mySwiper"
          breakpoints={{
            0: {
              slidesPerView: 3.5,
            },
            768: {
              slidesPerView: 4,
            },
            1024: {
              slidesPerView: 7.5,
            },
          }}
        >

          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>

          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>

          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>

          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>

          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>
          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>
          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>
          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>
          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>
          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>
          <SwiperSlide>
            <img
              src='https://i.pinimg.com/originals/eb/e4/a3/ebe4a37984a8745e78555906765df486.jpg'
              onClick={() => {
                props.handlePlayNow(game.masterCasinoGameId)
              }}
              className='casinoGame-img'
              alt='Casino'
            />
            <Grid sx={{ textAlign: 'center' }} className='winnerParent'>
              <Typography className='winnerName'>
                Eduardo
              </Typography>

              <Typography className='winnerAmount'>
                <img src={chipCoinIcon} alt='chipCoinIcon' />
                $32.79
              </Typography>
            </Grid>
          </SwiperSlide>
        </Swiper>
      </LatestWinnerWrapper >
    </div >
  )
}

export default LatesWinner
