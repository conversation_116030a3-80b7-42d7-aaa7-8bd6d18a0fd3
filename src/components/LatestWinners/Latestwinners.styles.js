import { Box } from '@mui/material'
import { styled } from '@mui/system'

export const LatestWinnerWrapper = styled(Box)(({ theme }) => {
  return {
    '& .gameHeading ': {
      position: 'relative',
      '& .heading': {
        display: 'flex',
        gap: theme.spacing(1),
        alignItems: 'center',
        padding: theme.spacing(1, 0)
      },
      '& p': {
        color: theme.colors.textWhite,
        fontSize: theme.spacing(1.5625),
        fontWeight: theme.typography.fontWeightMedium,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1)
        }
      },
      '& button': {
        border: 'none',
        '&:after': {
          display: 'none'
        }
      },
      '& .swiper-button-next': {
        borderRadius: theme.spacing(0, 4.1875, 4.1875, 0),
        background: theme.colors.GreenishCyan,
        padding: theme.spacing(0.625, 1.875),
        zIndex: '1',
        marginRight: '-12px',
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0, 1),
          height: theme.spacing(1.25)
        },
        '& img': {
          [theme.breakpoints.down('md')]: {
            width: '10px'
          },
          width: '15px'
        }
      },
      '& .swiper-button-prev': {
        right: '68px',
        left: 'auto',
        borderRadius: theme.spacing(4.1875, 0, 0, 4.1875),
        background: theme.colors.GreenishCyan,
        zIndex: '1',
        padding: theme.spacing(0.625, 1.875),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0, 1),
          height: theme.spacing(1.25),
          right: '38px'
        },
        '& img': {
          [theme.breakpoints.down('md')]: {
            width: '10px'
          },
          width: '15px'
        }
      }
    },
    '& .swiper-wrapper': {
      marginTop: theme.spacing(0.25),
      '& .swiper-slide': {
        transition: 'all 200ms ease-in-out',
        lineHeight: '0',
        '& img': {
          width: '100%',
          borderRadius: theme.spacing(1)
        },
        '& .casino-card': {
          textAlign: 'center',
          position: 'relative',
          transition: 'all 200ms ease-in-out',
          '&:hover': {
            transform: 'translateY(-0.25rem)',
            transition: 'all 600ms ease-in-out',
            '& .overlayPlay': {
              display: 'flex',
              borderRadius: '8px'
            }
          },
          '& .fav-icon': {
            '& img': {
              width: '10%',
              position: 'absolute',
              top: '10px',
              right: '10px',
              zIndex: '8'
            },
            '&:hover': {
              backgroundColor: theme.colors.textWhite,
              cursor: 'pointer'
            }
          },
          '& .casinoGame-img': {
            width: '100%',
            aspectRatio: '1',
            '&:hover': {
              backgroundColor: theme.colors.textWhite,
              cursor: 'pointer'
            }
          }
        },
        '& .tournamentLogo': {
          position: 'absolute',
          left: '2px',
          top: '5px',
          width: '30px',
          height: '30px'
        },
        '& .prgamatic-jackpot-amount-wrapper': {
          position: 'absolute',
          top: '12px',
          left: '47%',
          display: 'flex',
          justifyContent: 'center',
          gap: '4px',
          alignItems: 'center',
          background: '#000000B2',
          borderRadius: '17px',
          whiteSpace: 'nowrap',
          transform: 'translate(-50%, 0)',
          padding: '1px 5px'
        }
      }
    },
    '& .overlayPlay': {
      position: 'absolute',
      display: 'none',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      margin: '0 auto',
      inset: '0',
      flexDirection: 'column',
      background: 'rgba(215, 143, 31, 0.9)',
      cursor: 'pointer',
      transition: 'all 200ms ease-in-out',
      borderRadius: '8px',
      '& a': {
        color: theme.colors.textWhite,
        textDecoration: 'none'
      }
    },

    '& .swiper-slide': {
      background: '#093931',
      borderRadius: theme.spacing(1),
      display: 'flex'
    },

    '& .winnerParent': {
      padding: theme.spacing(1.5, 0)
    },

    '& .winnerName': {
      color: theme.colors.textWhite,
      fontSize: theme.spacing(1),
      fontWeight: 'bold',
      '& img': {
        width: '20px !important',
        [theme.breakpoints.down('md')]: {
          width: '16px !important'
        }
      }
    },

    '& .winnerAmount': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: theme.spacing(1),
      '& img': {
        width: '20px !important',
        [theme.breakpoints.down('md')]: {
          width: '16px'
        }
      }
    }
  }
})
