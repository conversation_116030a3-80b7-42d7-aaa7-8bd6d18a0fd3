import React, { useEffect, useState } from 'react'
import snsWebSdk from '@sumsub/websdk'
import { useGetProfileMutation } from '../../reactQuery';
import { useUserStore } from '../../store/useUserSlice';


const SumSubKycVL5 = ({ token, userId }) => {
  const user = useUserStore((state) => state);
  useEffect(() => {
    if (token) {
      launchWebSdk(token, userId, user?.userDetails?.email, user?.userDetails?.phone);
    }
  }, [token]);

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails({ ...res?.data?.data });
    },
    onError: (error) => {
      console.log('error', error);
    },
  });


  function launchWebSdk(accessToken, externalActionId, applicantEmail, applicantPhone) {
    let snsWebSdkInstance = snsWebSdk.init(
      accessToken,
      () => this.getNewAccessToken()
      // Provide the access token update callback

    )
      .withConf({
        lang: 'en',
        email: applicantEmail,
        phone: applicantPhone,
        externalActionId: externalActionId,
        theme: 'dark',
      })
      .withOptions({ addViewportTag: false, adaptIframeHeight: true })
      .on('idCheck.onStepCompleted', (payload) => {
        console.log('onStepCompleted', payload);
      })
      .on('idCheck.onApplicantStatusChanged', (payload) => {
        if (payload.reviewStatus === "completed") {
          setTimeout(() => {
            getProfileMutation.mutate();
          }, 2000);
        }
      })
      .on('idCheck.onError', (error) => {
        console.log('onError', error);
      })
      .build();

    snsWebSdkInstance.launch('#sumsub-websdk-container');
  }

  return (
    <div>
      <div id="sumsub-websdk-container"></div>
    </div>
  );
};

export default SumSubKycVL5;
