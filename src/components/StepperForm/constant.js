export const equations = [
  {
    equation: '((10 + 20) - (30 x 3)) ÷ 3',
    options: {
      A: -10,
      B: 0,
      C: -20,
      D: 10
    },
    correctOption: 'C'
  },
  {
    equation: '((10 + 10) - (40 x 3)) ÷ 4',
    options: {
      A: -30,
      B: -25,
      C: -35,
      D: -20
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 20) - (10 x 2)) ÷ 5',
    options: {
      A: 15,
      B: 20,
      C: 25,
      D: 30
    },
    correctOption: 'B'
  },
  {
    equation: '((30 + 40) - (20 x 3)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 10) - (35 x 2)) ÷ 5',
    options: {
      A: -5,
      B: 0,
      C: 5,
      D: 10
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 20) - (50 x 2)) ÷ 10',
    options: {
      A: 0,
      B: 1,
      C: 2,
      D: 3
    },
    correctOption: 'C'
  },
  {
    equation: '((30 + 40) - (20 x 2)) ÷ 5',
    options: {
      A: 4,
      B: 5,
      C: 6,
      D: 7
    },
    correctOption: 'C'
  },
  {
    equation: '((80 + 20) - (30 x 3)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((100 + 50) - (60 x 2)) ÷ 5',
    options: {
      A: 5,
      B: 6,
      C: 7,
      D: 8
    },
    correctOption: 'B'
  },
  {
    equation: '((90 + 30) - (50 x 2)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((35 + 15) - (20 x 3)) ÷ 5',
    options: {
      A: -3,
      B: -2,
      C: 0,
      D: 2
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 20) - (25 x 3)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((120 + 10) - (30 x 4)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((75 + 25) - (40 x 2)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((80 + 30) - (20 x 5)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 40) - (50 x 2)) ÷ 5',
    options: {
      A: 6,
      B: 7,
      C: 8,
      D: 9
    },
    correctOption: 'C'
  },
  {
    equation: '((90 + 60) - (40 x 4)) ÷ 10',
    options: {
      A: -1,
      B: 0,
      C: 1,
      D: 2
    },
    correctOption: 'A'
  },
  {
    equation: '((45 + 15) - (20 x 2)) ÷ 5',
    options: {
      A: 3,
      B: 4,
      C: 5,
      D: 6
    },
    correctOption: 'B'
  },
  {
    equation: '((80 + 40) - (25 x 4)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((120 + 50) - (40 x 3)) ÷ 5',
    options: {
      A: 9,
      B: 10,
      C: 11,
      D: 12
    },
    correctOption: 'B'
  },
  {
    equation: '((150 + 40) - (45 x 4)) ÷ 10',
    options: {
      A: 0,
      B: 1,
      C: 2,
      D: 3
    },
    correctOption: 'B'
  },
  {
    equation: '((200 + 60) - (50 x 4)) ÷ 10',
    options: {
      A: 5,
      B: 6,
      C: 7,
      D: 8
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 10) - (25 x 2)) ÷ 5',
    options: {
      A: 3,
      B: 4,
      C: 5,
      D: 6
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 20) - (15 x 5)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((10 + 20) - (30 x 3)) ÷ 3',
    options: {
      A: -10,
      B: 0,
      C: -20,
      D: 10
    },
    correctOption: 'C'
  },
  {
    equation: '((10 + 10) - (40 x 3)) ÷ 4',
    options: {
      A: -30,
      B: -25,
      C: -35,
      D: -20
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 20) - (10 x 2)) ÷ 5',
    options: {
      A: 15,
      B: 20,
      C: 25,
      D: 30
    },
    correctOption: 'B'
  },
  {
    equation: '((30 + 40) - (20 x 3)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 10) - (35 x 2)) ÷ 5',
    options: {
      A: -5,
      B: 0,
      C: 5,
      D: 10
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 20) - (50 x 2)) ÷ 10',
    options: {
      A: 0,
      B: 1,
      C: 2,
      D: 3
    },
    correctOption: 'C'
  },
  {
    equation: '((30 + 40) - (20 x 2)) ÷ 5',
    options: {
      A: 4,
      B: 5,
      C: 6,
      D: 7
    },
    correctOption: 'C'
  },
  {
    equation: '((80 + 20) - (30 x 3)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((100 + 50) - (60 x 2)) ÷ 5',
    options: {
      A: 5,
      B: 6,
      C: 7,
      D: 8
    },
    correctOption: 'B'
  },
  {
    equation: '((90 + 30) - (50 x 2)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((35 + 15) - (20 x 3)) ÷ 5',
    options: {
      A: -3,
      B: -2,
      C: 0,
      D: 2
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 20) - (25 x 3)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((120 + 10) - (30 x 4)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((75 + 25) - (40 x 2)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((80 + 30) - (20 x 5)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 40) - (50 x 2)) ÷ 5',
    options: {
      A: 6,
      B: 7,
      C: 8,
      D: 9
    },
    correctOption: 'C'
  },
  {
    equation: '((90 + 60) - (40 x 4)) ÷ 10',
    options: {
      A: -1,
      B: 0,
      C: 1,
      D: 2
    },
    correctOption: 'A'
  },
  {
    equation: '((45 + 15) - (20 x 2)) ÷ 5',
    options: {
      A: 3,
      B: 4,
      C: 5,
      D: 6
    },
    correctOption: 'B'
  },
  {
    equation: '((80 + 40) - (25 x 4)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((120 + 50) - (40 x 3)) ÷ 5',
    options: {
      A: 9,
      B: 10,
      C: 11,
      D: 12
    },
    correctOption: 'B'
  },
  {
    equation: '((150 + 40) - (45 x 4)) ÷ 10',
    options: {
      A: 0,
      B: 1,
      C: 2,
      D: 3
    },
    correctOption: 'B'
  },
  {
    equation: '((200 + 60) - (50 x 4)) ÷ 10',
    options: {
      A: 5,
      B: 6,
      C: 7,
      D: 8
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 10) - (25 x 2)) ÷ 5',
    options: {
      A: 3,
      B: 4,
      C: 5,
      D: 6
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 20) - (15 x 5)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((10 + 20) - (30 x 3)) ÷ 3',
    options: {
      A: -10,
      B: 0,
      C: -20,
      D: 10
    },
    correctOption: 'C'
  },
  {
    equation: '((10 + 10) - (40 x 3)) ÷ 4',
    options: {
      A: -30,
      B: -25,
      C: -35,
      D: -20
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 20) - (10 x 2)) ÷ 5',
    options: {
      A: 15,
      B: 20,
      C: 25,
      D: 30
    },
    correctOption: 'B'
  },
  {
    equation: '((30 + 40) - (20 x 3)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 10) - (35 x 2)) ÷ 5',
    options: {
      A: -5,
      B: 0,
      C: 5,
      D: 10
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 20) - (50 x 2)) ÷ 10',
    options: {
      A: 0,
      B: 1,
      C: 2,
      D: 3
    },
    correctOption: 'C'
  },
  {
    equation: '((30 + 40) - (20 x 2)) ÷ 5',
    options: {
      A: 4,
      B: 5,
      C: 6,
      D: 7
    },
    correctOption: 'C'
  },
  {
    equation: '((80 + 20) - (30 x 3)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((100 + 50) - (60 x 2)) ÷ 5',
    options: {
      A: 5,
      B: 6,
      C: 7,
      D: 8
    },
    correctOption: 'B'
  },
  {
    equation: '((90 + 30) - (50 x 2)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((35 + 15) - (20 x 3)) ÷ 5',
    options: {
      A: -3,
      B: -2,
      C: 0,
      D: 2
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 20) - (25 x 3)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((120 + 10) - (30 x 4)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((75 + 25) - (40 x 2)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((80 + 30) - (20 x 5)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 40) - (50 x 2)) ÷ 5',
    options: {
      A: 6,
      B: 7,
      C: 8,
      D: 9
    },
    correctOption: 'C'
  },
  {
    equation: '((90 + 60) - (40 x 4)) ÷ 10',
    options: {
      A: -1,
      B: 0,
      C: 1,
      D: 2
    },
    correctOption: 'A'
  },
  {
    equation: '((45 + 15) - (20 x 2)) ÷ 5',
    options: {
      A: 3,
      B: 4,
      C: 5,
      D: 6
    },
    correctOption: 'B'
  },
  {
    equation: '((80 + 40) - (25 x 4)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((120 + 50) - (40 x 3)) ÷ 5',
    options: {
      A: 9,
      B: 10,
      C: 11,
      D: 12
    },
    correctOption: 'B'
  },
  {
    equation: '((150 + 40) - (45 x 4)) ÷ 10',
    options: {
      A: 0,
      B: 1,
      C: 2,
      D: 3
    },
    correctOption: 'B'
  },
  {
    equation: '((200 + 60) - (50 x 4)) ÷ 10',
    options: {
      A: 5,
      B: 6,
      C: 7,
      D: 8
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 10) - (25 x 2)) ÷ 5',
    options: {
      A: 3,
      B: 4,
      C: 5,
      D: 6
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 20) - (15 x 5)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((10 + 20) - (30 x 3)) ÷ 3',
    options: {
      A: -10,
      B: 0,
      C: -20,
      D: 10
    },
    correctOption: 'C'
  },
  {
    equation: '((10 + 10) - (40 x 3)) ÷ 4',
    options: {
      A: -30,
      B: -25,
      C: -35,
      D: -20
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 20) - (10 x 2)) ÷ 5',
    options: {
      A: 15,
      B: 20,
      C: 25,
      D: 30
    },
    correctOption: 'B'
  },
  {
    equation: '((30 + 40) - (20 x 3)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 10) - (35 x 2)) ÷ 5',
    options: {
      A: -5,
      B: 0,
      C: 5,
      D: 10
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 20) - (50 x 2)) ÷ 10',
    options: {
      A: 0,
      B: 1,
      C: 2,
      D: 3
    },
    correctOption: 'C'
  },
  {
    equation: '((30 + 40) - (20 x 2)) ÷ 5',
    options: {
      A: 4,
      B: 5,
      C: 6,
      D: 7
    },
    correctOption: 'C'
  },
  {
    equation: '((80 + 20) - (30 x 3)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((100 + 50) - (60 x 2)) ÷ 5',
    options: {
      A: 5,
      B: 6,
      C: 7,
      D: 8
    },
    correctOption: 'B'
  },
  {
    equation: '((90 + 30) - (50 x 2)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((35 + 15) - (20 x 3)) ÷ 5',
    options: {
      A: -3,
      B: -2,
      C: 0,
      D: 2
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 20) - (25 x 3)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  },
  {
    equation: '((120 + 10) - (30 x 4)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((75 + 25) - (40 x 2)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((80 + 30) - (20 x 5)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((100 + 40) - (50 x 2)) ÷ 5',
    options: {
      A: 6,
      B: 7,
      C: 8,
      D: 9
    },
    correctOption: 'C'
  },
  {
    equation: '((90 + 60) - (40 x 4)) ÷ 10',
    options: {
      A: -1,
      B: 0,
      C: 1,
      D: 2
    },
    correctOption: 'A'
  },
  {
    equation: '((45 + 15) - (20 x 2)) ÷ 5',
    options: {
      A: 3,
      B: 4,
      C: 5,
      D: 6
    },
    correctOption: 'B'
  },
  {
    equation: '((80 + 40) - (25 x 4)) ÷ 10',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'B'
  },
  {
    equation: '((120 + 50) - (40 x 3)) ÷ 5',
    options: {
      A: 9,
      B: 10,
      C: 11,
      D: 12
    },
    correctOption: 'B'
  },
  {
    equation: '((150 + 40) - (45 x 4)) ÷ 10',
    options: {
      A: 0,
      B: 1,
      C: 2,
      D: 3
    },
    correctOption: 'B'
  },
  {
    equation: '((200 + 60) - (50 x 4)) ÷ 10',
    options: {
      A: 5,
      B: 6,
      C: 7,
      D: 8
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 10) - (25 x 2)) ÷ 5',
    options: {
      A: 3,
      B: 4,
      C: 5,
      D: 6
    },
    correctOption: 'B'
  },
  {
    equation: '((60 + 20) - (15 x 5)) ÷ 5',
    options: {
      A: 1,
      B: 2,
      C: 3,
      D: 4
    },
    correctOption: 'A'
  }
]
