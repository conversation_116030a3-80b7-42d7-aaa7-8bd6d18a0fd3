import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { Button, Grid, Box, Typography, CircularProgress } from '@mui/material'
import useStyles from './StepperForm.styles'
import OtpInput from 'react-otp-input'
import useStepperStore from '../../store/useStepperStore'
import { useUserStore } from '../../store/useUserSlice'
import { userOtpSchema } from './schema/phoneSchema'
import { toast } from 'react-hot-toast'
import { getResendOtpMutation, useGetProfileMutation, usePhoneVerifyMutation } from '../../reactQuery'
import TagManager from 'react-gtm-module'

const PhoneOtpVerification = ({ phone, code, handleOtpClose }) => {
  const classes = useStyles()
  const activeStep = useStepperStore((state) => state.activeStep)
  const steps = useStepperStore((state) => state.steps)
  const handleNext = useStepperStore((state) => state.handleNext)
  const stepperCalledFor = useStepperStore((state) => state.stepperCalledFor)
  const [timer, setTimer] = useState(60) // Timer for resent OTP
  const [isTimerActive, setIsTimerActive] = useState(true)
  const [otp, setOtp] = useState('')

  const user = useUserStore((state) => state)

  const {
    register,
    formState: { errors },
    handleSubmit: handleOtpSubmit,
    setError,
    setValue
  } = useForm({ defaultValues: { otp: '' }, resolver: yupResolver(userOtpSchema) })

  // Timer countdown logic
  useEffect(() => {
    let interval
    if (isTimerActive && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1)
      }, 1000)
    } else if (timer === 0) {
      setIsTimerActive(false)
    }
    return () => {
      clearInterval(interval)
    }
  }, [isTimerActive, timer])

  useEffect(() => {
    TagManager.dataLayer({
      dataLayer: { event: 'checkout_phone_verification_OTP' }
    })
  }, [])
  const mutation = usePhoneVerifyMutation({
    onSuccess: (res) => {
      toast.success('Phone number verified successfully')
      if (res?.data?.success) {
        getProfileMutation()
        // portalStore.closePortal();

        TagManager.dataLayer({
          dataLayer: {
            event: 'checkout_phone_verification_success',
            user_id: user?.userDetails?.userId,
            email: user?.userDetails?.email,
            phone: user?.userDetails?.phoneNumber
          }
        })

        handleNext()
      }
    },
    onError: (error) => {
      TagManager.dataLayer({
        dataLayer: {
          event: 'checkout_phone_verification_error',
          user_id: user?.userDetails?.userId,
          email: user?.userDetails?.email,
          phone: user?.userDetails?.phoneNumber,
          error: error.response?.data?.errors?.[0]?.description
        }
      })
      // if (error.response.status === 429) {
      //   toast.error(error.response.statusText);
      // } else
      if (error.response?.data?.errors?.[0]?.errorCode === 3025) {
        // toast.error(error.response?.data?.errors?.[0]?.description);
        setError('otp', { type: 'focus', message: 'Invalid OTP. Please enter again.' }, { shouldFocus: true })
      }
      // else {
      // toast.error(error.response?.data?.errors?.[0]?.description);
      // }
    }
  })

  const { mutate: getProfileMutation, isLoading } = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails(res?.data?.data)
    },
    onError: (error) => {
      console.log('Error getting profile:', error)
    }
  })

  const onOtpSubmit = (data) => {
    const newData = {
      otp: String(data.otp),
      code: Number(code),
      phone
    }
    mutation.mutate({ ...newData })
  }

  const resetMutation = getResendOtpMutation({
    onSuccess: (res) => {
      toast.success(res.data.message)
      setTimer(60) // Reset the timer to 15 seconds
      setIsTimerActive(true) // Start the timer
    },
    onError: (error) => {
      // if (error.response.status === 429) {
      //   toast.error(error.response.statusText);
      // } else {
      // toast.error(error.response?.data?.errors?.[0]?.description);
      // }
    }
  })

  const resetOtpFun = () => {
    const newData = {
      phoneCode: String(code),
      phone
    }
    resetMutation.mutate({ ...newData })
  }

  const handleOtp = (otp) => {
    setOtp(otp)
    setValue('otp', otp)
  }

  return (
    <>
      <Grid className={classes.StepperModal}>
        <Grid>
          <Box className='verification-code-container'>
            <Typography className='verification-code-text'>VERIFICATION CODE</Typography>
            <Box className='small-subheader-container'>
              <Typography className='small-subheader-text'>
                We have send the OTP on {phone} will apply auto to the field
              </Typography>
            </Box>

            <form onSubmit={handleOtpSubmit(onOtpSubmit)}>
              <Box>
                <Grid className='enter-otp-wrap'>
                  <Grid className='amount-input opt-input'>
                    <OtpInput
                      value={otp}
                      inputType='number'
                      onChange={handleOtp}
                      numInputs={6}
                      className='otpNumber'
                      inputStyle={{ userSelect: 'none' }} // Prevents user selection
                      containerStyle={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        userSelect: 'none',
                        width: '100%'
                      }}
                      renderInput={(props) => <input {...props} />}
                    />
                  </Grid>
                  {errors?.otp && <p className='inputError'>{errors?.otp?.message}</p>}
                </Grid>
                <Grid className='resend-otp'>
                  <Typography>
                    Don’t get the OTP?
                    <button
                      type='button'
                      style={{ cursor: !(resetMutation.isLoading || isTimerActive) && 'pointer' }}
                      disabled={resetMutation.isLoading || isTimerActive}
                      onClick={resetOtpFun}
                      data-tracking='Store.Checkout.Step2.ResendCode.Link'
                      data-tracking-caller={stepperCalledFor}
                    >
                      Resend OTP{' '}
                    </button>
                    {isTimerActive && `(${timer}s)`}
                  </Typography>
                </Grid>

                <Grid className='btn-wrap'>
                  {activeStep > 0 && (
                    <Button variant='contained' className='btn btn-primary' onClick={handleOtpClose}>
                      Back
                    </Button>
                  )}
                  <Button
                    variant='contained'
                    className='btn btn-primary'
                    type='submit'
                    disabled={mutation.isLoading}
                    data-tracking='Store.Checkout.Step2.VerifyCode.Btn'
                    data-tracking-caller={stepperCalledFor}
                  >
                    {mutation.isLoading ? <CircularProgress size={10} style={{ marginRight: 8 }} /> : 'Verify OTP'}
                  </Button>
                </Grid>
              </Box>
            </form>
          </Box>
        </Grid>
      </Grid>
    </>
  )
}

export default PhoneOtpVerification
