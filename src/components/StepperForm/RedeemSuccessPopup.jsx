import React, { useEffect, useState } from 'react';
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogActions,
    Typography,
    Button,
    Box,
} from '@mui/material';
import { paymentFailed, paymentSuccess } from '../ui-kit/icons/webp';
import useStyles from './StepperForm.styles'
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { usePortalStore } from '../../store/store';
import StepperForm from '.';

const RedeemSuccessPopup = () => {
    const { status } = useParams();
    const location = useLocation();
    const navigate = useNavigate()
    const portalStore = usePortalStore((state) => state);
    const [showStatusModal, setShowStatusModal] = useState(false);
    const [statusType, setStatusType] = useState('');

    const classes = useStyles();

    const handleClose = () => {
        setShowStatusModal(false);
        navigate('/')
    };

    const onDone = () => {
        setShowStatusModal(false);
        navigate('/')
        portalStore.openPortal(
            () => <StepperForm stepperCalledFor={"redeem"} selectedRedeemMethod={"trustly"} isTrustlyClosed />,
            'StepperModal'
        );
    };

    useEffect(() => {
        if (location?.pathname?.includes('/redeem/trustly/success') && status === "success") {
            setStatusType('success');
            setShowStatusModal(true);
        } else if (location?.pathname?.includes('/redeem/trustly/failed') && status === "failed") {
            setStatusType('failed');
            setShowStatusModal(true);
        }
    }, [status, location?.pathname]);

    const isSuccess = statusType === 'success';

    if (!showStatusModal) return null;

    return (
        <Dialog
            className={classes.paymentStatusModal}
            open={showStatusModal}
            aria-labelledby="status-modal-title"
        >
            <Box className="payment-container">
                <img
                    src={isSuccess ? paymentSuccess : paymentFailed}
                    alt={isSuccess ? "Payment-success" : "Payment-failed"}
                />
                <DialogTitle
                    className="status-modal-title"
                    sx={{ color: isSuccess ? 'rgba(5, 183, 17, 1)' : 'rgba(235, 0, 0, 1)' }}
                >
                    {isSuccess
                        ? 'BANK ACCOUNT REGISTERED SUCCESSFULLY!'
                        : 'BANK ACCOUNT REGISTRATION FAILED!'}
                </DialogTitle>
                <DialogContent>
                    <Typography variant="h4">
                        {isSuccess
                            ? 'Thank you for processing your most recent redeem payment.'
                            : 'Your payment was not successfully processed. Please try again!'}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button
                        variant="contained"
                        className='btn btn-primary'
                        color={isSuccess ? 'success' : 'error'}
                        onClick={isSuccess ? onDone : handleClose}
                    >
                        {isSuccess ? 'Done' : 'Close'}
                    </Button>
                </DialogActions>
            </Box>
        </Dialog>
    );
};

export default RedeemSuccessPopup;
