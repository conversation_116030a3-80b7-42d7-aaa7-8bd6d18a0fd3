import { useState } from 'react'
import toast from 'react-hot-toast'

import { usePortalStore } from '../../../store/store'
import { equations } from '../constant'

export const useEquationValidator = () => {
  const [userAnswer, setUserAnswer] = useState('')
  const [randomEquation, setRandomEquation] = useState(null)
  const [isEquationCorrect, setIsEquationCorrect] = useState(false)
  const portalStore = usePortalStore((state) => state)

  const pickRandomEquation = () => {
    const randomIndex = Math.floor(Math.random() * equations.length)
    setRandomEquation(equations[randomIndex])
    setIsEquationCorrect(false)
    // setUserAnswer('')
  }
  const handleAnswerSubmit = () => {
    if (userAnswer === randomEquation?.options[randomEquation?.correctOption]) {
      setIsEquationCorrect(true)
      return true
    } else {
      setIsEquationCorrect(false)
      toast.error(`Wrong answer: ${userAnswer} is incorrect!`)
      portalStore.closePortal()
      return false
    }
  }

  return {
    randomEquation,
    userAnswer,
    setUserAnswer,
    isEquationCorrect,
    pickRandomEquation,
    handleAnswerSubmit
  }
}
export default useEquationValidator
