
import {styled} from '@mui/material/styles';
import {Stepper, 
    Step, 
    StepLabel, 
    StepConnector, 
} from '@mui/material';

const ColorStepIconRoot = styled('div')(({theme, ownerState})=>(
    {
        backgroundColor: ownerState.completed || ownerState.active ? '#FDB72E' : theme.palette.grey[800],
        color: ownerState.completed|| ownerState.active ? '#000' : theme.palette.grey[500],
        fontWeight:'bold',
        border:'2px solid #fff',
        width: 30,
        height:30,
        borderRadius:'50%',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        // ...theme.applyStyles('dark',{
        //     backgroundColor:theme.palette.grey[700],
        // })
    }
))


function ColorStepIcon(props){
    const {active, completed, icon, className} = props;
    return (
        <ColorStepIconRoot ownerState ={{completed, active}} className>
            {icon}
        </ColorStepIconRoot>
    )
}


const DashedConnector = styled(StepConnector)(({theme})=>(
    {
        
        [`& .MuiStepConnector-line`]: {
            borderColor: theme.palette.grey[500],
            borderStyle:'dashed',
            borderWidth:2,
            
            borderRadius:1,
        
        },
        
    }
))

const StepperContainer = ({activeStep, steps})=>{

    return (

                <Stepper activeStep={activeStep} alternativeLabel connector={<DashedConnector/>}>
                        {steps.map((step, index) => {
                        const stepProps = {};
                        const labelProps = {};

                            return (
                                <Step key={step.label} {...stepProps}>
                                <StepLabel  StepIconComponent={ColorStepIcon} 
                                {...labelProps} 
                                sx={{'& .MuiStepLabel-label':{
                                    color:'#fff !important'},}}>{step.label}</StepLabel>
                                </Step>
                            );
                            })}
                </Stepper>
    )
}

export default StepperContainer; 
