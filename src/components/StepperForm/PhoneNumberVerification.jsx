import React, { useCallback, useEffect, useState } from 'react'
import {
  Button,
  Grid,
  Typography,
  Select,
  MenuItem,
  Box,
  CircularProgress,
  InputAdornment,
  TextField
} from '@mui/material'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-hot-toast'
import { getOtpMutation, useGetProfileMutation } from '../../reactQuery'
import { UsaFlag, indiaFlag } from '../../components/ui-kit/icons/svg'
import { useUserStore } from '../../store/useUserSlice'
import PhoneOtpVerification from './PhoneOTPVerification'
import { userMobileVerificationSchema } from './schema/phoneSchema'
import useStepperStore from '../../store/useStepperStore'
import useStyles from './StepperForm.styles'
import TagManager from 'react-gtm-module'

const environment = import.meta.env.VITE_NODE_ENV

const PhoneNumberVerification = ({ packageDetails }) => {
  const classes = useStyles()
  const [phoneCode, setPhoneCode] = useState('1') // Default value set here
  const [phoneNumber, setPhoneNumber] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const user = useUserStore((state) => state)
  const [isSentOtp, setIsSentOtp] = useState(false)
  const { activeStep, handleBack, handleNext, stepperCalledFor } = useStepperStore((state) => state)

  // const portalStore = usePortalStore((state) => state);
  const {
    register,
    formState: { errors },
    handleSubmit,
    setError,
    setValue
  } = useForm({
    resolver: yupResolver(userMobileVerificationSchema),
    defaultValues: {
      phoneCode: '1', // Set default phone code here
      phoneNumber: '' // Optionally, set a default value for phone number
    }
  })

  useEffect(() => {
    register('phoneCode') // Register phoneCode to ensure it's captured in the form data
    register('phoneNumber') // Register phoneNumber to ensure it's captured in the form data
  }, [register])

  useEffect(() => {
    if (user?.userDetails?.phoneVerified) {
      setPhoneNumber(user?.userDetails?.phone)
      setPhoneCode(user?.userDetails?.phoneCode)
    }
    if (stepperCalledFor === 'purchase') {
      TagManager.dataLayer({
        dataLayer: {
          event: 'checkout_phone_verification',
          user_id: user?.userDetails?.userId,
          email: user?.userDetails?.email,
          item_id: packageDetails?.packageId,
          item_name: packageDetails?.packageName,
          price: packageDetails?.amount,
          catalog: packageDetails?.isSpecialPackage ? 'Special_Package' : 'Basic_Package',
          gcCoin: packageDetails?.gcCoin,
          scCoin: packageDetails?.scCoin
        }
      })
    }
  }, [])

  // useEffect(() => {
  //     setPhoneNumber('');
  // }, [phoneCode]);

  const handleOtpOpen = () => {
    // portalStore.openPortal(() => <OtpVerification phone={phoneNumber} code={phoneCode} />, 'innerModal');
    setIsSentOtp(true)
  }
  const handleOtpClose = () => {
    setIsSentOtp(false)
  }
  const { mutate: getProfileMutation } = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails(res?.data?.data)
    },
    onError: (error) => {
      console.log('Error getting profile:', error)
    }
  })
  const mutation = getOtpMutation({
    onSuccess: () => {
      toast.success('Verification code sent successfully')
      handleOtpOpen()
      setIsLoading(false)
      // reset();
      TagManager.dataLayer({
        dataLayer: {
          event: 'checkout_phone_verification_code_sent',
          user_id: user?.userDetails?.userId,
          email: user?.userDetails?.email,
          phone: phoneNumber
        }
      })
    },
    onError: (error) => {
      if (error.response?.data?.errors?.[0]?.errorCode === 3024) {
        TagManager.dataLayer({
          dataLayer: {
            event: 'checkout_phone_verification_invalid_phone',
            user_id: user?.userDetails?.userId,
            email: user?.userDetails?.email,
            phone: phoneNumber,
            error: error.response?.data?.errors?.[0]?.description
          }
        })
      } else if (error.response.status === 429) {
        // toast.error(error.response.statusText);
      } else if (error.response?.data?.errors?.[0]?.errorCode === 3016) {
        // toast.error(error.response?.data?.errors?.[0]?.description);
        setError(
          'phoneNumber',
          {
            type: 'focus',
            message:
              'This phone number is already registered at The Money Factory, please use a different phone number.'
          },
          { shouldFocus: true }
        )
      } else if (error.response?.data?.errors?.[0]?.errorCode === 3019) {
        // toast.success(error.response?.data?.errors?.[0]?.description);
        user.setUserDetails.phoneVerified = true
        getProfileMutation()
      } else {
        // toast.error(error.response?.data?.errors?.[0]?.description);
      }
      setIsLoading(false)
    }
  })

  const formatPhoneNumber = (number) => {
    if (phoneCode === '1') {
      const cleaned = ('' + number).replace(/\D/g, '')
      const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/)
      if (match) {
        return `(${match[1]}) ${match[2]}-${match[3]}`
      }
      return cleaned
    }
    return number
  }

  const handlePhoneNumberChange = (event) => {
    const value = event.target.value
    const formattedValue = formatPhoneNumber(value)
    setPhoneNumber(formattedValue)
    setValue('phoneNumber', value, { shouldValidate: true }) // Update the form value
  }

  const onPhoneNumberSubmit = useCallback(
    async (data) => {
      setIsLoading(true)
      const unformattedPhoneNumber = data.phoneNumber.replace(/\D/g, '') // Send the unformatted number
      setPhoneNumber(unformattedPhoneNumber)
      mutation.mutate({
        phoneCode: `${data.phoneCode}`,
        phone: unformattedPhoneNumber
      })
    },
    [mutation]
  )

  const handlePhoneCodeChange = (event) => {
    const value = event.target.value
    setPhoneCode(value)
    setValue('phoneCode', value, { shouldValidate: true }) // Update the form value
  }

  const phoneCodeOptions = [{ code: '1', flag: UsaFlag, label: '+1' }]

  if (environment !== 'production') phoneCodeOptions.push({ code: '91', flag: indiaFlag, label: '+91' })

  return (
    <>
      {!isSentOtp
        ? (
          <Grid className={classes.StepperModal} sx={{ top: '70px' }}>
            <Grid>
              <Box className='personal-details-container'>
                <Typography className='personal-details-text'>PHONE VERIFICATION</Typography>
              </Box>
              <Box className='small-subheader-container'>
                <Typography className='small-subheader-text'>
                  Enter your phone number to receive a verification code
                </Typography>
              </Box>
            </Grid>
            <Box className='phone-input-section'>
              <form onSubmit={handleSubmit(onPhoneNumberSubmit)}>
                <Grid className='custom-phone-input'>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <TextField
                      id='outlined-start-adornment'
                      sx={{
                        m: 1,
                        width: '25ch',
                        '& .MuiInputBase-root.Mui-disabled': {
                          color: 'gray' // Ensure input text is gray when disabled
                        },
                        '& .MuiInputBase-input.Mui-disabled': {
                          WebkitTextFillColor: 'gray' // Ensure input text fill is gray when disabled
                        }
                      }}
                      placeholder='Phone number'
                      disabled={user?.userDetails?.phoneVerified}
                      type='text'
                      value={phoneNumber}
                      onChange={handlePhoneNumberChange}
                      inputProps={{
                        maxLength: 10,
                        'data-tracking': 'Store.Checkout.Step2.Phone.Fld',
                        'data-tracking-caller': `${stepperCalledFor}`
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position='start'>
                            <Select
                              value={phoneCode}
                              onChange={handlePhoneCodeChange}
                              disabled={user?.userDetails.phoneVerified}
                              displayEmpty
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                '& .MuiSelect-select': {
                                  padding: '8px 0px' // Adjust padding to better align with the input field
                                }
                              }}
                              inputProps={{
                                sx: {
                                  padding: 0, // Remove default padding to fit within the input
                                  display: 'flex',
                                  alignItems: 'center'
                                }
                              }}
                            >
                              {phoneCodeOptions.map((option) => (
                                <MenuItem key={option.code} value={option.code} sx={{ color: '#fff', fontWeight: 600 }}>
                                  <img src={option.flag} style={{ marginRight: '8px' }} alt={`Flag of ${option.label}`} />
                                  {option.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </InputAdornment>
                        )
                      }}
                    />
                  </Box>
                  {errors?.phoneCode && <p className='inputError' style={{ textAlign: 'center' }}>{errors.phoneCode.message}</p>}
                  {errors?.phoneNumber && <p className='inputError' style={{ textAlign: 'center' }}>{errors.phoneNumber.message}</p>}
                </Grid>
                <Grid className='btn-wrap'>
                  {activeStep > 0 && (
                    <Button
                      className='btn btn-primary'
                      onClick={handleBack}
                      data-tracking='Store.Checkout.Step2.Back.Btn'
                      data-tracking-caller={stepperCalledFor}
                    >
                      Back
                    </Button>
                  )}
                  {user?.userDetails?.phoneVerified
                    ? (
                      <Button className='btn btn-primary' onClick={handleNext}>
                        Next
                      </Button>)
                    : (
                      <Button
                        type='submit'
                        className='btn btn-primary'
                        disabled={isLoading}
                        data-tracking='Store.Checkout.Step2.SendCode.Btn'
                        data-tracking-caller={stepperCalledFor}
                      >
                        Send Code
                        {isLoading && <CircularProgress size={10} style={{ marginLeft: 8 }} />}
                      </Button>)}
                </Grid>
              </form>
            </Box>
          </Grid>)
        : (
          <PhoneOtpVerification phone={phoneNumber} code={phoneCode} handleOtpClose={handleOtpClose} />)}
    </>
  )
}

export default PhoneNumberVerification
