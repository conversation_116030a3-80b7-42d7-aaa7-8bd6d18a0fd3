import * as React from 'react';
import { useState } from 'react';
import {
    Button,
    Typography,
    Box,
    Grid

} from '@mui/material';

import useStyles from './StepperForm.styles';
import useStepperStore from '../../store/useStepperStore';
import { useUserStore } from '../../store/useUserSlice';
import KYCSection from '../../pages/Accounts/components/KYCSections';


const KYCVerification = () => {
    const classes = useStyles();
    const { activeStep, steps, handleBack, handleNext } = useStepperStore((state) => state);
    const user = useUserStore((state) => state)
    const [initializeKYC, setInitializeKYC] = useState(false);

    const handleKYCInitialize = () => {
        setInitializeKYC(!initializeKYC);
    }

    return (
        <Grid className={classes.StepperModal} >
            <Grid>
                <Box className='verification-code-container kyc-main-content'>
                    <Typography className='verification-code-text'>KYC VERIFICATION</Typography>
                    <Box className='small-subheader-container' >
                        <Typography className='small-subheader-text'>

                            You are about to start your Identity Verification. Please
                            prepare a valid ID and have your mobile device handy.


                        </Typography>
                    </Box>

                    {
                        initializeKYC && <KYCSection handleClose={handleKYCInitialize} setInitializeKYC={setInitializeKYC} />
                    }

                    <Grid className='btn-wrap'>
                        {
                            activeStep > 0 &&
                            <Button className='btn btn-primary' onClick={handleBack}>
                                Back
                            </Button>
                        }
                        {
                            (user?.userDetails.kycStatus === 'K3' || user?.userDetails?.kycStatus === 'K2') ?
                                <Button className='btn btn-primary' onClick={handleKYCInitialize} disabled={initializeKYC}>
                                    Start Verification
                                </Button>
                                :
                                <Button className='btn btn-primary' onClick={handleNext}>
                                    Next
                                </Button>
                        }
                    </Grid>
                </Box>


            </Grid>


        </Grid>
    )
}

export default KYCVerification;