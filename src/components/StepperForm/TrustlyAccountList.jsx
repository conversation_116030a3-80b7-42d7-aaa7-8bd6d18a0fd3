import React from 'react'
import { Grid, RadioGroup, FormControlLabel, Radio, CircularProgress, Box, Typography } from '@mui/material'
import { Controller } from 'react-hook-form'
/* eslint-disable multiline-ternary */

const TrustlyAccountList = ({ trustlyData = {}, isTrustlyDataLoading, classes, errors, control, onBankSelect, existingBankAccounts }) => {
  const trustlyDetails = trustlyData?.data?.data || []

  return (
    <div className='bank-account-section'>
      {isTrustlyDataLoading ? (
        <Box
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '150px'
          }}
        >
          <CircularProgress />
        </Box>
      ) : trustlyDetails.length > 0 ? (
        <Controller
          name='trustlyAccount'
          control={control}
          rules={{ required: 'Please select a bank account' }}
          render={({ field }) => (
            <RadioGroup {...field}>
              {trustlyDetails?.map((data) => (
                <Grid container key={data?.trustlyTransactionId} alignItems='center' className='select-bank-card'>
                  <Grid item xs={2} style={{ display: 'flex' }}>
                    <img
                      src={`https://paywithmybank.com/start/assets/institutions/icons/${data?.moreDetails?.bankIdentifier}.png`}
                      alt='Bank Logo'
                      style={{ width: '30px', height: 'inherit' }}
                    />
                  </Grid>

                  <Grid item xs={10} className='bank-details'>
                    <Box className='bank-name'>
                      <Typography>{data?.moreDetails?.bankName}</Typography>
                    </Box>

                    <Box className='bank-number'>
                      <Typography className='holder-name'>
                        {data?.moreDetails?.accountType?.toLowerCase().replace(/\b\w/g, (c) => c.toUpperCase())} | ****
                        {data?.moreDetails?.accountNumber}
                      </Typography>

                      <FormControlLabel
                        value={data?.trustlyTransactionId}
                        control={
                          <Radio
                            onClick={onBankSelect}
                            sx={{
                              color: '#4D4D4D',
                              padding: 0,
                              paddingLeft: '10px',
                              '&.Mui-checked': {
                                color: '#FFA538'
                              }
                            }}
                          />
                        }
                        label=''
                        style={{ marginRight: '5px', marginTop: '-13px' }}
                      />
                    </Box>
                  </Grid>
                </Grid>
              ))}
            </RadioGroup>
          )}
        />
      ) : (
        <div style={{ fontSize: '16px', color: 'red', textAlign: 'center', padding: '20px' }}>
          No Bank Account Selected...
        </div>
      )}
      {(existingBankAccounts > 0) && (
        <p style={{ color: 'red' }}>
          Delete existing account from settings page to add new one.
        </p>
      )}
      {errors?.trustlyAccount && (
        <p style={{ paddingTop: '3px' }} className={classes.inputError}>
          {errors?.trustlyAccount?.message}
        </p>
      )}
    </div>
  )
}

export default TrustlyAccountList
