import React from 'react'
import { Grid, Button, Typography, Box } from '@mui/material'
import { Bank, skrillLarge } from '../ui-kit/icons/svg'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import TrustlyBankIcons from '../ui-kit/icons/png/trustly_bankicons.png'
import { formatPriceWithCommas } from '../../utils/helpers'

const SelectRedeemMethod = ({
  handleRedeemMethodClick,
  handleClose,
  setShowHistory,
  transactionsData,
  isDisable,
  userDetails,
  selectedRedeemMethod
}) => {
  const activeMethods = transactionsData?.activePaymentMethods || []

  const isActive = (method) => activeMethods?.includes(method)

  // const [showPayByBank, setShowPayByBank] = useState(true)

  // useEffect(() => {
  //     if (import.meta.env.VITE_NODE_ENV === 'production' && !userDetails?.isInternalUser) {
  //         setShowPayByBank(false);
  //     } else {
  //         setShowPayByBank(true);
  //     }
  // }, [userDetails]);

  return (
    <Box className='select-reedem-method-wrap'>
      <Box
        style={{
          display: 'flex',
          alignItems: 'end',
          justifyContent: 'space-between',
          marginBottom: '1rem'
        }}
      >
        <Typography variant='heading' style={{ marginBottom: '0' }}>
          Select Redeem Method
        </Typography>
      </Box>
      <Grid className='select-reedem-cta-card'>
        {isActive('SKRILL') && (
          <a href='javascript:void(0);' className='select-reedem-cta' onClick={() => handleRedeemMethodClick('skrill')}>
            <Grid className='reedem-icon'>
              <img src={skrillLarge} className='skrill-logo' alt='Skrill' />
            </Grid>
            <Typography>Skrill</Typography>
            <ChevronRightIcon className='reedem-arrow' />
          </a>
        )}

        {isActive('PAY_BY_BANK') && (
          <a href='javascript:void(0);' className='select-reedem-cta' onClick={() => handleRedeemMethodClick('bank')}>
            <Grid Grid className='reedem-icon'>
              <img src={Bank} alt='Pay By Bank' style={{ height: '30px', width: '60px' }} />
            </Grid>
            <Typography>Online Banking</Typography>
            <ChevronRightIcon className='reedem-arrow' />
          </a>
        )}

        {isActive('TRUSTLY') && (
          <a
            href='javascript:void(0);'
            className='select-reedem-cta'
            onClick={() => handleRedeemMethodClick('trustly')}
          >
            <Grid Grid className='reedem-icon'>
              <img src={TrustlyBankIcons} alt='pbb-img' style={{ height: '20px', width: '60px' }} />
            </Grid>
            <Typography>Online Banking</Typography>
            <ChevronRightIcon className='reedem-arrow' />
          </a>
        )}

        <Box className='sc-wrap'>
          {selectedRedeemMethod === '' && (
            <>
              <Typography>
                SC -{' '}
                <Typography variant='span' style={{ color: '#FDB72E' }}>
                  {formatPriceWithCommas(userDetails?.userWallet?.totalScCoin)}
                </Typography>
              </Typography>
              <Typography>|</Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  color: 'white',
                  fontWeight: 500
                }}
              >
                Redeemable SC -{' '}
                <Typography variant='span' style={{ color: '#FDB72E' }}>
                  {formatPriceWithCommas(userDetails?.userWallet?.scCoin?.wsc)}
                </Typography>
              </Typography>
            </>
          )}
        </Box>

        <Grid className='btn-wrap'>
          <Button variant='contained' className='btn btn-secondary' disabled={isDisable} onClick={handleClose}>
            Back
          </Button>
          <Button
            variant='contained'
            className='btn btn-primary'
            style={{ color: isDisable || (!transactionsData?.myTransactions?.rows?.length && 'rgba(0, 0, 0, 0.26)') }}
            disabled={isDisable || !transactionsData?.myTransactions?.rows?.length}
            type='button'
            onClick={() => setShowHistory(true)}
          >
            Show Request
          </Button>
        </Grid>
      </Grid>
    </Box>
  )
}

export default SelectRedeemMethod
