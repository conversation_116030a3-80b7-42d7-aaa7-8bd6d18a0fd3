import React, { useEffect } from 'react'
import { Grid, Box, Typography } from '@mui/material'
import useStyles from './StepperForm.styles'
import useStepperStore from '../../store/useStepperStore'
import PaymentModal from '../../pages/Store/PaymentModal'
import { useDepositInitStore, usePortalStore } from '../../store/store'
import usePaysafePayment from '../../pages/Store/PaymentModal/hooks/usePaysafe'
import PaymentTimer from '../../pages/Store/PaymentModal/PaymentTimer'

const PaymentMethod = ({ packageDetails }) => {
  const classes = useStyles()
  const { handleBack } = useStepperStore((state) => state)
  const portalStore = usePortalStore((state) => state)
  const { isPaymentScreenLoading, setIsPaymentScreenLoading } = usePaysafePayment()
  const transactionDetails = useDepositInitStore((state) => state.transactionDetails)  

  return (
    <>
      <Grid>
        <Box className='verification-code-container'>
          <Typography className='verification-code-text'>PAYMENT METHOD</Typography>
          {transactionDetails?.success && <PaymentTimer initialTime={10 * 60} onTimeout={portalStore.closePortal} />}
        </Box>
        <Box sx={{ padding: '0 10px' }} className='small-subheader-container'>
          <Typography className='small-subheader-text'>Secure & Convenient Payment methods.</Typography>
        </Box>
        <PaymentModal
          packageDetails={packageDetails}
          isPaymentScreenLoading={isPaymentScreenLoading}
          setIsPaymentScreenLoading={setIsPaymentScreenLoading}
        />
      </Grid>
    </>
  )
}

export default PaymentMethod
