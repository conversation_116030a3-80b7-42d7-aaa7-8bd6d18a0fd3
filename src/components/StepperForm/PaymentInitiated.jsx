import React, { useEffect } from 'react'
import { Grid, Typography } from '@mui/material'
import { Success } from '../ui-kit/icons/utils'
import { useGamesStore } from '../../store/store'

const RedeemInitiated = ({ type, handleClose }) => {
  const setSelectedSubCat = useGamesStore((state) => state.setSelectedSubCat)
  useEffect(() => {
    setSelectedSubCat('Lobby')
    setTimeout(() => handleClose(), 1000)
  })
  return (
    <>
      <Grid className='personal-details-container inner-modal-header'>
        <Typography className='personal-details-text' variant='h4'>
          {type === 'deposit' ? 'Payment Processing' : 'Redeem Request'}{' '}
        </Typography>
      </Grid>

      <Grid className='payment-status-content' display='flex' flexDirection='column' alignItems='center'>
        <img
          src={Success}
          alt='Success'
          style={{ height: '80px', width: '80px', margin: '1rem 0' }}
          className='payment-status-icon'
        />
        <Typography variant='h6'>
          {type === 'deposit' ? 'Payment Initiated !' : 'Redeem Request Submitted !'}
        </Typography>
        <Typography>
          {type === 'deposit'
            ? 'Please wait while we redirect you to the next page.'
            : 'Your wallet will be credited shortly'}
        </Typography>
      </Grid>
    </>
  )
}

export default RedeemInitiated
