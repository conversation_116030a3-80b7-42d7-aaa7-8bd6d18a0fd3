import React from 'react';
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogActions,
    Typography,
    Button,
    Box,
} from '@mui/material';
import { paymentFailed, paymentSuccess } from '../ui-kit/icons/webp';
import useStyles from './StepperForm.styles'



const StatusModal = ({ type, onDone, onTryAgain }) => {

    const classes = useStyles()

    const isSuccess = type === 'success';

    const handleDone = () => { onDone() }
    const handleTryAgain = () => { onTryAgain() }
    return (
        <Dialog
            className={classes.paymentStatusModal}
            open={true}
            aria-labelledby="status-modal-title"
        >
            <Box
                className="payment-container"
            >
                {isSuccess ? (
                    <img src={paymentSuccess} alt="Payment-success" />
                ) : (
                    <img src={paymentFailed} alt="Payment-failed" />
                )}
                <DialogTitle className="status-modal-title"
                    sx={{ color: isSuccess ? 'rgba(5, 183, 17, 1)' : 'rgba(235, 0, 0, 1)', }}>
                    {isSuccess
                        ? 'BANK ACCOUNT REGISTERED SUCCESSFULLY!'
                        : 'BANK ACCOUNT REGISTRATION FAILED!'}
                </DialogTitle>
                <DialogContent>
                    <Typography variant="h4">
                        {isSuccess
                            ? 'Thank you for processing your most recent redeem payment.'
                            : 'Your payment was not successfully processed. Please try again!'}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button
                        variant="contained"
                        className='btn btn-primary'
                        color={isSuccess ? 'success' : 'error'}
                        onClick={isSuccess ? handleDone : handleTryAgain}>
                        {isSuccess ? 'Done' : 'Close'}
                    </Button>
                </DialogActions>
            </Box>
        </Dialog>
    );
};

export default StatusModal;
