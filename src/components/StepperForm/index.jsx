import * as React from 'react'
import { useEffect, useMemo } from 'react'
import { Box, Grid, IconButton } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './StepperForm.styles'
import PhoneNumberVerification from './PhoneNumberVerification'
import KYCVerification from './KYCVerification'
import UserDetails from './UserDetails'
import useStepperStore from '../../store/useStepperStore'
import WithDraw from './WithDraw'
import StepperContainer from './StepperContainer'
import PaymentMethod from './Payment'
import { useUserStore } from '../../store/useUserSlice'
import { BrandLogo } from '../ui-kit/icons/brand'
import { useGetProfileMutation } from '../../reactQuery'
import { useSiteLogoStore } from '../../store/store'
import usePaysafePayment from '../../pages/Store/PaymentModal/hooks/usePaysafe'
import PaymentLoader from '../Loader/PaymentLoader'

import { useNavigate } from 'react-router-dom'
import { usePaymentStore } from '../../store/usePaymentStore'

const getStepConfigurations = (packageDetails, selectedRedeemMethod) => ({
  redeem: [
    { label: 'Personal Details', component: <UserDetails /> },
    { label: 'Phone Verification', component: <PhoneNumberVerification /> },
    { label: 'KYC Verification', component: <KYCVerification /> },
    { label: 'Redeem', component: <WithDraw selectedRedeemMethod={selectedRedeemMethod} /> }
  ],
  purchase: [
    { label: 'Personal Details', component: <UserDetails packageDetails={packageDetails} /> },
    { label: 'Phone Verification', component: <PhoneNumberVerification packageDetails={packageDetails} /> },
    // { label: 'KYC Verification', component: <KYCVerification /> },
    {
      label: 'Payment Method',
      component: <PaymentMethod packageDetails={packageDetails} />
    }
  ],
  kycSettings: [
    { label: 'Personal Details', component: <UserDetails /> },
    { label: 'Phone Verification', component: <PhoneNumberVerification /> },
    { label: 'KYC Verification', component: <KYCVerification /> }
  ]
})

const determineStep = (kycStatus, phoneVerified, stepperCalledFor) => {
  if (stepperCalledFor === 'redeem') {
    if (kycStatus === 'K1' || kycStatus === 'K0') return 0
    if (!phoneVerified) return 1
    if (phoneVerified && (kycStatus === 'K3' || kycStatus === 'K2')) return 2
    return 3
  } else {
    if (kycStatus === 'K1' || kycStatus === 'K0') return 0
    if (!phoneVerified) return 1
    return 2
  }
}

export default function StepperForm({ stepperCalledFor, packageDetails, selectedRedeemMethod, isTrustlyClosed }) {
  const classes = useStyles()
  const navigate = useNavigate()
  const { kycStatus, phoneVerified } = useUserStore((state) => state.userDetails)
  const { activeStep, setSteps, setActiveStep, setStepperCalledFor } = useStepperStore((state) => state)
  const portalStore = usePortalStore((state) => state)
  const setKycStatus = useUserStore((state) => state.setKycStatus)
  const setUserDetails = useUserStore((state) => state.setUserDetails)

  useEffect(() => {
    getProfileMutation.mutate()
  }, [])

  const steps = useMemo(
    () => getStepConfigurations(packageDetails, selectedRedeemMethod)[stepperCalledFor] || [],
    [packageDetails, selectedRedeemMethod, stepperCalledFor]
  )
  const logoData = useSiteLogoStore((state) => state)
  const { isPaymentScreenLoading } = usePaysafePayment()
  const { isLoading } = usePaymentStore((state) => state.status)

  const handleClose = () => {
    if (isLoading) return
    if (isTrustlyClosed) {
      portalStore.closePortal()
      navigate('/')
    } else {
      setKycStatus(false)
      portalStore.closePortal()
    }
  }

  useEffect(() => {
    setSteps(steps)
    setStepperCalledFor(stepperCalledFor)
  }, [])

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (res?.data?.data?.kycStatus) {
        setActiveStep(determineStep(res?.data?.data?.kycStatus, res?.data?.data?.phoneVerified, stepperCalledFor))
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  useEffect(() => {
    if (kycStatus) {
      setActiveStep(determineStep(kycStatus, phoneVerified, stepperCalledFor))
    }
  }, [kycStatus, phoneVerified, stepperCalledFor])

  return (
    <>
      {isPaymentScreenLoading && <PaymentLoader />}
      <Grid>
        <Grid className={classes.StepperModal}>
          <Box>
            <Box className='stepper-image-container'>
              <img src={logoData?.desktopLogo || BrandLogo} alt='brand-logo' width='41px' height='49px' />
            </Box>
            <Box
              sx={{
                width: '320px',
                display: 'flex',
                justifyContent: 'center',
                marginBottom: '50px'
              }}
            >
              <Box className='stepper-form-container'>
                <Grid className='modal-close'>
                  <IconButton sx={isLoading ? { display: 'none' } : {}} edge='start' color='inherit' onClick={handleClose} aria-label='close'>
                    <CloseIcon />
                  </IconButton>
                </Grid>
                <Box className='stepper-wrap'>
                  <StepperContainer activeStep={activeStep} steps={steps} />
                </Box>
                <>{steps[activeStep]?.component}</>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </>
  )
}
