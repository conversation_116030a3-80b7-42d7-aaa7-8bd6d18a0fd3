import React, { useEffect, useState } from 'react'
import {
  Grid,
  FormLabel,
  OutlinedInput,
  Button,
  Box,
  FormControl,
  CircularProgress
} from '@mui/material'
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline'
import useStyles from './StepperForm.styles'
import { useGetTrustlyData } from '../../pages/Accounts/hooks/usePayByBankData'
import { useTrustlyPay } from '../../pages/Store/PaymentModal/hooks/useTrustlyPay'
import { initiateTrustlyScript } from '../../pages/Store/PaymentModal/utils/trustly/initiators'
import TrustlyAccountList from './TrustlyAccountList'
import { formatPriceWithCommas } from '../../utils/helpers'
/* eslint-disable multiline-ternary */

const TrustlyRedeemForm = ({
  userDetails,
  preventInvalidCharacters,
  errors,
  handleBack,
  isDisable,
  setBankAmountValue,
  register,
  control,
  isCanadianUser,
  randomEquation,
  userAnswer,
  setUserAnswer,
  isEquationCorrect,
  pickRandomEquation
}) => {
  const classes = useStyles()

  const [isWidgetOpen, setIsWidgetOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { trustlyData = {}, isTrustlyDataLoading, refetch } = useGetTrustlyData()

  const bankAccountLimit = 1
  const existingBankAccounts = trustlyData?.data?.data?.length || 0

  useEffect(() => {
    if (isCanadianUser) {
      pickRandomEquation()
    }
  }, [isCanadianUser])

  const initiateTrustly = useTrustlyPay.initTrustlyRedeemMutation({
    onSuccess: (res) => {
      const establish = res?.data?.data?.establishData
      const trustlyOptions = res?.data?.data?.TrustlyOptions
      Trustly.selectBankWidget(establish, trustlyOptions)
      setLoading(false)
    },
    onError: (error) => {
      console.error('**************error', error)
      setLoading(false)
    }
  })

  const initiateTrustlyUserFlow = () => {
    setLoading(true)
    setIsWidgetOpen(true)
    initiateTrustlyScript({
      callback: () => {
        initiateTrustly.mutate()
      },
      failure: () => {
        console.log('Trustly Script Failure')
      }
    })
  }

  console.log('###Existing', existingBankAccounts, trustlyData)

  return (
    <Box className='select-bank-account'>
      <FormControl fullWidth>
        <Grid className='inputWrap'>
          {isCanadianUser ? (
            <>
              <FormLabel className='label'>
                Redeem Your Prize{' '}
                <span className='textAmount' style={{ cursor: 'pointer' }}>
                  {` ( Max  Amount : ${formatPriceWithCommas(userDetails?.userWallet?.scCoin?.wsc) || '0.00'} )`}
                </span>
              </FormLabel>
              <br />
              <Grid className='input-grp max-input-wrap'>
                <OutlinedInput
                  id='outlined-basic'
                  label=''
                  onKeyDown={preventInvalidCharacters}
                  min='1'
                  className='no-spinners'
                  variant='outlined'
                  placeholder='Please insert the total amount of the prize'
                  {...register('amount')}
                  name='amount'
                  type='text'
                />
                <Button className='inner-btn' onClick={setBankAmountValue}>
                  Max
                </Button>
              </Grid>
            </>
          ) : (
            <>
              <FormLabel className='label'>
                Redeem Your Amount{' '}
                <span className='textAmount' style={{ cursor: 'pointer' }}>
                  ( Max Amount : {userDetails?.userWallet?.scCoin?.wsc || '0.00'} )
                </span>
              </FormLabel>
              <br />
              <Grid className='input-grp max-input-wrap'>
                <OutlinedInput
                  id='outlined-basic'
                  label=''
                  onKeyDown={preventInvalidCharacters}
                  min='1'
                  className='no-spinners'
                  variant='outlined'
                  placeholder='Amount'
                  {...register('amount')}
                  name='amount'
                  type='text'
                />
                <Button className='inner-btn' onClick={setBankAmountValue}>
                  Max
                </Button>
              </Grid>
            </>
          )}

          {errors?.amount && (
            <p style={{ paddingTop: '15px' }} className={classes.inputError}>
              {errors.amount.message}
            </p>
          )}
        </Grid>

        <TrustlyAccountList
          trustlyData={trustlyData}
          isTrustlyDataLoading={isTrustlyDataLoading}
          classes={classes}
          errors={errors}
          refetchTrustlyAccounts={refetch}
          control={control}
          onBankSelect={() => setIsWidgetOpen(false)}
          existingBankAccounts={existingBankAccounts}
        />

        {!existingBankAccounts > 0 && (
          <Grid className='add-bank-account-wrap'>
            <Button
              type='button'
              className='bank-account-btn'
              onClick={initiateTrustlyUserFlow}
              disabled={existingBankAccounts >= bankAccountLimit}
            >
              {loading ? (
                <CircularProgress size={20} />
              ) : (
                <>
                  <AddCircleOutlineIcon /> Add Bank Account
                </>
              )}
            </Button>
          </Grid>
        )}
      </FormControl>
      {isCanadianUser && (
        <Grid className='equation-section'>
          {randomEquation && (
            <Grid className='equation-wrap'>
              <p className='label'>
                In order to be declared the winner of the prize shown above. Please correctly answer the following skill
                testing question:
              </p>

              <Grid className='equation-display'>
                <FormLabel className='equation-value'>{randomEquation?.equation}</FormLabel>
                <Grid className='input-wrap'>
                  <OutlinedInput
                    type='text'
                    inputMode='numeric' // mobile-friendly
                    sx={{ input: { textAlign: 'center' } }}
                    id='outlined-basic'
                    label=''
                    onKeyDown={(e) => {
                      const key = e.key
                      const isDigit = /^[0-9]$/.test(key)
                      const isMinus = key === '-' && e.target.selectionStart === 0 && !e.target.value.includes('-')
                      const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(key)

                      if (!(isDigit || isMinus || isControl)) {
                        e.preventDefault()
                      }
                    }}
                    onChange={(e) => {
                      const value = e.target.value
                      if (/^-?\d*$/.test(value)) {
                        setUserAnswer(Number(value))
                      }
                    }}
                    className='no-spinners'
                    variant='outlined'
                    placeholder='Enter your Answer'
                    name='equation-answer'
                  />
                </Grid>
              </Grid>
            </Grid>
          )}
        </Grid>
      )}

      {!isWidgetOpen && (
        <Grid className='btn-wrap'>
          <Button variant='contained' className='btn btn-secondary' disabled={isDisable} onClick={handleBack}>
            <span>Back</span>
          </Button>
          <Button
            variant='contained'
            className='btn btn-primary'
            type='submit'
            disabled={isCanadianUser ? userAnswer === '' : false}
          >
            <span>Redeem</span>
          </Button>
        </Grid>
      )}

      <Box sx={{ position: 'relative', minHeight: '50px' }}>{isWidgetOpen && <div id='widget-id' />}</Box>
    </Box>
  )
}

export default TrustlyRedeemForm
