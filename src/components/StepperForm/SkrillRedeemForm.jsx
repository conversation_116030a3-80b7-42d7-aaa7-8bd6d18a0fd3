import React, { useEffect } from 'react'
import { Grid, FormLabel, OutlinedInput, Button, FormControlLabel, Checkbox } from '@mui/material'
import useStyles from './StepperForm.styles'
import { formatPriceWithCommas } from '../../utils/helpers'
import useTransaction from '../../pages/Accounts/hooks/useRedeemRequests'
import { Controller } from 'react-hook-form'
/* eslint-disable multiline-ternary */

const SkrillRedeemForm = ({
  userDetails,
  preventInvalidCharacters,
  register,
  setAmountValue,
  errors,
  isDisable,
  handleBack,
  isCanadianUser,
  randomEquation,
  userAnswer,
  setUserAnswer,
  // isEquationCorrect,
  pickRandomEquation,
  watch,
  setValue,
  control
}) => {
  const classes = useStyles()
  useEffect(() => {
    if (isCanadianUser) {
      pickRandomEquation()
    }
  }, [])
  const amount = watch('amount')
  const email = watch('actionableEmail')

  const isCheckboxDisabled = !amount || !email
  useEffect(() => {
    if (isCheckboxDisabled) {
      setValue('checkbox', false)
    }
  }, [isCheckboxDisabled, setValue])

  return (
    <Grid display='flex' justifyContent='space-between' className='reedem-kyc-content'>
      <Grid className='leftSection'>
        <Grid className='inputWrap'>
          {isCanadianUser ? (
            <>
              <FormLabel className='label'>
                Redeem Your Prize{' '}
                <span className='textAmount' style={{ cursor: 'pointer' }}>
                  {` ( Max  Amount : ${formatPriceWithCommas(userDetails?.userWallet?.scCoin?.wsc) || '0.00'} )`}
                </span>
              </FormLabel>
              <br />
              <Grid className='input-grp max-input-wrap'>
                <OutlinedInput
                  id='outlined-basic'
                  label=''
                  onKeyDown={preventInvalidCharacters}
                  min='1'
                  className='no-spinners'
                  variant='outlined'
                  placeholder='Please insert the total amount of the prize'
                  {...register('amount')}
                  name='amount'
                  type='text'
                />
                <Button className='inner-btn' onClick={setAmountValue}>
                  Max
                </Button>
              </Grid>
            </>
          ) : (
            <>
              <FormLabel className='label'>
                Redeem Your Amount{' '}
                <span className='textAmount' style={{ cursor: 'pointer' }}>
                  {` ( Max  Amount : ${userDetails?.userWallet?.scCoin?.wsc || '0.00'} )`}
                </span>
              </FormLabel>
              <br />
              <Grid className='input-grp max-input-wrap'>
                <OutlinedInput
                  id='outlined-basic'
                  label=''
                  onKeyDown={preventInvalidCharacters}
                  min='1'
                  className='no-spinners'
                  variant='outlined'
                  placeholder='Amount'
                  {...register('amount')}
                  name='amount'
                  type='text'
                />
                <Button className='inner-btn' onClick={setAmountValue}>
                  Max
                </Button>
              </Grid>
            </>
          )}

          {errors?.amount && (
            <p style={{ paddingTop: '15px' }} className={classes.inputError}>
              {errors?.amount?.message}
            </p>
          )}
        </Grid>

        <Grid className='inputWrap'>
          <FormLabel className='label'>Skrill Email Address</FormLabel>
          <br />
          <OutlinedInput
            className='skrill-email'
            variant='outlined'
            id='Email_Address'
            {...register('actionableEmail')}
            placeholder='Skrill Email Address'
            autoComplete='off'
          />
          {errors?.actionableEmail && (
            <p style={{ paddingTop: '3px' }} className={classes.inputError}>
              {errors?.actionableEmail?.message}
            </p>
          )}
        </Grid>

        <Grid>
          <Grid className='themeCheckBoxWrap'>
            {/* <FormControlLabel
              className=''
              control={
                <Checkbox
                  sx={{ color: 'grey', borderColor: 'blue' }}
                  {...register('checkbox')}
                  disabled={isCheckboxDisabled}
                />
              }
              label='I confirm that information provided is correct.'
            /> */}
            <Controller
              name='checkbox'
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      {...field}
                      checked={field.value || false}
                      onChange={(e) => field.onChange(e.target.checked)}
                      disabled={isCheckboxDisabled}
                    />
                  }
                  label='I confirm that information provided is correct.'
                />
              )}
            />
            {errors?.checkbox && (
              <p style={{ paddingTop: '3px' }} className={classes.inputError}>
                {errors?.checkbox?.message}
              </p>
            )}
          </Grid>
          {isCanadianUser && (
            <Grid className='equation-section'>
              {randomEquation && (
                <Grid className='equation-wrap'>
                  <p className='label'>
                    In order to be declared the winner of the prize shown above. Please correctly answer the following
                    skill testing question:
                  </p>

                  <Grid className='equation-display'>
                    <FormLabel className='equation-value'>{randomEquation?.equation}</FormLabel>
                    <Grid className='input-wrap'>
                      <OutlinedInput
                        type='text'
                        inputMode='numeric' // mobile-friendly
                        sx={{ input: { textAlign: 'center' } }}
                        id='outlined-basic'
                        label=''
                        onKeyDown={(e) => {
                          const key = e.key
                          const isDigit = /^[0-9]$/.test(key)
                          const isMinus = key === '-' && e.target.selectionStart === 0 && !e.target.value.includes('-')
                          const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(key)

                          if (!(isDigit || isMinus || isControl)) {
                            e.preventDefault()
                          }
                        }}
                        onChange={(e) => {
                          const value = e.target.value
                          if (/^-?\d*$/.test(value)) {
                            setUserAnswer(Number(value))
                          }
                        }}
                        className='no-spinners'
                        variant='outlined'
                        placeholder='Enter your Answer'
                        name='equation-answer'
                        // type='number'
                      />
                    </Grid>
                  </Grid>
                </Grid>
              )}
            </Grid>
          )}

          <Grid className='btn-wrap'>
            <Button variant='contained' className='btn btn-secondary' disabled={isDisable} onClick={handleBack}>
              <span>Back</span>
            </Button>
            <Button
              variant='contained'
              className='btn btn-primary'
              type='submit'
              disabled={isCanadianUser ? userAnswer === '' : false}
            >
              Redeem
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default SkrillRedeemForm
