import React, { useEffect, useState } from 'react';
import {
    CircularProgress,
    Box,
    IconButton
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice';
import StepperForm from '.';
import useStyles from './StepperForm.styles'
import StatusModal from './StatusModal';


const AddBankAccount = ({ redirectUrl }) => {
    const [loading, setLoading] = useState(true);
    const [showIFrame, setShowIFrame] = useState(true);

    const [statusType, setStatusType] = useState('')
    const portalStore = usePortalStore((state) => state)
    const classes = useStyles();

    useEffect(() => {
        const iframe = document.getElementById('iframe-id');
        if (iframe) {
            iframe.contentWindow.onbeforeunload = (event) => {
                // Prevent iframe from navigating
                event.returnValue = 'You are about to leave this page!';
                return false;
            };
        }

        window.addEventListener("message", (event = {}) => {
            const { source: { location: { pathname = '' } = {} } = {} } = event

            if (pathname.includes('/redeem/pay-by-bank/failed')) {
                setShowIFrame(false);
                setStatusType('failed');
            } else if (pathname.includes('/redeem/pay-by-bank/success')) {
                setShowIFrame(false);
                setStatusType('success');
            }
        });

    }, [])

    const handleLoad = () => {
        setLoading(false);
    };

    const handleClose = () => {
        portalStore.closePortal()
    };

    const onDone = () => {
        portalStore.openPortal(() => <StepperForm stepperCalledFor={'redeem'} selectedRedeemMethod={"bank"} />, 'StepperModal')
    }


    return (
        <Box className={classes.iframeContainer}>
            <IconButton
                onClick={handleClose}
                className='iframe-close'
            >
                <CloseIcon />
            </IconButton>
            {
                statusType &&
                <StatusModal
                    type={statusType}
                    onTryAgain={handleClose}
                    onDone={onDone}
                />}

            {loading && (
                <Box className="loader-overlay">
                    <CircularProgress />
                </Box>
            )}
            {showIFrame && <iframe
                id="iframe-id"
                src={redirectUrl}
                title="Add Bank Account"
                width="100%"
                height="600px"
                style={{ border: 'none' }}
                onLoad={handleLoad}
            ></iframe>}

        </Box>
    )
}

export default AddBankAccount;