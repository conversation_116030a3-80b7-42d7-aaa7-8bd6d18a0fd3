import React from 'react';
import { Grid, RadioGroup, FormControlLabel, Radio, Typography, CircularProgress, Box } from '@mui/material';

import { PaymentQuery } from '../../reactQuery';
import { toast } from 'react-hot-toast'
import { Controller } from 'react-hook-form';


const BankAccountList = ({
  payByBankData = {},
  isPayByBankDataLoading,
  classes,
  errors,
  refetchBankAccounts,
  control, bankAccountLimit,
  existingBankAccounts
}) => {

  const deleteMutation = PaymentQuery.deleteBankAccountMutation({
    successToggler: () => {
      toast.success('Bank account deleted successfully!');
      refetchBankAccounts();
    },
  });

  const handleDelete = (bankId) => {
    deleteMutation.mutate({ bankAccountId: bankId });
  };

  const bankDetails = payByBankData?.data?.bankDetails || [];

  return (
    <div className='bank-account-section'>
      <Typography variant="h4">
        Select Your Bank Account  (Max 4 accounts*)
      </Typography>

      {isPayByBankDataLoading ? (
        <Box
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '150px',
          }}
        >
          <CircularProgress />
        </Box>
      ) : bankDetails.length > 0 ? (
        <Controller
          name="bankAccount"
          control={control}
          rules={{ required: "Please select a bank account" }}
          render={({ field }) => (
            <RadioGroup {...field}>
              {bankDetails.map((bank) => (
                <Grid
                  container
                  key={bank.id}
                  alignItems="center"
                  className='select-bank-card'
                >
                  <Grid item xs={1}>
                    <FormControlLabel
                      value={bank.id}
                      control={<Radio style={{ color: '#ffa500' }} />}
                      label=""
                    />
                  </Grid>
                  <Grid item xs={8}>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#ffa500' }}>
                      {bank.bankName}
                    </div>
                    <div style={{ fontSize: '14px', color: '#aaa' }}>{bank.accountHolderName}</div>
                  </Grid>
                  <Grid
                    item
                    xs={3}
                    style={{
                      textAlign: 'right',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      color: '#fff',
                    }}
                  >
                    {bank?.lastDigits}
                  </Grid>
                  {/* <Tooltip title="Delete Account">
                    <IconButton
                      aria-label="delete account"
                      onClick={() => handleDelete(bank?.id)}
                      edge="end"
                     className='delete-icon'
                     disabled={existingBankAccounts >= bankAccountLimit}
                    >
                      <Delete />
                    </IconButton>
                  </Tooltip> */}
                </Grid>
              ))}
            </RadioGroup>
          )}
        />
      ) : (
        <div style={{ fontSize: '16px', color: 'red', textAlign: 'center', padding: '20px' }}>
          No Bank Account Selected...
        </div>
      )}
      {(existingBankAccounts >= bankAccountLimit) && (
        <p style={{ color: 'red' }} >
          You can add upto 4 account
        </p>
      )}
      {errors?.bankAccount && (
        <p style={{ paddingTop: '3px' }} className={classes.inputError}>
          {errors?.bankAccount?.message}
        </p>
      )}
    </div>
  );
};

export default BankAccountList;

