import * as Yup from 'yup'
const onlySpacesRegex = /^\s*$/
const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/
const numberPattern = /^-?\d+(\.\d+)?$/
export const userMobileVerificationSchema = Yup.object().shape({
  phoneCode: Yup.number().required('Phone code is required'),
  phoneNumber: Yup.string()
    .matches(phoneRegExp, 'Phone number is not valid')
    .required('Phone number is required')
    .min(8)
    .max(10)
    .test('no-only-spaces', 'Phone number cannot contain spaces', (value) => {
      return !onlySpacesRegex.test(value)
    })
})

export const userOtpSchema = Yup.object().shape({
  otp: Yup.string()
    .matches(numberPattern, 'OTP is not valid')
    .max(6, 'String length must not exceed 6 characters')
    .required('OTP is required')
})
