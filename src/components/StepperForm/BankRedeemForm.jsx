import React, { useState } from 'react'
import { Grid, FormLabel, OutlinedInput, Button, Box, FormControl, CircularProgress } from '@mui/material'
import { toast } from 'react-hot-toast'
import useStyles from './StepperForm.styles'
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline'
import BankAccountList from './BankAccountList'
import AddBankAccount from './AddBankAccount'
import { usePortalStore } from '../../store/userPortalSlice'
import { useAddPayByBankData, useGetPayByBankData } from '../../pages/Accounts/hooks/usePayByBankData'
/* eslint-disable multiline-ternary */

const BankRedeemForm = ({
  userDetails,
  preventInvalidCharacters,
  errors,
  handleBack,
  isDisable,
  setBankAmountValue,
  register,
  control
}) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const { payByBankData = {}, isPayByBankDataLoading, refetch } = useGetPayByBankData()

  const [loading, setLoading] = useState(false)
  const { addBankAccountMutate } = useAddPayByBankData()

  // Define the limit for the number of bank accounts
  const bankAccountLimit = 4
  const existingBankAccounts = payByBankData?.bankDetails?.length || 0

  const handleAddBankAccount = async () => {
    if (existingBankAccounts >= bankAccountLimit) {
      toast.error(`You can select a maximum of ${bankAccountLimit} bank accounts.`)
      return
    }
    try {
      // Set loading to true while API is being called
      setLoading(true)
      // Call the API to get the redirect URL
      const { data } = await addBankAccountMutate()

      if (data?.redirectUrl) {
        portalStore.openPortal(() => <AddBankAccount redirectUrl={data.redirectUrl} />, 'innerModal')
      } else {
        toast.error('Unable to fetch redirect URL. Please try again.')
      }
    } catch (error) {
      console.error('Error fetching redirect URL:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box className='select-bank-account'>
      <FormControl fullWidth>
        <Grid className='inputWrap'>
          <FormLabel className='label'>
            Redeem Your Amount{' '}
            <span className='textAmount' style={{ cursor: 'pointer' }}>
              {` ( Max  Amount : ${userDetails?.userWallet?.scCoin?.wsc || '0.00'} )`}
            </span>
          </FormLabel>
          <br />
          <Grid className='input-grp max-input-wrap'>
            <OutlinedInput
              id='outlined-basic'
              label=''
              onKeyDown={preventInvalidCharacters}
              min='1'
              className='no-spinners'
              variant='outlined'
              placeholder='Amount'
              {...register('amount')}
              name='amount'
              type='text'
            />
            <Button className='inner-btn' onClick={setBankAmountValue}>
              Max
            </Button>
          </Grid>
          {errors?.amount && (
            <p style={{ paddingTop: '15px' }} className={classes.inputError}>
              {errors?.amount?.message}
            </p>
          )}
        </Grid>
        <BankAccountList
          payByBankData={payByBankData}
          isPayByBankDataLoading={isPayByBankDataLoading}
          classes={classes}
          errors={errors}
          refetchBankAccounts={refetch}
          control={control}
          bankAccountLimit={bankAccountLimit}
          existingBankAccounts={existingBankAccounts}
        />
        <Grid className='add-bank-account-wrap'>
          <Button
            type='button'
            className='bank-account-btn'
            onClick={handleAddBankAccount}
            disabled={existingBankAccounts >= bankAccountLimit}
          >
            {loading ? (
              <CircularProgress size={20} />
            ) : (
              <>
                <AddCircleOutlineIcon /> Add Bank Account
              </>
            )}
          </Button>
        </Grid>
      </FormControl>

      <Grid className='btn-wrap'>
        <Button variant='contained' className='btn btn-secondary' disabled={isDisable} onClick={handleBack}>
          <span>Back</span>
        </Button>
        <Button variant='contained' className='btn btn-primary' type='submit'>
          <span>Redeem</span>
        </Button>
      </Grid>
    </Box>
  )
}

export default BankRedeemForm
