import React, { useState, useEffect } from 'react'
import useStyles from '../Header.styles'
import '../../../../src/App.css'
import { Button, Grid, Tooltip, Typography } from '@mui/material'
import { toast } from 'react-hot-toast'
import { whitePlay } from '../../../components/ui-kit/icons/svg'
import { CasinoCard, Heart, HeartFill } from '../../../components/ui-kit/icons/utils'
import TournamentLogo from '../../../components/ui-kit/icons/png/tournament-logo.png'
import { useFavToggleMutation } from '../../../reactQuery/casinoQuery'
import { getItem, getLoginToken } from '../../../utils/storageUtils'
import { useUserStore } from '../../../store/useUserSlice'
// import { motion } from 'framer-motion'
import { useLocation, useParams } from 'react-router-dom'
import { updateSDKPageVisit } from '../../../utils/optimoveHelper'
import ScrollToTop from '../../../components/ScrollToTop'
import { useSelectedProviderStore, useSearchDialogStore, useSubCategoryOnLoadStore, useGamesStore } from '../../../store/store'
import { CasinoQuery } from '../../../reactQuery'
// import useSubCategory from '../../../pages/Lobby/hooks/useSubCategory'
import { usePragmaticJackpotStore } from '../../../store/usePragmaticJackpot'
import LazyImage from '../../../utils/lazyImage'
import { formatPriceWithCommas } from '../../../utils/helpers'
import PragmaticJackpotSCLogo from '../../../components/ui-kit/icons/svg/pragmatic-sc.svg'
import PragmaticJackpotGCLogo from '../../../components/ui-kit/icons/svg/pragmatic-gc.svg'

const SearchedGamesList = ({
  data,
  gameDataCount,
  limit,
  pageNo,
  handleLoadMore,
  refetch,
  handlePlayNow,
  subCategories,
  isloading
}) => {
  const classes = useStyles()
  const location = useLocation()
  const { subCategoryName } = useParams()
  const coinType = getItem('coin')
  const currentUrl = window.location.origin + location.pathname + location.search
  if (import.meta.env.VITE_NODE_ENV === 'production') {
    updateSDKPageVisit(currentUrl, subCategoryName)
  }
  const subCategoryState = useSubCategoryOnLoadStore((state) => state)
  const auth = useUserStore((state) => state)
  const selectedProviderStore = useSelectedProviderStore((state) => state)
  const { setFavBySearch } = useSearchDialogStore((state) => state)
  const [favorites, setFavorites] = useState({})
  const [isLoad, setIsLoad] = useState(false) // eslint-disable-line
  const { favoriteIds, setFavoriteIds } = useGamesStore() // eslint-disable-line
  const { pragmaticJackpotSc, pragmaticJackpotGc } = usePragmaticJackpotStore()

  useEffect(() => {
    const initialFavorites = {}
    data?.forEach((game) => {
      if (game?.FavoriteGames) {
        initialFavorites[game?.masterCasinoGameId?.toString()] = game?.FavoriteGames
      }
    })
    setFavorites(initialFavorites)
    setFavoriteIds(initialFavorites)
  }, [data])

  const mutationFavToggle = useFavToggleMutation({
    onSuccess: (res, variables) => {
      setFavorites((prevState) => ({
        ...prevState,
        [variables.gameId]: !prevState[variables.gameId] // Update favorite status for the toggled game
      }))

      setFavoriteIds((prevState) => ({
        ...prevState,
        [variables.gameId]: !prevState[variables.gameId] // Update favorite status for the toggled game
      }))

      if (selectedProviderStore.selectedProviderId !== '') {
        subcategoryListMutation.mutate({ masterCasinoProviderId: selectedProviderStore.selectedProviderId })
      } else {
        subcategoryListMutation.mutate()
      }
      setFavBySearch(true)
      setIsLoad(false)
      toast.success(variables?.request ? 'Added to Favorites' : 'Removed from Favorites')
    },
    onError: (error) => {
      setIsLoad(false)
      if (error?.response?.data?.errors.length > 0) {
        const { errors } = error.response.data
        errors.forEach((error) => {
          console.log(error)
        })
      }
    }
  })

  const toggleFavorite = (gameId, isFav) => {
    setIsLoad(true)
    mutationFavToggle.mutate({ request: !isFav, gameId })
  }
  /* success response of subcategoryListMutation */
  const successToggler = (data) => {
    subCategoryState.setSubCategories(data?.data?.data)
  }
  /* error response of subcategoryListMutation */
  const errorToggler = (error) => {
    // setGameDataLoading(false)
    console.log(error)
    // toast.error(error?.response?.data?.errors?.[0]?.description)
  }
  /* subcategoryListMutation api hit  */
  const subcategoryListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })
  return (
    <>
      <ScrollToTop />
      <>
        <Grid className='search-modal-scroll'>
          <Grid className='games-grid' initial='hidden' animate='visible'>
            <>
              <Grid className={classes.subcategoryListWrap} key='categoryGames'>
                {data?.map((game, idx) => {
                  const gameId = String(game?.masterCasinoGameId)

                  const isScPragmatic = coinType === 'SC' && pragmaticJackpotSc?.hasOwnProperty(gameId)
                  const isGcPragmatic = coinType === 'GC' && pragmaticJackpotGc?.hasOwnProperty(gameId)
                  const jackpotValue = isScPragmatic
                    ? pragmaticJackpotSc?.[gameId]
                    : isGcPragmatic
                    ? pragmaticJackpotGc?.[gameId]
                    : null
                  return (
                    <div
                      className='custom-col-2'
                      key={`${game?.masterCasinoGameId}_${idx}`}
                      // transition={{ delay: idx / 10 }}
                      // initial={{ y: 20, opacity: 0 }}
                      // animate={{ y: 0, opacity: 1 }}
                    >
                      <Grid key={`${game?.masterCasinoGameId}_1_${idx}`}>
                        <Grid>
                          <Tooltip
                            title={game?.gameInTournament ? 'TOURNAMENT' : ''}
                            arrow
                            disableHoverListener={!game?.gameInTournament}
                            placement='top-start' // You can adjust this if needed
                            componentsProps={{
                              tooltip: {
                                sx: {
                                  backgroundColor: '#FF3000 ',
                                  color: 'white',
                                  textAlign: 'center',
                                  fontWeight: '800',
                                  fontSize: '14px'
                                },
                                style: {
                                  maxWidth: 'none' // optional: ensures full text is visible
                                }
                              },
                              arrow: {
                                sx: {
                                  color: '#FF3000',
                                  position: 'absolute',
                                  left: '16px !important',
                                  transform: 'translate(0px, 0px) !important',
                                  marginTop: '-8px !important'
                                }
                              }
                            }}
                          >
                            <Grid className='casino-card'>
                              {(!!getLoginToken() || auth.isAuthenticate) && (
                                <Grid className='fav-icon'>
                                  <span
                                    onClick={
                                      !isloading
                                        ? () =>
                                            toggleFavorite(
                                              game?.masterCasinoGameId,
                                              favorites[game?.masterCasinoGameId?.toString()]
                                            )
                                        : undefined
                                    }
                                  >
                                    <img
                                      alt='heart'
                                      src={favorites[game?.masterCasinoGameId?.toString()] ? HeartFill : Heart}
                                      height='100%'
                                      width='100%'
                                    />
                                  </span>
                                </Grid>
                              )}
                              <Grid className='casino-card'>
                                <img
                                  src={game?.imageUrl || CasinoCard}
                                  alt='Casino'
                                  className='casinoGame-img'
                                  height='100%'
                                  width='100%'
                                />
                                <Grid
                                  className='casino-overlay'
                                  onClick={() => {
                                    handlePlayNow(game.masterCasinoGameId, game.name)
                                  }}
                                >
                                  <Typography
                                    variant='h6'
                                    sx={{ lineHeight: '20px', textAlign: 'center', padding: '0 10px' }}
                                  >
                                    <b>{game?.name}</b>
                                  </Typography>

                                  <img
                                    src={whitePlay}
                                    alt='Play'
                                    className={classes.playImg}
                                    height='100%'
                                    width='100%'
                                  />

                                  <b>Play Now </b>
                                </Grid>
                                {jackpotValue !== null && (
                                  <div className='prgamatic-jackpot-amount-wrapper'>
                                    <LazyImage
                                      src={coinType === 'SC' ? PragmaticJackpotSCLogo : PragmaticJackpotGCLogo}
                                      alt='prgamatic-jakcpot-logo'
                                    />
                                    <Typography
                                      style={{
                                        color: ` ${coinType === 'SC' ? '#00C80E' : '#FDB72E'}`,
                                        fontWeight: '700',
                                        fontSize: '10px'
                                      }}
                                    >
                                      {formatPriceWithCommas(jackpotValue)} {coinType}
                                    </Typography>
                                  </div>
                                )}
                                {game?.gameInTournament && (
                                  <img src={TournamentLogo} alt='tournament-logo' className='tournamentLogo' />
                                )}
                              </Grid>
                            </Grid>
                          </Tooltip>
                        </Grid>
                      </Grid>
                    </div>
                  )
                })}
              </Grid>
              {gameDataCount === 0 && (
                <Grid className='no-data-content'>
                  <Typography sx={{ textAlign: 'center', color: '#fff' }}> No Games Found</Typography>
                </Grid>
              )}
            </>
          </Grid>
        </Grid>
        {/* {Math.ceil(gameDataCount / limit) > pageNo && (
          <Grid className={classes.loadMore}>
            <Button variant='contained' className='btn-gradient' onClick={() => handleLoadMore()}>
              <span> Load More</span>
            </Button>
          </Grid>
        )} */}
      </>
    </>
  )
}

export default SearchedGamesList
