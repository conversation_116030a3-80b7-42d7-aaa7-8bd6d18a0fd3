import { makeStyles } from '@mui/styles'

import { ButtonPrimary, ButtonSecondary } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  wrapper: {
    maxWidth: '1200px',
    margin: '0 auto',
    width: '100%',
    padding: theme.spacing(0.77),
    '&.game-play-header': {
      maxWidth: '100%',
      padding: theme.spacing(0, 1),
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(0)
      }
    }
  },

  header: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flexDirection: 'column !important',
    gap: theme.spacing(0.25),
    backgroundColor: theme.colors.sidebarBg,
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    MozBackdropFilter: 'blur(20px)',
    width: 'calc(100% - 250px)',
    position: 'fixed',
    right: '0',
    top: '0',
    zIndex: '10',
    [theme.breakpoints.down('lg')]: {
      width: '100%',
      padding: theme.spacing(0, 0.77)
    },
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(0, 0.2)
    },
    '& .wrapper': {
      padding: theme.spacing(0.77)
    },
    '& .header-left': {
      marginRight: 'auto',
      '& a': {
        display: 'flex',
        alignItems: 'center',
        '& img': {
          width: '60px',
          position: 'relative',
          top: theme.spacing(0.313),
          [theme.breakpoints.down('md')]: {
            width: '47px'
          }
        }
      },
      // [theme.breakpoints.up('lg')]: {
      //   display: 'none'
      // }

      '& .no-game-play': {
        display: 'none',
        [theme.breakpoints.down('lg')]: {
          display: 'block'
        }
      },
      '& .maintenance-wrap': {
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        [theme.breakpoints.down('lg')]: {
          display: 'none'
        },
        '& p': {
          fontSize: '1.125rem',
          fontWeight: '600',
          color: theme.colors.YellowishOrange
        }
      }
    },
    '& .header-content': {
      display: 'flex',
      justifyContent: 'flex-end',
      alignItems: 'center',
      position: 'relative',
      [theme.breakpoints.down('lg')]: {
        justifyContent: 'space-between'
      },

      '& .header-right': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(1),
        [theme.breakpoints.down(360)]: {
          gap: theme.spacing(0.313)
        },
        '& .profileBtn': {
          '& img': {
            cursor: 'pointer'
          },
          [theme.breakpoints.down('md')]: {
            display: 'none'
          }
        },
        '& .buyBtn': {
          margin: theme.spacing(0),
          '& button': {
            borderRadius: theme.spacing(7.8),
            padding: theme.spacing(0.375, 2),
            color: theme.colors.textBlack,
            fontWeight: theme.typography.fontWeightExtraBold,
            fontSize: theme.spacing(1),
            background: theme.colors.Yellowish,
            gap: theme.spacing(0.4),
            textTransform: 'uppercase',
            minWidth: '115px',
            // boxShadow: '0px 0.637px 1.401px 0px #EAB647, 0px 1.932px 4.25px 0px rgba(234, 182, 71, 0.04), 0px 5.106px 11.233px 0px rgba(234, 182, 71, 0.09), 0px 16px 35.2px 0px rgba(234, 182, 71, 0.30)',
            [theme.breakpoints.down('md')]: {
              minWidth: '75px',
              padding: theme.spacing(0.375, 0),
              fontSize: theme.spacing(0.75)
            },
            // [theme.breakpoints.down('md')]: {
            //   display: 'none',
            // },

            '&:hover': {
              '& img': {}
            }
          }
        }
      },
      '& .header-sc-gc-wrap': {
        background: theme.colors.hederScGcBg,
        borderRadius: '20px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        '& .header-sc-btn': {
          background: theme.colors.scGradient,
          display: 'flex',
          alignItems: 'center',
          padding: theme.spacing(0.125, 0.313),
          borderRadius: '30px',
          minWidth: '100px',
          boxShadow: theme.shadows[9],
          textDecoration: 'none',
          '&.disabled': {
            background: 'transparent',
            filter: 'grayscale(1)',
            opacity: '0.4',
            boxShadow: theme.shadows[0]
          },
          '& .MuiTypography-body1': {
            fontWeight: theme.typography.fontWeightBold,
            fontSize: theme.spacing(1)
          },
          '& img': {
            marginRight: theme.spacing(0.313)
          }
        },
        '& .header-gc-btn': {
          background: theme.colors.gcGradient,
          display: 'flex',
          alignItems: 'center',
          padding: theme.spacing(0.125, 0.313),
          borderRadius: '30px',
          minWidth: '100px',
          textDecoration: 'none',
          '&.disabled': {
            background: 'transparent',
            filter: 'grayscale(1)',
            opacity: '0.4',
            boxShadow: theme.shadows[0]
          },
          '& .MuiTypography-body1': {
            fontWeight: theme.typography.fontWeightBold,
            fontSize: theme.spacing(1)
          },
          '& img': {
            marginRight: theme.spacing(0.313)
          }
        }
      }
    },
    '& .login': {
      ...ButtonSecondary(theme),
      '&:hover': {
        backgroundColor: 'transparent',
        border: `1px solid ${theme.colors.YellowishOrange}`
      }
    },

    '& .signup': {
      ...ButtonPrimary(theme),
      '&:hover': {
        background: theme.colors.YellowishOrange
      }
    },
    '& .header-search': {
      padding: '0',
      minWidth: 'auto',
      color: theme.colors.YellowishOrange,
      border: 'none',
      '&:hover': {
        border: 'none'
      }
      // [theme.breakpoints.down('md')]: {
      //   display: 'none',
      // },
    },
    '& .info-header': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: '#333234',
      width: '100%',
      padding: '0rem 2.5rem 0rem 0.5rem',
      '& p': {
        color: theme.colors.YellowishOrange,
        fontWeight: '500',
        lineHeight: '1',
        [theme.breakpoints.down('md')]: {
          fontSize: '0.75rem'
        }
      },
      '& span': {
        color: theme.colors.textWhite,
        minWidth: '57px'
      },
      '& .close-icon': {
        top: '5px',
        width: '0.75rem',
        right: '1rem',
        cursor: 'pointer'
      }
    }
  },
  searchModalWrap: {
    backdropFilter: 'blur(20px)',

    '& .MuiPaper-root': {
      minWidth: '800px',
      borderRadius: '0.313rem',
      position: 'absolute',
      top: '0',
      // padding: "0.313rem",

      [theme.breakpoints.down('md')]: {
        minWidth: '75px'
        // minWidth: '90%'
      },

      '& .search-modal-input': {
        '& .MuiInputAdornment-root': {
          marginLeft: '0.625rem'
        },

        '& .MuiFormControl-root': {
          width: '100%',

          '& .MuiInputBase-root': {
            color: theme.colors.textWhite,
            minHeight: '3.5rem',
            background: theme.colors.sidebarBg,
            width: '100%',
            borderRadius: '0.625rem',
            border: '2px solid #D6A300',

            '& .MuiSvgIcon-root': {
              color: theme.colors.textWhite
            },

            '&:after, &:before': {
              display: 'none'
            }
          }
        }
      },

      '& .search-modal-scroll': {
        maxHeight: 'calc(100vh - 300px)',
        overflowY: 'auto'
      }
    },

    '& .search-modal-bottom-section': {
      display: 'none',
      overflowY: 'hidden',

      '&.active': {
        display: 'block'
      }
    }
  },
  headerTabs: {
    [theme.breakpoints.down('sm')]: {
      display: 'none'
    },
    display: 'flex',
    border: `1px solid ${theme.colors.Pastel}`,
    borderRadius: theme.spacing(3.5),
    padding: '1px',
    position: 'relative',
    '& .hoverTooltip': {
      position: 'absolute',
      top: '50px',
      '& .hoverParent': {
        gap: '10px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        background: theme.colors.Pastel,
        padding: theme.spacing(0.5, 1),
        borderRadius: '1rem',
        minHeight: '85px',
        '& .hoverText': {
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
          flexGrow: '1',
          flexDirection: 'column',
          '& .hoverText1': {
            fontSize: '0.9rem',
            color: theme.colors.Yellowish,
            fontWeight: 'bold'
          },
          '& .hoverText2': {
            fontSize: '0.8rem',
            color: theme.colors.textWhite,
            fontWeight: 'bold'
          }
        }
      }
    },
    '& .MuiTabs-root': {
      minHeight: 'auto',
      '& .MuiTab-iconWrapper': {
        display: 'flex',
        marginRight: '0',
        '& p': {
          fontWeight: '500',
          lineHeight: 'normal'
        },
        '& img': {
          marginRight: theme.spacing(0.5),
          width: '25px'
        }
      }
    },
    '& .MuiTabs-flexContainer': {
      gap: theme.spacing(0.2)
    },
    '& .MuiTabs-scroller': {
      '& button': {
        zIndex: '1',
        color: theme.colors.textWhite,
        borderRadius: theme.spacing(3.5),
        minHeight: '2.5rem',
        // padding: '0',
        lineHeight: '12px',
        flexDirection: 'column',
        alignItems: 'flex-start',
        opacity: '0.5',
        fontWeight: 'bold',
        fontSize: theme.spacing(0.875),
        padding: theme.spacing(0.2, 1),
        '& b': {
          color: theme.colors.Yellowish,
          fontSize: theme.spacing(0.875)
        },
        '&:nth-child(2)': {
          flexDirection: 'row',
          alignItems: 'center',
          fontWeight: 'bold',
          color: theme.colors.Yellowish,
          fontSize: theme.spacing(0.875)
        },
        '&.Mui-selected': {
          opacity: '1'
        }
      },
      '& .MuiTabs-indicator': {
        height: '100%',
        backgroundColor: theme.colors.Pastel,
        borderRadius: theme.spacing(3.5)
      }
    }
  },
  lobbySearchWrap: {
    position: 'relative',
    [theme.breakpoints.down('sm')]: {
      width: theme.spacing(1)
    },
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(0.625),
      left: theme.spacing(1.25),
      [theme.breakpoints.down('sm')]: {
        left: '0'
      }
    },
    '& .MuiTextField-root': {
      width: '100%',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        [theme.breakpoints.down('sm')]: {
          border: 'none'
        },
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        // "&::placeholder": {
        //   color: 'red',
        // },
        '&::-ms-input-placeholder': {
          color: 'red'
        },
        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(2.18)
        }
      }
    }
  },
  subcategoryListWrap: {
    display: 'grid',
    paddingTop: '10px',
    alignItems: 'stretch',
    gridTemplateColumns: 'repeat(5, 1fr)',
    gap: '0.75rem',
    // overflowY: "auto",
    maxHeight: '30rem',

    [theme.breakpoints.down('lg')]: {
      gridTemplateColumns: 'repeat(4, 1fr)',
      marginBottom: '45px'
    },

    [theme.breakpoints.down('md')]: {
      gridTemplateColumns: 'repeat(3, 1fr)'
    },

    [theme.breakpoints.down('sm')]: {
      gridTemplateColumns: 'repeat(2, 1fr)'
    },

    '& .casino-card': {
      position: 'relative',
      transition: 'all 200ms ease-in-out',
      lineHeight: '0',
      // maxWidth: '119px',
      // maxHeight: '168px',

      '& .fav-icon': {
        width: '20px',
        height: '20px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: '8',

        '& img': {
          width: '20px',
          height: '20px',
          objectFit: 'contain',
          objectPosition: 'center'
        },

        '&:hover': {
          // backgroundColor: theme.colors.textWhite,
          cursor: 'pointer'
        }
      },

      '& .casinoGame-img': {
        width: '100%',
        aspectRatio: '2/3',
        borderRadius: '8px',
        height: '100%',

        '&:hover': {
          backgroundColor: theme.colors.textWhite,
          cursor: 'pointer'
        }
      },

      '& .casino-img': {
        width: '100%',
        aspectRatio: '1'
      },

      '&:hover': {
        transform: 'translateY(-0.25rem)',

        '& .casino-overlay': {
          display: 'flex',
          opacity: '1',
          transition: 'all 300ms ease-in-out'
        }
      },

      '& .casino-overlay': {
        position: 'absolute',
        opacity: '0',
        display: 'flex',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        margin: '0 auto',
        inset: '0',
        flexDirection: 'column',
        background: 'rgb(255, 84, 37)',
        // background: 'linear-gradient(180deg, rgba(255,84,37,0.9) 0%, rgba(251,162,83,0.9) 100%)',
        cursor: 'pointer',
        transition: 'all 200ms ease-in-out',
        borderRadius: '8px',
        color: theme.colors.textWhite,
        '& a': {
          color: theme.colors.textWhite,
          textDecoration: 'none'
        },
        '& h6': {
          color: theme.colors.textWhite
          // wordBreak: 'break-all'
        }
      },
      '& .tournamentLogo': {
        position: 'absolute',
        left: '2px',
        top: '5px',
        width: '30px',
        height: '30px'
      },
      '& .prgamatic-jackpot-amount-wrapper': {
        position: 'absolute',
        top: '12px',
        left: '47%',
        display: 'flex',
        justifyContent: 'center',
        gap: '4px',
        alignItems: 'center',
        background: '#000000B2',
        borderRadius: '17px',
        whiteSpace: 'nowrap',
        transform: 'translate(-50%, 0)',
        padding: '1px 5px'
      }
    }
  },

  playImg: {
    width: '60px',
    margin: '20px 0',
    height: 'auto',
    [theme.breakpoints.down('xl')]: {
      width: '40px',
      margin: '5px 0'
    }
  },

  loadMore: {
    width: '100%',
    textAlign: 'center',
    margin: theme.spacing(1, 0),

    '& button': {
      ...ButtonPrimary(theme),

      '&:hover': {
        ...ButtonPrimary(theme)
      }
    }
  }
}))
