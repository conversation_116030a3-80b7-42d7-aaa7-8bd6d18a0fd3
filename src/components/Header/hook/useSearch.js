import { debounce } from 'lodash'
import { useCallback, useState, useEffect } from 'react'

import { CasinoQuery } from '../../../reactQuery'

function useSearch () {
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [limit, setLimit] = useState(20)
  const [pageNo, setPageNo] = useState(1)
  const [gameData, setGameData] = useState([])
  const [gameDataCount, setGameDataCount] = useState()

  const debouncedSetSearch = useCallback(
    debounce((searchQuery) => {
      const trimmedQuery = searchQuery.trim()
      setDebouncedSearch(trimmedQuery)
    }, 300),
    []
  )

  useEffect(() => {
    debouncedSetSearch(search)
  }, [search, debouncedSetSearch])

  const successToggler = (data) => {
    setGameData(data?.data?.data?.rows)
    setGameDataCount(data?.data?.data?.count)
  }
  const errorToggler = (error) => {
    console.log(error)
    // toast.error(error?.response?.data?.errors?.[0]?.description)
  }
  const gameListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })

  // Reset pageNo to 1 whenever debouncedSearch changes, but don't trigger the API call here.
  useEffect(() => {
    setPageNo(1)
  }, [debouncedSearch])

  // Trigger the API call only when both debouncedSearch and pageNo are updated.
  useEffect(() => {
    if (debouncedSearch !== '') {
      const filterData = { subCategoryId: 0, search: debouncedSearch, limit, page: pageNo }
      gameListMutation.mutate(filterData)
    }
  }, [debouncedSearch, pageNo]) // Keep both as dependencies to ensure they are up-to-date.

  const handleSearch = (event) => {
    setSearch(event.target.value)
  }
  const handleLoadMore = () => {
    if (Math.ceil(gameDataCount / limit) > pageNo) {
      setPageNo(prevPageNo => prevPageNo + 1)
    }
  }

  return {
    handleSearch,
    data: gameData,
    handleLoadMore,
    gameDataCount,
    limit,
    pageNo,
    setLimit
  }
}

export default useSearch
