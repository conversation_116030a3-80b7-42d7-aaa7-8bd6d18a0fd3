import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  mobileMenuList: {
    display: 'none',
    [theme.breakpoints.down('sm')]: {
      backgroundColor: '#0C2320',
      border: `1px solid ${theme.colors.Pastel}`,
      display: 'flex',
      padding: '1px',
      alignItems: 'center',
      minWidth: '140px',
      position: 'relative',
      borderRadius: '3.5rem',
      transition: 'height 0s',
      gap: '1rem',
      // position: "absolute",
      // right: "2px",
      // top: "-20px",
      height: '37px',
      '& .arrowIcon': {
        position: 'absolute',
        right: '3px',
        top: '5px'
      },
      '&.hide': {
        borderRadius: '1rem',
        height: '90px',
        transition: 'height 0s',
        '& li:nth-child(2)': {
          display: 'flex'
        }
      },
      '& ul': {
        padding: '0',
        width: '100%',
        '& li': {
          display: 'flex',
          flexDirection: 'column',
          borderRadius: '3.5rem',
          padding: '0rem 1rem',
          alignItems: 'flex-start',
          height: '45px',
          minWidth: '150px',
          justifyContent: 'center',
          '&:hover': {
            background: theme.colors.Pastel
          },
          '& img': {
            width: '20px'
          },
          '& .primaryText': {
            color: theme.colors.Yellowish,
            fontSize: '14.5px',
            fontWeight: 'bold'
          },
          '& .secondaryText': {
            color: theme.colors.textWhite,
            fontSize: '14.5px',
            fontWeight: 'bold',
            '& span': {
              color: 'rgb(255 255 255 / 50%)'
            }
          }
        }
      },
      '& li:nth-child(2)': {
        display: 'none'
      }
    }
  }
}))
