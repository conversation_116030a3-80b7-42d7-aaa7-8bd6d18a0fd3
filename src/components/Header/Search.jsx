// import useSubCategory from '../../pages/Lobby/hooks/useSubCategory'
import Dialog from '@mui/material/Dialog'
import { Grid, FormControl, Input, InputAdornment } from '@mui/material'
import DialogContent from '@mui/material/DialogContent'
import SearchIcon from '@mui/icons-material/Search'
import useStyles from './Header.styles'
import { useGamesStore, usePortalStore, useSearchDialogStore, useSearchInputStore, useUserStore } from '../../store/store'
import useSearch from './hook/useSearch'
import UserNameModal from '../Modal/Signup/UserNameModal'
import Signin from '../Modal/Signin'
import { getItem, getLoginToken } from '../../utils/storageUtils'
import { useNavigate } from 'react-router-dom'
import SearchedGamesList from './SearchedGamesList/SearchedGamesList'
import MobileVerification from '../../pages/MobileVerification'
import toast from 'react-hot-toast'
import { useGetProfileMutation } from '../../reactQuery'
import { useState } from 'react'
import { PlayerRoutes } from '../../routes'
const Search = () => {
  const { handleSearch, data, gamesLoading, handleLoadMore, gameDataCount, limit, pageNo } = useSearch()

  const { refetch, isLoading, subCategories } = useGamesStore()
  const auth = useUserStore((state) => state)
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const setSearchDialog = useSearchDialogStore((state) => state.setSearchDialog)
  const searchDialog = useSearchDialogStore((state) => state.isDialog)

  const inputText = useSearchInputStore((state) => state.searchQuery)
  const setInputText = useSearchInputStore((state) => state.setSearchText)
  const [gameId, setGameId] = useState(null)
  const [name, setName] = useState('')
  const classes = useStyles()
  const coinType = getItem('coin')

  const handleInputChange = (event) => {
    setInputText(event.target.value)
  }
  const gameType = 'All Games'

  const handlePlayNow = (masterCasinoGameId, name) => {
    if (!!getLoginToken() || auth.isAuthenticate) {
      if (auth.userDetails.username) {
        if (coinType === 'SC' && auth?.userDetails?.userWallet?.totalScCoin > 0)
          navigate(`/game-play/${masterCasinoGameId}`, { state: { name, gameType } })
        else if (coinType === 'GC' && auth?.userDetails?.userWallet?.gcCoin > 0)
          navigate(`/game-play/${masterCasinoGameId}`, { state: { name, gameType } })
        else {
          toast.error('Please make a purchase!')
          navigate(PlayerRoutes.Store)
        }
        setSearchDialog(false)
        setInputText('')
        // navigate(`/game-play/${masterCasinoGameId}`)
      } else {
        portalStore.openPortal(() => <UserNameModal />, 'loginModal')
      }
    } else {
      portalStore.openPortal(() => <Signin />, 'loginModal')
    }
  }
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (!res?.data?.data?.phoneVerified) {
        portalStore.openPortal(
          () => <MobileVerification calledFor='gamePlay' handlePlayNow={() => handlePlayNow(gameId, name)} />,
          'innerModal'
        )
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })
  const handlePhoneVerification = (gameId, name) => {
    if (coinType === 'SC' && !auth?.userDetails?.phoneVerified && (!!getLoginToken() || auth.isAuthenticate)) {
      setGameId(gameId)
      setName(name)
      getProfileMutation.mutate()
    } else {
      handlePlayNow(gameId, name)
    }
  }

  return (
    <Dialog
      open={searchDialog}
      onClose={() => {
        setSearchDialog(false)
        setInputText('')
      }}
      aria-labelledby='alert-dialog-title'
      aria-describedby='alert-dialog-description'
      // className='search-modal-wrap'
      className={classes.searchModalWrap}
    >
      <Grid className='search-modal-input'>
        <FormControl variant='standard'>
          <Input
            id='standard-adornment-amount'
            onChange={(event) => {
              handleInputChange(event)
              handleSearch(event)
            }}
            autoFocus
            placeholder='Search Games Here...!'
            startAdornment={
              <InputAdornment position='start'>
                <SearchIcon />
              </InputAdornment>
            }
            autoComplete='off'
          />
        </FormControl>
      </Grid>
      <DialogContent className={`search-modal-bottom-section ${inputText.length > 0 ? 'active' : ''}`}>
        <SearchedGamesList
          data={data}
          refetch={refetch}
          handlePlayNow={handlePhoneVerification}
          gamesLoading={gamesLoading}
          gameDataCount={gameDataCount}
          handleLoadMore={handleLoadMore}
          limit={limit}
          pageNo={pageNo}
          isloading={isLoading}
          subCategories={subCategories}
        />
      </DialogContent>
    </Dialog>
  )
}

export default Search
