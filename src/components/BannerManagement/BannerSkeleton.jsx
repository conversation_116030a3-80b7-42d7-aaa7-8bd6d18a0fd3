import React from 'react'
import { Grid, Box } from '@mui/material'
import useGetDeviceType from '../../utils/useGetDeviceType'

const BannerSkeleton = () => {
  const { isMobile } = useGetDeviceType()

  return (
    <Grid
      sx={{
        position: 'relative',
        width: '100%',
        aspectRatio: isMobile ? '375/161' : '15/4',
        borderRadius: 1,
        overflow: 'hidden',
        backgroundColor: '#333234'
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `
            linear-gradient(
              110deg,
              transparent 0%,
              transparent 40%,
              rgba(255, 255, 255, 0.08) 50%,
              rgba(255, 255, 255, 0.12) 55%,
              rgba(255, 255, 255, 0.08) 60%,
              transparent 70%,
              transparent 100%
            )
          `,
          backgroundSize: '200% 100%',
          animation: 'youtubeShimmer 2s ease-in-out infinite',
          '@keyframes youtubeShimmer': {
            '0%': {
              backgroundPosition: '-200% 0'
            },
            '100%': {
              backgroundPosition: '200% 0'
            }
          }
        }}
      />

      {/* Secondary shimmer for more depth */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `
            radial-gradient(
              circle at 30% 40%,
              rgba(255, 255, 255, 0.05) 0%,
              transparent 50%
            ),
            radial-gradient(
              circle at 70% 60%,
              rgba(255, 255, 255, 0.03) 0%,
              transparent 50%
            )
          `,
          animation: 'pulse 3s ease-in-out infinite alternate',
          '@keyframes pulse': {
            '0%': {
              opacity: 0.3
            },
            '100%': {
              opacity: 0.7
            }
          }
        }}
      />

    
    </Grid>
  )
}

export default BannerSkeleton
