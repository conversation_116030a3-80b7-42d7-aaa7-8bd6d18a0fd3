import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const ScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });// More reliable way to scroll to the top
  }, [location]); // Dependency on location changes

  return null; // This component doesn't render anything
};

export default ScrollToTop;
