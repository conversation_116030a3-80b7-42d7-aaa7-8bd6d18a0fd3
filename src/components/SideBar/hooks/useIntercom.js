import { useEffect } from "react";

const useIntercom = (data) => {
  useEffect(() => {
    const loadIntercom = () => {
      if (window.Intercom) {
        // Initialize Intercom if it's already available
        window.Intercom('reattach_activator');
        window.Intercom('update', window.intercomSettings);
      } else {
        // If Intercom isn't available, set up the Intercom function
        window.Intercom = function () {
          window.Intercom.q.push(arguments);
        };
        window.Intercom.q = [];
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = `https://widget.intercom.io/widget/truve12n`; 
        script.async = true;
        document.head.appendChild(script);

        script.onload = () => {
          window.Intercom('boot', window.intercomSettings);
        };
      }
    };

   
    window.intercomSettings = {
      app_id: 'truve12n',
      alignment: 'left',
      horizontal_padding: '20',
      vertical_padding: '50',
      hide_default_launcher: data,
    };

    loadIntercom();

    
    return () => {
      if (window.Intercom) {
        window.Intercom('shutdown');
      }
    };
  }, []);
};

export default useIntercom;
