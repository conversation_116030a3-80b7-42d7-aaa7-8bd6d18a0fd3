import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  mobNavbar: {
    position: 'fixed',
    width: '100%',
    bottom: '0',
    display: 'none',
    zIndex: '9',
    backgroundColor: 'rgba(41, 57, 55, 0.8)',
    backdropFilter: 'blur(20px)',
    gap: '10px',
    [theme.breakpoints.down('lg')]: {
      display: 'flex',
      padding: theme.spacing(0.5, 1.4),
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    '& .mob-toggle-wrap': {
      flexGrow: '1',
      '& a': {
        display: 'flex',
        alignItems: 'center',
        fontSize: theme.spacing(0.8),
        color: theme.colors.textWhite,
        fontWeight: '700',
        textDecoration: 'none',
        gap: theme.spacing(0.2),
        flexDirection: 'column',
        padding: '3px 0',
        borderRadius: '8px',
        '& .active': {
          background: theme.colors.YellowishOrange
        },
        '& img': {
          width: theme.spacing(1),
          height: theme.spacing(1)
        },
        '&.active': {
          background: theme.colors.YellowishOrange,
          color: theme.colors.textBlack,
          '& .image-active': {
            display: 'block'
          },
          '& .image': {
            display: 'none'
          }
        },
        '& .image-active': {
          display: 'none'
        }
      }
    }
  }
}))
