import { Link, Grid } from '@mui/material'
import React from 'react'
import { useUserStore } from '../../store/useUserSlice'
import {
  menuIcon,
  homeIcon,
  promotionIcon,
  mobprofileIcon,
  menuIconActive,
  homeIconActive,
  promotionIconActive,
  mobprofileIconActive,
  buyIcon,
  buyActiveIcon
} from '../ui-kit/icons/svg'
import useStyles from '../../components/MobNavbar/MobNavbar.styles'
import { useLocation, useNavigate } from 'react-router-dom'
import { PlayerRoutes } from '../../routes'

const MobNavbar = ({ isMobNav, setIsMobNav }) => {
  const classes = useStyles()
  const navigate = useNavigate()
  const location = useLocation()
  const auth = useUserStore((state) => state)

  const openSideBar = (e) => {
    e.preventDefault()
    setIsMobNav(!isMobNav)
  }

  const handleHome = () => {
    setIsMobNav(false)
    navigate(PlayerRoutes.Lobby)
  }
  const handleBuy = () => {
    setIsMobNav(false)
    navigate(PlayerRoutes.Store)
  }
  const handlePromotion = () => {
    setIsMobNav(false)
    navigate(PlayerRoutes.PromotionsPage)
  }

  const handleProfile = () => {
    setIsMobNav(false)
    navigate(PlayerRoutes.Account)
  }

  const isLobbySelected = location?.pathname === '/lobby' || location?.pathname === '/'
  const isPromotionsSelected = location?.pathname === '/promotions-page'
  const isProfileSelected = location?.pathname === '/user/account-details'
  const isBuySelected = location?.pathname === '/user/store'
  return (
    <Grid className={classes.mobNavbar}>
      {!location.pathname.includes('/game-play') && (
        <Grid className='mob-toggle-wrap'>
          <Link className={`${isMobNav ? 'active' : ''}`} to='#' onClick={openSideBar}>
            <img src={menuIcon} alt='Menu icon' className='image' />
            <img src={menuIconActive} alt='Active menu icon' className='image-active' />
            Menu
          </Link>
        </Grid>
      )}

      {auth?.isAuthenticate && (
        <Grid className='mob-toggle-wrap'>
          <Link className={`${isBuySelected && !isMobNav ? 'active' : ''}`} to='#' onClick={handleBuy}>
            <img src={buyIcon} alt='Buy Icon' className='image' />
            <img src={buyActiveIcon} alt='Active buy icon' className='image-active' />
            Buy
          </Link>
        </Grid>
      )}
      <Grid className='mob-toggle-wrap'>
        <Link className={`${isLobbySelected && !isMobNav ? 'active' : ''}`} to='#' onClick={handleHome}>
          <img src={homeIcon} alt='Home icon' className='image' />
          <img src={homeIconActive} alt='Active home icon' className='image-active' />
          Home
        </Link>
      </Grid>
      <Grid className='mob-toggle-wrap'>
        <Link className={`${isPromotionsSelected && !isMobNav ? 'active' : ''}`} to='#' onClick={handlePromotion}>
          <img src={promotionIcon} alt='Promotion icon' className='image' />
          <img src={promotionIconActive} alt='Active promotion icon' className='image-active' />
          Promotion
        </Link>
      </Grid>
      {auth?.isAuthenticate && (
        <Grid className='mob-toggle-wrap'>
          <Link className={`${isProfileSelected && !isMobNav ? 'active' : ''}`} onClick={handleProfile}>
            <img src={mobprofileIcon} alt='Header User' className='image' />
            <img src={mobprofileIconActive} alt='Header User' className='image-active' />
            Profile
          </Link>
        </Grid>
      )}
    </Grid>
  )
}

export default MobNavbar
