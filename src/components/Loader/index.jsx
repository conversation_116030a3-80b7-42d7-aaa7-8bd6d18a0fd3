import React from 'react'
import { Loading } from '../ui-kit/icons/images'
import { Grid } from '@mui/material'

const Loader = () => {
  return (
    <div id="site-loader" className="loader-wrapper">
      <div className="loader">
        <Grid className='load-more'>
          {/* <video id="brand-logo" height="200px" autoPlay loop muted playsInline>
            <source
              src="src/components/ui-kit/icons/brand/Loading.webm"
              type="video/webm"
            />
            Your browser does not support the video tag.
          </video> */}
          <img id="brand-logo" src={Loading} alt="Brand Logo" />
        </Grid>
      </div>
    </div>
  )
}

export default Loader
