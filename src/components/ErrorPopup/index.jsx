import React from 'react'
import {
  Grid,
  IconButton,
  Typography,
  DialogContent
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import  VerificationFailed  from '../ui-kit/icons/svg/verified-failed.svg'

const ErrorPopup = ({ message }) => {
  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }

  return (
    <>
      <Grid className='inner-modal-header'>
        <Typography variant='h4'> Failed </Typography>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
      <DialogContent className='payment-status-modal'>
        <Grid className='payment-status-content'>
          <img src={VerificationFailed} alt='Success' className='payment-status-icon' />
          <Typography>{message}</Typography>
        </Grid>
      </DialogContent>
    </>
  )
}

export default ErrorPopup
