import React from 'react'
import ContentWrapper from './dynamicContent.styles'
import useDynamicGamepageStore from '../../../store/useDynamicGamepageStore'
import parse from 'html-react-parser'

const DynamicContent = () => {
  const dynamicGamePageData = useDynamicGamepageStore((state) => state.dynamicGamePageData)

  const htmlContent = dynamicGamePageData?.dynamicGameDetails?.htmlContent

  return (
    <ContentWrapper className='maxWidthContainer'>
      {htmlContent ? (
        parse(htmlContent)
      ) : (
        <center>
          {' '}
          <p>
            <b>No content available.</b>
          </p>
        </center>
      )}
    </ContentWrapper>
  )
}

export default DynamicContent
