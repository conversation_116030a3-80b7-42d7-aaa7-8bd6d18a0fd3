import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  SeoDynamicGamePage: {
    paddingTop: '7rem',
    '& .fullWidthContainer': {
      // background: '#1A1A1A',
      margin: '2rem 0',
      [theme.breakpoints.down('md')]: {
        margin: '1rem 0'
      }
    },
    '& .maxWidthContainer': {
      margin: '0 auto',
      padding: '0 2rem',
      maxWidth: '73rem !important',
      [theme.breakpoints.down('md')]: {
        padding: '0 1rem'
      }
    },
    '& .MuiTypography-h1': {
      fontSize: '70px',
      fontWeight: '700',
      textAlign: 'center',
      maxWidth: '66rem',
      margin: '0 auto',
      '& span': {
        color: '#FDB72E'
      },
      [theme.breakpoints.down('lg')]: {
        fontSize: '45px'
      },
      [theme.breakpoints.down('md')]: {
        fontSize: '2rem'
      }
    },
    '& .MuiTypography-h2': {
      fontSize: '28px',
      fontWeight: '700',
      '& span': {
        color: '#FDB72E'
      }
    },
    '& .MuiTypography-body1': {
      fontSize: '20px',
      fontWeight: '500',
      '& span': {
        color: '#FDB72E'
      },
      '& a': {
        color: '#23A8FC'
      },
      [theme.breakpoints.down('md')]: {
        fontSize: '18px'
      }
    },
    '& .MuiTypography-h3': {
      fontSize: '22px',
      fontWeight: '700'
    },
    '& .yellow-text': {
      color: '#FDB72E'
    },
    '& .pointer-h3': {
      '&::before': {
        position: 'absolute',
        content: "''",
        height: '0.75rem',
        width: '0.75rem',
        background: '#fdb72e',
        borderRadius: '100%',
        top: '6px',
        left: '-30px',
        [theme.breakpoints.down('md')]: {
          height: '0.5rem',
          width: '0.5rem',
          left: '-15px',
          top: '5px'
        }
      }
    },
    '& .pointer-p': {
      '&::before': {
        position: 'absolute',
        content: "''",
        height: '0.25rem',
        width: '0.25rem',
        background: '#fdb72e',
        borderRadius: '100%',
        top: '12px',
        left: '-15px',
        [theme.breakpoints.down('md')]: {
          left: '-10px'
        }
      }
    },
    '& .more-games': {
      margin: '3rem auto',
      '& h2': {
        textAlign: 'center',
        fontSize: '2.5rem',
        marginBottom: '1.5rem'
      },

      '& .swiper-button-prev': {
        fontWeight: 800,
        color: '#FDB72E',
        position: 'absolute',
        top: '64%',
        transform: 'translateY(-50%)',
        zIndex: 2,
        left: '1rem',
        [theme.breakpoints.down('md')]: {
          '&:after': {
            fontSize: '1.5rem'
          }
        },
        [theme.breakpoints.down('sm')]: {
          top: '75%'
        }
      },
      '& .swiper-button-next': {
        fontWeight: 800,
        color: '#FDB72E',
        position: 'absolute',
        right: '1rem',
        top: '64%',
        transform: 'translateY(-50%)',
        zIndex: 2,
        [theme.breakpoints.down('md')]: {
          // top: '75%',
          '&:after': {
            fontSize: '1.5rem'
          }
        },
        [theme.breakpoints.down('sm')]: {
          top: '75%'
        }
      },
      '& .swiper-button-disabled': {
        color: '#585858 !important'
      },
      '& .swiper-slide': {
        filter: 'brightness(0.4)'
      },

      '& .swiper-slide-prev': {
        filter: 'brightness(0.7)'
      },
      '& .swiper-slide-active': {
        filter: 'brightness(1)'
      },
      '& .swiper-slide-next': {
        filter: 'brightness(0.7)'
      }
    }
  }
}))
