import { Box, styled } from '@mui/material'

const TipWrapper = styled(Box)(({ theme }) => ({
  '& .image-gallery': {
    '& .image-wrap ': {
      display: 'grid',
      gridTemplateColumns: 'repeat(5, 1fr)',
      gap: '1rem',
      flexWrap: 'wrap',
      padding: '2rem 0',
      [theme.breakpoints.down('lg')]: {
        gridTemplateColumns: 'repeat(4, 1fr)'
      },
      [theme.breakpoints.down('md')]: {
        gridTemplateColumns: 'repeat(3, 1fr)'
      },
      [theme.breakpoints.down(560)]: {
        gridTemplateColumns: 'repeat(2, 1fr)'
      },
      '& img': {
        width: '100%'
      }
    }
  }
}))

export default TipWrapper
