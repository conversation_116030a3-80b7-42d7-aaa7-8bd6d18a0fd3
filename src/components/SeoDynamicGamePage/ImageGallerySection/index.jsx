import { Box } from '@mui/material'
import './imageGallerySection.styles'
import TipWrapper from './imageGallerySection.styles'
import useDynamicGamepageStore from '../../../store/useDynamicGamepageStore'
import { usePortalStore } from '../../../store/store'
import Signup from '../../Modal/Signup'

const TopImageGallery = () => {
  const dynamicGamePageData = useDynamicGamepageStore((state) => state.dynamicGamePageData)
  const portalStore = usePortalStore()
  const handleGameClick = () => {
    portalStore.openPortal(() => <Signup />, 'loginModal')
  }
  return (
    <section>
      <TipWrapper>
        <Box className='image-gallery'>
          <figure>
            <Box className='image-wrap'>
              {dynamicGamePageData?.dynamicGameDetails?.image?.map((gameCard, index) => (
                <figure key={index}>
                  <img src={gameCard?.imageUrl} alt={gameCard?.altTag} onClick={handleGameClick} />
                </figure>
              ))}
            </Box>
          </figure>
        </Box>
      </TipWrapper>
    </section>
  )
}

export default TopImageGallery
