import { Box, styled } from '@mui/material'

const CardSectionWrapper = styled(Box)(({ theme }) => ({
  '& .card-wrap': {
    padding: '1.8125rem 0',
    '& .top-games-section': {
      margin: '2rem 0 3rem 0rem',
      [theme.breakpoints.down('md')]: {
        margin: '1rem 0 1rem 0rem'
      },
      '& h3': {
        fontSize: theme.spacing(1.75),
        fontWeight: '700',
        // color: theme.colors.YellowishOrange,
        margin: theme.spacing(0.5, 0),
        // lineHeight: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.5)
        }
      },
      '& p': {
        fontSize: theme.spacing(1.25),
        fontWeight: '500',
        // color: theme.colors.YellowishOrange,
        // margin: theme.spacing(1, 0),
        // lineHeight: theme.spacing(2),
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(1.25)
        }
      },
      '& .game-img': {
        maxHeight: '30rem',
        overflow: 'hidden',
        LineHeight: '1',
        height: '100%',
        border: '2px solid #8F8F8F',
        borderRadius: '10px',
        [theme.breakpoints.down('md')]: {
          maxHeight: '15rem'
        },
        '& img': {
          objectFit: 'cover',
          height: '100%',
          width: '100%'
        }
      },
      '& .MuiGrid-item': {
        height: '100%'
      }
    }
  }
}))

export default CardSectionWrapper
