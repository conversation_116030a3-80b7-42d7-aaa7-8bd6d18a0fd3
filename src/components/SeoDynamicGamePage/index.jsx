/* eslint-disable multiline-ternary */

import React, { useEffect, useState } from 'react'
import LandingHeader from '../../pages/Landing/LandingHeader'
import SeoLandingFooter from '../SeoLandingPages/SeoLandingFooter'
import TopImageGallery from './ImageGallerySection'
import DynamicContent from './DynamicContent'
import CardSection from './CardSection'
import GameFaq from './FaqSection'
import RelatedBlogSection from './RelatedBlogSection'
import MoreGameSection from './MoreGameSection'
import { Box, Typography, CircularProgress } from '@mui/material'
import makeStyles from './dynamicGamePage.styles'
import { Link, useLocation, useParams } from 'react-router-dom'
import useDynamicGamePageQuery from '../../reactQuery/useDynamicGamePageQuery'
import SeoHead from '../../utils/seoHead'
import BreadcrumbSchema from '../SeoLandingPages/SchemaComponents/BreadCrumbSchema'

const SeoDynamicGamePage = () => {
  const classes = makeStyles()
  const { gameSlug } = useParams()
  const [pageData, setPageData] = useState([])
  const { data, isLoading, error } = useDynamicGamePageQuery({ slug: gameSlug })

  useEffect(() => {
    if (data) {
      setPageData(data?.dynamicGameDetails)
      const script = document.createElement('script')
      script.type = 'application/ld+json'

      script.textContent = JSON.stringify(data?.dynamicGameDetails?.schema)
      document.head.appendChild(script)
    }
  }, [data])


  if (isLoading) {
    return (
      <Box
        className='maxWidthContainer'
        display='flex'
        justifyContent='center'
        alignItems='center'
        minHeight='90vh'
        width='100%'
      >
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    console.error('SeoDynamicGamePage API Error:', error)
    return (
      <Box
        className='maxWidthContainer'
        display='flex'
        flexDirection='column'
        justifyContent='center'
        alignItems='center'
        minHeight='90vh'
        width='100%'
      >
        <Typography variant='h4' color='error' gutterBottom>
          Page Not Found
        </Typography>
        <Typography variant='body1' color='textSecondary'>
          The game page "{gameSlug}" could not be found.
        </Typography>
        <Link to='/games' style={{ marginTop: '20px', textDecoration: 'none' }}>
          <Typography variant='body2' color='primary'>
            ← Back to Games
          </Typography>
        </Link>
      </Box>
    )
  }

  if (!data || !data.dynamicGameDetails) {
    return (
      <Box
        className='maxWidthContainer'
        display='flex'
        flexDirection='column'
        justifyContent='center'
        alignItems='center'
        minHeight='90vh'
        width='100%'
      >
        <Typography variant='h4' gutterBottom>
          No Content Available
        </Typography>
        <Typography variant='body1' color='textSecondary'>
          No content found for "{gameSlug}".
        </Typography>
        <Link to='/games' style={{ marginTop: '20px', textDecoration: 'none' }}>
          <Typography variant='body2' color='primary'>
            ← Back to Games
          </Typography>
        </Link>
      </Box>
    )
  }

  return (
    <>
      <SeoHead
        title={`${pageData?.metaTitle}`}
        // description={`Enjoy top-rated casino games ${pageData?.title}! Play exciting ${pageData?.title}, hit progressive ${pageData?.title}, and collect virtual rewards today.`}
        description={pageData?.metaDescription}

      />

      <div className={classes.SeoDynamicGamePage}>
        <header>
          <LandingHeader />
        </header>

        <BreadcrumbSchema
          items={[
            { name: 'Home', url: 'https://www.themoneyfactory.com/' },
            { name: 'Games', url: 'https://www.themoneyfactory.com/games' },
            { name: pageData?.heading, url: `https://www.themoneyfactory.com/games/${pageData?.slug}` }
          ]}
        />

        <section>
          <Box className='maxWidthContainer'>
            <Typography variant='h1'>
              Play <Typography variant='span'>{pageData?.heading} </Typography>Online at The Money Factory
            </Typography>
            <figure>
              <TopImageGallery />
            </figure>
          </Box>
        </section>

        <DynamicContent />

        {pageData?.GamePageCards && <CardSection />}
        {pageData?.GamePageFaqs?.length > 0 && <GameFaq />}
        {pageData?.blogPosts?.length > 0 && <RelatedBlogSection />}
        <MoreGameSection />
        <footer>
          <SeoLandingFooter />
        </footer>
      </div>
    </>
  )
}

export default SeoDynamicGamePage
