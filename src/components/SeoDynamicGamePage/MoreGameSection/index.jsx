import React, { useRef, useMemo } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, EffectCoverflow } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'

import { Typography, Box } from '@mui/material'
import useDynamicGamePageQuery from '../../../reactQuery/useDynamicGamePageQuery'
import { useNavigate, useParams } from 'react-router-dom'

export default function MoreGameSection() {
  const prevRef = useRef(null)
  const nextRef = useRef(null)
  const { gameSlug } = useParams()
  const { data, isLoading, error } = useDynamicGamePageQuery()
  const navigate = useNavigate()

  const images = useMemo(() => {
    return (
      data?.dynamicGameDetails
        ?.filter((game) => game?.slug !== gameSlug)
        ?.map((game) => {
          const firstImage = game?.image?.find((img) => img?.imageUrl)
          return {
            imageUrl: firstImage?.imageUrl || null,
            slug: game?.slug
          }
        })
        ?.filter(Boolean) || []
    )
  }, [data, gameSlug])

  if (isLoading || error) return null

  return (
    <section>
      <div className='maxWidthContainer more-games'>
        <Typography variant='h2' className='yellow-text'>
          More Games
        </Typography>

        {images.length === 0 ? (
          <Box sx={{ textAlign: 'center', padding: '20px' }}>
            <Typography variant='body1' color='white'>
              No More Games Avilable
            </Typography>
          </Box>
        ) : (
          <>
            <div ref={prevRef} className='swiper-button-prev' />
            <div ref={nextRef} className='swiper-button-next' />

            <Swiper
              modules={[Navigation, EffectCoverflow]}
              effect='coverflow'
              loop={true}
              centeredSlides={true}
              initialSlide={Math.floor(images.length / 2)}
              slidesPerView={5}
              coverflowEffect={{
                rotate: 0,
                stretch: 0,
                depth: 100,
                modifier: 2.5,
                slideShadows: false
              }}
              navigation={{
                prevEl: prevRef.current,
                nextEl: nextRef.current
              }}
              onBeforeInit={(swiper) => {
                swiper.params.navigation.prevEl = prevRef.current
                swiper.params.navigation.nextEl = nextRef.current
              }}
              spaceBetween={30}
              breakpoints={{
                0: {
                  slidesPerView: 3,
                  spaceBetween: 10
                },
                900: {
                  slidesPerView: 5,
                  spaceBetween: 30
                }
              }}
            >
              {images.map((image, index) => (
                <SwiperSlide key={index} style={{ width: '220px' }}>
                  <figure>
                    <img
                      src={image?.imageUrl}
                      alt={`Slide ${index}`}
                      style={{
                        width: '100%',
                        objectFit: 'cover',
                        borderRadius: '12px'
                      }}
                      onClick={() => navigate(`/games/${image?.slug}`)}
                      loading='lazy'
                    />
                  </figure>
                </SwiperSlide>
              ))}
            </Swiper>
          </>
        )}
      </div>
    </section>
  )
}
