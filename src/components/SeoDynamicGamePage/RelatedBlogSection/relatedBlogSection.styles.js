import { Box, styled } from '@mui/material'

const BlogWrapper = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  '& h2': {
    fontSize: '2.75rem!important',
    marginTop: '3rem',
    [theme.breakpoints.down('md')]: {
      fontSize: '1.75rem !important'
    }
  },
  '& button': {
    maxWidth: '12rem',
    width: '100%',
    margin: '1.25rem 0',
    fontWeight: '700 !important',
    fontSize: '1.125rem',
    [theme.breakpoints.down('md')]: {
      fontSize: '1rem !important',
      maxWidth: '8rem'
    }
  },
  '& .updates-section': {
    '& h3': {
      fontSize: theme.spacing(1.375),
      fontWeight: theme.typography.fontWeightBold,
      color: theme.colors.textWhite,
      textAlign: 'left',
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1),
        marginBottom: theme.spacing(1)
      }
    },
    '& .blog-text': {
      color: '#D2D3D5',
      fontSize: '1.25rem',
      maxWidth: '47rem',
      margin: '1.25rem auto',
      [theme.breakpoints.down('md')]: {
        fontSize: '1rem'
      }
    },
    '& .update-blog': {
      display: 'flex',
      flexDirection: 'column',
      gap: '0.5rem',
      cursor: 'pointer',
      '&:hover': {
        '& h4': {
          color: '#ffffff'
        },

        '& img': {
          transform: 'scale(1.02)'
        }
      },

      '& img': {
        maxHeight: '300px',
        // minHeight: '300px',
        height: '100%',
        width: '100%',
        borderRadius: '1rem',
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        [theme.breakpoints.down('md')]: {
          minHeight: '250px'
        }
      },

      '& .blog-detail': {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        '& p': {
          fontSize: theme.spacing(1),
          fontWeight: '500',
          color: '#D2D3D580',
          '&.blog-date': {
            '&::before': {
              top: '8px',
              left: '-16px',
              width: '7px',
              height: '7px',
              content: "''''",
              position: 'absolute',
              background: '#AAAAAA',
              borderRadius: '100%',
              [theme.breakpoints.down(1450)]: {
                width: '6px',
                height: '6px',
                top: '6px'
              }
            }
          },
          [theme.breakpoints.down(1450)]: {
            fontSize: theme.spacing(0.75)
          }
        }
      }
    }
  }
}))

export default BlogWrapper
