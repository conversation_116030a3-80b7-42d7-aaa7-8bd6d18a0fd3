import React from 'react'
import BlogWrapper from './relatedBlogSection.styles'
import { Box, Button, Grid, Typography } from '@mui/material'
import useDynamicGamepageStore from '../../../store/useDynamicGamepageStore'
import { useNavigate } from 'react-router-dom'
import { PlayerRoutes } from '../../../routes'
import useBlogSection from '../../SeoLandingPages/BlogSection/hook/useBlogSection'
import LazyImage from '../../../utils/lazyImage'

const RelatedBlogSection = () => {
  const dynamicGamePageData = useDynamicGamepageStore((state) => state.dynamicGamePageData)
  const relatedBlogs = dynamicGamePageData?.dynamicGameDetails?.blogPosts || []
  const { formatDateToWords } = useBlogSection()
  const navigate = useNavigate()

  return (
    <section>
      <BlogWrapper>
        <div className='maxWidthContainer'>
          <Typography variant='h2'>Related Blog</Typography>
          <Box className='updates-section'>
            <Typography variant='body1' className='blog-text'>
              Stay one step ahead with The Money factory casino updates! We're your ultimate source for all things
              gaming on our site from new jackpots, thrilling features to providers.
            </Typography>
            <Grid container spacing={{ xs: 1, md: 2 }}>
              {relatedBlogs?.map((blog) => (
                <Grid key={blog?.blogPostId} item xs={12} sm={6} lg={4} onClick={() => navigate(`/blog/${blog?.slug}`)}>
                  <article className='update-blog'>
                    <LazyImage src={blog?.bannerImageUrl} alt='blog' />
                    <Typography variant='h3'>
                      {blog?.metaTitle?.length > 60 ? `${blog?.metaTitle?.substring(0, 60)}...` : blog?.metaTitle}
                    </Typography>
                    <Box className='blog-detail'>
                      <Typography variant='body1'>The Money factory Social Casino</Typography>
                      <Typography className='blog-date' variant='body1'>
                        {formatDateToWords(blog?.createdAt)}
                      </Typography>
                    </Box>
                  </article>
                </Grid>
              ))}
            </Grid>
          </Box>

          {relatedBlogs.length > 0 && (
            <Button className='btn btn-primary' type='button' onClick={() => navigate(PlayerRoutes.Blog)}>
              View More
            </Button>
          )}
        </div>
      </BlogWrapper>
    </section>
  )
}

export default RelatedBlogSection
