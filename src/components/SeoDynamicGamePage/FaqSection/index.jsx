import React, { useState } from 'react'
import FaqWrapper from './faqSection.styles'
import { Typography, Box, Accordion, AccordionSummary, AccordionDetails } from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import useDynamicGamepageStore from '../../../store/useDynamicGamepageStore'

const GameFaq = () => {
  const [expanded, setExpanded] = useState(null)

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : null)
  }

  const dynamicGamePageData = useDynamicGamepageStore((state) => state.dynamicGamePageData)

  const gameFaqs = dynamicGamePageData?.dynamicGameDetails?.GamePageFaqs || []

  return (
    <section>
      <FaqWrapper>
        <Box className='maxWidthContainer'>
          <Typography variant='h2'>Frequently Ask Questions</Typography>
          <Box className='faq-wrap'>
            {gameFaqs.map((faq, index) => (
              <Box key={index} component='div'>
                <Accordion expanded={expanded === index} onChange={handleChange(index)}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>{faq.question}</Typography>
                  </AccordionSummary>

                  <AccordionDetails>
                    {Array.isArray(faq.answer) ? (
                      faq.answer.map((line, i) => (
                        <Typography
                          key={i}
                          variant='body1'
                          className={(i > 0 && index === 7) || index === 8 ? 'pointer' : ''}
                        >
                          {line}
                        </Typography>
                      ))
                    ) : (
                      <Typography variant='body1' className='space-down'>
                        {faq.answer}
                      </Typography>
                    )}
                  </AccordionDetails>
                </Accordion>
              </Box>
            ))}
          </Box>
        </Box>
      </FaqWrapper>
    </section>
  )
}

export default GameFaq
