import React from 'react'
import { Button, Grid, IconButton } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './ImgModal.styles'
import { useNavigate } from 'react-router-dom'
import { PlayerRoutes } from '../../routes'
import { usePopupStore } from '../../store/useBannerSlice'

const KeepPlaying = () => {
  const classes = useStyles()
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)
  const { lobbyPopups } = usePopupStore((state) => state)
  const handleClose = () => {
    portalStore.closePortal()
  }

  const handleClaim = () => {
    navigate(PlayerRoutes.Store)
    handleClose()
  }

  return (
    <Grid className='welcome-banner-modal'>
      <Grid className='modal-img-wrap'>
        <img src={(lobbyPopups && lobbyPopups[1]?.desktopImageUrl)} alt='Welcome' />
        <Grid className='img-modal-btn'>
          <Grid className={classes.btnGradientWrap}>
            <Button variant='contained' className='btn-gradient' type='button' onClick={handleClaim}>
              <span className='btn-span'>{(lobbyPopups && lobbyPopups[1]?.btnText) || 'Claim Now'}</span>
            </Button>
          </Grid>
        </Grid>
      </Grid>
      <Grid className='inner-modal-header'>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default KeepPlaying
