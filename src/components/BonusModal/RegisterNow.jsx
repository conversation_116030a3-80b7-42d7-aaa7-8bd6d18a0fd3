import React from 'react'
import { But<PERSON>, Grid, IconButton } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './ImgModal.styles'
import UsernamePopup from '../../pages/Lobby/components/UsernamePopup'
import { usePopupStore } from '../../store/useBannerSlice'

const RegisterNow = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const { lobbyPopups } = usePopupStore((state) => state)
  const handleClose = () => {
    portalStore.closePortal()
  }

  const handleRegisterNow = () => {
    portalStore.openPortal(() => <UsernamePopup />, 'innerModal')
  }

  return (
    <Grid className='welcome-banner-modal'>
      <Grid className='modal-img-wrap'>
        <img src={(lobbyPopups && lobbyPopups[2]?.desktopImageUrl)} alt='Welcome' />
        <Grid className='img-modal-btn'>
          <Grid className={classes.btnGradientWrap}>
            <Button variant='contained' className='btn-gradient' type='button' onClick={handleRegisterNow}>
              <span className='btn-span'>{(lobbyPopups && lobbyPopups[2]?.btnText) || 'Register Now and Claim'}</span>
            </Button>
          </Grid>
        </Grid>
      </Grid>
      <Grid className='inner-modal-header'>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default RegisterNow
