import { makeStyles } from '@mui/styles'
export default makeStyles((theme) => ({
  btnPrimary: {
    transition: 'all 0.3s ease 0s',
    background: theme.colors.primaryGradient,
    '&.MuiButton-root': {
      padding: theme.spacing(0.125),
      borderRadius: '30px',
      overflow: 'hidden',
      minWidth: '30px',
      textTransform: 'capitalize',
      fontWeight: theme.typography.fontWeightSemiBold
    },
    '& .btn-primary-content': {
      background: theme.colors.primaryBtnBg,
      width: '100%',
      height: '100%',
      padding: theme.spacing(0.3, 1.5),
      borderRadius: '30px',
      justifyContent: 'center',
      display: 'flex',
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(0.75),
        padding: theme.spacing(0.3, 1)
      },
      '&:hover': {
        background: theme.colors.primaryGradient
      }
    },
    '&:hover': {
      '&.MuiButton-root': {
        background: theme.colors.primaryGradient
      }
    }

  },
  btnWhiteGradient: {
    transition: 'all 0.3s ease 0s',
    width: '100%',
    '& .btn-gradient': {
      '&.MuiButtonBase-root': {
        background: theme.colors.btnSecondaryBg,
        boxShadow: theme.shadows[1],
        borderRadius: '30px',
        color: theme.colors.authCardBg,
        position: 'relative',
        overflow: 'hidden',
        minHeight: '30px',
        width: '100%',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '&:before': {
          position: 'absolute',
          width: '700px',
          height: '100%',
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: '1rem 1rem'
        },
        '& span': {
          position: 'relative',
          color: theme.colors.authCardBg,
          zIndex: '2',
          fontWeight: theme.typography.fontWeightSemiBold,
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          background: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
          '& span': {
            color: theme.colors.white
          }
        }
      }
    }
  },
  btnGradientWrap: {
    width: '100%',
    marginRight: theme.spacing(0.313),

    '& .btn-gradient': {
      '&.MuiButtonBase-root': {
        background: theme.colors.primaryGradient,
        boxShadow: theme.shadows[1],
        borderRadius: '30px',
        minHeight: '30px',
        color: theme.colors.white,
        position: 'relative',
        overflow: 'hidden',
        width: '100%',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '&:before': {
          position: 'absolute',
          width: '700px',
          height: '100%',
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: '1rem 1rem'
        },
        '& .btn-span': {
          position: 'relative',
          color: theme.colors.white,
          zIndex: '2',
          fontWeight: theme.typography.fontWeightExtraBold,
          display: 'flex',
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          backgroundColor: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder
        }
      }
    }
  },
  imgModal: {
    backgroundColor: '#1B181E !important',
    backdropFilter: 'blur(1px)',
    position: 'relative',
    paddingBottom: theme.spacing(5),
    '& .MuiPaper-root': {
      borderRadius: theme.spacing(2.5),
      color: theme.colors.white,
      backgroundSize: '100% 100%',
      minWidth: '400px',
      padding: '0',
      position: 'relative',
      overflow: 'visible',
      [theme.breakpoints.down('sm')]: {
        minWidth: '80%'
      },
      '&:before': {
        position: 'absolute',
        width: '100%',
        height: '70px',
        background: theme.colors.modalBottomGradient,
        bottom: '0px',
        content: "''",
        zIndex: '1',
        borderBottomLeftRadius: theme.spacing(2),
        borderBottomRightRadius: theme.spacing(2)
      },
      '& .modal-img-wrap': {
        width: '100%',
        position: 'relative',
        paddingBottom: '120%',
        [theme.breakpoints.down('sm')]: {
          paddingBottom: '140%'
        },
        '& img': {
          width: '100%',
          height: '100%',
          top: '0',
          left: '0',
          position: 'absolute',
          borderRadius: theme.spacing(0.625)
        },
        '& .img-modal-btn': {
          textAlign: 'center',
          position: 'absolute',
          transform: 'translate(-50%, -50%)',
          top: 'auto',
          left: '50%',
          bottom: '-35px',
          width: '80%',
          zIndex: '2',
          '& .MuiButtonBase-root': {
            minWidth: '100px'
          }
        }
      }
    },
    '& .inner-modal-header': {
      padding: theme.spacing(0.313),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      position: 'absolute',
      top: theme.spacing(0.625),
      right: theme.spacing(0.625),

      '& .modal-close': {
        '& .MuiButtonBase-root': {
          background: theme.colors.modalCloseBg,
          padding: '0.625rem',
          borderRadius: theme.spacing(0.625),
          height: theme.spacing(2),
          width: theme.spacing(2),
          color: theme.colors.themeText,
          [theme.breakpoints.down('sm')]: {
            marginRight: '0'
          },
          '& svg': {
            color: theme.colors.white,
            fontSize: theme.spacing(0.875)
          },
          '&:hover': {
            '& svg': {
              color: theme.colors.highlighColor
            }
          }
        }
      }
    }

  }

}))
