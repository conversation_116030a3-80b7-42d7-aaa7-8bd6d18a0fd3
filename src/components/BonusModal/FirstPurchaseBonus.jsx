import React from 'react'
import { Button, Grid, IconButton } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { firstTimePurchasePopUp } from '../ui-kit/icons/png'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './ImgModal.styles'
import { useNavigate } from 'react-router-dom'
import { PlayerRoutes } from '../../routes'
import { usePopupStore } from '../../store/useBannerSlice'

const FirstPurchaseBonus = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const navigate = useNavigate()
  const { lobbyPopups } = usePopupStore((state) => state)
  const handleClose = () => {
    portalStore.closePortal()
  }

  const handleClaim = () => {
    navigate(PlayerRoutes.Store)
    handleClose()
  }

  return (
    <Grid className='welcome-banner-modal'>
      <Grid className='modal-img-wrap'>
        <img src={(lobbyPopups && lobbyPopups[0]?.desktopImageUrl) || firstTimePurchasePopUp} alt='Welcome' height='100' width='100%' />
        <Grid className='img-modal-btn'>
          <Grid className={classes.btnGradientWrap}>
            <Button variant='contained' className='btn-gradient' type='button' onClick={handleClaim}>
              <span className='btn-span'>{(lobbyPopups && lobbyPopups[0]?.btnText) || 'Claim Now'}</span>
            </Button>
          </Grid>
        </Grid>
      </Grid>

      <Grid className='inner-modal-header'>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default FirstPurchaseBonus
