import React from 'react'
import { Grid, IconButton, DialogContent } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import useStyles from './styles'
import { usePortalStore } from '../../store/userPortalSlice'

import Cms from '../../pages/Cms/Cms'
import { useNavigate } from 'react-router-dom'
import { PlayerRoutes } from '../../routes'

const CmsModal = ({ path, handleConfirm, fromLanding, istournamentpopup }) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const navigate = useNavigate()
  const handleClose = () => {
    if (istournamentpopup) {
      portalStore.closePortal()
      navigate(PlayerRoutes.Lobby)
      return
    }
    portalStore.closePortal()
  }

  return (
    <Grid className={`${classes.cmsModalWrap} ${classes.cmsWithFooter}`}>
      <Grid className='modal-close'>
        <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
          <CloseIcon />
        </IconButton>
      </Grid>

      <DialogContent>
        <Cms path={path} handleConfirm={handleConfirm} fromLanding={fromLanding} />
      </DialogContent>
    </Grid>
  )
}
export default CmsModal
