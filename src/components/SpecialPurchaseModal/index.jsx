import React, { useState, useEffect } from 'react';
import {
    Button,
    Grid,
    IconButton,
    Typography,
    DialogContent,
    Box,
    Link
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import useStyles from './SpecialPurhcaseModal.styles';
import { usePortalStore } from '../../store/userPortalSlice';
import { BrandLogo } from '../ui-kit/icons/brand';
import { purchaseBg } from '../ui-kit/icons/utils';
import Signin from '../Modal/Signin';
import { getLoginToken, setItem } from '../../utils/storageUtils';
import { useUserStore } from '../../store/useUserSlice';
import DailyBonusTimer from '../CountDownTimer/dailyBonusTimer';
import { useGetProfileMutation } from '../../reactQuery';
import StepperForm from '../StepperForm';
import { useSiteLogoStore } from '../../store/store';
const SpecialPurchaseModal = () => {
    const classes = useStyles();
    const portalStore = usePortalStore((state) => state);
    const auth = useUserStore((state) => state);
    const [fromCoinBundles, setFromCoinBundles] = useState(!auth.isAuthenticate);
    const [selectedPackage, setSelectedPackage] = useState(null);
    const [remainingTime, setRemainingTime] = useState(null);
    const user = useUserStore((state) => state);
    const logoData = useSiteLogoStore((state) => state)
    useEffect(() => {
        if (user && user?.userDetails && user?.userDetails?.welcomePurchaseBonus) {
            setSelectedPackage(user?.userDetails?.welcomePurchaseBonus);
            setRemainingTime(user?.userDetails?.welcomePurchaseBonus?.welcomePurchaseBonusEndTime)
        }
    }, [user]);
    const getProfileMutation = useGetProfileMutation({
        onSuccess: (res) => {
            portalStore.openPortal(()=><StepperForm stepperCalledFor={'purchase'} packageDetails={selectedPackage}/>,'StepperModal');
        },
        onError: (error) => {
            console.log('error', error)
        }
    })
    const handleBuyNow = () => {
        if (!!getLoginToken() || auth.isAuthenticate) {
            (function () {
                window._conv_q = window._conv_q || [];
                _conv_q.push(["pushRevenue", "credit", selectedPackage, "100466670"]);
            })();
            getProfileMutation.mutate()
        } else {
            portalStore.openPortal(() => <Signin fromCoinBundles={fromCoinBundles} selectedPackage={selectedPackage} />, 'innerModal');
        }
    };


    const handleClose = () => {
        setItem("special_package", true);
        portalStore.closePortal();
    };

    return (
        <Grid>
            <DialogContent className={classes.purchaseModal}>
                <Box className={classes.redeemModalContainer}>
                    <Grid display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
                        <Grid className='modal-close'>
                            <IconButton edge='start' color='inherit' className='close' onClick={handleClose} aria-label='close'>
                                <CloseIcon />
                            </IconButton>
                        </Grid>
                    </Grid>
                    <Grid container spacing={1}>
                        <Grid item xs={12} lg={5}>
                            <img src={purchaseBg} className='purchase-bg' alt='Purchase Background' />
                        </Grid>
                        <Grid item xs={12} lg={7}>
                            <Grid className='purchase-content'>
                                <Grid className='modal-logo'>
                                    <img src={logoData?.desktopLogo ||  BrandLogo} alt='Logo' />
                                </Grid>
                                {remainingTime && (
                                    <Typography variant='h4'>
                                        <span style={{ display: 'flex', justifyContent: 'center' }}>
                                            Offer ends in: <DailyBonusTimer eventDateTime={remainingTime} />
                                        </span>
                                    </Typography>
                                )}
                                <Typography variant='h4'>
                                    {selectedPackage?.bonusPercentage}% First Time Purchase Bonus availible for <span>Limited time only!</span>
                                </Typography>
                                <Typography variant='h3'>
                                    Buy <span style={{ color: "#ec5c24" }}>"{selectedPackage?.gcCoin} GC"</span> + FREE <span style={{ color: '#81c208' }}>"{selectedPackage?.scCoin} SC"</span> for <span style={{ color: '#FDB72E' }}>"${selectedPackage?.amount}"</span>
                                </Typography>
                                <Grid className='modal-btn'>
                                    <Button type='button' className='btn btn-primary' onClick={handleBuyNow}>Grab Your Bonus Coins Now</Button>
                                    <Button type='button' onClick={handleClose} className='btn text-btn'>Skip now</Button>
                                </Grid>
                                <Grid className='modal-terms-content'>
                                    <Typography variant='h4'>Terms and Conditions</Typography>
                                    <Grid className="modal-link-wrap">
                                        <Link>Offer valid till timer running</Link>
                                        <Link>One-time use per user</Link>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Box>
            </DialogContent>
        </Grid>
    );
};

export default SpecialPurchaseModal;
