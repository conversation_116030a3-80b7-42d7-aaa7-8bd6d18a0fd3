import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  purchaseModal: {
    "& .modal-close": {
      color: theme.colors.textWhite,
      marginLeft: "auto",
      position: "absolute",
      right: theme.spacing(1),
      top: theme.spacing(1),
      cursor: "pointer",
      zIndex: "5",
    },
    position: "relative",
    "& .purchase-bg": {
      width: "100%",
      position: "absolute",
      bottom: "0",
      left: theme.spacing(-1.8),
      [theme.breakpoints.down('md')]: {
        display: "none",
      },
    },
    "& .modal-logo": {
      textAlign: "center",
      marginBottom: theme.spacing(1),
      "& img": {
        width: theme.spacing(10),
        margin: "0 auto",
      },
    },
    "& .purchase-content": {
      padding: theme.spacing(1),
      [theme.breakpoints.down('lg')]: {
        padding: theme.spacing(2, 8),
      },
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(2, 4),
      },
      "& .MuiTypography-h4": {
        color: theme.colors.textWhite,
        fontWeight: 600,
        fontSize: theme.spacing(1.5),
        textAlign: "center",
        marginBottom: theme.spacing(2),
        "& span": {
          color: theme.colors.YellowishOrange,
          display: "block",
        }
      },
      '& .MuiTypography-h3': {
        color: theme.colors.textWhite,
        fontWeight: 700,
        fontSize: theme.spacing(1.5),
        textAlign: "center",
      },
      "& .modal-btn": {
        display: "flex",
        flexDirection: "column",
        margin: theme.spacing(1, 0),

      },
      "& .modal-terms-content": {
        paddingBottom: theme.spacing(2),
        "& .MuiTypography-h4": {
          color: theme.colors.YellowishOrange,
          fontWeight: "600",
          fontSize: theme.spacing(0.75),
          marginBottom: theme.spacing(1),
        },
        "& .modal-link-wrap": {
          display: "flex",
          justifyContent: "center",
          '& .MuiTypography-root': {
            color: theme.colors.textWhite,
            fontSize: theme.spacing(0.75),
            position: "relative",
            textDecoration: "none",
            padding: theme.spacing(0, 0.5),
            pointerEvents: 'none',
            "&:before": {
              position: "absolute",
              content: "''",
              left: "-2px",
              top: theme.spacing(0.313),
              height: theme.spacing(0.313),
              width: theme.spacing(0.313),
              borderRadius: '100%',
              background: theme.colors.YellowishOrange,
            }
          }
        }
      }
    }

  }

}))
