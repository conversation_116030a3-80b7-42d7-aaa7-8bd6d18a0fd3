//USERNAME MODAL
import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  bonusModalWrap: {
    minWidth: theme.spacing(15.625),
    background: theme.colors.bonusModalBg,
    position: "relative",
    borderRadius: "19px",
    backgroundColor: "transparent",
    "& .modal-close": {
      position: "absolute",
      right: theme.spacing(1.25),
      top: theme.spacing(1),
      background: theme.colors.textWhite,
      height: theme.spacing(1.375),
      width: theme.spacing(1.375),
      borderRadius: "100%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      zIndex: 2,
      "& .MuiButtonBase-root": {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "0",
        margin: "0",
        position: "relative",
        "& svg": {
          height: theme.spacing(1),
          width: theme.spacing(1),
          transform: "translate(-50%, -50%)",
          top: "50%",
          left: "50%",
          position: "absolute",
        }
      }
    },
    "& .bonus-modal-img-wrap": {
      height: theme.spacing(14.5),
      width: theme.spacing(14.5),
      borderRadius: theme.spacing(1.25),
      // overflow: "hidden",
      margin: "0 auto",
      position: "relative",
      "& > img": {
        height: "100%",
        width: "100%",
        objectFit: "cover"
        ,
      },
      "& .modal-brand-logo": {
        position: "absolute",
        top: theme.spacing(-1.25),
        left: theme.spacing(-0.625),
        zIndex: "2",
        "& img": {
          width: theme.spacing(4.5),
        }
      }
    },
    "& .bonus-modal-details": {
      textAlign: "center",
      "& .bonus-coins-wrap": {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        margin: theme.spacing(0.625, 0),
        gap: theme.spacing(0.625),
        "& > .MuiGrid-root": {
          display: "flex",
          alignItems: "center",
          "& img": {
            marginRight: theme.spacing(0.313),
          },
          "& .MuiTypography-h4": {
            color: theme.colors.primaryYellowBg,
            fontWeight: theme.typography.fontWeightSemiBold,
            fontSize: theme.spacing(1.8),
          }
        },

      },
      "& .MuiTypography-body1": {
        color: theme.colors.textWhite,
        textAlign: "center",
        textTransform: "capitalize",
        marginBottom: theme.spacing(1),
      },
      "& .btn-primary": {
        background: theme.colors.primaryYellowBg,
        color: theme.colors.textBlack,
        borderRadius: theme.spacing(1.875),
        fontWeight: theme.typography.fontWeightBoldBlack,
      }

    }
  }
}))
