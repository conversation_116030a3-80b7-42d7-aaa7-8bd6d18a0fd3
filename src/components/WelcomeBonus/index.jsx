import React, { useEffect, useState } from 'react'
import useStyles from '../DailyBonus/Bonus.styles'
import { Button, Grid, Typography, DialogContent, IconButton, CircularProgress } from '@mui/material'
import { useClaimWelcomBonusMutation, useGetDailyBonusMutation } from '../../reactQuery/bonusQuery'
import { toast } from 'react-hot-toast'
import { useGetProfileMutation } from '../../reactQuery'
import { useUserStore } from '../../store/useUserSlice'
import CloseIcon from '@mui/icons-material/Close'
// import { usdIcon, usdchipIcon } from '../../../src/components/ui-kit/icons/svg/index'
import { usdIcon,usdchipIcon } from '../ui-kit/icons/opImages'
import { usePortalStore } from '../../store/userPortalSlice'
import { customEvent } from '../../utils/optimoveHelper'
import SpecialPurchaseModal from '../SpecialPurchaseModal'
import DailyBonus from '../DailyBonus'
import useSeon from '../../utils/useSeon'
import TagManager from 'react-gtm-module'

const WelcomeBonus = ({ welcomeBonusData }) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const [isLoading, setIsLoading] = useState(false)
  const sessionId = useSeon()
  const handleClose = () => {
    portalStore.closePortal()
    getProfileMutation.mutate()
  }

  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      if (res?.data?.data) {
        let resetData = res?.data?.data?.remainingTime
        portalStore.openPortal(() => <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />, 'bonusStreak')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {}
  })



  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)

      portalStore.closePortal()

      if (!res?.data?.data?.isDailyBonusClaimed && res?.data?.data?.isDailyBonusAllowed) {
        mutationGetDailyBonus.mutate()
      } else if (res?.data?.data && res?.data?.data?.welcomePurchaseBonusApplicable) {
        portalStore.openPortal(() => <SpecialPurchaseModal />, 'termsNConditionModal')
      } 
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const mutationClaimWelcomeBonus = useClaimWelcomBonusMutation({
    onSuccess: (res) => {
      TagManager.dataLayer({
        dataLayer: {
          event: 'bonus',
          bonus_type: 'welcome_bonus',
          user_id: res?.data?.userWallet?.ownerId,
          gcCoin: res?.data?.userWallet?.gcCoin,
          scCoin: res?.data?.userWallet?.totalScCoin
        }
      })
      const parameters = {
        bonus_type: 'welcome_bonus',
        gcCoin: res?.data?.userWallet?.gcCoin,
        scCoin: res?.data?.userWallet?.totalScCoin
      }
      if (import.meta.env.VITE_NODE_ENV === 'production') {
        customEvent('claim_welcome_bonus', parameters, res?.userWallet?.ownerId)
      }
      localStorage.setItem('allowedUserAccess', true)
      setIsLoading(false)
      toast.success('Bonus successfully redeemed')
      getProfileMutation.mutate()
      portalStore.closePortal()
      // window.location.reload()
    },
    onError: (error) => {
      portalStore.closePortal()
    }
  })

  const claimWelcomeBonus = () => {
    const data = { sessionKey: sessionId, rtyuioo: sessionId === ' ' ? true : false }
    setIsLoading(true)
    mutationClaimWelcomeBonus.mutate(data)
  }

  return (
    <>
      <Grid className={classes.bonusModalWrap}>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>

        <DialogContent>
          <Grid className='bonus-modal-img-wrap'>
            <img src={welcomeBonusData?.imageUrl} alt='Bonus' />
          </Grid>
          <Grid className='bonus-modal-details'>
            <Grid className='bonus-coins-wrap'>
              <Grid>
                <img src={usdchipIcon} alt='chipCoinIcon' />
                <Typography variant='h4'>{welcomeBonusData?.gcAmount} GC</Typography>
              </Grid>
              <Grid>
                <img src={usdIcon} alt='Coins' />
                <Typography variant='h4'>{welcomeBonusData?.scAmount} SC</Typography>
              </Grid>
            </Grid>
            <Typography>{welcomeBonusData?.description}</Typography>
            <Button
              variant='contained'
              className='btn-primary'
              onClick={claimWelcomeBonus}
              disabled={isLoading}
              data-tracking='Play.Bonus.WecomeBonus.HereIsYourGift.Btn'
            >
              {welcomeBonusData?.btnText}
              {isLoading && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
            </Button>
            <Grid className='bonus-terms'>
              <Typography>{welcomeBonusData?.termCondition?.EN}</Typography>
            </Grid>
          </Grid>
        </DialogContent>
      </Grid>
    </>
  )
}

export default WelcomeBonus
