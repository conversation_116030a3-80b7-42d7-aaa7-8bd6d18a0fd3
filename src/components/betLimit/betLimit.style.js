import { makeStyles } from '@mui/styles'

import { ButtonPrimary } from '../../MainPage.styles'


export default makeStyles((theme) => ({ 
  betLimitModal: {
    border: '1px solid #757473',
    borderRadius: '11px',
    zIndex: 99999,
    // backgroundImage: `url(${modalBg})`,
    background: '#202020CC',
    backgroundSize: '100% 100%',
    backgroundRepeat: 'no-repeat',
    width: '360px',
    // minHeight: '420px',
    [theme.breakpoints.down('sm')]: {
      width: '330px',
      minHeight: '390px'
    },
    [theme.breakpoints.down(350)]: {
      width: '310px'
    },
    // background: 'linear-gradient(180deg, #222222 19%, #000000cc 63.6%)',
    '& .modal-section': {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing(1),
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(1),
        display: 'block'
      }
    },
    '& .login': {
      ...ButtonPrimary(theme),
      border: 'none',
      '&:hover': {
        background: theme.colors.YellowishOrange,
        border: 'none'
      },
      marginTop: '0.5rem'
    },
    '& .close-icon': {
      position: 'absolute',
      top: '1rem',
      right: '1rem',
      cursor: 'pointer',
      width: '0.875rem'
    },
    '& .idle-head': {
      fontSize: '2.5rem',
      // lineHeight: '0',
      // opacity: '0',
      fontWeight: '700',
      marginBottom: '1rem',
      // background: '#FFA538',
      background: 'linear-gradient(171.6deg, #FFC538 6.97%, #E37A34 68.29%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      // textTransform: 'uppercase',
      textAlign: 'center',
      [theme.breakpoints.down('sm')]: {
        fontSize: '24px',
        fontWeight: '700'
      },
      [theme.breakpoints.down('md')]: {
        fontSize: '30px',
        fontWeight: '700'
      }
    },
    '& .bet-body': {
      // opacity: '0',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      '& h6': {
        fontWeight: '500',
      }
    },
    '& .bet-img': {
      // opacity: '0',
      width: '100%',
      // display: 'none',
      maxWidth: '180px',
      height: 'auto',
      // height: '290px',
      // boxShadow: '0 4px 15px rgba(0, 0, 0, 0.5)',
      // filter: 'drop-shadow(0px 10px 20px rgba(255, 165, 0, 0.6))',
      margin: '0 auto',
      // WebkitMaskImage: 'radial-gradient(circle, rgba(0, 0, 0, 1) 75%, rgba(0, 0, 0, 0) 100%)', // Apply edge fading
      // maskImage: 'radial-gradient(circle, rgba(0, 0, 0, 1) 75%, rgba(0, 0, 0, 0) 100%)',
      WebkitMaskSize: 'cover',
      maskSize: 'cover',
      [theme.breakpoints.down('sm')]: {
        // height: '270px'
      }
    },
    '& h5': {
      opacity: '1',
      color: '#FDB72E',
      fontSize: '20px',
      margin: '1rem 0',
      fontWeight: '700'
    },
    '& .idle-text': {
      fontSize: '18px',
      fontWeight: '600',
      textAlign: 'center',
      lineHeight: 1.2,
      [theme.breakpoints.down('sm')]: {
        fontSize: '16px !important',
        fontWeight: '600'
      },
      '&.idle-text2': {
        fontSize: '15px',
        lineHeight: 1.1,
        marginBottom:'10px'
      }
    }
  },
  modalWrapper: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    // margin: '1.5rem',
    minHeight: '380px',
    height: '100%',
    // width: 'calc(100% - 3rem) !important',
    // display: 'none',
    padding: '1rem !important',
    '& .MuiBox-root': {
      padding: '0'
    },
    [theme.breakpoints.down('sm')]: {
      fontSize: '12px',
      fontWeight: '600'
    },
    [theme.breakpoints.down('md')]: {
      fontSize: '14px',
      fontWeight: '600'
    }
  }
}))
