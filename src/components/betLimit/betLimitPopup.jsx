import React from 'react'
import { <PERSON><PERSON>, <PERSON>, DialogContent, Typo<PERSON>, Button } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import useStyles from './betLimit.style'


import betlimit1 from '../ui-kit/icons/png/bet-limit-1.png'
import betlimit2 from '../ui-kit/icons/png/bet-limit-2.png'

import betlimit4 from '../ui-kit/icons/png/bet-limit-4.png'
import closeIcon from '../ui-kit/icons/png/sidebar-cross.png'
// Randomly select one of the session images
const sessionImages = [betlimit1, betlimit2, betlimit4]
const randomBetImg = sessionImages[Math.floor(Math.random() * sessionImages.length)]

const BetLimitPopup = (props) => {
  const classes = useStyles()

  const portalStore = usePortalStore((state) => state)
  const handleClose = () => {
    portalStore.closePortal()
  }
  
  return (
    <Grid className={classes.betLimitModal}>
      <DialogContent sx={{ padding: '0' }}>
        <Box sx={{ width: '100%' }} style={{ padding: '0' }} className={classes.modalWrapper}>
           <img className="close-icon" onClick={handleClose} src={closeIcon} alt="Close" />
          <Typography className='idle-head' variant='h4'>
            Oops!!
          </Typography>
          <img className='bet-img' src={randomBetImg} height={100} width={100} alt='idle-bg' loading='lazy' />

          <Box className='bet-body'>
            <Typography variant='h5'>Bet Limit reached</Typography>
            <Typography sx={{ color: '#FFFFFF' }} className='idle-text idle-text2 ' variant='h6'>
            {props?.message}
            
            </Typography>
          </Box>
                    
        </Box>
      </DialogContent>
    </Grid>
  )
}

export default BetLimitPopup
