import React from 'react'
import Check<PERSON>con from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import useStyles from './PasswordErrorMsg.style'
import { Typography } from '@mui/material';
const PasswordErrorMsg = ({password}) => {
  const classes = useStyles();

  const renderValidationMessage = (condition, message) => {
    return (
      <div className={classes.passwordTextContainer}>
      {!condition ? <CheckIcon style={{ color: 'green',marginRight:"5px", width: '20px' }} />  : <CloseIcon style={{ color: 'red',marginRight:"5px", width: '20px' }} />}
      <Typography  className={!condition ? classes.passwordText:classes.passwordTextError}>
         {message}
      </Typography>
      </div>
    );
  };
  return (
    <div className={classes.passwordErrorContainer}>
   <Typography className={classes.passwordHeading}>
    </Typography>
     { renderValidationMessage(
       !/[a-z]/.test(password),
       "Contain at least one lowercase letter.",
      )}
     { renderValidationMessage(
        !/[A-Z]/.test(password),
        "Contain at least one uppercase letter.",
     )}
     {  renderValidationMessage(
      !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(password),
        "Contain at least one special character.",
       )}
     {  renderValidationMessage(
      !/\d/.test(password),
     "Contain at least one number.",
      )}
     { renderValidationMessage(
       !(password?.length >= 8 && password?.length <= 20),
       "Be 8-20 characters long.",
     )}
     </div>
  )
}

export default PasswordErrorMsg
