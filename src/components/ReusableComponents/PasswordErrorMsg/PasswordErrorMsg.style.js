import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  passwordErrorContainer: {
    margin: "20px 0px"
  },
  passwordHeading: {
    color: theme.colors.textWhite,
  },
  passwordTextContainer: {
    display: "flex",
    alignItems: "center"
  },
  passwordTextError: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    marginBottom: `${theme.spacing(0.1)} !important`,
    fontWeight: '600 !important',
    marginTop: "2px !important"
  },
  passwordText: {
    color: "green",
    fontSize: `${theme.spacing(0.8)}!important`,
    fontWeight: '600 !important',
    marginTop: "2px !important"
  }
}))
