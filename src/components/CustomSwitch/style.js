
import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  themeSwitch: {
    '& .MuiSwitch-switchBase': {
      '& .MuiSwitch-thumb': {
        height: theme.spacing(1.5),
        width: theme.spacing(1.5),
        background: theme.colors.switchTrackoff,
        border: `1px solid ${theme.colors.YellowishOrange}`,
        marginTop: '-2px'
      },
      '&.Mui-checked': {
        '& + .MuiSwitch-track': {
          backgroundColor: `${theme.colors.YellowishOrange}`,
          opacity: 1
        }
      }

    },
    '& .MuiSwitch-track': {
      height: theme.spacing(1),
      backgroundColor: theme.colors.timerBg

    }
  }

}))
