import React from 'react'
import useStyles from './style'
import Switch from '@mui/material/Switch'
import { Grid } from '@mui/material'

const CustomSwitch = ({ checked, onChange }) => {
  const classes = useStyles()
  const label = { inputProps: { 'aria-label': 'Color switch demo' } }

  return (
    <Grid className={classes.themeSwitch}>
      <Switch
        {...label}
        checked={checked}
        onChange={onChange}
        color='secondary'
      />
    </Grid>
  )
}

export default CustomSwitch
