// useScratchCardStyles.js

import { makeStyles } from '@mui/styles'

import scratchBg from '../../components/ui-kit/icons/webp/scratch-bg.webp'

const useScratchCardStyles = makeStyles((theme) => ({
  modalWrapper: {
    backgroundColor: 'transparent',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: theme.spacing(2),
    justifyContent: 'center',
    maxWidth: '450px',
    [theme.breakpoints.down('md')]: {
      padding: '1.5rem 0.5rem !important'
    },
    '& .close-icon': {
      position: 'absolute',
      top: '1rem',
      right: '1rem',
      fill: '#fff',
      cursor: 'pointer'
    },
    '& h4': {
      color: '#FDB72E',
      marginTop: '2rem',
      fontSize: '1.5rem',
      textAlign: 'center',
      fontWeight: '700'
    },
    '& p': {
      color: '#fff',
      textAlign: 'center',
      fontSize: '1.25rem',
      fontWeight: '700'
    }
  },
  heading: {
    color: '#fff',
    fontSize: '2.5rem',
    fontWeight: '700 !important',
    background: 'linear-gradient(180deg, #FFEA94 16.67%, #FFA538 80.43%)',
    WebkitBackgroundClip: 'text',
    textShadow: '0px 4.09px 6.13px #A3A1A199',
    WebkitTextFillColor: 'transparent',
    marginBottom: '2rem !important',
    textAlign: 'center',
    [theme.breakpoints.down('md')]: {
      fontSize: '2rem !important',
      marginBottom: '1rem !important'
    }
  },
  cardWrapper: {
    width: 300,
    height: 250,
    position: 'relative',
    borderRadius: 24,
    cursor: 'grab',
    overflow: 'hidden',
    userSelect: 'none',
    border: '3px solid #fff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    [theme.breakpoints.down('md')]: {
      transform: 'scale(0.9)'
    }
  },
  rewardText: {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '4rem',
    textAlign: 'center',
    lineHeight: '1',
    fontWeight: 'bold',
    textShadow: '0px 4.04px 10.04px #00000099',
    overflow: 'hidden',
    color: '#FDB72E',
    [theme.breakpoints.down('md')]: {
      fontSize: '2rem !important'
    }
  },
  scratchCardRoot: {
    position: 'relative',
    width: '100%',
    backgroundImage: `url(${scratchBg})`,
    borderRadius: '20px',
    height: '100%'
  },

  canvasFix: {
    position: 'absolute !important',
    top: 0,
    left: 0,
    width: '100% !important',
    height: '100% !important'
  }
}))

export default useScratchCardStyles
