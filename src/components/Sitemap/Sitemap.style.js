

import { makeStyles } from '@mui/styles'

import { Container, LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
    lobbyRight: {
        ...LobbyRight(theme),
        minHeight: 'auto',

        [theme.breakpoints.down('sm')]: {
            marginTop: '0',
        },
    },

    wrapper: {
        ...Container(theme),
        "& .border-bottom": {
            borderBottom: '1px solid #545454',
        },
        "& .border": {
            borderTop: '1px solid #545454',
            borderBottom: '1px solid #545454',
            marginLeft: theme.spacing(0),
            width: '100%',
            [theme.breakpoints.down('sm')]: {
                borderTop: '0',
            },
        },
        "& .footerBottomDetail": {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '20px',
            padding: '20px',
            flexDirection: 'column',
            // color: 'rgb(255 255 255 / 30%)',
            color: '#fff',
            [theme.breakpoints.down('md')]: {
                marginBottom: '40px',
                padding: '20px 0',
            },
        },
        '& .social-links': {
            "& a": {
                display: "inline-block",
                padding: theme.spacing(0.313),
                "& img": {
                    width: theme.spacing(2),
                }
            }
        },
        '& .footerBottomText': {
            borderBottom: '1px solid #545454',
            padding: "2rem 0",
            "& p": {
                color: theme.colors.footerText,
                fontWeight: 400
            },
            "& .footer-contact": {
                marginTop: theme.spacing(2),
                "& a": {

                }
            },
            "& .payment-grid": {
                display: "grid",
                gridTemplateColumns: 'repeat(6, 1fr)',
                gap: theme.spacing(0.400)
            },
            "& .MuiGrid-container": {
                alignItems: "center"
            }
        },
        "& a": {
            color: theme.colors.textWhite,
            textDecoration: 'none',
        },
        "& .footerDropdownParent": {
            [theme.breakpoints.down('md')]: {
                flexDirection: 'column',
            },
        },
        "& .support-link": {
            fontSize: theme.spacing(1.125)
        },

    },
    siteMapWrap: {
        ul: { listStyleType: "none", padding: "0" },
        li: { margin: "0.5rem 0" },
        a: { textDecoration: "none", color: "#007bff" },
        "a:hover": { textDecoration: "underline" }
    }


}))