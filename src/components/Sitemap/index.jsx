import React from 'react';
import { Link } from 'react-router-dom';
import useStyles from './Sitemap.style'
import { Grid } from '@mui/material'
import { appRoutes } from '../../App.routes';
const Sitemap = () => {
    const classes = useStyles()
  return (
      <Grid className={classes.lobbyRight}>
          <Grid className={classes.wrapper}>
              <Grid container spacing={2}>
                  <Grid className={classes.siteMapWrap} >
                      <h1>Sitemap</h1>
                      <ul>
                          {appRoutes.map((route, index) => (
                              <li key={index}>
                                  <Link to={route.path}>{route.name}</Link>
                              </li>
                          ))}
                      </ul>
                  </Grid>
              </Grid>
          </Grid>
      </Grid>
  );
};

export default Sitemap;