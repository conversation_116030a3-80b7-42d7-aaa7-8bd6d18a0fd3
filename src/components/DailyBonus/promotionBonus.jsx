import React, { useState } from 'react'
import { Button, <PERSON>rid, IconButton, Typography, DialogContent, CircularProgress } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import useStyles from '../../components/DailyBonus/Bonus.styles'
import {
  useClaimPromotionBonusMutation,
  useGetDailyBonusMutation,
  useGetWelcomeBonusMutation
} from '../../reactQuery/bonusQuery'
import { toast } from 'react-hot-toast'
import { useGetProfileMutation } from '../../reactQuery'
// import { usdIcon, usdchipIcon } from '../ui-kit/icons/svg'
import { usdIcon, usdchipIcon } from '../ui-kit/icons/opImages'
import { useUserStore } from '../../store/useUserSlice'
import { usePortalStore } from '../../store/userPortalSlice'
import { customEvent } from '../../utils/optimoveHelper'
import SpecialPurchaseModal from '../SpecialPurchaseModal'
import { getCookie } from '../../utils/cookiesCollection'
import WelcomeBonus from '../WelcomeBonus'
import DailyBonus from '.'
import useSeon from '../../utils/useSeon'
import TagManager from 'react-gtm-module'

const PromotionBonus = (props) => {
  const classes = useStyles()
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const portalStore = usePortalStore((state) => state)
  const [promotionBonus, setPromotionBonus] = useState(props.promotionBonusData) // eslint-disable-line
  const [isLoading, setIsLoading] = React.useState(false)
  const pathCookie = getCookie('path')
  const sessionId = useSeon()

  const handleClose = () => {
    portalStore.closePortal()
  }

  const mutationGetWelcomeBonus = useGetWelcomeBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(() => <WelcomeBonus welcomeBonusData={res?.data?.data} />, 'bonusModal')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => console.log(error)
  })

  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      if (res?.data?.data) {
        let resetData = res?.data?.data?.remainingTime
        portalStore.openPortal(() => <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />, 'bonusStreak')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => console.log(error)
  })


  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      const userData = res?.data?.data
      setUserDetails(userData)
      portalStore.closePortal()
      if (pathCookie) {
        if (userData?.isWelcomeBonusAllowed && !userData?.isWelcomeBonusClaimed) {
          mutationGetWelcomeBonus.mutate()
        } else if (userData?.isDailyBonusAllowed && !userData?.isDailyBonusClaimed) {
          mutationGetDailyBonus.mutate()
        } else if (userData && userData?.welcomePurchaseBonusApplicable) {
          portalStore.openPortal(() => <SpecialPurchaseModal />, 'termsNConditionModal')
        } 
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const mutationClaimPromotionBonus = useClaimPromotionBonusMutation({
    onSuccess: (res) => {
      const userWallet = res?.data?.userWallet
      TagManager.dataLayer({
        dataLayer: {
          event: 'bonus',
          bonus_type: 'promotion_bonus',
          user_id: userWallet?.ownerId,
          gcCoin: userWallet?.gcCoin,
          scCoin: userWallet?.totalScCoin
        }
      })
      const parameters = {
        bonus_type: 'promotion_bonus',
        gcCoin: userWallet?.gcCoin,
        scCoin: userWallet?.totalScCoin
      }
      if (import.meta.env.VITE_NODE_ENV === 'production') {
        customEvent('claim_promotion_bonus', parameters, userWallet?.ownerId)
      }
      localStorage.setItem('allowedUserAccess', true)
      setIsLoading(false)
      handleClose()
      toast.success(res?.data?.message)
      getProfileMutation.mutate()
    },
    onError: (error) => {
      console.log(error)
      portalStore.closePortal()
    }
  })

  const claimPromotionBonus = () => {
    const data = { sessionKey: sessionId, rtyuioo: sessionId === ' ' }
    setIsLoading(true)
    mutationClaimPromotionBonus.mutate(data)
  }

  return (
    <Grid className={classes.bonusModalWrap}>
      <Grid className='modal-close'>
        <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
          <CloseIcon />
        </IconButton>
      </Grid>

      <DialogContent>
        <Grid className='bonus-modal-img-wrap'>
          <img src={promotionBonus?.imageUrl} alt='Bonus' />
        </Grid>
        <Grid className='bonus-modal-details'>
          <Grid className='bonus-coins-wrap'>
            <Grid>
              <img src={usdchipIcon} alt='chipCoinIcon' />
              <Typography variant='h4'>{promotionBonus?.gcAmount} GC</Typography>
            </Grid>
            <Grid>
              <img src={usdIcon} alt='Coins' />
              <Typography variant='h4'>{promotionBonus?.scAmount} SC</Typography>
            </Grid>
          </Grid>
          <Typography>{promotionBonus?.description}</Typography>
          <Button
            variant='contained'
            className='btn-primary'
            onClick={() => claimPromotionBonus()}
            disabled={isLoading}
          >
            {promotionBonus?.btnText}
            {isLoading && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
          </Button>
          <Grid className='bonus-terms'>
            <Typography>{promotionBonus?.termCondition?.EN}</Typography>
          </Grid>
        </Grid>
      </DialogContent>
    </Grid>
  )
}
export default PromotionBonus
