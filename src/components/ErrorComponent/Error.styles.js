import { makeStyles } from '@mui/styles'

import { MaintenanceBg } from '../ui-kit/icons/webp'

export default makeStyles((theme) => ({
  errorPageWrap: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    width: '100%',
    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(1)
    },
    '& .error-content-wrap': {
      backgroundImage: `url(${MaintenanceBg})`,
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat',
      padding: theme.spacing(2),
      textAlign: 'center',
      minWidth: theme.spacing(32.5625),
      borderRadius: theme.spacing(0.625),
      [theme.breakpoints.down('sm')]: {
        minWidth: '100%'
      },
      '& h1': {
        marginBottom: theme.spacing(0.9375),
        color: theme.colors.errorTextYellow,
        fontSize: theme.spacing(1.625),
        fontWeight: theme.typography.fontWeightExtraBold,
        lineHeight: theme.spacing(1.5625)
      },
      '& p': {
        fontSize: theme.spacing(0.9375),
        fontWeight: theme.typography.fontWeightBold,
        '&.more-details-cta': {
          marginTop: theme.spacing(1.125),
          cursor: 'pointer'
        }
      },
      '& .MuiButton-text': {
        fontSize: theme.spacing(1.25),
        fontWeight: theme.typography.fontWeightExtraBold,
        color: theme.colors.greenText,
        textDecorationColor: theme.colors.greenText,
        marginTop: theme.spacing(0.313),
        display: 'inline-block',
        textDecoration: 'underline'
      },
      '& .error-icon': {
        textAlign: 'center',
        margin: theme.spacing(1, 0, 2),
        '& img': {
          width: theme.spacing(18.75),
          [theme.breakpoints.down('sm')]: {
            width: '100%'
          }
        }
      }
    }
  }
}))
