import { Box, Grid, Typography, Button } from '@mui/material'
import React, { useEffect } from 'react'
import './Error.css'
import  ErrorIcon  from '../ui-kit/icons/svg/error-page.svg'
import { useNavigate } from 'react-router-dom'
import TagManager from 'react-gtm-module'

const extractErrorDetails = (error) => {
  const stack = error.stack || ''
  const message = error.message || 'Unknown error'

  // Regex patterns for different browsers
  const regexPatterns = [
    /\((.*):(\d+):(\d+)\)/, // Chrome/Edge/Safari
    /at (.+):(\d+):(\d+)/, // Fallback for others
    /@(.*):(\d+):(\d+)/ // Firefox
  ]

  let match = null

  // Try all patterns to extract details
  for (const regex of regexPatterns) {
    match = stack.match(regex)
    if (match) break
  }
  if (match) {
    const file = match[1]
    const line = match[2]
    const column = match[3]
    return { message, file, line, column }
  }

  return { message, file: 'Unknown', line: 'Unknown', column: 'Unknown' }
}

const ErrorComponent = ({ resetErrorBoundary, error }) => {
  const navigate = useNavigate()

  useEffect(() => {
    const { message, file, line } = extractErrorDetails(error)

    TagManager.dataLayer({
      dataLayer: {
        event: 'js_error',
        error_text: message,
        error_line: line
      }
    })
  }, [])

  const handleRefresh = () => {
    // resetBoundary()
    resetErrorBoundary()
    navigate('/')
  }

  return (
    <Box className='errorPageWrap'>
      <Grid className='error-content-wrap'>
        <Grid className='error-icon'>
          <img src={ErrorIcon} alt='Error' />
        </Grid>
        <Typography variant='h1'>Something Went Wrong</Typography>
        <Typography>We can’t get that information right now. Please try again later.</Typography>
        <Typography className='more-details-cta'>More Details.....</Typography>
        {/* <Link href="/" >Refresh the page</Link> */}
        <Button onClick={handleRefresh}>Refresh the page</Button>
      </Grid>
    </Box>
  )
}


export default ErrorComponent
