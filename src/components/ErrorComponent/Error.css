.errorPageWrap {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100%;
    padding: 16px; /* Use a fallback value for 'sm' breakpoint */
}

.errorPageWrap .error-content-wrap {
    background-size: cover;
    background-repeat: no-repeat;
    padding: 16px; /* Example value */
    text-align: center;
    min-width: 260px; /* Replace with appropriate value */
    border-radius: 5px; /* Replace with actual theme value */
}

@media (max-width: 600px) {
    .errorPageWrap {
        padding: 8px; /* Adjust as needed */
    }

    .errorPageWrap .error-content-wrap {
        min-width: 100%;
    }

    .errorPageWrap .error-content-wrap .error-icon img {
        width: 100%;
    }
}

.errorPageWrap .error-content-wrap h1 {
    margin-bottom: 15px; /* Replace with calculated theme value */
    color: #FFD700; /* Replace with actual color from theme */
    font-size: 26px; /* Replace with calculated theme value */
    font-weight: 800; /* Example value */
    line-height: 25px; /* Replace with calculated theme value */
}

.errorPageWrap .error-content-wrap p {
    font-size: 15px; /* Replace with theme value */
    font-weight: 700; /* Replace with theme value */
}

.errorPageWrap .error-content-wrap p.more-details-cta {
    margin-top: 18px; /* Replace with calculated value */
    cursor: pointer;
}

.errorPageWrap .error-content-wrap .MuiButton-text {
    font-size: 20px; /* Replace with theme value */
    font-weight: 800; /* Replace with theme value */
    color: #00FF00; /* Replace with theme value */
    text-decoration: underline;
    margin-top: 5px; /* Replace with calculated theme value */
    display: inline-block;
}

.errorPageWrap .error-content-wrap .error-icon {
    text-align: center;
    margin: 8px 0 16px; /* Replace with calculated values */
}

.errorPageWrap .error-content-wrap .error-icon img {
    width: 150px; /* Replace with calculated theme value */
}