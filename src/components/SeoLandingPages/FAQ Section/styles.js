import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  faqWrapper: {
    maxWidth: '93rem',
    margin: '3rem auto',
    padding: '3rem 1rem',
    textAlign: 'center',
    // Simple mobile optimizations
    [theme.breakpoints.down('md')]: {
      margin: '2rem auto 1rem auto',
      padding: '1rem'
    },
    '& h1': {
      fontWeight: 700,
      fontSize: '3rem',
      marginTop: '2rem',
      marginBottom: '1rem',
      color: theme.colors.YellowishOrange,
      [theme.breakpoints.down('md')]: {
        fontSize: '1.5rem',
        marginBottom: '1rem'
      }
    },
    '& h4': {
      fontWeight: 600,
      fontSize: '1.5rem',
      margin: '2rem auto',
      maxWidth: '700px',
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        fontSize: '0.875rem'
      }
      // color: theme.colors.YellowishOrange
    },
    '& .MuiAccordion-root': {
      border: '1px solid transparent',
      margin: '0 0 1rem 0 !important',
      [theme.breakpoints.down('md')]: {
        margin: '0 0 0.75rem 0 !important'
      },
      '&.Mui-expanded': {
        border: '1px solid #494949'
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        backgroundColor: '#292929 !important',
        borderRadius: '0.625rem',
        [theme.breakpoints.down('md')]: {
          padding: '0 1rem'
        },
        '& h2': {
          fontSize: '1.625rem',
          textAlign: 'left',
          fontWeight: '600',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '&.Mui-expanded': {
          color: theme.colors.YellowishOrange,
          minHeight: '50px'
        }
      },
      '& .MuiAccordionSummary-content': {
        margin: '1rem 0',
        '&.Mui-expanded': {
          margin: '1rem 0'
        }
      },
      '& .MuiAccordionDetails-root': {
        paddingLeft: '4.5rem',
        [theme.breakpoints.down('md')]: {
          paddingLeft: '3.5rem',
          padding: '1rem'
        },
        '& p': {
          fontSize: '1.5rem',
          fontWeight: '500',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        }
      }
    }
  },
  footer: {
    margin: '1rem'
  }
}))
