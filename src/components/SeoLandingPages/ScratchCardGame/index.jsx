import React, { useState } from 'react'
import { Accordion, AccordionDetails, AccordionSummary, Box, Grid, Typography } from '@mui/material'
import { Add, Remove } from '@mui/icons-material'
import useStyles from '../styles.js'
// import LandingHeader from '../LandingHeader/index.jsx'

import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { Link } from 'react-router-dom'
import { usePortalStore } from '../../../store/userPortalSlice.js'
import Signup from '../../Modal/Signup/index.jsx'
import SeoLandingFooter from '../SeoLandingFooter/index.jsx'
import LandingHeader from '../../../pages/Landing/LandingHeader/index.jsx'
import SeoHead from '../../../utils/seoHead.jsx'

const ScratchCardGames = () => {
  const [expanded, setExpanded] = useState(false)
  const classes = useStyles()
  const [expandedFaq, setExpandedFaq] = useState(false)

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false)
  }
  const handleFaqChange = () => {
    setExpandedFaq((prev) => !prev)
  }
  const portalStore = usePortalStore()
  const handleGameClick = () => {
    portalStore.openPortal(() => <Signup />, 'loginModal')
  }
  const faqs = [
    {
      question: '1. Are these real-money scratch card games?',
      answer:
        ' No real-money betting here. We use a sweepstakes model where you can play with Gold Coins (just for fun) or Sweepstakes Coins that give you the chance to win real prizes — legally, with no purchase required.'
    },
    {
      question: '2. Do I need to download anything?',
      answer:
        'Nope! All our scratch card games free online are browser-based. Just log in and play on mobile, desktop, or tablet.'
    },
    {
      question: '3. Can I really win prizes with online scratch cards?',
      answer:
        ' Yes! Play with Sweepstakes Coins and you could win gift cards, cash equivalents, and more — all through our legal sweepstakes system.'
    },
    {
      question: '4. What’s the best scratch card game for beginners?',
      answer:
        '  Try Lucky Clover Card or Wild 7s Scratch. They’re simple, fun, and give a great introduction to how online scratchers work.'
    }
  ]
  return (
    <>
      {/* <Helmet>
        <title>Play Online Casino Scratch Cards – Win Instantly!</title>
        <meta
          name='description'
          content='Play online casino scratch cards and win instantly! Enjoy fun scratch card games with exciting prizes and no deposit required.'
        />
        <meta name='robots' content='index, follow' />
      </Helmet> */}
       <SeoHead
        title='Play Online Casino Scratch Cards – Win Instantly!'
        description='Play online casino scratch cards and win instantly! Enjoy fun scratch card games with exciting prizes and no deposit required.'
        // keywords={['about us', 'company', 'mission', 'team']}
        // url='https://www.themoneyfactory.com/games/online-casino-scratch-card-games'
        // imageUrl='https://testttyourdomain.com/images/about-us.jpg'
      />
      <Grid className={`${classes.scratchCardWrap} ${classes.subgamePage}`}>
        <LandingHeader />
        <Box className='jackpot-cards-wrap'>
          <Typography variant='h1'>Try Your Luck with Online Scratch Card Games</Typography>
          <Grid className='cards-wrap'>
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/fist-of-destruction.webp`}
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/slayer-inc.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/big-bass.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/elven-princesses.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/buffalo-king.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/jokers-jewel.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/coin-volcano.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/sky-pearls.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/gates-of-olympus.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/great-rhino.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/gates-of-olympus.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/sugar-rush.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/black-wolf.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/outlaws-inc.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = ' ../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
          </Grid>
        </Box>
        <Typography variant='body1'>
          Fast. Fun. Free. Scratch your way to real rewards with no-risk,{' '}
          <Typography variant='span' style={{ fontWeight: '700' }}>
            legal online casino
          </Typography>{' '}
          scratch cards at The Money Factory. Whether you’re a casual gamer or a jackpot chaser, online scratch cards
          offer instant entertainment and excitement, no complicated rules, no downloads, and no real-money bets. Just
          pure scratch-and-win gameplay with Sweepstakes Coins that give you a shot at real prizes, legally and 100%
          free.
        </Typography>
        <Typography variant='body1' className='space-down'>
          At{' '}
          <Link
            target='_blank'
            style={{ color: '#23A8FC', textDecorationColor: '#23A8FC' }}
            to='https://www.themoneyfactory.com/'
          >
            The Money Factory
          </Link>
          , we’ve brought the nostalgic thrill of paper scratch-offs into a digital sweepstakes casino environment
          that’s easy to use and always available. Play on desktop or mobile, anytime you’re ready for a quick win.
        </Typography>
        <Typography variant='h2'>What Are Online Casino Scratch Cards?</Typography>
        <Typography variant='body1'>
          Online scratch card games are digital versions of the classic lottery-style tickets you know and love, only
          now they come with more themes, more features, and even more chances to win.
        </Typography>
        <Typography variant='body1'>
          Each card has a hidden prize grid. Tap or click to reveal symbols, and if you match three of a kind (or hit
          special icons), you score an instant win. Some cards even include bonus rounds, mini-games, or multiplier
          effects that add a whole new layer of fun.
        </Typography>
        <Typography variant='body1'>
          Think of them as the perfect combo of luck, suspense, and simplicity — all in under 10 seconds per game.
        </Typography>
        <Typography variant='h2'>Why Play Scratch Cards Online at The Money Factory?</Typography>
        <Typography variant='body1' className='space-down'>
          Not all scratch cards online are created equal. Here’s why thousands of U.S. players trust{' '}
          <Link
            target='_blank'
            style={{ color: '#23A8FC', textDecorationColor: '#23A8FC' }}
            to='https://www.themoneyfactory.com/'
          >
            {' '}
            The Money Factory
          </Link>{' '}
          for their free online casino scratch card fix:
        </Typography>
        <Box className='pointer-box'>
          <Typography variant='h5' className='pointer'>
            100% Legal & No-Risk
          </Typography>
          <Typography variant='body1' className='space-down'>
            We run on a sweepstakes model, which means no real-money gambling. Use Gold Coins for fun or play with
            Sweepstakes Coins to win real prizes, legally available across the U.S.
          </Typography>
        </Box>
        <Box className='pointer-box'>
          <Typography variant='h5' className='pointer'>
            Instant Win Thrills
          </Typography>
          <Typography variant='body1' className='space-down'>
            Each scratch takes just seconds, no long waits, no complicated rules. Just fast results and fast rewards.
          </Typography>
        </Box>
        <Box className='pointer-box'>
          <Typography variant='h5' className='pointer'>
            Big Variety, Bigger Rewards
          </Typography>
          <Typography variant='body1' className='space-down'>
            From classic scratchers to modern animated versions, we offer a wide variety of themes and prize types. Some
            cards have instant bonus rounds, others reveal wild symbols or even jackpot tiers.
          </Typography>
        </Box>
        <Box className='pointer-box'>
          <Typography variant='h5' className='pointer'>
            Free Welcome Bonus
          </Typography>
          <Typography variant='body1'>New players get:</Typography>
          <Box className='list-points'>
            <Typography variant='body1' className='white-pointer'>
              15,000 Gold Coins to start playing instantly
            </Typography>
            <Typography variant='body1' className='white-pointer'>
              3 Sweepstakes Coins after verifying your email
            </Typography>
            <Typography variant='body1' className='white-pointer space-down'>
              Ongoing access to giveaways, rewards, and daily scratchable bonuses
            </Typography>
          </Box>
        </Box>
        <Box className='pointer-box'>
          <Typography variant='h5' className='pointer'>
            No App. No Download
          </Typography>
          <Typography variant='body1'>
            Play directly in your browser. Whether you’re on your phone during a break or relaxing at home on a laptop,
            our online scratch card games are ready to go whenever you are.
          </Typography>
        </Box>

        {/* Accordion Section */}
        <Accordion onChange={handleChange('panel1')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              Our Most Popular Online Scratch Cards
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant='body1'>
              We’re always adding new scratch cards online casino players love. Here are a few top picks:
            </Typography>
            <Typography className='yellow-pointer' variant='body1'>
              <Typography variant='span' className='yellow-text'>
                Gold Rush Scratch
              </Typography>{' '}
              – Dig up wins with a wild west theme and bonus treasure chests.
            </Typography>
            <Typography className='yellow-pointer' variant='body1'>
              <Typography variant='span' className='yellow-text'>
                Lucky Clover Card
              </Typography>{' '}
              – A touch of Irish luck with 3x multipliers and mystery symbols.
            </Typography>
            <Typography className='yellow-pointer' variant='body1'>
              <Typography variant='span' className='yellow-text'>
                Cash Carnival
              </Typography>{' '}
              – Bright, fast, and loaded with mini-games hidden behind the scenes.
            </Typography>
            <Typography className='yellow-pointer' variant='body1'>
              <Typography variant='span' className='yellow-text'>
                Wild 7s Scratch
              </Typography>{' '}
              – Classic feel with modern animation and a chance to hit the jackpot.
            </Typography>
            <Typography variant='body1' className='space-down'>
              Every card comes with its own vibe, some are straightforward, some more interactive but all offer that
              satisfying reveal-and-win experience.
            </Typography>
            <Typography variant='h2'>How to Start Playing Free Scratch Card Games Online</Typography>
            <Typography variant='body1'>It only takes a couple of clicks to get started:</Typography>
            <Typography variant='body1' style={{ marginBottom: '0' }}>
              1.{' '}
              <Link onClick={handleGameClick} style={{ color: '#23A8FC', textDecorationColor: '#23A8FC' }}>
                Sign up for free
              </Link>{' '}
              at The Money Factory
            </Typography>
            <Typography variant='body1' style={{ marginBottom: '0' }}>
              2.{' '}
              <Typography variant='span' className='yellow-text'>
                Verify your email
              </Typography>{' '}
              to unlock your welcome bonus
            </Typography>
            <Typography variant='body1' style={{ marginBottom: '0' }}>
              3. Go to the{' '}
              <Typography variant='span' className='yellow-text'>
                {' '}
                Scratch Card Games{' '}
              </Typography>{' '}
              section
            </Typography>
            <Typography variant='body1' style={{ marginBottom: '0' }}>
              4. Start scratching and see what you reveal
            </Typography>
            <Typography variant='body1' className='space-down'>
              It’s fast, easy, and completely free — no banking info needed.
            </Typography>
            <Typography variant='h2'>Tips to Make the Most of Your Online Scratch Cards</Typography>
            <Typography variant='body1'>
              New to online scratchers? Here are a few simple tips to stretch your gameplay and boost your odds:
            </Typography>
            <Typography className='yellow-pointer' variant='body1'>
              <Typography variant='span' className='yellow-text'>
                Use Gold Coins first
              </Typography>{' '}
              to try out different scratch cards before using Sweepstakes Coins.
            </Typography>
            <Typography className='yellow-pointer' variant='body1'>
              <Typography variant='span' className='yellow-text'>
                Look for bonus rounds
              </Typography>{' '}
              – some scratch cards offer hidden games that lead to higher payouts.
            </Typography>
            <Typography className='yellow-pointer' variant='body1'>
              <Typography variant='span' className='yellow-text'>
                Play daily
              </Typography>{' '}
              – Many scratch cards come with daily bonuses or extra chances just for logging in.
            </Typography>
            <Typography className='yellow-pointer' variant='body1'>
              <Typography variant='span' className='yellow-text'>
                Watch for seasonal events
              </Typography>{' '}
              – Limited-edition scratchers with higher value symbols often launch during holidays or promotions.
            </Typography>
          </AccordionDetails>
        </Accordion>

        <Box className='faq-wrap'>
          <Typography variant='h3'>Frequently Ask Questions</Typography>
          {faqs.map((faq, index) => (
            <Accordion key={index} expanded={expanded === index} onChange={handleChange(index)}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>{faq.question}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                {Array.isArray(faq.answer) ? (
                  faq.answer.map((line, i) => (
                    <Typography key={i} variant='body1'>
                      {line}
                    </Typography>
                  ))
                ) : (
                  <Typography variant='body1'>{faq.answer}</Typography>
                )}
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>

        <Typography variant='h2'>Start Scratching. Start Winning. Start Now.</Typography>
        <Typography variant='body1'>
          No pressure. No downloads. No risk. Just tap, scratch, and win — anytime, anywhere.
        </Typography>
        <Typography variant='body1' className='space-down'>
          Join{' '}
          <Link
            target='_blank'
            style={{ color: '#23A8FC', textDecorationColor: '#23A8FC' }}
            to='https://www.themoneyfactory.com/'
          >
            {' '}
            The Money Factory
          </Link>{' '}
          today and try your luck with our online casino scratch cards. Your welcome bonus is just a click away.
        </Typography>
        <SeoLandingFooter />
      </Grid>
    </>
  )
}

export default ScratchCardGames
