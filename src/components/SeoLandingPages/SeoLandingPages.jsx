import React, { useRef, useEffect } from 'react'
import useStyles from './styles'
import { Button, Grid, Typography, Box } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import Signup from '../../components/Modal/Signup'
import { useNavigate } from 'react-router-dom'
import { useUserStore } from '../../store/useUserSlice'

import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'
import { Navigation, Pagination } from 'swiper/modules'
import LobbyFilter from '../../pages/LandingPage/LobbyFilter'
import useSubCategory from '../../pages/Lobby/hooks/useSubCategory'
import 'swiper/css/pagination'
import { fist, whitePlay } from '../ui-kit/icons/svg'
import { buffKing } from '../ui-kit/icons/opImages'
import SeoLandingFooter from './SeoLandingFooter'
import LandingHeader from '../../pages/Landing/LandingHeader'

const SeoLandingPage = () => {
  const portalStore = usePortalStore()
  const classes = useStyles()
  const navigate = useNavigate()
  const user = useUserStore((state) => state)
  const handlaeClaimWelcomeOffer = () => {
    portalStore.openPortal(() => <Signup />, 'loginModal')
  }
  const images = new Array(5).fill('CasinoCard')
  const swiperRef = useRef(null)
  useEffect(() => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slideTo(0)
    }
  }, [])

  const goToPrev = () => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slidePrev()
    }
  }

  const goToNext = () => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slideNext()
    }
  }

  const { setSubCategoryName, setPageNo, setGameData, featuredSubcategory, gameData } = useSubCategory()

  return (
    <Grid className={classes.landingPageWrap}>
      <>
        <LandingHeader />

        <Grid className='testimonials-card-wrap'>
          <Grid className='inner-heading'>
            <Typography variant='h3'>Hot Games</Typography>
            {/* <Typography>Popular games or special offers for guests can be placed here</Typography> */}
          </Grid>
          <Swiper
            grabCursor={true}
            centeredSlides={false}
            loop={true} // Enable looping
            pagination={{ clickable: true }}
            navigation={true}
            modules={[Navigation, Pagination]}
            spaceBetween={30}
            className='mySwiper'
            breakpoints={{
              0: {
                slidesPerView: 3,
                spaceBetween: 10,
                centeredSlides: true
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 10,
                centeredSlides: false
              },
              1024: {
                slidesPerView: 4.5,
                spaceBetween: 20,
                centeredSlides: false
              }
            }}
          >
            {gameData.map((image, index) => (
              <SwiperSlide>
                <Grid className='testimonials-card'>
                  <img key={index} src={image.imageUrl || fist} alt='Jackpot Image' />
                </Grid>
              </SwiperSlide>
            ))}
          </Swiper>
        </Grid>
        <Box className='flter-wrap'>
          <Grid className='inner-container'>
            <Grid className='filter-section'>
              <LobbyFilter
                featuredSubcategory={featuredSubcategory}
                setSubCategoryName={setSubCategoryName}
                setPageNo={setPageNo}
                setGameData={setGameData}
                hide={true}
              />
            </Grid>
          </Grid>
        </Box>

        <Box className={classes.hotGamesWrap}>
          <Grid className='inner-container'>
            <Grid className='inner-heading'>
              <Typography variant='h4' component='h4'>
                Hot
              </Typography>
            </Grid>
            <Grid className='hot-games-details'>
              <Typography>
                Our social casino offers a diverse selection of popular casino games and slots, each packed with
                exciting features, engaging themes, and thrilling jackpots.
              </Typography>
              <Typography>
                Whether you're a seasoned player or new to the scene, The Money Factory provides an unmatched gaming experience.
                Play free online popular casino games right here!
              </Typography>
            </Grid>
            <Grid className='game-grid-wrap'>
              <Grid className='slider-bnt-wrap'>
                <Button className='swiper-button-prev' onClick={goToPrev}></Button>
                <Button className='swiper-button-next' onClick={goToNext}></Button>
              </Grid>
              <Swiper
                spaceBetween={20} // Space between slides
                slidesPerView={5} // Number of slides visible
                loop={true} // Enable looping
                autoplay={{ delay: 3000 }} // Autoplay slides
                breakpoints={{
                  0: {
                    slidesPerView: 3,
                    spaceBetween: 10,
                    centeredSlides: true
                  },
                  768: { slidesPerView: 4 },
                  1024: { slidesPerView: 5 },
                  1400: { slidesPerView: 5 }
                }} // Responsive breakpoints
              >
                {gameData.map((image, index) => (
                  <SwiperSlide key={index}>
                    <Grid className={classes.casinoCard}>
                      <img src={image.imageUrl || fist} alt='Casino' className='casinoGame-img' loading='lazy' />
                      <Grid className='casino-overlay'>
                        <Typography
                          variant='h6'
                          sx={{
                            lineHeight: '20px',
                            textAlign: 'center',
                            padding: '0 10px'
                          }}
                        >
                          <b>{image.name}</b>
                        </Typography>

                        <img src={whitePlay} alt='Play' className='play-img' onClick={handlaeClaimWelcomeOffer} />

                        <b>Play Now</b>
                      </Grid>
                    </Grid>
                  </SwiperSlide>
                ))}
              </Swiper>
            </Grid>
          </Grid>
        </Box>

        <Box className={classes.jackpotSection}>
          <Grid className='jackpot-content'>
            <Grid className='inner-container'>
              <Grid container spacing={4}>
                <Grid item xs={12} md={7}>
                  <Grid className='inner-heading'>
                    <Typography variant='h4' component='h4'>
                      Jackpots at The Money Factory
                    </Typography>
                    <Typography>
                      Make a fortune in the blink of an eye with our range of progressive jackpot games! From video
                      slots to table and fish games, our local jackpot spans all major game categories and can drop at
                      any time.
                    </Typography>
                    <Typography>
                      So, what are you waiting for? Pick a jackpot title from our vast game collection, and pray that
                      Lady Luck smiles on you today.
                    </Typography>
                  </Grid>
                  <Grid className='jackpot-list-wrap'>
                    <Grid container spacing={1}>
                      <Grid item xs={12} md={6}>
                        <Typography variant='h4' component='h4'>
                          How to Win the Jackpot?
                        </Typography>
                        <Grid className='how-it-works-list-card'>
                          <Typography>
                            There's no big secret to winning the jackpot. It can happen anytime, on any eligible game,
                            and to any player. It doesn't matter if you just joined our casino or have been grinding for
                            months. Our jackpots do not play favorites. Each eligible game features four progressive
                            jackpots that vary in size:
                          </Typography>
                          <Typography>
                            Each eligible game features four progressive jackpots that vary in size:
                          </Typography>
                          <ol>
                            <li>Mini </li>
                            <li>Super</li>
                            <li>Grand</li>
                            <li>Fortune</li>
                          </ol>
                        </Grid>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant='h4' component='h4'>
                          How to Start Playing at Our Jackpot Casino Online
                        </Typography>
                        <Typography>
                          Before playing, you must join our jackpot slots casino and establish a starting balance.
                          Luckily, this takes no more than a couple of minutes and doesn't cost you a dime. Here's a
                          quick step-by-step guide to help bring you up to speed:
                        </Typography>
                        <ol>
                          <li>Hit the Sign Up button, share your email address, and set up a password.</li>
                          <li>
                            Follow the instructions to verify your account. Keep in mind that you can only play if you
                            are located in the U.S. and Canada.
                          </li>
                          <li>Claim our welcome bonus and the daily log-in bonus.</li>
                          <li>
                            Choose whether you want to play with Gold Coins or Sweep Coins via the GC/FC toggle up
                            top.
                          </li>
                          <li>Go to the Jackpots section and pick one of our games.</li>
                          <li>Set up your play level and start chasing that jackpot!</li>
                        </ol>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item xs={12} md={5}>
                  <Grid className='single-game-wrap'>
                    <Grid className={classes.casinoCard}>
                      <img src={buffKing} alt='Casino' className='casinoGame-img' loading='lazy' />
                      <Grid className='casino-overlay'>
                        <Typography
                          variant='h6'
                          sx={{
                            lineHeight: '20px',
                            textAlign: 'center',
                            padding: '0 10px'
                          }}
                        >
                          <b>Game Name</b>
                        </Typography>

                        <img src={whitePlay} alt='Play' className='play-img' />

                        <b>Play Now</b>
                      </Grid>
                    </Grid>
                  </Grid>
                  {/* <img src={CasinoCard} alt='Jackpot Image' style={{ width: '90%', height: '80%', borderRadius: '8px' }} /> */}
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <SeoLandingFooter />
      </>
    </Grid>
  )
}

export default SeoLandingPage
