import React, { useState, useEffect, useRef } from 'react'
import Signin from '../../../components/Modal/Signin'
import Signup from '../../../components/Modal/Signup'
import { BrandLogo } from '../../../components/ui-kit/icons/brand'
import useStyles from './style'
import { Button, Grid, Link } from '@mui/material'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'

import CloseIcon from '@mui/icons-material/Close'
import { menuIcon } from '../../../components/ui-kit/icons/webp'
import { usePortalStore, useSiteLogoStore } from '../../../store/store'
import { useNavigate } from 'react-router-dom'
import { PlayerRoutes } from '../../../routes'

const GamePageHeader = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [selectedSection, setSelectedSection] = useState('')
  const sectionRefs = useRef({})
  const sectionIds = ['how-it-works', 'about', 'hot-games', 'reviews', 'faq'] // Add all section IDs
  const navigate = useNavigate()
  const logoData = useSiteLogoStore((state) => state)
  const handleLogin = () => {
    portalStore.openPortal(() => <Signin />, 'loginModal')
  }
  const handleJoinNow = () => {
    portalStore.openPortal(() => <Signup />, 'signupModal')
  }

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const scrollToSection = (e, id) => {
    e.preventDefault()
    const section = document.getElementById(id)
    if (section) {
      const yOffset = -100
      const yPosition = section.getBoundingClientRect().top + window.pageYOffset + yOffset
      window.scrollTo({ top: yPosition, behavior: 'smooth' })
    }
  }

  // Intersection Observer for dynamic section highlighting
  useEffect(() => {
    const observerCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setSelectedSection(entry.target.id)
        }
      })
    }

    const observerOptions = {
      root: null,
      threshold: 0.3 // Trigger when 30% of the section is visible
    }

    const observer = new IntersectionObserver(observerCallback, observerOptions)

    // Attach observers to all sections
    sectionIds.forEach((id) => {
      const section = document.getElementById(id)
      if (section) {
        observer.observe(section)
        sectionRefs.current[id] = section // Store references
      }
    })

    return () => {
      observer.disconnect()
    }
  }, [sectionIds])

  const handleLinkClick = (e, id) => {
    scrollToSection(e, id)
    toggleMenu()
  }
  return (
    <Grid className={classes.gamePageHeader}>
      <Grid className='landing-header-content'>
        <Link href='#'>
          <img
            src={logoData?.desktopLogo || BrandLogo}
            alt='Logo'
            className='brand-logo'
            onClick={() => navigate(PlayerRoutes.Landing)}
          />
        </Link>
        <Grid className='header-menu-wrap'>
          <Link
            href='#'
            onClick={(e) => scrollToSection(e, 'Updates')}
            // className={`${selectedSection === 'Updates' ? 'active' : ''}`}
            className='active'
          >
            Updates
          </Link>
          <Link
            href='#'
            onClick={(e) => scrollToSection(e, 'about')}
            className={`${selectedSection === 'about' ? 'active' : ''}`}
          >
            Best Casino games
          </Link>
          <Link
            href='#'
            onClick={(e) => scrollToSection(e, 'hot-games')}
            className={`${selectedSection === 'hot-games' ? 'active' : ''}`}
          >
            New casino games
          </Link>
          <Link
            href='#'
            onClick={(e) => scrollToSection(e, 'reviews')}
            className={`${selectedSection === 'reviews' ? 'active' : ''}`}
          >
            Best Slots
          </Link>
          <Link
            href='#'
            onClick={(e) => scrollToSection(e, 'faq')}
            className={`${selectedSection === 'faq' ? 'active' : ''}`}
          >
            New Slots
          </Link>
          <Link
            href='#'
            onClick={(e) => scrollToSection(e, 'faq')}
            className={`${selectedSection === 'faq' ? 'active' : ''}`}
          >
            Casino Guides
          </Link>
        </Grid>
        <Grid className='header-right'>
          <Button type='button' className='btn btn-secondary' onClick={handleLogin}>
            <ArrowCircleRightOutlinedIcon />
            Login
          </Button>
          <Button type='button' className='btn btn-primary' onClick={handleJoinNow}>
            <ArrowCircleRightOutlinedIcon />
            SignUp
          </Button>
        </Grid>
        <Button className={`mobile-toggle-menu ${isMenuOpen ? 'btn-active' : ''}`} onClick={toggleMenu}>
          <img src={menuIcon} className='menu-icon' alt='Menu' />
          {/* <MenuIcon className="menu-icon" /> */}
          <CloseIcon className='cross-icon' />
        </Button>
        <Grid className={`landing-mob-menu ${isMenuOpen ? 'mob-menu-active' : ''}`}>
          <Grid className='menu-wrap'>
            <Link href='#' onClick={(e) => handleLinkClick(e, 'how-it-works')}>
              Updates
            </Link>
            <Link href='#' onClick={(e) => handleLinkClick(e, 'about')}>
              Best Casino games
            </Link>
            <Link href='#' onClick={(e) => handleLinkClick(e, 'hot-games')}>
              New casino games
            </Link>
            <Link href='#' onClick={(e) => handleLinkClick(e, 'reviews')}>
              Best Slots
            </Link>
            <Link href='#' onClick={(e) => handleLinkClick(e, 'faq')}>
              New Slots
            </Link>{' '}
            <Link href='#' onClick={(e) => handleLinkClick(e, 'faq')}>
              Casino Guides
            </Link>
            <Grid className='mob-sidebar-btn'>
              <Button
                type='button'
                className='btn btn-secondary'
                onClick={handleLogin}
                data-tracking='Home.Header.Login.Btn'
              >
                <ArrowCircleRightOutlinedIcon />
                Login
              </Button>
              <Button
                type='button'
                className='btn btn-primary'
                onClick={handleJoinNow}
                data-tracking='Home.Header.JoinNow.Btn'
              >
                <ArrowCircleRightOutlinedIcon />
                SignUp
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default GamePageHeader
