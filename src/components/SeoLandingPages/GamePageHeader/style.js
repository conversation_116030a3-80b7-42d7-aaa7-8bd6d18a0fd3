import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  gamePageHeader: {
    // padding: theme.spacing( 0.625),
    position: 'fixed',
    width: '100%',
    left: '0',
    top: '0',
    zIndex: 2,
    background: '#2C2B2C',
    // backdropFilter: "blur(20px)",
    [theme.breakpoints.down('xl')]: {
      padding: theme.spacing(0.625, 1)
    },
    [theme.breakpoints.down('lg')]: {
      padding: theme.spacing(0.625)
    },

    '& .brand-logo': {
      width: theme.spacing(6.875),
      height: theme.spacing(3.9375),
      // position: "relative",
      // top: theme.spacing(0.313),
      [theme.breakpoints.down('md')]: {
        width: theme.spacing(6.25),
        height: 'auto'
      }
    },
    '& .header-right': {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing(0.625),
      [theme.breakpoints.down('lg')]: {
        display: 'none'
      },
      '& .btn': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(0.313),
        padding: theme.spacing(0.625, 1.875),
        lineHeight: theme.spacing(1.375),
        fontWeight: '600',
        minHeight: theme.spacing(3.125)
      }
    },
    '& .header-menu-wrap': {
      display: 'flex',
      gap: theme.spacing(0.5),
      marginLeft: theme.spacing(3.4375),
      [theme.breakpoints.down('lg')]: {
        display: 'none'
      },
      '& a': {
        padding: theme.spacing(0, 1),
        fontSize: theme.spacing(1.25),
        fontWeight: '600',
        color: theme.colors.textWhite,
        textDecoration: 'none',
        '&:hover': {
          color: theme.colors.YellowishOrange
        },
        '&.active': {
          // color: theme.colors.YellowishOrange,
          position: 'relative',
          '&::before': {
            content: "''",
            position: 'absolute',
            width: '50%',
            bottom: '-2px',
            left: '1rem',
            height: '1px',
            background: '#fff',
            [theme.breakpoints.down(1450)]: {
              left: '0.5rem'
            }
          }
        },
        [theme.breakpoints.down(1450)]: {
          padding: theme.spacing(0, 0.5),
          fontSize: theme.spacing(1)
        }
      }
    },
    '& .landing-mob-menu': {
      position: 'fixed',
      background: theme.colors.textBlack,
      width: '100%',
      height: '100vh',
      left: '0',
      top: theme.spacing(7.5),
      padding: theme.spacing(2, 1),
      display: 'flex',
      alignItems: 'flex-start',
      justifyContent: 'center',
      transform: 'translateY(-300%)',
      transition: 'all 500ms ease-in-out',
      '& a': {
        padding: theme.spacing(0.8, 1),
        fontSize: theme.spacing(1.875),
        fontWeight: theme.typography.fontWeightExtraBold,
        color: theme.colors.textWhite,
        textDecoration: 'none',
        display: 'block',
        textAlign: 'center',
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      },
      '& .mob-sidebar-btn': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing(1),
        padding: theme.spacing(1, 0),
        '& .btn': {
          gap: theme.spacing(0.2)
        }
      },
      '&.mob-menu-active': {
        transform: 'translateY(-0px)',
        [theme.breakpoints.down('md')]: {
          transform: 'translateY(-40px)'
        }
      },
      '& .menu-wrap': {
        marginTop: theme.spacing(5.3)
      }
    },
    '& .mobile-toggle-menu': {
      display: 'none',
      [theme.breakpoints.down('lg')]: {
        display: 'flex',
        padding: 0,
        justifyContent: 'flex-end',
        // marginTop: theme.spacing(-0.625)
      },
      [theme.breakpoints.down('md')]: {
        minWidth: '40px !important',
        marginTop: '0 !important'
      },
      '& img': {
        width: theme.spacing(1.8),
        height: theme.spacing(1.8)
      },
      '& .cross-icon': {
        display: 'none'
      },
      '&.btn-active': {
        '& .cross-icon': {
          display: 'block',
          color: theme.colors.textWhite,
          fontSize: theme.spacing(2.4),
          position: 'relative',
          left: theme.spacing(0.375),
          top: theme.spacing(0.313)
        },
        '& .menu-icon': {
          display: 'none'
        }
      }
    },
    '& .landing-header-content': {
      display: 'flex',
      alignItems: 'center',
      maxWidth: theme.spacing(100),
      margin: '0 auto',
      justifyContent: 'space-between',
      padding: theme.spacing(0.313, 0.625),
      [theme.breakpoints.down('lg')]: {
        padding: theme.spacing(0, 0.625)
      }
    }
  }
}))
