import { makeStyles } from '@mui/styles'

import contactUs from "../../../components/ui-kit/icons/opImages/ContactUs.webp"
export default makeStyles((theme) => ({
  contactusWrapper: {
    padding: '8rem 3rem',
    textAlign: 'center',
    width: '100%',
    backgroundImage: `url(${contactUs})`,
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    backgroundSize: 'contain',
    [theme.breakpoints.down('md')]: {
      padding: '6rem 1rem',
      backgroundSize: 'cover'
    },
    '& .contact-wrap': {
      margin: '0 auto',
      maxWidth: '80rem'
    },
    '& h1': {
      fontSize: '3.75rem',
      fontWeight: '700',
      fontFamily: 'Ubuntu',
      color: theme.colors.YellowishOrange,
      [theme.breakpoints.down('md')]: {
        fontSize: '2rem'
      }
    },
    '& h5': {
      fontSize: '1.875rem',
      fontWeight: '700',
      color: theme.colors.textWhite,
      [theme.breakpoints.down('md')]: {
        fontSize: '1.25rem'
      }
    },
    '& .head-p': {
      fontSize: '1.4375rem',
      fontWeight: '600',
      maxWidth: '34.5rem',
      color: theme.colors.textWhite,
      margin: '0 auto',
      marginBottom: '3.75rem',
      [theme.breakpoints.down('md')]: {
        fontSize: '1rem',
        marginBottom: '1.5rem'
      }
    },
    '& .support-box': {
      background: '#1B1B1B80',
      boxShadow: '0px 4px 4px 0px #00000040',
      border: '1px solid #605F5F',
      borderRadius: '20px',
      padding: '2.25rem 2.5rem',
      [theme.breakpoints.down('md')]: {
        padding: '1rem'
      },
      '& .point-wrap': {
        display: 'flex',
        alignItems: 'start',
        gap: '2rem',
        marginBottom: '1.5rem',
        textAlign: 'start',
        [theme.breakpoints.down('md')]: {
          gap: '0.875rem'
        },
        '& h5': {
          fontSize: '1.375rem',
          fontWeight: '600',
          marginBottom: '0.575rem',
          wordBreak: 'break-all',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '& p': {
          fontSize: '0.875rem',
          fontWeight: '500',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.75rem'
          }
        },
        '& .icon-wrap': {
          height: '38px',
          minWidth: '38px',
          maxWidth: '38px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: '40px',
          background: '#FFA538',
          [theme.breakpoints.down('md')]: {
            height: '24px',
            minWidth: '24px',
            maxWidth: '24px',
            '& svg': {
              width: '16px'
            }
          }
        }
      },
      '& .follow-wrap': {
        borderTop: '1px solid #3D3D3D',
        '& h5': {
          fontSize: '1.25rem',
          fontWeight: '600',
          textAlign: 'left',
          paddingTop: '1rem'
        },
        '& .contact-icons': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'start',
          gap: '1rem',
          marginTop: '1rem',
          [theme.breakpoints.down('md')]: {
            gap: '0.75rem',
            '& img': {
              width: '30px'
            }
          }
        }
      }
    },
    '& .contact-box': {
      background: '#121212B2',
      boxShadow: '0px 4px 4px 0px #00000040',
      border: '1px solid #605F5F',
      borderRadius: '20px',
      padding: '2rem 2.5rem',
      [theme.breakpoints.down('md')]: {
        padding: '1.25rem'
      },
      '& h5': {
        fontSize: '1.5625rem',
        fontWeight: '700',
        marginBottom: '1.5rem',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.25rem',
          marginBottom: '1rem'
        }
      },
      '& .MuiFormControl-root': {
        marginBottom: '0.25rem'
      },
      '& .MuiInputBase-input ': {
        border: '1.75px solid #494949',
        borderRadius: '10px',
        color: 'white',
        padding: '8px 12px',
        '&:-webkit-autofill': {
          boxShadow: 'none !important',
          backgroundColor: 'none !important'
        }
      },
      '& .MuiInputBase-root ': {
        padding: '0'
      },
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none'
      },
      '& .MuiFormLabel-root ': {
        color: '#707070',
        top: '-7px',
        fontWeight: '700'
      },
      '& button': {
        maxWidth: '250px',
        width: '100%',
        fontSize: '20px',
        marginTop: '0.5rem',
        margin: '0 auto',
        [theme.breakpoints.down('md')]: {
          padding: '0.25rem',
          fontSize: '14px'
        }
      }
    }
  },
  errorLabel: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    lineHeight: 'normal !important',
    fontWeight: '600 !important',
    minHeight: '22px',
    textAlign: 'left',
    padding: '3px'
  }
}))
