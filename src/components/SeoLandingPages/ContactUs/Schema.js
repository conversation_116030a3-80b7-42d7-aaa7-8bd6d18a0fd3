import * as Yup from 'yup'
const onlySpacesRegex = /^\s*$/

const contactUsSchema = Yup.object().shape({
  fullName: Yup.string()
    .required('Name is required')
    .min(3, 'Full Name must be at least 3 characters')
    .max(100, 'Full Name must be at most 100 characters')
    .test(
      'no-leading-trailing-spaces',
      'Full Name should not have leading or trailing spaces',
      (value) => value && value === value.trim()
    )
    .matches(/^(?!\s)(?!.*\s{2,})[a-zA-Z]+(?: [a-zA-Z]+)*$/, 'Only alphabets allowed with single spaces between words'),
  email: Yup.string()
    .max(150, 'Email must be at most 150 characters')
    .required('Email is required')
    .test('no-leading-trailing-spaces', 'Email should not have leading or trailing spaces', (value) => {
      if (!value) return false
      return value.trim() === value
    })
    .test('is-email', 'Invalid email address', (value) => {
      if (!value) return true
      const emailRegex =
        /^(([^<>()[\]\\.,+;:\s@"]+(\.[^<>()[\]\\.,+;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return emailRegex.test(value)
    }),
  message: Yup.string()
    .max(500, 'Message must be at most 500 characters')
    .required('Please enter your message')
    .test('no-only-spaces', 'Message cannot contain only spaces', (value) => {
      return !onlySpacesRegex.test(value)
    })
    .test(
      'no-leading-trailing-spaces',
      'Full Name should not have leading or trailing spaces',
      (value) => value && value === value.trim()
    )
    .transform((value) => value.replace(/\s+/g, ' ').trim())
})

export default contactUsSchema
