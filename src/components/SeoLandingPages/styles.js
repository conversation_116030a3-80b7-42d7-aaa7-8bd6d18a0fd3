import { makeStyles } from '@mui/styles'

import { casinoCard } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  landingPageWrap: {
    margin: '0 auto',
    width: '100%',
    padding: theme.spacing(5, 1, 0),
    flexDirection: 'column',
    background: theme.colors.valutModal,

    '& .inner-heading': {
      marginBottom: theme.spacing(1.25),
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(0)
      },
      '& h4': {
        fontSize: theme.spacing(3),
        fontWeight: '700',
        color: theme.colors.textWhite,
        marginBottom: theme.spacing(1),
        lineHeight: theme.spacing(3.2),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.875)
        }
      },
      '& h3': {
        fontSize: theme.spacing(5),
        fontWeight: '700',
        color: theme.colors.YellowishOrange,
        marginBottom: theme.spacing(1),
        lineHeight: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(2)
        }
      },
      '& p': {
        fontSize: theme.spacing(1.5),
        fontWeight: theme.typography.fontWeightMedium,
        marginBottom: theme.spacing(1.4),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1)
        }
      }
    },
    '& .btn-primary': {
      '&.MuiButtonBase-root': {
        color: theme.colors.textBlack,
        fontSize: theme.spacing(1.25),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      }
    },
    '& .btn-secondary': {
      '&.MuiButtonBase-root': {
        fontSize: theme.spacing(1.25),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.875)
        }
      }
    },
    '& .inner-container': {
      padding: theme.spacing(0, 3.75),
      [theme.breakpoints.down('lg')]: {
        padding: theme.spacing(0, 0)
      }
    },
    '& .filter-section': {
      background: theme.colors.landingFIlterGradient,
      margin: theme.spacing(1, 0, 2),
      borderRadius: theme.spacing(0.375),
      padding: theme.spacing(0.625, 1),
      [theme.breakpoints.down('lg')]: {
        margin: theme.spacing(1, 0),
        padding: theme.spacing(0.625, 0.25)
      },
      '& button': {
        borderRadius: `${theme.spacing(0.375)} !important`
      }
    },
    '& .testimonials-card-wrap': {
      // maxWidth: `${theme.spacing(68.875)} !important`,
      padding: theme.spacing(5, 3.75),
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(2, 0)
      },
      '& .inner-heading': {
        '& h3, & p': {
          textAlign: 'center',
          lineHeight: 'inherit'
        }
      },
      '& .testimonials-card': {
        borderRadius: theme.spacing(0.625),
        overflow: 'hidden',
        display: 'flex',
        cursor: 'pointer',
        '& img': {
          width: '100%',
          margin: '0 auto',
          borderRadius: '10px'
        }
      },
      '& .swiper': {
        overflow: 'visible',
        userSelect: 'none',
        '& .swiper-button-prev, & .swiper-button-next': {
          height: theme.spacing(4.125),
          width: theme.spacing(4.125),
          borderRadius: '100%',
          background: theme.colors.inputBorder,
          [theme.breakpoints.down('lg')]: {
            height: theme.spacing(1.5625),
            width: theme.spacing(1.5625)
          },
          '&:after': {
            color: theme.colors.YellowishOrange,
            fontSize: theme.spacing(1),
            fontWeight: theme.typography.fontWeightBold,
            [theme.breakpoints.down('lg')]: {
              fontSize: theme.spacing(0.75)
            }
          }
        },
        '& .swiper-slide-prev, & .swiper-slide-next': {
          [theme.breakpoints.down(768)]: {
            filter: 'brightness(0.3)'
          }
        },
        '& .swiper-pagination': {
          bottom: theme.spacing(-2.75),
          [theme.breakpoints.down('sm')]: {
            bottom: theme.spacing(-4)
          },
          '& .swiper-pagination-bullet': {
            background: theme.colors.sliderDots,
            opacity: '1',
            '&.swiper-pagination-bullet-active': {
              background: theme.colors.textWhite
            }
          }
        },
        '& .swiper-slide-active': {
          [theme.breakpoints.down('md')]: {
            transform: 'scale(1.1) translate(0, -5px)'
          }
        }
      },
      '&.games-wrap': {
        padding: '0  3.5rem 4rem',
        [theme.breakpoints.down(471)]: {
          padding: '0  3.5rem 1rem'
        }
      }
    },

    '& .games-page-wrap': {
      maxWidth: '72rem',
      margin: '0 auto',
      padding: '4.25rem 0rem 0 0rem',
      [theme.breakpoints.down('md')]: {
        padding: '0'
      },
      '& h1': {
        fontSize: '2.75rem',
        fontWeight: '700',
        color: theme.colors.YellowishOrange,
        marginBottom: '1.5rem',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.875rem',
          marginBottom: '1rem'
        },
        '& span': {
          color: theme.colors.textWhite
        }
      },
      '& .game-content-box': {
        background: '#1A1A1A',
        height: '100%',
        padding: '1.5rem',
        borderRadius: '1.75rem',
        '& h3': {
          fontSize: '1.75rem',
          fontWeight: '700',
          [theme.breakpoints.down('md')]: {
            fontSize: '1.25rem'
          }
        },
        '& p': {
          fontSize: '1.125rem',
          fontWeight: '500',
          [theme.breakpoints.down('md')]: {
            fontSize: '1rem'
          },
          '&.pointer': {
            paddingLeft: '0.75rem',
            position: 'relative',
            marginBottom: '0rem',
            '&:before': {
              content: '""',
              top: '12px',
              left: '0',
              position: 'absolute',
              height: '4px',
              width: '4px',
              background: theme.colors.textWhite,
              borderRadius: '8px',
              [theme.breakpoints.down('md')]: {
                top: '8px'
              }
            }
          }
        },
        '& .yellow-text': {
          color: '#FDB72E'
        },
        '& .space-down': {
          marginBottom: '1rem'
        }
      }
    },

    '& .game-card-grid-wrap': {
      display: 'grid',
      gridTemplateColumns: 'repeat(7,1fr)',
      gridGap: theme.spacing(1.25),
      [theme.breakpoints.down('lg')]: {
        // gridTemplateColumns: 'repeat(auto-fit, minmax(170px, 1fr))',
        gridTemplateColumns: 'repeat(6,1fr)',
        padding: theme.spacing(2.5, 0)
      },
      // [theme.breakpoints.down(1400)]: {
      //   padding: theme.spacing(2.5, 2)
      // },
      [theme.breakpoints.down('md')]: {
        gridGap: theme.spacing(0.75),
        gridTemplateColumns: 'repeat(5,1fr)',
        padding: theme.spacing(0.75, 1)
      },
      [theme.breakpoints.down('sm')]: {
        gridGap: theme.spacing(0.5),
        gridTemplateColumns: 'repeat(4,1fr)',
        padding: theme.spacing(0.75, 0)
      },
      '& img': {
        width: '100%',
        borderRadius: '0.5rem',
        cursor: 'pointer'
      },
      '& p': {
        fontSize: '1.25rem',
        fontWeight: '700'
      }
    },
    '& .game-section-content': {
      marginTop: '2rem',
      '& p': {
        fontSize: theme.spacing(1.25),
        fontWeight: '400',
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1)
        },
        '& a': {
          color: '#0068C3'
        }
      },
      '& li': {
        fontSize: theme.spacing(1.5),
        fontWeight: '400',
        color: theme.colors.textWhite,
        marginBottom: '2rem',
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1)
        }
      },
      '& h2': {
        fontSize: theme.spacing(1.75),
        fontWeight: '700',
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.25),
          fontWeight: '700'
        }
      },
      '& .space-down': {
        marginBottom: '1.5rem',
        [theme.breakpoints.down('md')]: {
          marginBottom: '1rem'
        }
      },
      '& .yellow-text': {
        color: '#FDB72E'
      }
    },
    '& .btn-section-content': {
      gap: '1rem',
      marginBottom: '2rem',
      paddingBottom: '1rem',
      overflowX: 'auto',
      maxWidth: '100%',
      [theme.breakpoints.down('sm')]: {
        gap: '0.5rem',
        marginBottom: '1rem'
      },
      '& button': {
        marginLeft: '0',
        whiteSpace: 'nowrap',
        minWidth: 'auto',
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0.4),
          fontSize: '0.75rem !important'
        }
      }
    },
    '& .input-wrap': {
      width: '30%',
      position: 'relative',
      [theme.breakpoints.down('sm')]: {
        maxWidth: '100%'
      },
      [theme.breakpoints.down('md')]: {
        width: '100%'
      },
      '& .MuiInputBase-root': {
        borderRadius: '30px',
        border: '2px solid #132B27',
        color: theme.colors.textWhite,
        paddingLeft: '2.125rem',
        width: '100%',
        fontSize: '1rem',
        '& input': {
          padding: '10px 14px !important'
        },
        '&::after': {
          display: 'none'
        },
        '&::before': {
          display: 'none'
        }
      },
      '& .MuiFormControl-root': {
        width: '100%',
        '& .MuiInputBase-root': {
          borderRadius: '30px',
          border: '1.5px solid #435A57',
          color: theme.colors.textWhite,
          paddingLeft: '2.125rem',
          fontSize: '1rem',
          '& input': {
            padding: '10px 14px !important'
          }
        },
        '& .MuiOutlinedInput-notchedOutline': {
          outline: 'none',
          border: 'none'
        }
      },
      '& img': {
        position: 'absolute',
        top: '0.875rem',
        left: '1rem'
      }
    },
    '& .MuiAccordion-root': {
      // backgroundColor: '#292929 !important',
      border: '1px solid #333',
      margin: '0 0 1rem 0 !important',
      [theme.breakpoints.down('md')]: {
        margin: '0 0 0.75rem 0 !important'
      },
      '&.Mui-expanded': {
        border: '1px solid #333'
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        backgroundColor: '#1a1a1a !important',
        borderRadius: '0.625rem',
        [theme.breakpoints.down('md')]: {
          padding: '0 1rem'
        },
        '& p': {
          fontSize: '1.625rem',
          textAlign: 'left',
          fontWeight: '600',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '&.Mui-expanded': {
          color: `${theme.colors.YellowishOrange} !important`,
          minHeight: '50px',
          '& p': {
            color: `${theme.colors.YellowishOrange} !important`
          }
        }
      },
      '& .MuiAccordionSummary-content': {
        margin: '1rem 0',
        alignItems: 'center',
        '&.Mui-expanded': {
          margin: '1rem 0'
        },
        '& p': {
          marginBottom: '0'
        }
      },
      '& .MuiAccordionDetails-root': {
        paddingLeft: '4.5rem',
        [theme.breakpoints.down('md')]: {
          paddingLeft: '3.5rem',
          padding: '1rem'
        },
        '& p': {
          fontSize: '1.5rem',
          fontWeight: '500',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        }
      }
    }
  },
  hotGamesWrap: {
    padding: theme.spacing(0),
    [theme.breakpoints.down('lg')]: {
      padding: theme.spacing(1, 0)
    },
    '& .hot-games-details': {
      marginBottom: theme.spacing(6),
      [theme.breakpoints.down('md')]: {
        marginBottom: theme.spacing(4)
      },
      [theme.breakpoints.down('sm')]: {
        marginBottom: theme.spacing(2)
      },
      '& p': {
        fontSize: theme.spacing(1.5),
        fontWeight: theme.typography.fontWeightBold,
        marginBottom: theme.spacing(1),
        maxWidth: theme.spacing(82.5),
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(0.6875),
          marginBottom: theme.spacing(0.5)
        },
        '& span': {
          fontSize: theme.spacing(1.2),
          fontWeight: theme.typography.fontWeightMedium,
          marginBottom: theme.spacing(1.5),
          [theme.breakpoints.down('xl')]: {
            fontSize: theme.spacing(1)
          }
        }
      },
      '& h1': {
        marginTop: '0',
        [theme.breakpoints.down('sm')]: {
          marginBottom: theme.spacing(0.5)
        }
      }
    },
    '& .game-grid-wrap': {
      // gap: theme.spacing(1),
      '& .slider-bnt-wrap': {
        '& .swiper-button-prev, & .swiper-button-next': {
          height: theme.spacing(4.125),
          width: theme.spacing(4.125),
          borderRadius: '100%',
          position: 'absolute',
          zIndex: '1',
          top: theme.spacing(-4),
          background: theme.colors.inputBorder,
          border: 'none',
          [theme.breakpoints.down('lg')]: {
            height: theme.spacing(1.5625),
            width: theme.spacing(1.5625),
            minWidth: theme.spacing(1.5625),
            // top: theme.spacing(-3.5),
            position: 'static'
          },
          '&:after': {
            color: theme.colors.YellowishOrange,
            fontSize: theme.spacing(1),
            fontWeight: theme.typography.fontWeightBold,
            [theme.breakpoints.down('lg')]: {
              fontSize: theme.spacing(0.75)
            }
          }
        },
        '& .swiper-button-prev': {
          left: 'auto',
          right: theme.spacing(6)
        },
        [theme.breakpoints.down('lg')]: {
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing(1),
          justifyContent: 'center',
          marginBottom: theme.spacing(2)
        },
        [theme.breakpoints.down('sm')]: {
          marginBottom: theme.spacing(1)
        }
      }
    },
    '& .inner-heading': {
      '& h4': {
        fontWeight: '600',
        [theme.breakpoints.down('sm')]: {
          lineHeight: theme.spacing(2.2)
        }
      }
    }
  },
  jackpotSection: {
    padding: theme.spacing(2, 0),
    [theme.breakpoints.down('xl')]: {
      padding: theme.spacing(1, 0)
    },
    '& .jackpot-list-wrap': {
      '& h4': {
        fontSize: theme.spacing(2.375),
        fontWeight: theme.typography.fontWeightBold,
        marginBottom: theme.spacing(1.5),
        [theme.breakpoints.down('xl')]: {
          fontSize: theme.spacing(1.5)
        }
      },
      '& h5': {
        fontSize: theme.spacing(1.9),
        fontWeight: theme.typography.fontWeightSemiBold,
        marginBottom: theme.spacing(1),
        [theme.breakpoints.down('xl')]: {
          fontSize: theme.spacing(1.5)
        }
      },
      '& p': {
        fontSize: theme.spacing(1.2),
        fontWeight: theme.typography.fontWeightMedium,
        marginBottom: theme.spacing(1.5),
        [theme.breakpoints.down('xl')]: {
          fontSize: theme.spacing(1)
        },
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(0.5)
        }
      },
      '& span': {
        fontSize: theme.spacing(1.2),
        fontWeight: theme.typography.fontWeightMedium,
        marginBottom: theme.spacing(1.5),
        [theme.breakpoints.down('xl')]: {
          fontSize: theme.spacing(1)
        }
      },
      '& ol': {
        paddingLeft: theme.spacing(1),
        fontSize: theme.spacing(1.2),
        fontWeight: theme.typography.fontWeightMedium,
        paddingTop: theme.spacing(1),
        [theme.breakpoints.down('xl')]: {
          fontSize: theme.spacing(1)
        },
        '& li': {
          paddingBottom: theme.spacing(1.2)
        }
      }
    },
    '& .single-card': {
      [theme.breakpoints.down('md')]: {
        paddingTop: `${theme.spacing(1)} !important`
      }
    },
    '& .single-game-wrap': {
      position: 'relative',
      top: theme.spacing(1),
      padding: theme.spacing(3),
      [theme.breakpoints.down('lg')]: {
        top: '0',
        padding: theme.spacing(0)
      }
    }
  },
  casinoCard: {
    ...casinoCard(theme),
    '& .slot-img': {
      '&:hover': {
        background: 'transparent',
        backgroundColor: 'transparent !important'
      }
    }
  },
  casinoCardOne: {
    float: 'right',
    marginLeft: '15px',
    '& .slot-img': {
      '&:hover': {
        background: 'transparent',
        backgroundColor: 'transparent !important'
      }
    },
    [theme.breakpoints.down('sm')]: {
      width: '300px',
      marginLeft: '0px',
      float: 'none'
    },
    [theme.breakpoints.down(420)]: {
      width: '100%'
    }
  },
  jackpotContent: {
    display: 'flex',
    flexDirection: 'column'
  },
  singleCard: {
    display: 'flex',
    justifyContent: 'flex-end'
  },
  textWrap: {
    overflow: 'hidden'
  },
  blogWrapper: {
    maxWidth: theme.spacing(72),
    width: '100%',
    padding: '0 2rem',
    margin: '3.25rem auto',
    [theme.breakpoints.down('md')]: {
      margin: '1.25rem auto',
      padding: '0'
    },
    '& h1': {
      fontSize: theme.spacing(2.75),
      fontWeight: theme.typography.fontWeightExtraBold,
      marginBottom: theme.spacing(0.5),
      color: theme.colors.YellowishOrange,
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1)
      }
    },
    '& h2': {
      fontSize: theme.spacing(1.25),
      fontWeight: theme.typography.fontWeightBold,
      marginBottom: theme.spacing(3.875),
      color: theme.colors.textWhite,
      maxWidth: '52.3125rem',
      textAlign: 'center',
      margin: 'auto',
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(0.75),
        marginBottom: theme.spacing(1)
      }
    },
    '& .blog-hero-section': {
      '& .hero-banner': {
        cursor: 'pointer',
        // backgroundImage: `url(${mainBannerImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        height: '400px',
        borderRadius: '10px',
        '&:hover': {
          '& h3': {
            // color: '#ffffff80'
          }
        },
        [theme.breakpoints.down('sm')]: {
          height: '300px'
        },
        '& .hero-content': {
          height: '100%',
          width: '100%',
          display: 'flex',
          justifyContent: 'end',
          flexDirection: 'column',
          alignItems: 'end',
          transition: 'all 0.2s ease-in-out',
          padding: theme.spacing(3, 3.5),
          backgroundColor: 'rgba(17, 17, 17, 0.8)',
          '&:hover': {
            backgroundColor: 'rgba(17, 17, 17, 0.7)'
          },
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(1),
            alignItems: 'start'
          },
          '& h3': {
            fontSize: theme.spacing(2.5),
            fontWeight: theme.typography.fontWeightExtraBold,
            marginBottom: theme.spacing(2),
            color: theme.colors.textWhite,
            transition: 'all 0.2s ease-in-out',
            [theme.breakpoints.down('md')]: {
              fontSize: theme.spacing(1.25),
              marginBottom: theme.spacing(1)
            }
          },
          '& .blog-detail': {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'start',
            width: '100%',
            gap: theme.spacing(6.25),
            '& p': {
              fontSize: theme.spacing(1.625),
              fontWeight: theme.typography.fontWeightBold,
              color: theme.colors.blogtext,
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(0.75)
              }
            }
          }
        }
      },
      '& .hero-blog-list': {
        padding: theme.spacing(1.25, 1, 0.25, 1.25),
        borderRadius: '10px',
        border: '1px solid #aaaaaa',
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1.25, 1.75)
        },
        [theme.breakpoints.down('sm')]: {
          padding: theme.spacing(1, 0.75)
        },
        '& .btn-wrap': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%',
          gap: '0.75rem',
          marginBottom: '0.75rem',
          '& button': {
            width: '100%',
            fontSize: '1.125rem',
            cursor: 'pointer',
            minHeight: '2.5rem',
            [theme.breakpoints.down('md')]: {
              minHeight: '2.125rem',
              fontSize: '1rem'
            },
            '&:hover': {
              background: theme.colors.YellowishOrange,
              borderColor: theme.colors.YellowishOrange,
              color: theme.colors.textBlack
            },
            '&.btn-secondary': {
              background: 'transparent',
              border: '1px solid #ffffff50',
              color: '#ffffff50',
              '&:hover': {
                background: theme.colors.YellowishOrange,
                borderColor: theme.colors.YellowishOrange,
                color: theme.colors.textBlack
              }
            }
          },
          '& .MuiInput-root': {
            width: '100%',
            border: '2px solid #132B27',
            borderRadius: '3rem',
            '& input': {
              padding: '0.75rem 1rem',
              color: '#fff'
            },
            '&:before': {
              display: 'none'
            },
            '&:after': {
              display: 'none'
            }
          }
        },
        '& .blog-list-wrap': {
          maxHeight: '450px',
          overflowY: 'auto',
          '& h3': {
            textAlign: 'center',
            marginTop: '1.25rem'
          },
          '& .blog-box': {
            gap: '1.375rem',
            display: 'flex',
            padding: theme.spacing(0.75, 0),
            justifyContent: 'start',
            cursor: 'pointer',
            borderBottom: '1px solid #ffffff40',
            '&:hover': {
              '& .img-wrap': {
                '& img': {
                  transform: 'scale(1.05)'
                }
              },
              '& .blog-content': {
                '& h4': {
                  color: '#ffffff80'
                }
              }
            },
            [theme.breakpoints.down(1400)]: {
              flexDirection: 'column',
              alignItems: 'center'
            },
            [theme.breakpoints.down('lg')]: {
              flexDirection: 'row',
              alignItems: 'inherit',
              justifyContent: 'start'
            },
            [theme.breakpoints.down('md')]: {
              padding: theme.spacing(1, 0.75)
            },
            [theme.breakpoints.down('sm')]: {
              // flexDirection: 'column'
              gap: '1rem'
            },
            '& .img-wrap': {
              maxWidth: '8rem',
              minWidth: '8rem',
              maxHeight: '5rem',
              borderRadius: '10px',
              overflow: 'hidden',
              border: '1px solid #d9d9d950',
              '& img': {
                // minHeight: '100px',
                objectFit: 'cover',
                height: '100%',
                transition: 'all 0.2s ease-in-out',
                width: '100%'
              },
              [theme.breakpoints.down('md')]: {
                maxWidth: '7.375rem',
                minWidth: '7.375rem',
                maxHeight: '5.8125rem'
              }
            },
            '& .blog-content': {
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'start',
              gap: '0.25rem',
              '& h4': {
                fontSize: theme.spacing(1.125),
                fontWeight: theme.typography.fontWeightBold,
                color: theme.colors.textWhite,
                // marginBottom: '0.5rem'
                transition: 'all 0.2s ease-in-out',
                wordBreak: 'break-word',

                [theme.breakpoints.down('md')]: {
                  fontSize: theme.spacing(0.75)
                }
              },
              '& p': {
                fontSize: theme.spacing(0.75),
                fontWeight: theme.typography.fontWeightRegular,
                color: '#B8B5B5',
                marginLeft: '0.65rem',
                '&::before': {
                  top: '5px',
                  left: '-12px',
                  width: '6px',
                  height: '6px',
                  content: "''''",
                  position: 'absolute',
                  background: theme.colors.textWhite,
                  borderRadius: '100%'
                }
              }
            },
            '&:last-child': {
              borderBottom: 'none'
            }
          }
        }
      }
    },
    '& .trends-section': {
      marginTop: '5rem',
      [theme.breakpoints.down('md')]: {
        marginTop: '1rem'
      },
      '& h3': {
        fontSize: theme.spacing(1.75),
        fontWeight: theme.typography.fontWeightExtraBold,
        marginBottom: theme.spacing(1),
        color: theme.colors.textWhite,
        transition: 'all 0.2s ease-in-out',

        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.5),
          marginBottom: theme.spacing(1)
        }
      },
      '& .trend-box': {
        background: theme.colors.blogSection,
        borderRadius: theme.spacing(1),
        padding: theme.spacing(2, 3),
        marginTop: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1),
          marginTop: theme.spacing(1)
        }
      },
      '& .trend-main-blog': {
        display: 'flex',
        flexDirection: 'column',
        gap: '1.25rem',
        // cursor: 'pointer',
        [theme.breakpoints.down('lg')]: {
          gap: '0.75rem'
        },
        '&:hover': {
          '& h3': {
            color: '#ffffff80'
          }
        },
        '& .blog-img': {
          cursor: 'pointer',
          borderRadius: '2.5rem',
          overflow: 'hidden',
          width: '100%',
          // backgroundImage: `url(${trendBannerImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          height: '360px',
          [theme.breakpoints.down('md')]: {
            height: '200px',
            borderRadius: '1.5rem'
          },
          '& .img-content': {
            height: '100%',
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '1.5rem',
            position: 'relative',
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              background: '#00000030'
            },
            '& .text-box': {
              borderRadius: '1.25rem',
              background: '#00000070',
              maxWidth: '17.875rem',
              padding: '1.75rem',
              fontSize: '2rem',
              fontWeight: '600',
              color: theme.colors.textWhite,
              height: 'fit-content',
              textAlign: 'center',
              [theme.breakpoints.down('md')]: {
                fontSize: '0.75rem',
                padding: theme.spacing(0.65, 1.5),
                maxWidth: '10.875rem'
              }
            },
            '& .trend-label': {
              background: theme.colors.YellowishOrange,
              padding: theme.spacing(0.65, 2),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1.25rem',
              fontWeight: '600',
              borderRadius: '4rem',
              color: '#000',
              position: 'absolute',
              top: '1rem',
              left: '1rem',
              [theme.breakpoints.down('md')]: {
                fontSize: '0.75rem',
                padding: theme.spacing(0.65, 1.5)
              },
              [theme.breakpoints.down('sm')]: {
                fontSize: '0.75rem',
                padding: theme.spacing(0.25, 1)
              }
            }
          }
        },
        '& .blog-detail': {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: '1rem',
          '& .blog-user': {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '22px',
            [theme.breakpoints.down('lg')]: {
              gap: '12px'
            },
            '& h5': {
              fontSize: '1.625rem',
              fontWeight: '600',
              color: theme.colors.textWhite,
              [theme.breakpoints.down('lg')]: {
                fontSize: '0.75rem'
              }
            },
            '& .blog-avatar ': {
              height: '3.75rem',
              minWidth: '3.75rem',
              width: '3.75rem',
              borderRadius: '4rem',
              overflow: 'hidden',
              '& img': {
                height: '100%',
                width: '100%',
                objectFit: 'cover'
              },
              [theme.breakpoints.down('lg')]: {
                height: '1.75rem',
                minWidth: '1.75rem',
                width: '1.75rem'
              }
            }
          },
          '& p': {
            fontSize: '1.625rem',
            fontWeight: '500',
            color: '#aaaaaa',
            '&::before': {
              top: '13px',
              left: '-20px',
              width: '12px',
              height: '12px',
              content: "''''",
              position: 'absolute',
              background: '#aaaaaa',
              borderRadius: '100%',
              [theme.breakpoints.down('lg')]: {
                width: '9px',
                height: '9px',
                top: '5px'
              }
            },
            [theme.breakpoints.down('lg')]: {
              fontSize: '0.75rem'
            }
          }
        },
        '& h3': {
          fontSize: '3.25rem',
          fontWeight: '600',
          color: theme.colors.textWhite,
          marginBottom: '0',
          [theme.breakpoints.down('lg')]: {
            fontSize: '1.75rem'
          },
          [theme.breakpoints.down('lg')]: {
            fontSize: '1rem'
          }
        },
        '& p': {
          fontSize: '1.5rem',
          fontWeight: '400',
          color: '#D2D3D5',
          [theme.breakpoints.down('lg')]: {
            fontSize: '0.875rem'
          }
        }
      },
      '& .trend-list-wrap': {
        '& .trend-blog': {
          padding: theme.spacing(1, 0),
          gap: '1.375rem',
          display: 'flex',
          justifyContent: 'start',
          borderBottom: '1px solid #ffffff40',
          cursor: 'pointer',
          '&:hover': {
            '& .trend-content': {
              '& h4': {
                color: '#ffffff80'
              }
            },
            '& .img-wrap': {
              '& img': {
                transform: 'scale(1.05)'
              }
            }
          },
          [theme.breakpoints.down('lg')]: {
            justifyContent: 'start'
          },
          [theme.breakpoints.down('sm')]: {
            gap: '0.75rem'
          },
          '& .img-wrap': {
            maxWidth: '14rem',
            minWidth: '14rem',
            maxHeight: '10rem',
            borderRadius: '10px',
            overflow: 'hidden',
            border: '1px solid #d9d9d950',
            [theme.breakpoints.down('lg')]: {
              maxWidth: '12.1875rem',
              minWidth: '12.1875rem',
              maxHeight: '7.875rem'
            },
            [theme.breakpoints.down('sm')]: {
              maxWidth: '8.5rem',
              minWidth: '8.5rem',
              maxHeight: '6.5rem'
            },
            '& img': {
              objectFit: 'cover',
              height: '100%',
              width: '100%',
              transition: 'all 0.2s ease-in-out'
            }
          },
          '& .trend-content': {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            gap: '0.25rem',
            '& h4': {
              fontSize: theme.spacing(1.375),
              fontWeight: theme.typography.fontWeightBold,
              color: theme.colors.textWhite,
              // marginBottom: '0.5rem'
              transition: 'all 0.2s ease-in-out',
              wordBreak: 'break-word',

              [theme.breakpoints.down('lg')]: {
                fontSize: theme.spacing(1)
              }
            },
            '& p': {
              fontSize: theme.spacing(1.25),
              fontWeight: theme.typography.fontWeightRegular,
              color: '#B8B5B5',
              marginLeft: '0.65rem',
              '&::before': {
                top: '11px',
                left: '-12px',
                width: '6px',
                height: '6px',
                content: "''''",
                position: 'absolute',
                background: theme.colors.textWhite,
                borderRadius: '100%',
                [theme.breakpoints.down('lg')]: {
                  top: '6px'
                }
              },
              [theme.breakpoints.down('lg')]: {
                fontSize: theme.spacing(0.875)
              }
            }
          }
          // '&:last-child': {
          //   borderBottom: 'none'
          // }
        }
      }
    },
    '& .article-section': {
      marginTop: '5rem',
      [theme.breakpoints.down('md')]: {
        marginTop: '1rem'
      },
      '& h3': {
        fontSize: theme.spacing(1.75),
        fontWeight: theme.typography.fontWeightExtraBold,
        marginBottom: theme.spacing(2),
        transition: 'all 0.2s ease-in-out',
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.5),
          marginBottom: theme.spacing(1)
        }
      },
      '& .article-box': {
        background: theme.colors.blogSection,
        borderRadius: theme.spacing(1),
        padding: theme.spacing(1, 3),
        marginTop: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          marginTop: theme.spacing(1),
          padding: theme.spacing(0.75)
        }
      },
      '& .article-wrap': {
        '& .article-main-blog': {
          display: 'flex',
          padding: '1.5rem 0',
          borderBottom: '1px solid #4A4A4A',
          cursor: 'pointer',
          '&:hover': {
            '& img': {
              transform: 'scale(1.05)'
            },
            '& h3': {
              color: '#ffffff80'
            }
          },
          // flexDirection: 'column',
          gap: '2.5rem',
          [theme.breakpoints.down('md')]: {
            flexDirection: 'column',
            gap: '1rem',
            padding: '0.75rem 0'
          },
          '& .blog-img': {
            cursor: 'pointer',
            borderRadius: '2.5rem',
            overflow: 'hidden',
            width: '100%',
            maxWidth: '33.25rem',
            height: '270px',
            [theme.breakpoints.down('sm')]: {
              borderRadius: '1.5rem'
            },
            '& img': {
              height: '100%',
              width: '100%',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.05)'
              }
            },
            [theme.breakpoints.down('lg')]: {
              maxWidth: '23rem',
              maxHeight: '230px'
            },
            [theme.breakpoints.down('md')]: {
              maxWidth: '100%'
            },
            [theme.breakpoints.down(500)]: {
              maxHeight: '165px !important',
              borderRadius: '1.5rem'
            }
          },
          '& .article-right': {
            width: '100%',
            '& .blog-detail': {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: '1rem',
              '& .blog-user': {
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '1rem',
                '& h5': {
                  fontSize: '1.375rem',
                  fontWeight: '600',
                  color: theme.colors.textWhite,
                  [theme.breakpoints.down('lg')]: {
                    fontSize: '0.75rem'
                  }
                },
                '& .blog-avatar ': {
                  height: '2.75rem',
                  width: '2.75rem',
                  borderRadius: '4rem',
                  overflow: 'hidden',
                  '& img': {
                    height: '100%',
                    width: '100%',
                    objectFit: 'cover'
                  },
                  [theme.breakpoints.down('lg')]: {
                    height: '1.75rem',
                    minWidth: '1.75rem',
                    width: '1.75rem'
                  }
                }
              },
              '& p': {
                fontSize: '1.25rem',
                fontWeight: '500',
                color: '#aaaaaa',
                [theme.breakpoints.down('lg')]: {
                  fontSize: '0.75rem'
                }
              }
            },
            '& .article-date': {
              paddingLeft: '1.5rem',
              margin: '12px 0',
              '&::before': {
                top: '10px',
                left: '0px',
                width: '10px',
                height: '10px',
                content: "''''",
                position: 'absolute',
                background: '#aaaaaa',
                borderRadius: '100%',
                [theme.breakpoints.down('lg')]: {
                  width: '9px',
                  height: '9px',
                  top: '7px'
                }
              },
              [theme.breakpoints.down('lg')]: {
                paddingLeft: '0.75rem',
                marginTop: '0.5rem'
              }
            },
            '& h3': {
              fontSize: '1.75rem',
              fontWeight: '600',
              color: theme.colors.textWhite,
              marginBottom: '0',
              [theme.breakpoints.down('lg')]: {
                fontSize: '1.5rem'
              },
              [theme.breakpoints.down('sm')]: {
                fontSize: '0.875rem'
              }
            },
            '& p': {
              fontSize: '1.25rem',
              fontWeight: '400',
              // marginTop: '0.5rem',
              color: '#D2D3D5',
              [theme.breakpoints.down('lg')]: {
                fontSize: '1rem'
              }
            },
            '& .share-icon': {
              display: 'flex',
              justifyContent: 'space-between',
              gap: '1rem',
              alignItems: 'center',
              cursor: 'pointer',
              marginTop: '1rem',
              [theme.breakpoints.down('md')]: {
                marginTop: '0.5rem'
              },
              '& img': {
                [theme.breakpoints.down('md')]: {
                  height: '0.75rem',
                  width: '0.75rem'
                }
              },
              '& .icons-wrap': {
                display: 'flex',
                gap: '0.75rem',
                alignItems: 'center',
                '& a': {
                  height: '1.275rem',
                  [theme.breakpoints.down('md')]: {
                    height: '1rem'
                  }
                },
                '& img': {
                  maxWidth: '21px'
                },
                '& .fb-icon': {
                  marginLeft: '30px',
                  position: 'relative',
                  '&:before': {
                    position: 'absolute',
                    content: "''",
                    height: '1px',
                    width: '30px',
                    backgroundColor: '#4A4A4A',
                    left: '-37px',
                    top: '50%'
                  }
                }
              }
            }
          }
        },
        '& .loadmore': {
          display: 'flex',
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: theme.spacing(2.5),
          paddingBottom: '1rem',
          [theme.breakpoints.down('md')]: {
            marginTop: theme.spacing(1)
          },
          '& button': {
            minHeight: '3.5rem',
            maxWidth: '15.625rem',
            width: '100%',
            color: '#d2d2d2',
            border: '1px solid #ffffff50',
            background: 'transparent',
            cursor: 'pointer',
            [theme.breakpoints.down('md')]: {
              minHeight: '2rem',
              maxWidth: '10.625rem'
            }
          }
        }
      }
    },
    '& .updates-section': {
      marginTop: '5rem',
      [theme.breakpoints.down('md')]: {
        marginTop: '1rem'
      },
      '& h3': {
        fontSize: theme.spacing(1.75),
        fontWeight: theme.typography.fontWeightExtraBold,
        marginBottom: theme.spacing(1),
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.5),
          marginBottom: theme.spacing(1)
        }
      },
      '& p': {
        fontSize: theme.spacing(1.25),
        fontWeight: '500',
        marginBottom: theme.spacing(2),
        color: '#D2D3D5',
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.875),
          marginBottom: theme.spacing(0.5)
        }
      },
      '& .update-blog': {
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
        cursor: 'pointer',
        '&:hover': {
          '& h4': {
            color: '#ffffff'
          },

          '& img': {
            transform: 'scale(1.02)'
          }
        },

        '& img': {
          maxHeight: '300px',
          // minHeight: '300px',
          height: '100%',
          width: '100%',
          borderRadius: '1.5rem',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          [theme.breakpoints.down('md')]: {
            minHeight: '250px'
          }
        },
        '& h4': {
          fontSize: theme.spacing(1.25),
          fontWeight: theme.typography.fontWeightBold,
          marginTop: theme.spacing(0.5),
          color: '#ffffff70',
          transition: 'all 0.2s ease-in-out',

          [theme.breakpoints.down(1450)]: {
            fontSize: theme.spacing(1.5)
          },
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1)
          }
        },
        '& .blog-detail': {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          '& p': {
            fontSize: theme.spacing(1),
            fontWeight: '500',
            color: '#D2D3D580',
            '&.blog-date': {
              '&::before': {
                top: '9px',
                left: '-16px',
                width: '9px',
                height: '9px',
                content: "''''",
                position: 'absolute',
                background: '#AAAAAA',
                borderRadius: '100%',
                [theme.breakpoints.down(1450)]: {
                  width: '6px',
                  height: '6px',
                  top: '6px'
                }
              }
            },
            [theme.breakpoints.down(1450)]: {
              fontSize: theme.spacing(0.75)
            }
          }
        }
      }
    }
  },
  articleWrapper: {
    maxWidth: theme.spacing(72),
    width: '100%',
    padding: '0 2rem',
    margin: '3.25rem auto',
    [theme.breakpoints.down('md')]: {
      margin: '1.25rem auto',
      padding: '0'
    },
    '& .article-wrap': {
      '& h1': {
        fontSize: '2.5rem',
        fontWeight: '600',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.5rem'
        }
      },
      '& h2': {
        fontSize: '2.5rem',
        fontWeight: '600',
        color: '#FFA538',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.5rem'
        }
      },
      '& h3': {
        fontSize: '1.5625rem',
        fontWeight: '600',
        [theme.breakpoints.down('md')]: {
          fontSize: '1rem',
          fontWeight: '700'
        }
      },
      '& p': {
        fontSize: '1.25rem',
        fontWeight: '500',
        color: '#D2D3D5',
        marginBottom: '1rem',
        [theme.breakpoints.down('md')]: {
          fontSize: '0.875rem'
        }
      },
      '& .blog-detail': {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: '1rem',
        maxWidth: '700px',
        '& .blog-user': {
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '22px',
          [theme.breakpoints.down('lg')]: {
            gap: '12px'
          },
          '& h5': {
            fontSize: '1.625rem',
            fontWeight: '600',
            color: theme.colors.textWhite,
            [theme.breakpoints.down('lg')]: {
              fontSize: '0.75rem'
            }
          },
          '& .blog-avatar ': {
            height: '3.75rem',
            minWidth: '3.75rem',
            width: '3.75rem',
            borderRadius: '4rem',
            overflow: 'hidden',
            '& img': {
              height: '100%',
              width: '100%',
              objectFit: 'cover'
            },
            [theme.breakpoints.down('lg')]: {
              height: '1.75rem',
              minWidth: '1.75rem',
              width: '1.75rem'
            }
          }
        },
        '& p': {
          fontSize: '1.625rem',
          fontWeight: '500',
          color: '#aaaaaa',
          marginBottom: '0',

          '&.date': {
            '&::before': {
              top: '13px',
              left: '-20px',
              width: '12px',
              height: '12px',
              content: "''''",
              position: 'absolute',
              background: '#aaaaaa',
              borderRadius: '100%',
              [theme.breakpoints.down('lg')]: {
                width: '9px',
                height: '9px',
                top: '5px'
              }
            }
          },
          [theme.breakpoints.down('lg')]: {
            fontSize: '0.75rem'
          }
        }
      },
      '& .blog-img': {
        marginTop: '1.125rem',
        marginBottom: '3rem',
        borderRadius: '1.25rem',
        overflow: 'hidden',
        position: 'relative',
        lineHeight: '0',

        // paddingBottom: '60%',
        '& img': {
          height: '100%',
          width: '100%',
          // position: 'absolute',
          objectFit: 'contain',
          left: '0',
          top: '0',

          borderRadius: '20px',
          overflow: 'hidden'
        },
        [theme.breakpoints.down('sm')]: {
          marginBottom: '1rem'
        }
      },
      '& .para-wrap': {
        marginBottom: '2.5rem'
      },
      '& .share-wrap': {
        padding: '1.4375rem 0',
        margin: '1rem 0',
        borderTop: '1px solid #818181',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'start',
        [theme.breakpoints.down('sm')]: {
          flexDirection: 'column',
          alignItems: 'center',
          gap: '0.75rem'
        },
        '& p': {
          fontSize: '1.125rem',
          fontWeight: '500',
          color: '#D2D3D5'
        },
        '& .icon-wrap': {
          display: 'flex',
          gap: '14px'
        },
        '& .btn-wrap ': {
          display: 'flex',
          gap: '1rem',
          [theme.breakpoints.down('sm')]: {
            fontSize: '0.675rem'
          },
          '& button': {
            background: '#000',
            borderRadius: '1.5rem',
            color: '#8c8c8c',
            border: '1px solid #8c8c8c',
            fontSize: '1.125rem',
            fontWeight: '500',
            padding: '0.125rem 1rem',
            // cursor: 'pointer',
            [theme.breakpoints.down('sm')]: {
              fontSize: '0.875rem',
              padding: '0.125rem 0.5rem'
            }
          }
        }
      }
    },
    '& .updates-section': {
      marginTop: '5rem',
      [theme.breakpoints.down('md')]: {
        marginTop: '1rem'
      },
      '& h3': {
        fontSize: theme.spacing(1.75),
        fontWeight: theme.typography.fontWeightExtraBold,
        marginBottom: theme.spacing(1),
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.5),
          marginBottom: theme.spacing(1)
        }
      },
      '& p': {
        fontSize: theme.spacing(1.25),
        fontWeight: '500',
        marginBottom: theme.spacing(2),
        color: '#D2D3D5',
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.875),
          marginBottom: theme.spacing(0.5)
        }
      },
      '& .update-blog': {
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
        cursor: 'pointer',
        '&:hover': {
          '& h4': {
            color: '#ffffff'
          },

          '& img': {
            transform: 'scale(1.02)'
          }
        },
        '& img': {
          maxHeight: '220px',
          minHeight: '120px',
          height: '100%',
          cursor: 'pointer',
          width: '100%',
          borderRadius: '1.5rem',
          transition: 'all 0.2s ease-in-out',

          [theme.breakpoints.down('md')]: {
            minHeight: '140px'
          }
        },
        '& h4': {
          fontSize: theme.spacing(1.5),
          fontWeight: theme.typography.fontWeightBold,
          marginTop: theme.spacing(0.5),
          color: '#ffffff70',
          transition: 'all 0.2s ease-in-out',

          [theme.breakpoints.down(1450)]: {
            fontSize: theme.spacing(1.5)
          },
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1)
          }
        },
        '& .blog-detail': {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: '4px',
          '& p': {
            fontSize: theme.spacing(0.875),
            fontWeight: '500',
            color: '#D2D3D580',
            '&.blog-date': {
              '&::before': {
                top: '7px',
                left: '-10px',
                width: '6px',
                height: '6px',
                content: "''''",
                position: 'absolute',
                background: '#AAAAAA',
                borderRadius: '100%',
                [theme.breakpoints.down(1450)]: {
                  width: '6px',
                  height: '6px',
                  top: '6px'
                }
              }
            },
            [theme.breakpoints.down(1450)]: {
              fontSize: theme.spacing(0.75)
            }
          }
        }
      }
    },
    '& .welcome-bonus': {
      border: '1px solid #AAAAAA50',
      borderRadius: '0.625rem',
      padding: '2.125rem 1.875rem',
      marginBottom: '2.5rem',
      [theme.breakpoints.down('md')]: {
        padding: '1.25rem'
      },
      '& h3': {
        fontSize: '1.875rem',
        fontWeight: '700',
        textAlign: 'center',
        color: '#FFA538',
        marginBottom: '2.5rem',
        [theme.breakpoints.down('md')]: {
          marginBottom: '1rem'
        }
      },
      '& p': {
        fontSize: '1.25rem',
        fontWeight: '700',
        textAlign: 'center'
      },
      '& .welcome-box': {
        padding: '1.5rem 0.75rem',
        border: '1px solid #4b4b4b',
        borderRadius: '0.625rem',
        cursor: 'pointer',
        textAlign: 'center',
        background: 'linear-gradient(179.72deg, #3E0001 0.25%, #981806 48.02%, #D52C0F 99.76%)',
        '& h2': {
          fontSize: '2rem',
          fontWeight: '700',
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(1),
            padding: '0'
          }
        },
        '& img': {
          borderRadius: '1.75rem',
          padding: '1rem 0',
          maxWidth: '21.875rem',
          width: '100%',
          transition: 'all 0.2s ease-in-out',
          [theme.breakpoints.down('md')]: {
            borderRadius: '2.5rem'
          },
          '&:hover': {
            transform: 'scale(1.02)'
          }
        }
      }
    },
    '& .hero-blog-list': {
      padding: theme.spacing(1.25, 1),
      borderRadius: '10px',
      border: '1px solid #aaaaaa50',
      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(1.25, 1.75)
      },
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(1, 0.75)
      },
      '& h2': {
        fontSize: '2rem',
        fontWeight: '700',
        color: '#FFA538',
        textAlign: 'center'
      },
      '& .blog-list-wrap': {
        maxHeight: '450px',
        overflowY: 'auto',
        '& .blog-box': {
          padding: theme.spacing(1, 0.75),
          gap: '1.375rem',
          cursor: 'pointer',
          display: 'flex',
          justifyContent: 'start',
          borderBottom: '1px solid #ffffff40',
          '&:hover': {
            '& .img-wrap': {
              '& img': {
                transform: 'scale(1.05)'
              }
            },
            '& .blog-content': {
              '& h4': {
                color: '#ffffff80'
              }
            }
          },
          [theme.breakpoints.down(1400)]: {
            flexDirection: 'column',
            alignItems: 'center'
          },
          [theme.breakpoints.down('lg')]: {
            // flexDirection: 'row',
            alignItems: 'inherit',
            justifyContent: 'start'
          },
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(1, 0.75)
          },
          [theme.breakpoints.down('sm')]: {
            flexDirection: 'row',
            gap: '1rem'
          },
          '& .img-wrap': {
            maxWidth: '10.375rem',
            minWidth: '10.375rem',
            maxHeight: '6.8125rem',
            borderRadius: '10px',
            overflow: 'hidden',
            border: '1px solid #d9d9d950',
            '& img': {
              objectFit: 'cover',
              minHeight: '100px',
              height: '100%',
              transition: 'all 0.2s ease-in-out',
              width: '100%'
            },
            [theme.breakpoints.down('md')]: {
              maxWidth: '7.375rem',
              minWidth: '7.375rem',
              maxHeight: '5.8125rem'
            }
          },
          '& .blog-content': {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            gap: '0.25rem',
            '& h4': {
              fontSize: theme.spacing(1.125),
              fontWeight: theme.typography.fontWeightBold,
              color: theme.colors.textWhite,
              // marginBottom: '0.5rem'
              transition: 'all 0.2s ease-in-out',
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(0.75)
              }
            },
            '& p': {
              fontSize: theme.spacing(0.75),
              fontWeight: theme.typography.fontWeightRegular,
              color: '#B8B5B5',
              marginLeft: '0.65rem',
              '&::before': {
                top: '5px',
                left: '-12px',
                width: '6px',
                height: '6px',
                content: "''''",
                position: 'absolute',
                background: theme.colors.textWhite,
                borderRadius: '100%'
              }
            }
          },
          '&:last-child': {
            borderBottom: 'none'
          }
        }
      }
    }
  },
  jackpotPageWrap: {
    '& .point-text': {
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .top-games-section': {
      margin: '2rem 0 3rem 0rem',
      [theme.breakpoints.down('md')]: {
        margin: '1rem 0 1rem 0rem'
      },
      '& h5': {
        fontSize: theme.spacing(1.75),
        fontWeight: '700',
        // color: theme.colors.YellowishOrange,
        margin: theme.spacing(0.5, 0),
        // lineHeight: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.5)
        }
      },
      '& p': {
        fontSize: theme.spacing(1.25),
        fontWeight: '500',
        // color: theme.colors.YellowishOrange,
        // margin: theme.spacing(1, 0),
        // lineHeight: theme.spacing(2),
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(1.25)
        }
      },
      '& .game-img': {
        maxHeight: '30rem',
        overflow: 'hidden',
        height: '100%',
        border: '1px solid #FDB72E',
        borderRadius: '10px',
        [theme.breakpoints.down('md')]: {
          maxHeight: '15rem'
        },
        '& img': {
          objectFit: 'cover',
          height: '100%',
          width: '100%'
        }
      },
      '& .MuiGrid-item': {
        height: '100%'
      }
    },
    '& .feature-box': {
      display: 'flex',
      alignItems: 'start',
      gap: '3.5rem',
      padding: '10px 0',
      [theme.breakpoints.down('md')]: {
        gap: '1.5rem'
      },
      '& .icon-box': {
        maxWidth: '100px',
        width: '100%',
        height: '100px',
        border: '1px solid #727272',
        padding: '1.25rem',
        borderRadius: '0.625rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        [theme.breakpoints.down('md')]: {
          maxWidth: '40px',
          width: '100%',
          height: '40px',
          '& img': {
            width: '1.25rem'
          }
        }
      },
      '& .content-box': {
        '& h5': {
          fontSize: '1.75rem',
          fontWeight: '600',
          marginBottom: '0.25rem',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.5)
          }
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    },
    '& .pointer': {
      fontSize: '1.25rem',
      paddingLeft: '1rem',
      position: 'relative',
      marginBottom: '0rem',
      '&:before': {
        content: '""',
        top: '11px',
        left: '0',
        position: 'absolute',
        height: '8px',
        width: '8px',
        background: theme.colors.YellowishOrange,
        borderRadius: '8px',
        [theme.breakpoints.down('md')]: {
          top: '11px'
        }
      }
    }
  },
  socialSlotWrap: {
    margin: '0 auto',
    width: '100%',
    maxWidth: '1800px',
    padding: theme.spacing(7, 3, 0),
    flexDirection: 'column',
    background: theme.colors.valutModal,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 1, 0)
    },
    '& .MuiAccordion-root': {
      // backgroundColor: '#292929 !important',
      border: '1px solid transparent',
      margin: '2rem 0 !important',
      [theme.breakpoints.down('md')]: {
        margin: '0 0 0.75rem 0 !important'
      },
      '&.Mui-expanded': {
        border: '1px solid #494949'
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        backgroundColor: '#292929 !important',
        borderRadius: '0.625rem',
        [theme.breakpoints.down('md')]: {
          padding: '0 1rem'
        },
        '& p': {
          fontSize: '1.25rem',
          textAlign: 'left',
          fontWeight: '600',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '&.Mui-expanded': {
          color: theme.colors.YellowishOrange,
          minHeight: '50px'
        }
      },
      '& .MuiAccordionSummary-content': {
        margin: '1rem 0',
        '&.Mui-expanded': {
          margin: '1rem 0'
        }
      },
      '& .MuiAccordionDetails-root': {
        paddingLeft: '2.5rem',
        [theme.breakpoints.down('md')]: {
          paddingLeft: '1rem',
          padding: '0.75rem'
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '& h5': {
          fontSize: '1.75rem',
          fontWeight: '600',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '1.25rem'
          },
          '&.yellow-pointer': {
            // fontSize: '1.875rem',
            paddingLeft: '1rem',
            position: 'relative',
            marginBottom: '0rem',
            '&:before': {
              content: '""',
              top: '14px',
              left: '0',
              position: 'absolute',
              height: '8px',
              width: '6px',
              background: theme.colors.YellowishOrange,
              // borderRadius: '8px'
              [theme.breakpoints.down('md')]: {
                height: '6px',
                top: '8px',
                width: '6px'
              }
            }
          }
        }
      },
      '& .MuiAccordionSummary-expandIconWrapper': {
        '& svg': {
          fill: theme.colors.textWhite
        }
      }
    },
    '& .list-points': {
      paddingLeft: '1rem'
    },
    '& .why-choose-section': {
      '& h5': {
        fontSize: '1.5rem !important',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.25rem !important'
        },
        '&.yellow-pointer': {
          '&:before': {
            borderRadius: '20px',
            height: '8px !important',
            top: '12px !important',
            [theme.breakpoints.down('md')]: {
              height: '6px',
              top: '8px !important',
              width: '6px'
            }
          }
        }
      }
    }
  },
  scratchCardWrap: {
    margin: '0 auto',
    width: '100%',
    maxWidth: '1800px',
    padding: theme.spacing(7, 3, 0),
    flexDirection: 'column',
    background: theme.colors.valutModal,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 1, 0)
    },
    '& h4': {
      fontSize: theme.spacing(2.5),
      fontWeight: '700',
      color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      }
    },
    '& .yellow-text': {
      color: theme.colors.YellowishOrange
    },
    '& .space-down': {
      paddingBottom: '2rem !important'
    },
    '& p': {
      fontSize: theme.spacing(1.875),
      fontWeight: '500',
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: '1.25rem !important'
      },
      '&.white-pointer': {
        fontSize: '1.25rem',
        paddingLeft: '1rem',
        position: 'relative',
        marginBottom: '0rem',
        '&:before': {
          content: '""',
          top: '10px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.textWhite,
          borderRadius: '8px',
          [theme.breakpoints.down('md')]: {
            height: '6px',
            top: '10px',
            width: '6px'
          }
        }
      },
      '&.yellow-pointer': {
        fontSize: '1.875rem',
        paddingLeft: '1rem',
        position: 'relative',
        marginBottom: '0rem',
        '&:before': {
          content: '""',
          top: '14px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.YellowishOrange,
          borderRadius: '8px',
          [theme.breakpoints.down('md')]: {
            height: '6px',
            top: '11px',
            width: '6px'
          }
        }
      }
    },
    '& .MuiAccordion-root': {
      // backgroundColor: '#292929 !important',
      border: '1px solid transparent',
      margin: '2rem 0 !important',
      [theme.breakpoints.down('md')]: {
        margin: '0 0 0.75rem 0 !important'
      },
      '&.Mui-expanded': {
        border: '1px solid #494949'
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        backgroundColor: '#292929 !important',
        borderRadius: '0.625rem',
        [theme.breakpoints.down('md')]: {
          padding: '0 1rem'
        },
        '& p': {
          fontSize: '1.625rem',
          textAlign: 'left',
          fontWeight: '600',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '&.Mui-expanded': {
          color: theme.colors.YellowishOrange,
          minHeight: '50px'
        }
      },
      '& .MuiAccordionSummary-content': {
        margin: '1rem 0',
        '&.Mui-expanded': {
          margin: '1rem 0'
        }
      },
      '& .MuiAccordionDetails-root': {
        paddingLeft: '2.5rem',
        [theme.breakpoints.down('md')]: {
          paddingLeft: '1rem',
          padding: '0.75rem'
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '& h5': {
          fontSize: '1.5rem',
          fontWeight: '600',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '1.25rem'
          },
          '&.yellow-pointer': {
            // fontSize: '1.875rem',
            paddingLeft: '1rem',
            position: 'relative',
            marginBottom: '0rem',
            '&:before': {
              content: '""',
              top: '19px',
              left: '0',
              position: 'absolute',
              height: '12px',
              width: '8px',
              background: theme.colors.YellowishOrange,
              // borderRadius: '8px'
              [theme.breakpoints.down('md')]: {
                height: '6px',
                top: '8px',
                width: '6px'
              }
            }
          }
        }
      },
      '& .MuiAccordionSummary-expandIconWrapper': {
        '& svg': {
          fill: theme.colors.textWhite
        }
      }
    },
    '& .list-points': {
      paddingLeft: '1rem'
    },
    '& h5': {
      fontSize: theme.spacing(2.5),
      fontWeight: '700',
      // color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      },
      '&.pointer': {
        fontSize: '1.5rem',
        paddingLeft: '1rem',
        position: 'relative',
        marginBottom: '0rem',
        '&:before': {
          content: '""',
          top: '12px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.YellowishOrange,
          borderRadius: '8px'
        }
      }
    }
  },
  casinoTableWrap: {
    margin: '0 auto',
    width: '100%',
    padding: theme.spacing(7, 3, 0),
    flexDirection: 'column',
    background: theme.colors.valutModal,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 1, 0)
    },

    '& h5': {
      fontSize: theme.spacing(2.5),
      fontWeight: '700',
      // color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      },
      '&.pointer': {
        fontSize: '1.5rem',
        paddingLeft: '1rem',
        position: 'relative',
        marginBottom: '0rem',
        '&:before': {
          content: '""',
          top: '12px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.YellowishOrange,
          borderRadius: '8px'
        }
      }
    },
    '& .point-text': {
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .space-down': {
      paddingBottom: '2rem !important'
    },
    '& .pointer-box': {
      padding: '0.25rem 0'
    },
    '& .yellow-text': {
      color: theme.colors.YellowishOrange
    },
    '& p': {
      fontSize: theme.spacing(1.875),
      fontWeight: '500',
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .feature-box': {
      display: 'flex',
      alignItems: 'start',
      gap: '3.5rem',
      padding: '10px 0',
      [theme.breakpoints.down('md')]: {
        gap: '1.5rem'
      },
      '& .icon-box': {
        maxWidth: '100px',
        width: '100%',
        height: '100px',
        border: '1px solid #727272',
        padding: '1.25rem',
        borderRadius: '0.625rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        [theme.breakpoints.down('md')]: {
          maxWidth: '40px',
          width: '100%',
          height: '40px',
          '& img': {
            width: '1.25rem'
          }
        }
      },
      '& .content-box': {
        '& h5': {
          fontSize: '1.75rem',
          fontWeight: '600',
          marginBottom: '0.25rem',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    },
    '& .pointer': {
      fontSize: '1.25rem',
      paddingLeft: '1rem',
      position: 'relative',
      marginBottom: '0rem',
      '&:before': {
        content: '""',
        top: '12px',
        left: '0',
        position: 'absolute',
        height: '8px',
        width: '8px',
        background: theme.colors.YellowishOrange,
        borderRadius: '8px',
        [theme.breakpoints.down('md')]: {
          top: '11px'
        }
      }
    }
  },
  slingoCasinoWrap: {
    margin: '0 auto',
    width: '100%',
    padding: theme.spacing(7, 3, 0),
    flexDirection: 'column',
    background: theme.colors.valutModal,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 1, 0)
    },
    '& h4': {
      fontSize: theme.spacing(2.5),
      fontWeight: '700',
      color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      }
    },
    '& h5': {
      fontSize: theme.spacing(2.5),
      fontWeight: '700',
      // color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      },
      '&.pointer': {
        fontSize: '1.5rem',
        paddingLeft: '1rem',
        position: 'relative',
        marginBottom: '0rem',
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.5)
        },
        '&:before': {
          content: '""',
          top: '12px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.YellowishOrange,
          borderRadius: '8px'
        }
      }
    },
    '& .MuiAccordion-root': {
      // backgroundColor: '#292929 !important',
      border: '1px solid transparent',
      margin: '2rem 0 !important',
      [theme.breakpoints.down('md')]: {
        margin: '0 0 0.75rem 0 !important'
      },
      '&.Mui-expanded': {
        border: '1px solid #494949'
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        backgroundColor: '#292929 !important',
        borderRadius: '0.625rem',
        [theme.breakpoints.down('md')]: {
          padding: '0 1rem'
        },
        '& p': {
          fontSize: '1.625rem',
          textAlign: 'left',
          fontWeight: '600',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '&.Mui-expanded': {
          color: theme.colors.YellowishOrange,
          minHeight: '50px'
        }
      },
      '& .MuiAccordionSummary-content': {
        margin: '1rem 0',
        '&.Mui-expanded': {
          margin: '1rem 0'
        }
      },
      '& .MuiAccordionDetails-root': {
        paddingLeft: '2.5rem',
        [theme.breakpoints.down('md')]: {
          paddingLeft: '1rem',
          padding: '0.75rem'
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '& h5': {
          fontSize: '1.5rem',
          fontWeight: '600',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '1.25rem'
          },
          '&.yellow-pointer': {
            // fontSize: '1.875rem',
            paddingLeft: '1rem',
            position: 'relative',
            marginBottom: '0rem',
            '&:before': {
              content: '""',
              top: '19px',
              left: '0',
              position: 'absolute',
              height: '12px',
              width: '8px',
              background: theme.colors.YellowishOrange,
              // borderRadius: '8px'
              [theme.breakpoints.down('md')]: {
                height: '6px',
                top: '8px',
                width: '6px'
              }
            }
          }
        }
      },
      '& .MuiAccordionSummary-expandIconWrapper': {
        '& svg': {
          fill: theme.colors.textWhite
        }
      }
    },
    '& .jackpot-cards-wrap': {
      paddingBottom: '3.5rem',
      '& h3': {
        fontSize: theme.spacing(2.5),
        fontWeight: '700',
        color: theme.colors.YellowishOrange,
        marginBottom: theme.spacing(2),
        // lineHeight: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.75)
        }
      },
      '& .cards-wrap': {
        display: 'grid',
        gridTemplateColumns: 'repeat(8, 1fr)',
        rowGap: '1rem',
        columnGap: '1rem',
        // padding: '0 2rem',

        [theme.breakpoints.down('md')]: {
          gridTemplateColumns: 'repeat(5, 1fr)',
          padding: '0'
        },
        [theme.breakpoints.down('sm')]: {
          gridTemplateColumns: 'repeat(3, 1fr)',
          rowGap: '1rem',
          columnGap: '1rem'
        },
        '& img': {
          height: '100%',
          width: '100%'
        }
      }
    },
    '& .point-text': {
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .space-down': {
      paddingBottom: '2rem !important'
    },
    '& .pointer-box': {
      padding: '0 0.75rem',
      '& p.pointer': {
        '&:before': {
          content: '""',
          top: '17px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.textWhite,
          borderRadius: '8px',
          [theme.breakpoints.down('md')]: {
            top: '11px'
          }
        }
      }
    },
    '& .yellow-text': {
      color: theme.colors.YellowishOrange
    },
    '& p': {
      fontSize: theme.spacing(1.875),
      fontWeight: '500',
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: `${theme.spacing(1.25)} !important`
      }
    },
    '& .slingo-feature-box': {
      display: 'flex',
      alignItems: 'start',
      gap: '3.5rem',
      padding: '10px 0',
      [theme.breakpoints.down('md')]: {
        gap: '1.5rem'
      },
      '& .icon-box': {
        maxWidth: '100px',
        width: '100%',
        height: '100px',
        border: '1px solid #727272',
        padding: '1.25rem',
        borderRadius: '0.625rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        [theme.breakpoints.down('md')]: {
          maxWidth: '40px',
          width: '100%',
          height: '40px',
          '& img': {
            width: '1.25rem'
          }
        }
      },
      '& .content-box': {
        paddingLeft: '2rem',
        '&.pointer': {
          '&:before': {
            content: '""',
            top: '17px',
            left: '10px',
            position: 'absolute',
            height: '10px',
            width: '10px',
            background: theme.colors.YellowishOrange,
            borderRadius: '8px',
            [theme.breakpoints.down('md')]: {
              top: '11px'
            }
          }
        },
        '& h5': {
          fontSize: '1.75rem',
          fontWeight: '600',
          marginBottom: '0.25rem',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    },
    '& .pointer': {
      fontSize: '1.25rem',
      paddingLeft: '1rem',
      position: 'relative',
      marginBottom: '0rem',
      '&:before': {
        content: '""',
        top: '12px',
        left: '0',
        position: 'absolute',
        height: '8px',
        width: '8px',
        background: theme.colors.YellowishOrange,
        borderRadius: '8px',
        [theme.breakpoints.down('md')]: {
          top: '11px'
        }
      }
    }
  },
  popularGameWrap: {
    margin: '0 auto',
    width: '100%',
    padding: theme.spacing(7, 3, 0),
    flexDirection: 'column',
    background: theme.colors.valutModal,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 1, 0)
    },
    '& h4': {
      fontSize: theme.spacing(2.5),
      fontWeight: '700',
      color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      }
    },
    '& h5': {
      fontSize: theme.spacing(1.75),
      fontWeight: '700',
      // color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(0.5),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      },
      '&.pointer': {
        fontSize: '1.875rem',
        paddingLeft: '1rem',
        position: 'relative',
        marginBottom: '0rem',
        '&:before': {
          content: '""',
          top: '17px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.YellowishOrange,
          borderRadius: '8px'
        }
      }
    },
    '& .point-text': {
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .space-down': {
      paddingBottom: '2rem !important'
    },
    '& .pointer-box': {
      padding: '0.25rem 0'
    },
    '& .yellow-text': {
      color: theme.colors.YellowishOrange
    },
    '& p': {
      fontSize: theme.spacing(1.875),
      fontWeight: '500',
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .feature-box': {
      display: 'flex',
      alignItems: 'start',
      gap: '3.5rem',
      padding: '10px 0',
      [theme.breakpoints.down('md')]: {
        gap: '1.5rem'
      },
      '& .icon-box': {
        maxWidth: '100px',
        width: '100%',
        height: '100px',
        border: '1px solid #727272',
        padding: '1.25rem',
        borderRadius: '0.625rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        [theme.breakpoints.down('md')]: {
          maxWidth: '40px',
          width: '100%',
          height: '40px',
          '& img': {
            width: '1.25rem'
          }
        }
      },
      '& .content-box': {
        '& h5': {
          fontSize: '1.5rem',
          fontWeight: '600',
          marginBottom: '0.25rem',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    },
    '& .pointer': {
      fontSize: '1.25rem',
      paddingLeft: '1rem',
      position: 'relative',
      marginBottom: '0rem',
      '&:before': {
        content: '""',
        top: '17px',
        left: '0',
        position: 'absolute',
        height: '8px',
        width: '8px',
        background: theme.colors.YellowishOrange,
        borderRadius: '8px',
        [theme.breakpoints.down('md')]: {
          top: '11px'
        }
      }
    }
  },
  liveDealerWrap: {
    margin: '0 auto',
    width: '100%',
    padding: theme.spacing(7, 3, 0),
    flexDirection: 'column',
    background: theme.colors.valutModal,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 1, 0)
    },
    '& h4': {
      fontSize: theme.spacing(2.5),
      fontWeight: '700',
      color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      }
    },
    '& h5': {
      fontSize: theme.spacing(2.5),
      fontWeight: '700',
      // color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      },
      '&.pointer': {
        fontSize: '1.875rem',
        paddingLeft: '1rem',
        position: 'relative',
        marginBottom: '0rem',
        '&:before': {
          content: '""',
          top: '17px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.YellowishOrange,
          borderRadius: '8px'
        }
      }
    },
    '& .point-text': {
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .space-down': {
      paddingBottom: '2rem !important'
    },
    '& .pointer-box': {
      padding: '0.25rem 0'
    },
    '& .yellow-text': {
      color: theme.colors.YellowishOrange
    },
    '& p': {
      fontSize: theme.spacing(1.875),
      fontWeight: '500',
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .feature-box': {
      display: 'flex',
      alignItems: 'start',
      gap: '3.5rem',
      padding: '10px 0',
      [theme.breakpoints.down('md')]: {
        gap: '1.5rem'
      },
      '& .icon-box': {
        maxWidth: '100px',
        width: '100%',
        height: '100px',
        border: '1px solid #727272',
        padding: '1.25rem',
        borderRadius: '0.625rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        [theme.breakpoints.down('md')]: {
          maxWidth: '40px',
          width: '100%',
          height: '40px',
          '& img': {
            width: '1.25rem'
          }
        }
      },
      '& .content-box': {
        paddingLeft: '2rem',
        [theme.breakpoints.down('md')]: {
          paddingLeft: '1rem'
        },
        '& h5': {
          fontSize: '1.5rem',
          fontWeight: '600',
          marginBottom: '0.25rem',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          },
          '&.pointer': {
            fontSize: '1.5rem',
            paddingLeft: '1rem',
            position: 'relative',
            marginBottom: '0rem',
            '&:before': {
              content: '""',
              top: '10px',
              left: '-5px',
              position: 'absolute',
              height: '8px',
              width: '8px',
              background: '#fff',
              borderRadius: '8px',
              [theme.breakpoints.down('md')]: {
                top: '11px'
              }
            }
          }
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    },
    '& .MuiAccordion-root': {
      // backgroundColor: '#292929 !important',
      border: '1px solid transparent',
      margin: '2rem 0 !important',
      [theme.breakpoints.down('md')]: {
        margin: '0 0 0.75rem 0 !important'
      },
      '&.Mui-expanded': {
        border: '1px solid #494949'
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        backgroundColor: '#292929 !important',
        borderRadius: '0.625rem',
        [theme.breakpoints.down('md')]: {
          padding: '0 1rem'
        },
        '& p': {
          fontSize: '1.625rem',
          textAlign: 'left',
          fontWeight: '600',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '&.Mui-expanded': {
          color: theme.colors.YellowishOrange,
          minHeight: '50px'
        }
      },
      '& .MuiAccordionSummary-content': {
        margin: '1rem 0',
        '&.Mui-expanded': {
          margin: '1rem 0'
        }
      },
      '& .MuiAccordionDetails-root': {
        paddingLeft: '2.5rem',
        [theme.breakpoints.down('md')]: {
          paddingLeft: '1rem',
          padding: '0.75rem'
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '1rem'
          }
        },
        '& h5': {
          fontSize: '1.5rem',
          fontWeight: '600',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '1.25rem'
          },
          '&.yellow-pointer': {
            // fontSize: '1.875rem',
            paddingLeft: '1rem',
            position: 'relative',
            marginBottom: '0rem',
            '&:before': {
              content: '""',
              top: '12px',
              left: '0',
              position: 'absolute',
              height: '8px',
              width: '6px',
              background: theme.colors.YellowishOrange,
              // borderRadius: '8px'
              [theme.breakpoints.down('md')]: {
                height: '6px',
                top: '8px',
                width: '6px'
              }
            }
          }
        }
      },
      '& .MuiAccordionSummary-expandIconWrapper': {
        '& svg': {
          fill: theme.colors.textWhite
        }
      }
    },
    '& .pointer': {
      fontSize: '1.25rem',
      paddingLeft: '1rem',
      position: 'relative',
      marginBottom: '0rem',
      '&:before': {
        content: '""',
        top: '10px',
        left: '0',
        position: 'absolute',
        height: '8px',
        width: '8px',
        background: theme.colors.YellowishOrange,
        borderRadius: '8px',
        [theme.breakpoints.down('md')]: {
          top: '11px'
        }
      }
    }
  },
  instantWinWrap: {
    margin: '0 auto',
    width: '100%',
    padding: theme.spacing(7, 3, 0),
    flexDirection: 'column',
    background: theme.colors.valutModal,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 1, 0)
    },
    '& h4': {
      fontSize: theme.spacing(1.5),
      fontWeight: '700',
      color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      }
    },
    '& h5': {
      fontSize: theme.spacing(1.5),
      fontWeight: '700',
      // color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(0.5),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      },
      '&.pointer': {
        fontSize: '1.25rem',
        paddingLeft: '1rem',
        position: 'relative',
        marginBottom: '0rem',
        '&:before': {
          content: '""',
          top: '17px',
          left: '0',
          position: 'absolute',
          height: '8px',
          width: '8px',
          background: theme.colors.YellowishOrange,
          borderRadius: '8px'
        }
      }
    },
    '& .point-text': {
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .space-down': {
      paddingBottom: '2rem !important'
    },
    '& .pointer-box': {
      padding: '0.25rem 0'
    },
    '& .yellow-text': {
      color: theme.colors.YellowishOrange
    },
    '& p': {
      fontSize: theme.spacing(1.875),
      fontWeight: '500',
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.25)
      }
    },
    '& .feature-box': {
      display: 'flex',
      alignItems: 'start',
      gap: '3.5rem',
      padding: '10px 0',
      [theme.breakpoints.down('md')]: {
        gap: '1.5rem'
      },
      '& .icon-box': {
        maxWidth: '100px',
        width: '100%',
        height: '100px',
        border: '1px solid #727272',
        padding: '1.25rem',
        borderRadius: '0.625rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        [theme.breakpoints.down('md')]: {
          maxWidth: '40px',
          width: '100%',
          height: '40px',
          '& img': {
            width: '1.25rem'
          }
        }
      },
      '& .content-box': {
        '& h5': {
          fontSize: '2.5rem',
          fontWeight: '600',
          marginBottom: '0.25rem',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        },
        '& p': {
          fontSize: '1.875rem',
          fontWeight: '500',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    },
    '& .pointer': {
      fontSize: '1.25rem',
      paddingLeft: '1rem',
      position: 'relative',
      marginBottom: '0rem',
      [theme.breakpoints.down('md')]: {
        fontSize: '1.25rem'
      },
      '&:before': {
        content: '""',
        top: '12px',
        left: '0',
        position: 'absolute',
        height: '8px',
        width: '8px',
        background: theme.colors.YellowishOrange,
        borderRadius: '8px',
        [theme.breakpoints.down('md')]: {
          top: '11px'
        }
      }
    },
    '& .MuiAccordion-root': {
      // backgroundColor: '#292929 !important',
      border: '1px solid transparent',
      margin: '2rem 0 !important',
      [theme.breakpoints.down('md')]: {
        margin: '0 0 0.75rem 0 !important'
      },
      '&.Mui-expanded': {
        border: '1px solid #494949'
      },
      '& .MuiButtonBase-root': {
        color: theme.colors.textWhite,
        backgroundColor: '#292929 !important',
        borderRadius: '0.625rem',
        [theme.breakpoints.down('md')]: {
          padding: '0 1rem'
        },
        '& p': {
          fontSize: '1.625rem',
          textAlign: 'left',
          fontWeight: '600',
          marginBottom: '0',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '&.Mui-expanded': {
          color: theme.colors.YellowishOrange,
          minHeight: '50px'
        }
      },
      '& .MuiAccordionSummary-content': {
        margin: '1rem 0',
        '&.Mui-expanded': {
          margin: '1rem 0'
        }
      },
      '& .MuiAccordionDetails-root': {
        paddingLeft: '2.5rem',
        [theme.breakpoints.down('md')]: {
          paddingLeft: '1rem',
          padding: '0.75rem'
        },
        '& .pointer-box': {
          '& p': {
            marginBottom: '0.25rem'
          }
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        },
        '& h5': {
          fontSize: '1.5rem',
          fontWeight: '600',
          textAlign: 'left',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '1.25rem'
          },
          '&.yellow-pointer': {
            // fontSize: '1.875rem',
            paddingLeft: '1rem',
            position: 'relative',
            marginBottom: '0rem',
            '&:before': {
              content: '""',
              top: '12px',
              left: '0',
              position: 'absolute',
              height: '8px',
              width: '6px',
              background: theme.colors.YellowishOrange,
              // borderRadius: '8px'
              [theme.breakpoints.down('md')]: {
                height: '6px',
                top: '8px',
                width: '6px'
              }
            }
          }
        }
      },
      '& .MuiAccordionSummary-expandIconWrapper': {
        '& svg': {
          fill: theme.colors.textWhite
        }
      }
    }
  },
  '& .space-down': {
    paddingBottom: '2rem !important'
  },
  subgamePage: {
    margin: '0 auto',
    width: '100%',
    padding: theme.spacing(7, 3, 0),
    maxWidth: '87.5rem !important',
    flexDirection: 'column',
    background: theme.colors.valutModal,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(6, 1, 0)
    },
    '& h2': {
      fontSize: theme.spacing(1.75),
      fontWeight: '700',
      color: theme.colors.YellowishOrange,
      marginBottom: theme.spacing(1),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.5)
      }
    },
    '& .yellow-text': {
      color: theme.colors.YellowishOrange
    },
    '& .space-down': {
      paddingBottom: '1rem !important'
    },
    '& p': {
      fontSize: theme.spacing(1.25),
      fontWeight: '500',
      marginBottom: theme.spacing(0.5),
      // lineHeight: theme.spacing(2),
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1.125),
        marginBottom: theme.spacing(0.5)
      }
    },
    '& .jackpot-cards-wrap': {
      paddingBottom: '1.5rem',
      '& h1': {
        fontSize: theme.spacing(2.5),
        fontWeight: '700',
        color: theme.colors.YellowishOrange,
        marginBottom: theme.spacing(2),
        // lineHeight: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.75)
        }
      },
      '& .cards-wrap': {
        display: 'grid',
        gridTemplateColumns: 'repeat(7, 1fr)',
        rowGap: '1rem',
        columnGap: '1rem',
        // padding: '0 2rem',

        [theme.breakpoints.down('md')]: {
          gridTemplateColumns: 'repeat(5, 1fr)',
          padding: '0'
        },
        [theme.breakpoints.down('sm')]: {
          gridTemplateColumns: 'repeat(3, 1fr)',
          rowGap: '1rem',
          columnGap: '1rem'
        },
        '& img': {
          height: '100%',
          width: '100%'
        }
      }
    },
    '& .faq-wrap': {
      '& h3': {
        fontWeight: '700',
        fontSize: '1.75rem',
        margin: '1rem 0'
      },
      '& .MuiAccordion-root': {
        backgroundColor: 'transparent !important',
        border: `1px solid ${theme.colors.accordianBorder}`,
        borderRadius: `${theme.spacing(1.25)} !important`,
        margin: '0 !important',
        marginBottom: '1rem !important',
        '&.Mui-expanded': {
          borderColor: theme.colors.YellowishOrange
        },

        '& .MuiAccordionSummary-root': {
          padding: theme.spacing(1.1, 1.25),
          position: 'relative',
          backgroundColor: 'transparent !important',
          '& .MuiAccordionSummary-content': {
            margin: '0',
            '& .MuiTypography-root': {
              fontSize: theme.spacing(1.375),
              marginBottom: '0',
              fontWeight: theme.typography.fontWeightExtraBold,
              color: theme.colors.textWhite,
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(1.125),
                width: '90%'
              }
            },
            '&.Mui-expanded': {
              '& .MuiTypography-root': {
                color: theme.colors.YellowishOrange
              }
            }
          },
          '& .MuiAccordionSummary-expandIconWrapper': {
            position: 'absolute',
            right: theme.spacing(2),
            color: theme.colors.YellowishOrange,
            '& svg': {
              width: theme.spacing(2),
              height: theme.spacing(2)
            }
          }
        },
        '& .MuiAccordionDetails-root': {
          padding: theme.spacing(0.5, 1),
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(1)
          },
          '& .MuiTypography-root': {
            color: theme.colors.textWhite,
            fontSize: theme.spacing(1.25),
            lineHeight: theme.spacing(1.75),
            fontWeight: theme.typography.fontWeightMedium,
            marginBottom: theme.spacing(1),
            '& a': {
              color: theme.colors.YellowishOrange,
              textDecoration: 'none',
              padding: theme.spacing(0, 0.2)
            }
          }
        }
      }
    }
  }
}))
