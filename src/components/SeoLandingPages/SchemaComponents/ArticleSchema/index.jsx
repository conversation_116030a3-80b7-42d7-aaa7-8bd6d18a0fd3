import React from 'react'
import { Helmet } from 'react-helmet-async'

const ArticleSchema = ({
  slug,
  title,
  description,
  authorName,
  authorUrl = 'https://www.themoneyfactory.com/about',
  publishedAt,
  updatedAt,
  featuredImage,
  publisherName = 'TheMoneyfactory',
  publisherLogo = 'https://www.themoneyfactory.com/assets/logoGif.a06732e6.gif',
  readTime = 5
}) => {
  const schemaData = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://www.themoneyfactory.com/blog/${slug}`
    },
    headline: title,
    image: featuredImage,
    datePublished: publishedAt,
    dateModified: updatedAt || publishedAt,
    author: {
      '@type': 'Person',
      name: author<PERSON><PERSON>,
      url: authorUrl
    },
    publisher: {
      '@type': 'Organization',
      name: publisher<PERSON><PERSON>,
      logo: {
        '@type': 'ImageObject',
        url: publisher<PERSON><PERSON>
      }
    },
    description: description,
    timeRequired: `PT${readTime}M`
  }

  return (
    <Helmet>
      <script type='application/ld+json'>{JSON.stringify(schemaData)}</script>
    </Helmet>
  )
}

export default ArticleSchema
