import React, { useRef, useEffect, useState } from 'react'
import useStyles from './styles'
import { Grid, Typography, Box } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import Signup from '../Modal/Signup'
import { useLocation } from 'react-router-dom'

import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'
import { FreeMode, Navigation, Pagination } from 'swiper/modules'
import 'swiper/css/pagination'
import { fist } from '../ui-kit/icons/svg'
import { buffKing } from '../ui-kit/icons/opImages'
import SeoLandingFooter from './SeoLandingFooter'
import { useSeoCategoriesStore } from '../../store/store'
import SeoPageFilter from './SeoPageFilter/SeoPageFilter'
import seoContent from '../../utils/seoContent.json'
import parse from 'html-react-parser'
import NotFoundPage from '../../../src/pages/NotFoundPage'
import Loader from '../Loader'
import { CasinoQuery } from '../../reactQuery'
import casinoQuery from '../../reactQuery/casinoQuery'
import LandingHeader from '../../pages/Landing/LandingHeader'
import SeoHead from '../../utils/seoHead'
// import LandingHeader from './LandingHeader'
/* eslint-disable multiline-ternary */

const CommonPage = () => {
  const portalStore = usePortalStore()
  const classes = useStyles()
  const location = useLocation()
  const [selectedSeoSubcategory, setSelectedSeoSubcategory] = useState(null)
  const [categoryContent, setCategoryContent] = useState(null)
  const [loading, setLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)
  const [allGames, setAllGames] = useState()
  const { setSeoSubCategories } = useSeoCategoriesStore((state) => state)
  const { seoSubCategories } = useSeoCategoriesStore((state) => state)
  const pathName = location.pathname.split('/')[2]

  const { data: subCategories, isLoading, refetch } = casinoQuery.getSubcategoryListQuery({ params: {} })

  useEffect(() => {
    setSeoSubCategories(subCategories?.data)
  }, [subCategories])

  const successToggler = (data) => {
    setAllGames(data?.data?.data?.rows)
  }

  const errorToggler = (error) => {
    console.log('error: ', error)
  }

  const subcategoryListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })

  useEffect(() => {
    subcategoryListMutation.mutate({ subCategoryId: 0 })
  }, [])

  if (seoSubCategories && allGames && !seoSubCategories?.find((x) => x?.name === 'All Games')) {
    const allGamesObject = {
      imageUrl: '',
      selectedThumbnail: '',
      isFeatured: true,
      isMoreGame: false,
      masterGameSubCategoryId: 0,
      name: 'All Games',
      orderId: 0,
      subCategoryGames: allGames
    }
    seoSubCategories.unshift(allGamesObject)
  }

  useEffect(() => {
    setLoading(true)
    const pathName = location.pathname.split('/')[2]
    const decodedName = decodeURIComponent(pathName)
    const data = seoSubCategories?.find(
      (x) =>
        x?.name
          .replace(/\s+/g, '-') // Replace spaces with '-'
          .toLowerCase() === // Convert the whole string to lowercase
        decodedName
    )
    setSelectedSeoSubcategory(data)

    const categoryKey = decodedName.toLowerCase()
    const content = seoContent[categoryKey]

    if (content) {
      setCategoryContent(content)
      setNotFound(false)
    } else {
      setCategoryContent(null)
      setNotFound(true)
    }
    setLoading(false)
  }, [location, seoSubCategories])

  const handlaeClaimWelcomeOffer = () => {
    portalStore.openPortal(() => <Signup />, 'loginModal')
  }

  const swiperRef = useRef(null)
  useEffect(() => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slideTo(0)
    }
  }, [])

  const htmlRenderer = (htmlContent) => {
    return parse(htmlContent)
  }

  const [paginationEnabled, setPaginationEnabled] = useState(window.innerWidth > 470);
  useEffect(() => {
    const handleResize = () => {
      setPaginationEnabled(window.innerWidth > 470)
    };

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  if (loading) {
    return <Loader />
  }

  if (notFound) {
    return <NotFoundPage />
  }

  return isLoading ? (
    <Loader />
  ) : (
    <>
      {/* <Helmet>
        <title>{categoryContent?.metaTitle}</title>
        <meta name='description' content={categoryContent?.metaDesc} />
        <meta name='robots' content='index, follow' />
      </Helmet> */}
       <SeoHead
        title={`${categoryContent?.metaTitle}`}
        description={`${categoryContent?.metaDesc}`}
        // keywords={['about us', 'company', 'mission', 'team']}
        // url='https://www.themoneyfactory.com/games/slingo-casino-games'
        // imageUrl='https://testttyourdomain.com/images/about-us.jpg'
      />

      <Grid className={classes.landingPageWrap}>
        <LandingHeader />

        <Grid className='testimonials-card-wrap games-wrap'>
          <Grid className='inner-heading'>
            <Typography variant='h3'>{categoryContent.heading}</Typography>
            <Typography>{categoryContent.headerDescription}</Typography>
          </Grid>

          <Swiper
            grabCursor={true}
            centeredSlides={false}
            loop={true} // Enable looping
            navigation={true}
            modules={[Navigation, Pagination]}
            spaceBetween={20}
            className='mySwiper'
            pagination={paginationEnabled ? { clickable: true } : false} // Conditionally enable pagination
            breakpoints={{
              0: {
                slidesPerView: 2,
                spaceBetween: 20,
                centeredSlides: true
              },
              768: {
                slidesPerView: 4,
                spaceBetween: 20,
                centeredSlides: false
              },
              1024: {
                slidesPerView: 5.5,
                spaceBetween: 20,
                centeredSlides: false
              }
            }}
          >
            {pathName === 'all-games' ? (
              <>
                {allGames?.map((image, index) => (
                  <SwiperSlide key={index}>
                    <Grid className='testimonials-card'>
                      <img src={image.imageUrl || fist} alt='Jackpot Image' onClick={handlaeClaimWelcomeOffer} />
                    </Grid>
                  </SwiperSlide>
                ))}
              </>
            ) : (
              <>
                {selectedSeoSubcategory?.subCategoryGames?.map((image, index) => (
                  <SwiperSlide key={index}>
                    <Grid className='testimonials-card'>
                      <img src={image.imageUrl || fist} alt='Jackpot Image' onClick={handlaeClaimWelcomeOffer} />
                    </Grid>
                  </SwiperSlide>
                ))}
              </>
            )}
          </Swiper>
        </Grid>

        <Box className='flter-wrap'>
          <Grid className='inner-container'>
            <Grid className='filter-section'>
              <SeoPageFilter selectedSeoSubcategory={selectedSeoSubcategory} />
            </Grid>
          </Grid>
        </Box>

        <Box className={classes.hotGamesWrap}>
          <Grid className='inner-container'>
            <Grid className='inner-heading'>
              <Typography variant='h3' component='h3'>
                {selectedSeoSubcategory?.name}
              </Typography>
            </Grid>

            <Grid className='hot-games-details'>
              {categoryContent?.section1?.map((text, index) => (
                <Typography key={index}>{htmlRenderer(text)}</Typography>
              ))}
            </Grid>

            <Grid className='game-grid-wrap'>
              <Grid className='slider-bnt-wrap'>
                <button
                  id={`swiper-button-prev-${seoSubCategories?.masterGameSubCategoryId}`}
                  className='swiper-button-prev'
                ></button>
                <button
                  id={`swiper-button-next-${seoSubCategories?.masterGameSubCategoryId}`}
                  className='swiper-button-next'
                ></button>
              </Grid>

              <Swiper
                spaceBetween={20}
                loop={true}
                autoplay={{ delay: 3000 }}
                navigation={{
                  nextEl: `#swiper-button-next-${seoSubCategories?.masterGameSubCategoryId}`,
                  prevEl: `#swiper-button-prev-${seoSubCategories?.masterGameSubCategoryId}`
                }}
                modules={[FreeMode, Navigation]}
                breakpoints={{
                  0: {
                    slidesPerView: 3,
                    spaceBetween: 10,
                    centeredSlides: true
                  },
                  768: { slidesPerView: 4 },
                  1024: { slidesPerView: 5 },
                  1400: { slidesPerView: 5 }
                }}
              >
                {pathName === 'all-games'
                  ? allGames?.map((image, index) => (
                    <SwiperSlide key={index}>
                      <Grid className={classes.casinoCard}>
                        <img
                          src={image.imageUrl || fist}
                          alt='Casino'
                          className='casinoGame-img slot-img'
                          loading='lazy'
                          onClick={handlaeClaimWelcomeOffer}
                        />
                      </Grid>
                    </SwiperSlide>
                  ))
                  : selectedSeoSubcategory?.subCategoryGames?.map((image, index) => (
                    <SwiperSlide key={index}>
                      <Grid className={classes.casinoCard}>
                        <img
                          src={image.imageUrl || fist}
                          alt='Casino'
                          className='casinoGame-img slot-img'
                          loading='lazy'
                          onClick={handlaeClaimWelcomeOffer}
                        />
                      </Grid>
                    </SwiperSlide>
                  ))}
              </Swiper>
            </Grid>
          </Grid>
        </Box>

        <Box className={classes.jackpotSection}>
          <Grid className={classes.jackpotContent}>
            <Grid className='inner-container'>
              <Grid container spacing={4}>
                <Grid item xs={12} md={12} className={classes.textWrap}>
                  <img
                    src={buffKing}
                    alt='Casino'
                    className={classes.casinoCardOne}
                    loading='lazy'
                    onClick={handlaeClaimWelcomeOffer}
                  />
                  <Grid className='jackpot-list-wrap'>
                    {categoryContent?.section2?.map((text, index) => (
                      <Typography key={index}>{htmlRenderer(text)}</Typography>
                    ))}
                  </Grid>

                  <Grid className='jackpot-list-wrap'>
                    <Grid container spacing={1}>
                      {categoryContent?.section3 && (
                        <Grid item xs={12} md={6}>
                          {categoryContent?.section3?.map((text, index) => (
                            <Typography key={index}>{htmlRenderer(text)}</Typography>
                          ))}
                        </Grid>
                      )}
                      {categoryContent?.section4 && (
                        <Grid item xs={12} md={6}>
                          {categoryContent?.section4?.map((text, index) => (
                            <Typography key={index}>{htmlRenderer(text)}</Typography>
                          ))}
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Grid style={{ marginBottom: '1rem' }}>
          <SeoLandingFooter />
        </Grid>
      </Grid>
    </>
  )
}

export default CommonPage
