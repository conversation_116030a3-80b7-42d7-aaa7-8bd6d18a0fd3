import React, { useEffect } from 'react'
import '../../../../src/App.css'
import { Grid, Box } from '@mui/material'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import useStyles from './SeoFilter.styles'
import Tabs from '@mui/material/Tabs'
import Tab from '@mui/material/Tab'
import { defaultCategory } from '../../ui-kit/icons/svg'
import { useSeoCategoriesStore } from '../../../store/store'
import { useLocation, useNavigate } from 'react-router-dom'
import seoContent from '../../../utils/seoContent.json'

const SeoPageFilter = ({selectedSeoSubcategory}) => {
  const classes = useStyles()
  const [value, setValue] = React.useState()
  const location = useLocation()
  const navigate = useNavigate()
  const { seoSubCategories } = useSeoCategoriesStore((state) => state)

  function normalizeString(str) {
    return str.toLowerCase().replace(/\s+/g, '-')
  }
  const keys = Object.keys(seoContent)
  const categories = seoSubCategories?.map((game) => normalizeString(game.name))
  const commonValues = categories?.filter((value) => keys.includes(value))

  const matchedSubCategories = commonValues?.map((commonValue) => {
    const game = seoSubCategories?.find((game) => normalizeString(game.name) === commonValue)
    return game
  })

  useEffect(() => {
    const pathName = location.pathname.split('/')[2]
    const decodedName = decodeURIComponent(pathName)
    let indexValue = matchedSubCategories?.findIndex(
      (x) =>
        x?.name
          .replace(/\s+/g, '-') // Replace spaces with '-'
          .toLowerCase() === // Convert the whole string to lowercase
        decodedName
    )

    setValue(indexValue)
  }, [location, seoSubCategories, matchedSubCategories])

  const handleSubCategoryData = (name) => {
    const encodedName = encodeURIComponent(name)
    const formattedPath = encodedName.replace(/%20/g, '-').toLowerCase()
    navigate(`/games/${formattedPath}`)
  }

  return (
    <Grid className={classes.lobbyFilterWrap}>
      <Grid container spacing={{ xs: 0, md: 1 }}>
        <Grid item xs={12} lg={12}>
          <Grid className={classes.lobbyFilterSection}>
            <Box className='lobby-filter-left'>
              <Grid className={classes.roundedThemeTabs}>
                <Tabs value={value} variant='scrollable' scrollButtons='auto' aria-label='scrollable auto tabs example'>
                  {matchedSubCategories?.map((subCategory, index) => {
                    return (
                      <Tab
                        label={subCategory?.name}
                        icon={
                          <img
                            src={
                              selectedSeoSubcategory === subCategory?.name
                                ? subCategory.imageUrl?.selectedThumbnail || defaultCategory
                                : subCategory.imageUrl?.thumbnail || defaultCategory
                            }
                            alt='Subcategories'
                            key={`Subcategories-${index}`}
                            height='100%'
                            width='100%'
                          />
                        }
                        onClick={() => handleSubCategoryData(subCategory?.name)}
                        key={index}
                      />
                    )
                  })}
                </Tabs>
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}
export default SeoPageFilter
