import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  seoLandingFooter: {
    background: theme.colors.coinBundle,
    borderRadius: theme.spacing(0.9375),
    padding: theme.spacing(2, 4),
    // display: "flex",
    // alignItems: "center",
    // justifyContent: "center",
    [theme.breakpoints.down('lg')]: {
      padding: theme.spacing(2, 0.5)
    },
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1, 0.5)
    },
    '& .about-footer': {
      '& .about-footer-content': {
        gap: theme.spacing(1),
        [theme.breakpoints.down('lg')]: {
          display: 'flex',
          justifyContent: 'space-between',
          flexDirection: 'column',
          alignItems: 'center',
          gap: theme.spacing(0.625)
        },

        '& .about-footer-content-right': {
          '&  > p': {
            fontSize: theme.spacing(1.25),
            color: theme.colors.textWhite,
            marginBottom: theme.spacing(1),
            fontWeight: theme.typography.fontWeightMedium,
            [theme.breakpoints.down('md')]: {
              fontSize: theme.spacing(0.75)
            },
            '&.news-instruction-web': {
              marginTop: theme.spacing(1),
              fontSize: theme.spacing(0.875),
              color: theme.colors.infoText,
              [theme.breakpoints.down('md')]: {
                display: 'none'
              }
            },

            '&.news-instruction-mob': {
              marginTop: theme.spacing(1),
              fontSize: theme.spacing(0.875),
              color: theme.colors.infoText,
              display: 'none',
              [theme.breakpoints.down('md')]: {
                display: 'block',
                fontSize: theme.spacing(0.75)
              }
            }
          },
          '&:before': {
            position: 'absolute',
            left: '-7px',
            top: '0',
            background: theme.colors.textWhite,
            content: "''",
            height: '100%',
            width: '1px',
            display: 'none'
          }
        }
      },
      '& .footer-logo': {
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        marginBottom: theme.spacing(1),
        [theme.breakpoints.down('lg')]: {
          display: 'flex',
          justifyContent: 'center',
          borderBottom: '1px solid #ccc5',
          marginBottom: theme.spacing(0.5),
          paddingBottom: theme.spacing(1)
        },
        '& img': {
          width: '100%',
          maxWidth: '12rem',
          [theme.breakpoints.down('md')]: {
            width: theme.spacing(5)
          }
        }
      },
      '& .news-latter-wrap': {
        display: 'flex',
        position: 'relative',
        '& .MuiFormControl-root': {
          width: '100%',
          '& .MuiInputBase-root': {
            width: '100%',
            '& input': {
              background: theme.colors.newsInput,
              border: 'none',
              borderRadius: theme.spacing(0.313),
              padding: theme.spacing(0.4, 11, 0.4, 1),
              minHeight: theme.spacing(1.75),
              width: '100%',
              fontSize: theme.spacing(0.875),
              color: theme.colors.textWhite,
              fontWeight: theme.typography.fontWeightMedium,
              [theme.breakpoints.down('md')]: {
                padding: theme.spacing(0.313),
                fontSize: theme.spacing(0.313),
                minHeight: theme.spacing(0.75)
              },
              '&::-webkit-input-placeholder': {
                fontWeight: `${theme.typography.fontWeightMedium} !important`,
                color: `${theme.colors.textWhite} !important`,

                [theme.breakpoints.down('md')]: {
                  fontSize: `${theme.spacing(0.313)} !important`
                }
              }
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none',
              borderRadius: theme.spacing(0.313)
            }
          }
        },
        '& .btn-submit': {
          background: theme.colors.YellowishOrange,
          color: theme.colors.textWhite,
          fontWeight: theme.typography.fontWeightBold,
          fontSize: theme.spacing(1),
          minWidth: theme.spacing(10.25),
          borderRadius: theme.spacing(0.313),
          position: 'absolute',
          right: '0',
          [theme.breakpoints.down('md')]: {
            minWidth: 'auto',
            fontSize: theme.spacing(0.313)
          }
        }
      }
      // "&  p": {
      //     fontSize: theme.spacing(1.25),
      //     fontWeight: theme.typography.fontWeightMedium,
      //     marginBottom: theme.spacing(1.5),

      // },
    },
    '& .footer-link-grid': {
      display: 'grid',
      gridTemplateColumns: 'repeat(3, 1fr)',
      maxWidth: theme.spacing(50),
      marginLeft: 'auto',
      [theme.breakpoints.down('lg')]: {
        marginTop: theme.spacing(2)
      },
      [theme.breakpoints.down('md')]: {
        padding: '0 5px 0 16px',
        gap: '8px'
      },
      '& .footer-link-card': {
        '& a': {
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing(1),
          fontSize: theme.spacing(1.25),
          fontWeight: theme.typography.fontWeightMedium,
          padding: theme.spacing(0.313, 0),
          color: theme.colors.textWhite,
          textDecoration: 'none',
          cursor: 'pointer',
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(0.675),
            gap: theme.spacing(0.5)
          },
          '&:hover': {
            color: theme.colors.YellowishOrange
          },
          '&.active': {
            color: theme.colors.YellowishOrange
          },
          '& img': {
            width: theme.spacing(1.5),
            height: theme.spacing(1.5),
            [theme.breakpoints.down('lg')]: {
              width: theme.spacing(0.625),
              height: theme.spacing(0.625)
            }
          }
        },
        '& h4': {
          fontSize: theme.spacing(1.5),
          fontWeight: theme.typography.fontWeightExtraBold,
          textTransform: 'capitalize',
          // paddingLeft: theme.spacing(1),
          position: 'relative',
          whiteSpace: 'nowrap',
          marginBottom: theme.spacing(1),
          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(0.65),
            paddingLeft: theme.spacing(0)
          },
          '&:before': {
            position: 'absolute',
            left: '-16px',
            top: '10px',
            background: theme.colors.sitemapDot,
            height: '8px',
            width: '8px',
            borderRadius: '100%',
            content: "''",
            [theme.breakpoints.down('md')]: {
              height: '2px',
              width: '2px',
              top: '5px',
              left: '-10px'
            }
          }
        },
        '&.contact-col': {
          '& h4': {
            paddingLeft: '1.5rem',
            [theme.breakpoints.down('md')]: {
              paddingLeft: '1rem'
            },
            '&:before': {
              left: '6px',
              [theme.breakpoints.down('md')]: {
                left: '1px'
              }
            }
          },
          '& a': {
            [theme.breakpoints.down('md')]: {
              gap: '0.5rem'
            }
          }
        }
      }
    },
    '& .seo-mob-copyright': {
      display: 'none',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: theme.spacing(0.313, 0, 0),
      marginTop: theme.spacing(1),
      borderTop: `1px solid ${theme.colors.footerBorder}`,
      [theme.breakpoints.down('lg')]: {
        display: 'flex'
      },
      [theme.breakpoints.down('sm')]: {
        flexDirection: 'column'
      },
      '& p': {
        color: theme.colors.textWhite,
        fontSize: theme.spacing(0.75),
        fontWeight: theme.typography.fontWeightMedium
      },
      '& a': {
        color: theme.colors.textWhite,
        fontSize: theme.spacing(0.75),
        fontWeight: theme.typography.fontWeightMedium,
        textDecoration: 'none',
        cursor: 'pointer',
        padding: theme.spacing(0, 0.313),
        '&:hover': {
          color: theme.colors.YellowishOrange
        },
        '&:after': {
          position: 'absolute',
          content: "'|'",
          right: '0',
          top: '0',
          color: theme.colors.textWhite
        },
        '&:last-child': {
          '&:after': {
            display: 'none'
          }
        }
      }
    }
  },
  seoLandingFooterCopyright: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing(1, 0),
    '& p': {
      color: theme.colors.landingFooterText,
      fontSize: theme.spacing(1.25),
      fontWeight: theme.typography.fontWeightMedium
    },
    '& a': {
      color: theme.colors.landingFooterText,
      fontSize: theme.spacing(1.25),
      fontWeight: theme.typography.fontWeightMedium,
      textDecoration: 'none',
      cursor: 'pointer',
      padding: theme.spacing(0, 0.313),
      '&:hover': {
        color: theme.colors.YellowishOrange
      },
      '&:after': {
        position: 'absolute',
        content: "'|'",
        right: '0',
        color: theme.colors.landingFooterText
      },
      '&:last-child': {
        '&:after': {
          display: 'none'
        }
      }
    },
    [theme.breakpoints.down('lg')]: {
      display: 'none'
    }
  }
}))
