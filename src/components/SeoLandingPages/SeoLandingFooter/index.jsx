import React, { useState } from 'react'
import useStyles from './styles'
import { Grid, Typography, Box } from '@mui/material'
import { BrandLogo } from '../../ui-kit/icons/brand'
import { seoFacebook, seoInstagram, seoTwitter } from '../../ui-kit/icons/webp'
import { seoTelegram } from '../../ui-kit/icons/svg'
import { usePortalStore, useSiteLogoStore } from '../../../store/store'
import seoContent from '../../../utils/seoContent.json'
import casinoQuery from '../../../reactQuery/casinoQuery'
import { currectYear } from '../../../utils/helpers'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { FooterQuerys } from '../../../reactQuery'
import CmsModal from '../../CmsModal/CmsModal'
import { PlayerRoutes } from '../../../routes'
import { deleteCookie } from '../../../utils/cookiesCollection'

const SeoLandingFooter = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const logoData = useSiteLogoStore((state) => state)
  const { data: seoSubCategories } = casinoQuery.getSubcategoryListQuery({ params: {} })
  const location = useLocation()
  const pathName = location.pathname
  const [selectedTab, setSelectedTab] = useState(pathName)
  const navigate = useNavigate()

  const { data: cmsLinks } = FooterQuerys.getCmsQuery()
  const cmsData =
    cmsLinks?.length > 0
      ? cmsLinks?.filter((info) => info?.slug === 'privacy-policy' || info?.slug === 'about-terms')
      : []

  function normalizeString(str) {
    return str.toLowerCase().replace(/\s+/g, '-')
  }
  const keys = Object.keys(seoContent)
  const categories = seoSubCategories?.data?.map((game) => normalizeString(game.name))
  const commonValues = categories?.filter((value) => keys.includes(value))

  const matchedSubCategories = commonValues?.map((commonValue) => {
    const game = seoSubCategories?.data?.find((game) => normalizeString(game.name) === commonValue)
    return game
  })

  const handleNavigation = (event, targetPath) => {
    if (pathName === targetPath) {
      event.preventDefault() // Prevents reloading the same page
    } else {
      setSelectedTab(targetPath)
    }
  }

  const onLinkClick = (e, pathname) => {
    e.preventDefault()
    portalStore.openPortal(() => <CmsModal path={pathname} fromLanding={true} />, 'cmsModal')
  }

  const handleClick = () => {
    deleteCookie('path')
  }

  return (
    <>
      <Box className={classes.seoLandingFooter}>
        <Grid
          container
          spacing={{
            xs: 0.4, // Extra-small screens
            lg: 1 // Large screens
          }}
        >
          <Grid item xs={12} lg={4}>
            <Grid className='about-footer'>
              <Grid className='about-footer-content'>
                <Grid className='footer-logo'>
                  <img src={logoData?.desktopLogo || BrandLogo} alt='Logo' />
                </Grid>
                <Grid className='about-footer-content-right'>
                  <Typography>
                    Our mission is to provide a world-class social casino experience where players can enjoy the thrill
                    of winning in a fun, fair, and risk-free environment.
                  </Typography>
                  <Typography className='news-instruction-mob'>
                    By subscribing, you agree to our Privacy Policy and provide consent to receive updates from our
                    company.
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} lg={8}>
            <Grid className='footer-link-grid'>
              <Grid className='footer-link-card'>
                <Typography variant='h4'>links</Typography>
                <Link
                  to='/about-us'
                  className={`${selectedTab === '/about-us' ? 'active' : ''}`}
                  onClick={(event) => handleNavigation(event, '/about-us')}
                >
                  About
                </Link>
                {/* <Link>Payments</Link> */}
                <Link to={PlayerRoutes.Landing} state={{ scrollToSec: 'reviews' }} onClick={handleClick}>
                  Reviews
                </Link>

                {/* <a >Promotions</a> */}
                <Link
                  to='/faq'
                  className={`${selectedTab === '/faq' ? 'active' : ''}`}
                  onClick={(event) => handleNavigation(event, '/faq')}
                >
                  FAQ
                </Link>

                <Link
                  to='/contact-us'
                  className={`${selectedTab === '/contact-us' ? 'active' : ''}`}
                  onClick={(event) => handleNavigation(event, '/contact-us')}
                >
                  Contact Us
                </Link>
                <Link
                  to='/blog'
                  className={`${selectedTab === '/blog' ? 'active' : ''}`}
                  onClick={(event) => handleNavigation(event, '/blog')}
                >
                  Blog
                </Link>
              </Grid>
              <Grid className='footer-link-card'>
                <Typography variant='h4'>Game Categories</Typography>

                <Link to='/games' className={`${selectedTab === '/games' ? 'active' : ''}`}>
                  All Games
                </Link>
                <Link
                  to='/games/social-slot-games'
                  className={`${selectedTab === '/games/social-slot-games' ? 'active' : ''}`}
                >
                  Slots
                </Link>

                <Link
                  to='/games/popular-casino-games'
                  className={`${selectedTab === '/games/popular-casino-games' ? 'active' : ''}`}
                >
                  Featured
                </Link>

                <Link
                  to='/games/live-dealer-casino-games'
                  className={`${selectedTab === '/games/live-dealer-casino-games' ? 'active' : ''}`}
                >
                  Live Dealer
                </Link>

                <Link
                  to='/games/instant-win-casino-games'
                  className={`${selectedTab === '/games/instant-win-casino-games' ? 'active' : ''}`}
                >
                  Instant Win
                </Link>

                <Link
                  to='/games/casino-table-games'
                  className={`${selectedTab === '/games/casino-table-games' ? 'active' : ''}`}
                >
                  Table Games
                </Link>
              </Grid>
              <Grid className='contact-col footer-link-card'>
                <Typography variant='h4'>Contact</Typography>
                <a href='https://www.facebook.com/themoneyfactorycasino?mibextid=LQQJ4d' target='_blank'>
                  <img src={seoFacebook} alt='Facebook' />
                  Facebook
                </a>
                <a
                  href='https://www.instagram.com/themoneyfactory/?igsh=OTA0NjFscDMyZ3gz&utm_source=qr'
                  target='_blank'
                >
                  <img src={seoInstagram} alt='Instagram' />
                  Instagram
                </a>
                <a href='https://x.com/TMFcasino?s=03' target='_blank'>
                  <img src={seoTwitter} alt='Twitter' />
                  Twitter
                </a>
                <a href='https://t.me/TheMoneyFactoryUS' target='_blank'>
                  <img src={seoTelegram} alt='Telegram' />
                  Telegram
                </a>
                {/* <Link>
                  <img src={seoYoutube} alt="Youtube" />
                  Youtube</Link> */}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Box className='seo-mob-copyright'>
          <Typography>@ {currectYear} themoneyfactory.com I All rights reserved.</Typography>
          <Grid>
            {cmsData && cmsData?.length > 0 ? (
              cmsData?.map((data, index) => {
                return (
                  <Link className='footerLink' onClick={(e) => onLinkClick(e, `/cms/${data?.slug}`)} key={index}>
                    {data?.title?.EN}
                  </Link>
                )
              })
            ) : (
              <></>
            )}
            {/* <Link>Cookies Settings </Link> */}
          </Grid>
        </Box>
      </Box>
      <Box className={classes.seoLandingFooterCopyright}>
        <Typography>@ {currectYear} themoneyfactory.com I All rights reserved.</Typography>
        <Grid>
          {cmsData && cmsData?.length > 0 ? (
            cmsData?.map((data, index) => {
              return (
                <Link className='footerLink' onClick={(e) => onLinkClick(e, `/cms/${data?.slug}`)} key={index}>
                  {data?.title?.EN}
                </Link>
              )
            })
          ) : (
            <></>
          )}
          {/* <Link>Cookies Settings </Link> */}
        </Grid>
      </Box>
    </>
  )
}

export default SeoLandingFooter
