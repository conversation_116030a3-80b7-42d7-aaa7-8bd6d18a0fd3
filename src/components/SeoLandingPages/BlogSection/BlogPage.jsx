import React, { useRef, useEffect } from 'react'
import useStyles from '../styles'
import { Grid, Typography, Box, Input } from '@mui/material'
import fbIcon from '../../../components/ui-kit/icons/svg/facebook-icon.svg'
import telegramBlog from '../../../components/ui-kit/icons/svg/telegramBlog.svg'
import igIcon from '../../../components/ui-kit/icons/svg/instagram-icon.svg'
import CouponCross from '../../../components/ui-kit/icons/svg/social-close.svg'
import xIcon from '../../../components/ui-kit/icons/svg/x-icon.svg'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'
import SeoLandingFooter from '../SeoLandingFooter'
import bloguser from '../../ui-kit/blogAssets/blog-avatar-male.webp'
import shareIcon from '../../../components/ui-kit/icons/svg/social-share.svg'
import menuIcon from '../../../components/ui-kit/icons/svg/menu-icon.svg'
import useBlogSection from './hook/useBlogSection'
import { GeneralQuery } from '../../../reactQuery'
import { DefaultBlog } from '../../ui-kit/icons/opImages'
import parse from 'html-react-parser'
import useBlogStore from '../../../store/useBlogSection'
import LandingHeader from '../../../pages/Landing/LandingHeader'
import SeoHead from '../../../utils/seoHead'
import { useNavigate } from 'react-router-dom'
const BlogPage = () => {
  const classes = useStyles()
  const swiperRef = useRef(null)

  const {
    selectedTab,
    setSelectedTab,
    openShareId,
    setOpenShareId,
    searchOpen,
    searchText,
    handleClick,
    filteredArticles,
    shareOnInstagram,
    handleOnChange,
    mainArticle,
    mainTrendArticle,
    handleDynamicClick,
    formatDateToWords,
    popularBlogs,
    setPopularBlogs,
    recentlyAddedBlogs,
    setRecentlyAddedBlogs
  } = useBlogSection()

  const navigate = useNavigate()
  const { blogs, setBlogs } = useBlogStore((state) => state)
  console.log('🚀 ~ BlogPage ~ blogs:', blogs?.[0]?.bannerImageUrl)

  useEffect(() => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slideTo(0)
    }
  }, [])

  const blogSuccessToggler = (data) => {
    setBlogs(data)
  }

  GeneralQuery.getBlogsQuery({ blogSuccessToggler })

  useEffect(() => {
    if (blogs) {
      const filteredPopularBlogs = blogs?.filter((blog) => blog?.isPopularBlog === true)
      setPopularBlogs(filteredPopularBlogs)
      const recentBlogs = blogs?.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)).slice(0, 3)
      setRecentlyAddedBlogs(recentBlogs)
    }
  }, [blogs])

  const htmlRenderer = (htmlContent) => {
    return parse(htmlContent)
  }
  const handleImageClick = (article) => {
    setOpenShareId(openShareId === article.id ? null : article.id)
  }

  const sortedBlogs = blogs?.slice().sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  const recentBlog = sortedBlogs?.[0]
  const secondRecentBlog = sortedBlogs?.[1]

  return (
    <Grid className={classes.landingPageWrap}>
      <SeoHead
        title='The Money Factory Blog | Insights on Social Casino Games & Free Play Tips'
        description='Explore expert tips, trends, and insights on social casino gaming at The Money Factory Blog. Learn how to play for free, spot legit platforms, and enjoy risk-free entertainment.'
        // keywords={['about us', 'company', 'mission', 'team']}
        // url='https://www.themoneyfactory.com/blog'
        // imageUrl='https://testttyourdomain.com/images/about-us.jpg'
      />
      <>
        <header>
          <LandingHeader />
        </header>

        <main>
          <Box className={classes.blogWrapper}>
            <Typography variant='h1'>Uncover Expert Tips, Game Reviews, and Winning Strategies</Typography>
            <Typography variant='h2'>
              Browse expert reviews, exciting slot games, and educational content to enhance your gaming skills and
              discover the best slots to play for big wins
            </Typography>
            {/* MAIN ARTICLE SECTION */}

            <section>
              <Grid container spacing={{ xs: 1, md: 2 }} className='blog-hero-section'>
                <Grid item xs={12} lg={8}>
                  <article>
                    <Grid
                      className='hero-banner'
                      style={{ backgroundImage: `url(${recentBlog?.bannerImageUrl})` }}
                      onClick={() => navigate(recentBlog?.slug)}
                    >
                      <Grid className='hero-content'>
                        <Typography variant='h3'>{mainArticle.name}</Typography>
                        <Box className='blog-detail'>
                          <Typography variant='body1'>Clay Johnson</Typography>
                          <Typography variant='body1'>{mainArticle.publishedDate}</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </article>
                </Grid>

                {/* SEARCH POPULAR AND RECENT ARTICLES */}

                <Grid item xs={12} lg={4}>
                  <aside>
                    <Box className='hero-blog-list'>
                      <Box className='btn-wrap'>
                        {searchOpen ? (
                          <>
                            <Input
                              value={searchText}
                              onChange={(event) => {
                                handleOnChange(event)
                              }}
                              autoFocus
                              placeholder='Search Articles Here'
                              autoComplete='off'
                            />
                          </>
                        ) : (
                          <>
                            <button
                              className={`btn ${selectedTab === 'popular' ? 'btn-primary' : 'btn-secondary'}`}
                              onClick={() => setSelectedTab('popular')}
                            >
                              Popular
                            </button>
                            <button
                              className={`btn ${selectedTab === 'recent' ? 'btn-primary' : 'btn-secondary'}`}
                              onClick={() => setSelectedTab('recent')}
                            >
                              Recent
                            </button>
                          </>
                        )}
                      </Box>
                      <Box className='blog-list-wrap'>
                        {searchOpen ? (
                          filteredArticles.length > 0 ? (
                            filteredArticles.map((article) => (
                              <Box className='blog-box'>
                                <Box className='img-wrap'>
                                  <figure>
                                    <img src={article.image} alt='Blog' onClick={() => handleClick(article.id)} />
                                  </figure>
                                </Box>
                                <Box className='blog-content'>
                                  <Typography variant='h4' onClick={() => handleClick(article.id)}>
                                    {article.name}
                                  </Typography>
                                  <Typography variant='body1'>{article.publishedDate}</Typography>
                                </Box>
                              </Box>
                            ))
                          ) : searchText != '' ? (
                            <h3>No Articles Found</h3>
                          ) : (
                            <></>
                          )
                        ) : selectedTab === 'popular' ? (
                          popularBlogs && popularBlogs?.length > 0 ? (
                            popularBlogs?.map((article) => (
                              <Box className='blog-box' key={article?.blogPostId || article?.slug}>
                                <Box className='img-wrap'>
                                  <figure>
                                    <img
                                      src={article?.bannerImageUrl || DefaultBlog}
                                      alt={article?.bannerImageAlt}
                                      onClick={() => handleClick(article?.slug || article?.blogPostId, !!article?.slug)}
                                    />
                                  </figure>
                                </Box>
                                <Box className='blog-content'>
                                  <Typography
                                    variant='h4'
                                    onClick={() => handleClick(article?.slug || article?.blogPostId)}
                                  >
                                    {article?.metaTitle}
                                  </Typography>
                                  <Typography variant='body1'>{formatDateToWords(article?.createdAt)}</Typography>
                                </Box>
                              </Box>
                            ))
                          ) : (
                            <Typography variant='h6' style={{ color: 'red' }}>
                              No popular blogs available.
                            </Typography>
                          )
                        ) : recentlyAddedBlogs && recentlyAddedBlogs?.length > 0 ? (
                          recentlyAddedBlogs?.map((article) => (
                            <Box className='blog-box'>
                              <Box className='img-wrap'>
                                <figure>
                                  <img
                                    src={article.bannerImageUrl || DefaultBlog}
                                    alt={article?.bannerImageAlt}
                                    onClick={() => handleClick(article?.slug)}
                                  />
                                </figure>
                              </Box>
                              <Box className='blog-content'>
                                <Typography variant='h4' onClick={() => handleClick(article?.slug)}>
                                  {article.metaTitle}
                                </Typography>
                                <Typography variant='body1'>{formatDateToWords(article?.createdAt)}</Typography>
                              </Box>
                            </Box>
                          ))
                        ) : (
                          <Typography variant='h6' style={{ color: 'red' }}>
                            We don’t have any blog updates at the moment — stay tuned!
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </aside>
                </Grid>
              </Grid>
            </section>

            {/* TRENDING ARTICLE SECTION */}

            <section>
              <Grid className='trends-section'>
                <Typography variant='h3'>Trends</Typography>
                <Grid container spacing={{ xs: 0, md: 2 }} className='trend-box'>
                  <Grid item xs={12} md={6}>
                    <article>
                      <Box className='trend-main-blog'>
                        <Grid
                          className='blog-img'
                          style={{ backgroundImage: `url(${secondRecentBlog?.bannerImageUrl})` }}
                          onClick={() => navigate(secondRecentBlog?.slug)}
                        >
                          <Box className='img-content'>
                            <Box className='text-box'>Trends in Social Casino Games</Box>
                            <Box className='trend-label'>Trend</Box>
                          </Box>
                        </Grid>

                        {/* MAIN TREND ARTICLE  */}
                      </Box>
                    </article>
                  </Grid>

                  {/* TREND ARTICLE */}

                  <Grid item xs={12} md={6}>
                    <aside>
                      {blogs && blogs?.length > 0 ? (
                        <Box className='trend-list-wrap'>
                          {blogs?.slice(0, 2)?.map((article) => (
                            <Box className='trend-blog'>
                              <Box className='img-wrap'>
                                <figure>
                                  <img
                                    src={article?.bannerImageUrl || DefaultBlog}
                                    alt={article?.bannerImageAlt}
                                    onClick={() => handleClick(article?.slug)}
                                  />
                                </figure>
                              </Box>
                              <Box className='trend-content'>
                                <Typography variant='body1'>{formatDateToWords(article?.updatedAt)}</Typography>
                                <Typography variant='h4' onClick={() => handleClick(article?.slug)}>
                                  {article.metaTitle}
                                </Typography>
                              </Box>
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box
                          sx={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            color: '#fff',
                            padding: 3,
                            textAlign: 'center',
                            justifyContent: 'center',
                            alignItems: 'center',
                            minHeight: '300px'
                          }}
                        >
                          <Typography
                            variant='h4'
                            sx={{
                              fontWeight: 600,
                              fontSize: { xs: '1.5rem', md: '2rem' }
                            }}
                          >
                            We don’t have any blog updates at the moment — stay tuned!
                          </Typography>
                        </Box>
                      )}
                    </aside>
                  </Grid>
                </Grid>
              </Grid>
            </section>

            {/* ALL ARTICLE SECTION */}

            <section>
              <Grid className='article-section'>
                <Typography variant='h3'>All Articles</Typography>
                <Box className='article-box'>
                  <Box className='article-wrap'>
                    {blogs && blogs?.length > 0 ? (
                      blogs?.map((article, index) => (
                        <article>
                          <Box className='article-main-blog' key={article.blogPostId || index}>
                            <Grid className='blog-img'>
                              <Box className='img-content'>
                                <figure>
                                  <img
                                    src={article?.bannerImageUrl || DefaultBlog}
                                    alt={article?.bannerImageAlt}
                                    onClick={() => handleDynamicClick(article?.slug)}
                                  />
                                </figure>
                              </Box>
                            </Grid>
                            <Grid className='article-right'>
                              <Box className='blog-detail'>
                                <Box className='blog-user'>
                                  <Box className='blog-avatar'>
                                    <figure>
                                      {' '}
                                      <img src={bloguser} alt='user' />
                                    </figure>
                                  </Box>
                                  <Typography variant='h5'>Clay Johnson</Typography>
                                </Box>
                                <Typography variant='body1'>Blog</Typography>
                              </Box>
                              <Typography className='article-date' variant='body1'>
                                {formatDateToWords(article?.updatedAt)}
                              </Typography>
                              <Typography variant='h3' onClick={() => handleDynamicClick(article?.slug)}>
                                {article?.metaTitle}
                              </Typography>
                              <Typography variant='body1'>
                                {article?.metaDescription?.substring(0, 75) + '...'}
                              </Typography>

                              {/* SHARING SECTION */}
                              <Box className='share-icon'>
                                <div className='icons-wrap'>
                                  <figure>
                                    <img
                                      src={openShareId === article?.blogPostId ? CouponCross : shareIcon}
                                      alt='share'
                                      onClick={() =>
                                        setOpenShareId(openShareId === article?.blogPostId ? null : article?.blogPostId)
                                      }
                                    />
                                  </figure>
                                  {openShareId === article?.blogPostId && (
                                    <>
                                      <a
                                        href={`https://www.facebook.com/sharer/sharer.php?u=https://themoneyfactory.com/blog/${article.blogPostId}`}
                                        target='_blank'
                                        rel='noopener noreferrer'
                                        className='fb-icon'
                                      >
                                        <figure>
                                          <img src={fbIcon} alt='Facebook' />
                                        </figure>
                                      </a>
                                      <a
                                        href={`https://twitter.com/intent/tweet?url=https://themoneyfactory.com/blog/${article.blogPostId}`}
                                        target='_blank'
                                        rel='noopener noreferrer'
                                      >
                                        <figure>
                                          <img src={xIcon} alt='X' />
                                        </figure>
                                      </a>
                                      <a
                                        href={`https://telegram.me/share/url?url=https://themoneyfactory.com/blog/${article.blogPostId}`}
                                        target='_blank'
                                        rel='noopener noreferrer'
                                      >
                                        <figure>
                                          <img src={telegramBlog} alt='Telegram' />
                                        </figure>
                                      </a>
                                      <a target='_blank' rel='noopener noreferrer'>
                                        <figure>
                                          <img
                                            src={igIcon}
                                            alt='Instagram'
                                            onClick={() => shareOnInstagram(article.blogPostId)}
                                          />
                                        </figure>
                                      </a>
                                    </>
                                  )}
                                </div>
                                <div>
                                  <figure>
                                    <img src={menuIcon} alt='menu' onClick={() => handleDynamicClick(article?.slug)} />
                                  </figure>
                                </div>
                              </Box>
                            </Grid>
                          </Box>
                        </article>
                      ))
                    ) : (
                      <Typography variant='h5' style={{ color: 'white' }}>
                        We don’t have any blog updates at the moment — stay tuned!
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Grid>
            </section>

            {/* UPDATE ARTICLES SECTION */}

            <section>
              <Box className='updates-section'>
                <Typography variant='h3'>Updates</Typography>
                <Typography variant='body1'>
                  Stay one step ahead with The Money Factory's casino updates! We're your ultimate source for all things
                  gaming on our site; from new jackpots, thrilling features to providers.
                </Typography>

                {blogs && blogs.length > 0 ? (
                  <Grid container spacing={{ xs: 1, md: 2 }}>
                    {blogs.slice(0, 6).map((article) => (
                      <Grid item xs={12} sm={6} md={4} key={article?.slug}>
                        <article>
                          <Box className='update-blog'>
                            <figure>
                              <img
                                src={article?.bannerImageUrl || DefaultBlog}
                                alt={article?.bannerImageAlt}
                                onClick={() => handleClick(article?.slug, true)}
                              />
                            </figure>
                            <Typography variant='h4' onClick={() => handleClick(article?.slug, true)}>
                              {article?.metaTitle}
                            </Typography>
                            <Box className='blog-detail'>
                              <Typography variant='body1'>The Money Factory Social Casino</Typography>
                              <Typography className='blog-date' variant='body1'>
                                {formatDateToWords(article?.createdAt)}
                              </Typography>
                            </Box>
                          </Box>
                        </article>
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Typography variant='h5' style={{ color: 'White' }}>
                    We don’t have any blog updates at the moment — stay tuned!
                  </Typography>
                )}
              </Box>
            </section>
          </Box>
        </main>

        <footer>
          <SeoLandingFooter />
        </footer>
      </>
    </Grid>
  )
}

export default BlogPage
