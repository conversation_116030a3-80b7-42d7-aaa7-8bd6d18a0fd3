import React, { useState } from 'react'
// import LandingHeader from '../LandingHeader'
import SeoLandingFooter from '../SeoLandingFooter'
import { Grid, Typography, Box, Input, Accordion, AccordionSummary, AccordionDetails } from '@mui/material'
import useStyles from '../styles.js'
import { Link } from 'react-router-dom'
import { usePortalStore } from '../../../store/userPortalSlice.js'
import Signup from '../../Modal/Signup/index.jsx'
import LandingHeader from '../../../pages/Landing/LandingHeader/index.jsx'


import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import SeoHead from '../../../utils/seoHead.jsx'

const SlingoCasinoGames = () => {
  const [expanded, setExpanded] = useState(false)
  const classes = useStyles()
  const portalStore = usePortalStore()
  const [expandedFaq, setExpandedFaq] = useState(null)

  const handleFaqChange = (index) => (event, isExpanded) => {
    setExpandedFaq(isExpanded ? index : null)
  }
  const handleGameClick = () => {
    portalStore.openPortal(() => <Signup />, 'loginModal')
  }
  const faqs = [
    {
      question: '1. Are these real money Slingo games?',
      answer:
        'No real-money gambling here! All our Slingo games run on a sweepstakes system. You can play for free with Gold Coins or try your luck at winning real prizes using Sweepstakes Coins — all 100% legal across the U.S.'
    },
    {
      question: '2. Do I need to download an app?',
      answer: 'Nope! Just visit The Money Factory on your phone or computer and start spinning right from your browser.'
    },
    {
      question: '3. What’s the best Slingo game for beginners?',
      answer:
        'A: We recommend starting with Slingo Classic or Slingo Carnival — they’re simple, colorful, and easy to follow, even if it’s your first time.'
    }
  ]
  return (
    <>
      {/* <Helmet>
        <title>Play Slingo Casino Games Online – Free & Fun!</title>
        <meta
          name='description'
          content='Play the best Slingo casino games online for free! Enjoy exciting gameplay that blends slots and bingo for big wins.'
        />
        <meta name='robots' content='index, follow' />
      </Helmet> */}
      <SeoHead
        title='Play Slingo Casino Games Online – Free & Fun!'
        description='Play the best Slingo casino games online for free! Enjoy exciting gameplay that blends slots and bingo for big wins.'
        // keywords={['about us', 'company', 'mission', 'team']}
        // url='https://www.themoneyfactory.com/games/slingo-casino-games'
        // imageUrl='https://testttyourdomain.com/images/about-us.jpg'
      />
      <Grid className={`${classes.slingoCasinoWrap} ${classes.subgamePage}`}>
        <LandingHeader />
        <Box className='jackpot-cards-wrap'>
          <Typography variant='h1'>Discover a New Way to Win with Slingo Casino Games</Typography>
          <Grid className='cards-wrap'>
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/fist-of-destruction.webp`}
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/slayer-inc.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/big-bass.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/elven-princesses.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/buffalo-king.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/jokers-jewel.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/coin-volcano.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/sky-pearls.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/gates-of-olympus.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/great-rhino.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/gates-of-olympus.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/sugar-rush.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/black-wolf.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
            <img
              src={`${import.meta.env.VITE_LANDING_PAGE_IMAGE_URL}/outlaws-inc.webp`}
              loading='lazy'
              onClick={handleGameClick}
              onError={(e) => {
                e.target.onerror = null
                e.target.src = '../src/components/ui-kit/icons/png/game-2.webp'
              }}
            />
          </Grid>
        </Box>
        <Typography variant='h2'>Welcome to the world of Slingo</Typography>
        <Typography variant='body1'>
          where bingo meets slots in one fast-paced, wildly entertaining game format you won’t find anywhere else.
        </Typography>
        <Typography variant='body1'>
          At The Money Factory, we’ve taken this popular hybrid game and added our own twist: free-to-play action with a
          legal sweepstakes setup. That means you can enjoy Slingo casino games with no real-money risk and still play
          for real rewards.
        </Typography>
        <Typography variant='body1'>
          If you like games that are easy to pick up, full of surprises, and seriously fun, it’s time to play Slingo
          games online, free, safe, and packed with chances to win.
        </Typography>
        <Typography variant='h2'>What Is Slingo?</Typography>
        <Typography variant='body1'>
          Slingo is a cross between a slot machine and a bingo card. Each round, you spin a set of reels at the bottom
          of your screen, aiming to match numbers on a 5x5 bingo grid above. Complete lines (aka “Slingos”) and unlock
          boosters, bonuses, or wild symbols that change the game in real time.
        </Typography>
        <Typography variant='body1' className='space-down'>
          Whether you're new to gaming or a seasoned slots player, Slingo slots offer a whole new type of fun. Think
          classic bingo simplicity, plus the thrill of slot-style spins - all rolled into one.
        </Typography>
        <Typography variant='h2'>Why Play Slingo Games Online at The Money Factory?</Typography>
        <div className='pointer-box'>
          <Typography variant='h5' className='pointer'>
            {' '}
            New Players Get a Head Start
          </Typography>
          <Typography variant='body1' className='pointer'>
             As soon as you sign up, you'll receive:
          </Typography>
          <div className='pointer-box'>
            <Typography variant='body1' className='pointer'>
              15,000 Gold Coins to start playing instantly
            </Typography>
            <Typography variant='body1' className='pointer'>
              3 Sweepstakes Coins after verifying your email
            </Typography>
            <Typography variant='body1' className='space-down'>
              You’ll also score access to daily bonuses, giveaways, and other perks to keep the fun going strong.
            </Typography>
          </div>
        </div>
        <Typography variant='h5' className='pointer'>
          {' '}
          Instant Play, Anytime
        </Typography>
        <Typography variant='body1' className='space-down'>
          No apps. No downloads. Just open your browser, head to our Slingo casino games section, and start spinning.
          Whether you’re on mobile, desktop, or tablet, the game fits your lifestyle — no interruptions.
        </Typography>
        <Typography variant='h5' className='pointer'>
          {' '}
          A Growing Slingo Community
        </Typography>
        <Typography variant='body1' className='space-down'>
          Join thousands of other U.S. players who are spinning, scoring, and socializing. Participate in promotions,
          beat high scores, and enjoy a shared gaming experience like no other.
        </Typography>
        <Typography variant='h2'>Our Most Popular Slingo Games</Typography>
        <Typography variant='body1' className='space-down'>
          We’re constantly adding new Slingo games to our lineup, with something for every kind of player. Here are a
          few player favorites:
        </Typography>
        <Grid className='space-down' container spacing={{ xs: 1, md: 2 }}>
          <Grid item xs={12} md={6}>
            <Box className='feature-box'>
              <Box className='content-box pointer'>
                <Typography variant='body1'>
                  Slingo Boom – Fast action with explosive bonuses and limited spins that raise the stakes every round.
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box className='feature-box'>
              <Box className='content-box pointer'>
                <Typography variant='body1'>
                  Slingo Showdown – A card game twist with Wild West flair, where every spin deals out a poker-style
                  hand.
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box className='feature-box'>
              <Box className='content-box pointer'>
                <Typography variant='body1'>
                  Slingo Carnival – Bright, fun, and full of mini-games and surprises in a fairground-inspired setting.
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box className='feature-box'>
              <Box className='content-box pointer'>
                <Typography variant='body1'>
                  Slingo Rainbow Gold – Irish luck meets bonus-packed spins, pots of gold, and charming animations.
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
        <Typography variant='body1' className='space-down'>
          Each title comes with its own visuals, features, and gameplay quirks — so you’re never stuck playing the same
          thing twice.
        </Typography>
        <Typography variant='h2'>Pro Tips for Playing Slingo Like a Pro</Typography>
        <Typography variant='body1'>
          <Typography variant='span' className='yellow-text'>
            Use Your Boosters Wisely
          </Typography>{' '}
          – Some Slingo games let you use wilds or jokers to complete tricky spots. Don’t burn them early — save them
          for when it really counts.
        </Typography>
        <Typography variant='body1'>
          <Typography variant='span' className='yellow-text'>
            Keep an Eye on Your Spin Count
          </Typography>{' '}
          – Every round comes with a limited number of spins. Plan your moves to make the most out of each one.
        </Typography>
        <Typography variant='body1' className='space-down'>
          <Typography variant='span' className='yellow-text'>
            Look Out for Extra Spin Offers{' '}
          </Typography>{' '}
          – Missed a win by just one space? Some games give you a chance to buy extra spins using in-game coins or bonus
          features. Use them when it really matters.
        </Typography>
        <Box className='faq-wrap'>
          <Typography variant='h3'>Frequently Ask Questions</Typography>
          {faqs.map((faq, index) => (
            <Accordion key={index} expanded={expandedFaq === index} onChange={handleFaqChange(index)}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant='h5'>{faq.question}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                {Array.isArray(faq.answer) ? (
                  faq.answer.map((line, i) => (
                    <Typography key={i} variant='body1'>
                      {line}
                    </Typography>
                  ))
                ) : (
                  <Typography variant='body1'>{faq.answer}</Typography>
                )}
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>

        <Typography variant='h2'>How to Start Playing Free Slingo Games Online</Typography>
        <Typography variant='body1'>Getting started is easy:</Typography>
        <Typography variant='body1'>
          1.{' '}
          <Link style={{ color: '#23A8FC', textDecorationColor: '#23A8FC' }} onClick={handleGameClick}>
            {' '}
            Create your free account
          </Link>{' '}
          at The Money Factory
        </Typography>
        <Typography variant='body1'>
          2.{' '}
          <Typography variant='span' className='yellow-text'>
            {' '}
            Verify your email{' '}
          </Typography>{' '}
          to unlock your welcome bonus
        </Typography>
        <Typography variant='body1'>
          3.{' '}
          <Typography variant='span' className='yellow-text'>
            Visit the Slingo Games
          </Typography>{' '}
          section in the game lobby
        </Typography>
        <Typography variant='body1'>
          4.{' '}
          <Typography variant='span' className='yellow-text'>
            Start spinning and lining up wins
          </Typography>
        </Typography>
        <Typography variant='body1' className='space-down'>
          It takes less than 2 minutes to join and play- no banking info needed, no downloads required.
        </Typography>
        <Typography variant='h2'>Try Slingo Today — It’s Slots with a Twist</Typography>
        <Typography className='pointer' variant='body1'>
          Whether you're chasing jackpots, hitting wilds, or just killing time with something exciting, Slingo casino
          games offer a truly different gaming experience.
        </Typography>
        <Typography className='pointer' variant='body1'>
          👉{' '}
          <Link style={{ color: '#23A8FC', textDecorationColor: '#23A8FC' }} onClick={handleGameClick}>
            {' '}
            Sign up now
          </Link>{' '}
          to play free Slingo games online and start collecting rewards — no pressure, just play.
        </Typography>
        <Typography variant='body1' className='space-down pointer'>
          Join the fun. Spin smart. Sling often.
        </Typography>
        <SeoLandingFooter />
      </Grid>
    </>
  )
}

export default SlingoCasinoGames
