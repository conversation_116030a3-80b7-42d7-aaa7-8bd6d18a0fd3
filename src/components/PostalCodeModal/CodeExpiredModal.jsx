import React from 'react';
import {
    Dialog,
    DialogTitle,
    <PERSON>alog<PERSON><PERSON>,
    Button,
    Box,
} from '@mui/material';
import { paymentFailed } from '../ui-kit/icons/webp';
import useStyles from '../StepperForm/StepperForm.styles'


const CodeExpiredModal = ({ onClose }) => {
    const classes = useStyles()
    return (
        <Dialog
            className={classes.codeExpiredModalWrap}
            open={true}
            aria-labelledby="status-modal-title"
        >
            <Box className="payment-container">
                <img src={paymentFailed} alt="Payment-failed" />
                <DialogTitle className="status-modal-title"
                    sx={{ color: 'rgba(235, 0, 0, 1)' }}>
                    CODE HAS EXPIRED!
                </DialogTitle>
                <DialogActions>
                    <Button
                        variant="contained"
                        className='btn btn-primary'
                        onClick={onClose}>
                        Close
                    </Button>
                </DialogActions>
            </Box>
        </Dialog>
    );
};

export default CodeExpiredModal;
