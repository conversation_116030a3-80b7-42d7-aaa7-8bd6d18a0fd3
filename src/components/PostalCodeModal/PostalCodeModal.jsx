import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, TextField, IconButton, Box, Grid, Tooltip } from '@mui/material'
import { ContentCopy as  ContentCopyIcon, Close as CloseIcon } from '@mui/icons-material'
import useStyles from '../StepperForm/StepperForm.styles'
import CodeExpiredModal from './CodeExpiredModal'


const PostalCodeModal = ({ code, onClose }) => {
  const decodedCode = atob(code)
  const [timeLeft, setTimeLeft] = useState(30)
  const [isCodeActive, setIsCodeActive] = useState(true)
  const [isCopied, setIsCopied] = useState(false)
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false)

  const classes = useStyles()

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          setIsCodeActive(false)
          setIsStatusModalOpen(true)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const handleCopy = () => {
    navigator.clipboard.writeText(decodedCode)
    setIsCopied(true)
  }

  const handleCloseStatusModal = () => {
    setIsStatusModalOpen(false)
    onClose()
  }

  return (
    <Box className={classes.promoModalWrap}>
      <IconButton
        edge='start'
        color='inherit'
        className='close-btn'
        onClick={handleCloseStatusModal}
        aria-label='close'
      >
        <CloseIcon />
      </IconButton>

      <Typography variant='h6' sx={{ fontWeight: 'bold', color: '#FFC107' }} gutterBottom>
        Generated Code
      </Typography>
      <Grid className='copy-input-wrap'>
        <TextField fullWidth value={decodedCode} disabled className='copy-input' />
        <Tooltip title={isCopied ? 'Copied to clipboard!' : 'Copy'} open={isCopied || undefined} placement='top' arrow>
          <IconButton
            onClick={handleCopy}
            className='copy-icon'
            disabled={!isCodeActive}
            sx={{
              backgroundColor: '#333',
              borderRadius: '50%',
              color: 'white',
              padding: '8px',
              '&:hover': {
                backgroundColor: '#FFC107'
              }
            }}
          >
            <ContentCopyIcon />
          </IconButton>
        </Tooltip>
      </Grid>
      <Typography variant='body2' color='#fff' className='timer-text'>
        {isCodeActive ? `Code expires in ${timeLeft} seconds` : 'Code has expired.'}
      </Typography>
      {isStatusModalOpen && <CodeExpiredModal onClose={handleCloseStatusModal} />}
    </Box>
  )
}

export default PostalCodeModal
