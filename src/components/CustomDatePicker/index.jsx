import React, { useState, useEffect, useRef } from 'react'
import useStyles from '../../pages/Accounts/Accounts.styles'
import { Grid } from '@mui/material'
import Select from 'react-select'

const CustomDatePicker = ({ onDateValidation, setDate, disabled }) => {
  const classes = useStyles()
  const [day, setDay] = useState('')
  const [month, setMonth] = useState('')
  const [year, setYear] = useState('')
  const hasSetInitialDate = useRef(false)

  const days = Array.from({ length: 31 }, (_, i) => i + 1)
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ]
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: currentYear - 1900 - 18 + 1 }, (_, i) => currentYear - 18 - i)

  const getDaysInMonth = (month, year) => {
    const monthIndex = months.indexOf(month) + 1
    return new Date(year, monthIndex, 0).getDate()
  }

  useEffect(() => {
    if (setDate && !hasSetInitialDate.current) {
      const [year, month, day] = setDate.split('-')
      const newMonth = months[parseInt(month) - 1]
      setDay(parseInt(day))
      setMonth(newMonth)
      setYear(Number(year))
      hasSetInitialDate.current = true
    }
  }, [setDate, months])

  useEffect(() => {
    if (day && month && year) {
      const formattedDate = `${year}-${String(months.indexOf(month) + 1).padStart(2, '0')}-${String(day).padStart(
        2,
        '0'
      )}`
      const selectedDate = new Date(`${year}-${months.indexOf(month) + 1}-${day}`)
      const today = new Date()
      const eighteenYearsAgo = new Date(today.setFullYear(today.getFullYear() - 18))

      if (selectedDate > eighteenYearsAgo) {
        onDateValidation(false, `You must be at least 18 years old. (Selected date: ${formattedDate})`, formattedDate)
      } else {
        onDateValidation(true, `Selected date: ${formattedDate}`, formattedDate)
      }
    }
  }, [day, month, year, onDateValidation, months])

  const availableDays = days ? Array.from({ length: getDaysInMonth(month, year) }, (_, i) => i + 1) : []

  // react-select options
  const monthOptions = months.map((m) => ({ value: m, label: m }))
  const dayOptions = availableDays.map((d) => ({ value: d, label: d }))
  const yearOptions = years.map((y) => ({ value: y, label: y }))

  return (
    <Grid className={classes.dobWrap}>
      {/* Month */}
      {disabled ? (
        <div
          className='inputSelect disabled dobInputSelect'
          style={{ padding: '8px 12px', border: '1px solid #293937', borderRadius: '4px', width: '100%' }}
        >
          {month || 'Month'}
        </div>
      ) : (
        <Select
          value={monthOptions.find((opt) => opt.value === month)}
          onChange={(selected) => setMonth(selected?.value)}
          options={monthOptions}
          placeholder='Month'
          classNamePrefix='react-select'
          className='dobInputSelect'
          isDisabled={disabled}
        />
      )}

      {/* Day */}
      {disabled ? (
        <div
          className='inputSelect disabled dobInputSelect'
          style={{ padding: '8px 12px', border: '1px solid #293937', borderRadius: '4px' }}
        >
          {day || 'Day'}
        </div>
      ) : (
        <Select
          value={dayOptions.find((opt) => opt.value === day)}
          onChange={(selected) => setDay(selected?.value)}
          options={dayOptions}
          placeholder='Day'
          classNamePrefix='react-select'
          className='dobInputSelect'
          isDisabled={disabled}
        />
      )}

      {/* Year */}
      {disabled ? (
        <div
          className='inputSelect disabled dobInputSelect'
          style={{ padding: '8px 12px', border: '1px solid #293937', borderRadius: '4px' }}
        >
          {year || 'Year'}
        </div>
      ) : (
        <Select
          value={yearOptions.find((opt) => opt.value === year)}
          onChange={(selected) => setYear(selected?.value)}
          options={yearOptions}
          placeholder='Year'
          classNamePrefix='react-select'
          className='dobInputSelect'
          isDisabled={disabled}
        />
      )}
    </Grid>
  )
}

export default CustomDatePicker
