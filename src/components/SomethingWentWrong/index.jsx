import React, { useEffect, useState } from 'react'
import {
  Grid,
  IconButton,
  Typography
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import  VerificationFailed  from '../ui-kit/icons/svg/verified-failed.svg'

const SomethingWentWrong = ({ error }) => {
  const [errorMessage, setErrorMessage] = useState('Something Went Wrong !')

  const portalStore = usePortalStore((state) => state)

  const handleClose = () => {
    portalStore.closePortal()
  }

  useEffect(() => {
    if (error?.name === 'WelcomePurchaseBonusExpired') {
      setErrorMessage('Welcome Purchase Bonus Expired')
    } else if (error?.name === 'DepositInitiatedError') {
      // setErrorMessage(error?.description)
      setErrorMessage('Deposit is already running in another tab, please close that to initiate a new deposit, Happy Playing!!')
    }
  }, [error])

  return (
    <>
      <Grid className='inner-modal-header'>
        <Typography variant='h4'> Failed </Typography>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
      <Grid
        className='payment-status-content'
        display='flex'
        flexDirection='column'
        alignItems='center'
      >
        <img src={VerificationFailed} style={{ height: '100px', width: '100px' }} alt='Success' className='payment-status-icon' />
        <Typography sx={{ color: '#FFFFFF' }} variant='h4'>{errorMessage}</Typography>
        {errorMessage === 'Welcome Purchase Bonus Expired' || error?.name === 'DepositInitiatedError'
          ? (null)
          : (<Typography sx={{ color: '#FFFFFF' }}> Please Contact our support team.</Typography>)}
      </Grid>
    </>
  )
}

export default SomethingWentWrong
