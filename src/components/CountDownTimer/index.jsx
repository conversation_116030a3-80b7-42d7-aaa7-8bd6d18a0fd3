import React, { useEffect, useState } from 'react'
import moment from 'moment'
import { Box, Typography } from '@mui/material'
import useStyles from '../../pages/TournamentsPage/TournamentDetail/TournamentDetail.styles'

export const getLocalTimeAsUTC = () => {
  const prependZero = (num) => {
    return num < 10 ? `0${num}` : num
  }

  const currentDate = new Date()
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  const date = currentDate.getDate()

  const hours = currentDate.getHours()
  const minutes = currentDate.getMinutes()
  const seconds = currentDate.getSeconds()
  const milliseconds = currentDate.getMilliseconds()

  const localTimeAsUTC = `${year}-${prependZero(month + 1)}-${prependZero(date)}T${prependZero(hours)}:${prependZero(
      minutes
  )}:${prependZero(seconds)}.${milliseconds}Z`

  return localTimeAsUTC
}

const CountDownTimer = ({ eventDateTime, isLocalAsUTC }) => {
  const classes = useStyles()
  const calculateTimeLeft = () => {
    const eventTime = (Math.floor(new Date(moment(eventDateTime)) / 1000)).toString()
    let currentTime = (Math.floor(Date.now() / 1000)).toString()

    if (isLocalAsUTC) {
      currentTime = (Math.floor(new Date(moment.utc()) / 1000)).toString()
        }
    const leftTime = eventTime - currentTime
    let duration = moment.duration(leftTime, 'seconds')
    const interval = 1000
    if (duration.asSeconds() <= 0) {
      clearInterval(interval)
      // window.location.reload(true); //#skip the cache and reload the page from the server
    }
    duration = moment.duration(duration.asSeconds() - 1, 'seconds')

    return (
      <>
        {(duration.seconds() >= 0)
          ? (
            <Box className={classes.tournamentTimer}>
              <Box className='tournamentCountdown'>
                <span className='timer-digit days-digit tournamentTime'> {((duration.days() < 10) ? ('0' + duration.days()) : duration.days())}</span>
                <Typography variant='span' component='span'>
                  Days
                </Typography>
              </Box>
              <span className='counter-divider'>:</span>
              <Box className='tournamentCountdown'>
                <span className='timer-digit hours-digit tournamentTime'> {((duration.hours() < 10) ? ('0' + duration.hours()) : duration.hours())}</span>
                <Typography variant='span' component='span'>
                  Hour
                </Typography>
              </Box>
              <span className='counter-divider'>:</span>
              <Box className='tournamentCountdown'>
                <span className='timer-digit minutes-digit tournamentTime'> {((duration.minutes() < 10) ? ('0' + duration.minutes()) : duration.minutes())}</span>
                <Typography variant='span' component='span'>
                  Minute
                </Typography>
              </Box>
              <span className='counter-divider'>:</span>
              <Box className='tournamentCountdown'>
                <span className='timer-digit seconds-digit tournamentTime'>{((duration.seconds() < 10) ? ('0' + duration.seconds()) : duration.seconds())}</span>
                <Typography variant='span' component='span'>
                  Second
                </Typography>
              </Box>
            </Box>)
          : <>{/* <Button disabled={true} className={classes.joinTournamentBtn}>Started</Button>  */}</>}
      </>
    )
  }

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

  useEffect(() => {
    setTimeout(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)
  })

  return (
    timeLeft
  )
}

export default CountDownTimer
