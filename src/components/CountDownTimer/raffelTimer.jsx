import React, { useEffect, useState } from 'react'
import moment from 'moment'
import { Grid, Typography } from '@mui/material'
import useStyles from '../../pages/PromotionsPage/Promotions.styles'

const RaffleTimer = ({ eventDateTime }) => {
  const classes = useStyles()
  const [giveAwayStatus, setGiveAwayStatus] = useState('upcoming')

  const calculateTimeLeft = () => {
    const eventTime = moment(eventDateTime).unix() // Convert to Unix timestamp
    const currentTime = moment().unix()
    const leftTime = eventTime - currentTime

    if (leftTime <= 0) {
      return null // Event has started, or time has passed
    }

    const duration = moment.duration(leftTime, 'seconds')

    // Calculate total hours, including days
    const totalHours = Math.floor(duration.asHours())
    const minutes = duration.minutes()
    const seconds = duration.seconds()

    return {
      hours: totalHours,
      minutes,
      seconds
    }
  }

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

  useEffect(() => {
    const intervalId = setInterval(() => {
      const updatedTimeLeft = calculateTimeLeft()
      if (!updatedTimeLeft) {
        clearInterval(intervalId) // Clear the interval if the event has started
        setGiveAwayStatus('live')
      } else {
        setTimeLeft(updatedTimeLeft)
      }
    }, 1000)

    return () => clearInterval(intervalId) // Cleanup interval on component unmount
  }, [eventDateTime])

  if (!timeLeft) {
    return (
      <Typography variant='h6'>The event has started!</Typography>
    )
  }

  return (
    <Grid className={classes.counterSection}>
      <Typography>{giveAwayStatus}</Typography>
      <Grid className='counter-wrap'>
        <Grid className='counter-card'>{timeLeft.hours.toString().padStart(2, '0')}</Grid>
        <Grid className='counter-card'>{timeLeft.minutes.toString().padStart(2, '0')}</Grid>
        <Grid className='counter-card'>{timeLeft.seconds.toString().padStart(2, '0')}</Grid>
      </Grid>
      <Grid className='counter-wrap'>
        <Grid className='counter-card-value'>Hours</Grid>
        <Grid className='counter-card-value'>Minutes</Grid>
        <Grid className='counter-card-value'>Seconds</Grid>
      </Grid>
    </Grid>
  )
}

export default RaffleTimer
