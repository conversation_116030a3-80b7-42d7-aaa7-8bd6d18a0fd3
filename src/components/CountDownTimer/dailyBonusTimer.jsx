import React, { useEffect, useState } from 'react'
import moment from 'moment'
import { Box } from '@mui/material'

const DailyBonusTimer = ({ eventDateTime }) => {
  const calculateTimeLeft = () => {
    const eventTime = (Math.floor(new Date(moment(eventDateTime)) / 1000)).toString()
    const currentTime = (Math.floor(Date.now() / 1000)).toString()
    const leftTime = eventTime - currentTime
    let duration = moment.duration(leftTime, 'seconds')
    const interval = 1000
    if (duration.asSeconds() <= 0) {
      clearInterval(interval)
      // window.location.reload(true); //#skip the cache and reload the page from the server
    }
    duration = moment.duration(duration.asSeconds() - 1, 'seconds')
  
    return (
      <>
        {(duration.seconds() >= 0)
          ? (
            <Box style={{ display: 'flex', marginLeft: '5px' }}>
              {/* <Box className='tournamentCountdown'>
                <span className='timer-digit days-digit tournamentTime'> {((duration.days() < 10) ? ('0' + duration.days()) : duration.days())}</span>
              </Box> */}
              {/* <span className='counter-divider'>:</span> */}
              <Box className='tournamentCountdown'>
                <span className='timer-digit hours-digit'> {((duration.hours() < 10) ? ('0' + duration.hours()) : duration.hours())}</span>
              </Box>
              <span className='counter-divider'>:</span>
              <Box className='tournamentCountdown'>
                <span className='timer-digit minutes-digit'> {((duration.minutes() < 10) ? ('0' + duration.minutes()) : duration.minutes())}</span>
              </Box>
              <span className='counter-divider'>:</span>
              <Box className='tournamentCountdown'>
                <span className='timer-digit seconds-digit'>{((duration.seconds() < 10) ? ('0' + duration.seconds()) : duration.seconds())}</span>
              </Box>
            </Box>)
          : <> {/* <Button disabled={true} className={classes.joinTournamentBtn}>Started</Button>  */} </>}
      </>
    )
  }

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

  useEffect(() => {
    setTimeout(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)
  })

  return (
    timeLeft
  )
}

export default DailyBonusTimer
