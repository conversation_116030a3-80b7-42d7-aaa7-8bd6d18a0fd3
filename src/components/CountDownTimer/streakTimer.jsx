import { useEffect, useState } from 'react';

const StreakTimer = ({ initialTime, onTimerEnd }) => {
  const [timeLeft, setTimeLeft] = useState(initialTime);

  useEffect(() => {
    setTimeLeft(initialTime); // Restart timer when prop updates
  }, [initialTime]);

  useEffect(() => {
    if (timeLeft === '00:00:00') {
      onTimerEnd(); // Notify parent when timer ends
      return;
    }
    const interval = setInterval(() => {
      setTimeLeft((prevTime) => {
        const [h, m, s] = prevTime.split(':').map(Number);
        const totalSeconds = h * 3600 + m * 60 + s - 1;

        if (totalSeconds <= 0) {
          clearInterval(interval);
          return '00:00:00';
        }

        const newH = Math.floor(totalSeconds / 3600);
        const newM = Math.floor((totalSeconds % 3600) / 60);
        const newS = totalSeconds % 60;

        return `${String(newH).padStart(2, '0')}:${String(newM).padStart(2, '0')}:${String(newS).padStart(2, '0')}`;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [timeLeft]);

  return <span>{timeLeft}</span>;
};

export default StreakTimer;
