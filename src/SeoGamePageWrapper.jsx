import React from 'react'
import { useParams } from 'react-router-dom'

import SocialSlotGames from './components/SeoLandingPages/SlotGames/SocialSlotGames'
import SlingoCasinoGames from './components/SeoLandingPages/SlingoCasino'
import InstantWin<PERSON>asino from './components/SeoLandingPages/InstantWinCasino'
import ScratchCardGames from './components/SeoLandingPages/ScratchCardGame'
import PopularCasinoGames from './components/SeoLandingPages/PopularCasinoGames'
import LiveDealerCasino from './components/SeoLandingPages/LiveDealerCasino'
import SeoDynamicGamePage from './components/SeoDynamicGamePage'

const seoComponentMap = {
  'social-slot-games': SocialSlotGames,
  'slingo-casino-games': SlingoCasinoGames,
  'casino-table-games': SlingoCasinoGames, // mapped same component intentionally
  'online-casino-scratch-card-games': Scratch<PERSON>ardGames,
  'instant-win-casino-games': InstantWinCasino,
  'live-dealer-casino-games': LiveDealerCasino,
  'popular-casino-games': PopularCasinoGames
}

const SeoGamePageWrapper = () => {
  const { gameSlug } = useParams()
  // Add a simple test to verify the component is rendering
  if (!gameSlug) {
    return <div>No gameSlug parameter found</div>
  }

  const MatchedComponent = seoComponentMap[gameSlug]

  if (MatchedComponent) {
    console.log('Using matched component for:', gameSlug)
    return <MatchedComponent />
  }

  console.log('Using SeoDynamicGamePage for:', gameSlug)
  return <SeoDynamicGamePage />
}

export default SeoGamePageWrapper
