import { create } from 'zustand'

import { deleteCookie, getCookie, setCookie } from '../utils/cookiesCollection'

const useAuthStore = create((set) => ({
  isAuthenticated: false,
  pathCookieCheck: getCookie('path') ? true : false,
  setAuthenticated: (value) => {
    set({ isAuthenticated: value })
  },
  setPathCookieCheck: (value) => {
    if (value) {
      setCookie('path', '/home', 30)
    } else {
      deleteCookie('path')
    }
    set({ pathCookieCheck: value })
  }
}))

export default useAuthStore
