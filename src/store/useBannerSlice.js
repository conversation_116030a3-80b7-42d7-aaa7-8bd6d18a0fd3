import { create } from 'zustand'

export const useBannerStore = create((set) => ({
  beforeLoginBannersList: [],
  afterLoginBannersList: [],
  lobbyBanners: [],
  lobbySlider: [],
  promotionBanners: [],
  affiliatePage: [],
  personalBonus: [],
  tournamentPage: [],
  buyPage: [],
  bonusPromotionPage: [],
  referAfriend: [],
  hallofFame: [],
  packagePage: [],
  tier: [],
  tournamentDetailPage: [],
  isInitialized: false, // Track if banner data has been loaded

  setBannerList: (data) => {
    set((state) => ({
      ...state, // Spread the current state to avoid overwriting
      lobbyBanners: data?.filter((x) => x?.pageRoute === "lobby"),
      lobbySlider: data?.filter((x) => x?.pageRoute === "lobbySlider"),
      promotionBanners: data?.filter((x) => x?.pageRoute === "promotions-page"),
      affiliatePage: data?.filter((x) => x?.pageRoute === "affiliate"),
      personalBonus: data?.filter((x) => x?.pageRoute === "personalBonus"),
      tournamentPage: data?.filter((x) => x?.pageRoute === "tournaments-page"),
      buyPage: data?.filter((x) => x?.pageRoute === "buyPage"),
      packagePage: data?.filter((x) => x?.pageRoute === "user/store"),
      bonusPromotionPage: data?.filter((x) => x?.pageRoute === "bonusPromotionPage" && x?.name !== "personalBonus"),
      referAfriend: data?.filter((x) => x?.pageRoute === "refer-a-friend"),
      hallofFame: data?.filter((x) => x?.pageRoute === "hall-of-fame"),
      tier: data?.filter((x) => x?.pageRoute === "tier"),
      tournamentDetailPage: data?.filter((x) => x.pageRoute === "tournament-detail"),
      beforeLoginBannersList: data?.filter(({ visibility, isActive }) => visibility === 0 && isActive),
      afterLoginBannersList: data?.filter(({ visibility, isActive }) => visibility === 1 && isActive),
      isInitialized: true // Mark as initialized when data is set
    }));
  }
}));

export const usePopupStore = create((set) => ({
  setPopupList: (data) => {
    set(() => ({
      lobbyPopups: data?.filter((x) => x?.popupName === "lobbyPage"),
      promotionPopups: data?.filter((x) => x?.popupName === "promotionPage"),
      storePopups: data?.filter((x) => x?.popupName === "storePage"),
      rewardPopups: data?.filter((x) => x?.popupName === "rewardPage"),
    }))
  }
}))
