import { create } from 'zustand'

const initialState = {
  gameData: [],
  gameDataCount: 0,
  gameDataLoading: false,
  selectedSubCategory: null,
  limit: 21,
  pageNo: 1,
  search: '',
  selectedProvider: { value: '', label: '' },
  options: [],
  featuredSubcategory: [],
  subCategories: [],
  subCategoryName: 'Lobby',
  selectedSubCat: '',
  selectedSubCategoryId: 0,
  recentGamesList: [],
  favorites: [],
  favoriteIds: {}
}

export const useGamesStore = create((set) => ({
  ...initialState,

  // Setters
  setSubCategories: (value) => set(() => ({ subCategories: value })),
  setSelectedSubCategory: (value) => set(() => ({ selectedSubCategory: value })),
  setSelectedSubCat: (value) => set(() => ({ selectedSubCat: value })),
  setLimit: (value) => set(() => ({ limit: value })),
  setPageNo: (value) => set(() => ({ pageNo: value })),
  setSearch: (value) => set(() => ({ search: value })),
  setSelectedProvider: (value) => set(() => ({ selectedProvider: value })),
  setOptions: (value) => set(() => ({ options: value })),
  setFeaturedSubcategory: (value) => set(() => ({ featuredSubcategory: value })),
  setSubCategoryName: (value) => set(() => ({ subCategoryName: value })),
  setSelectedSubCategoryId: (value) => set(() => ({ selectedSubCategoryId: value })),
  setGameData: (value) => set(() => ({ gameData: value })),
  setGameDataCount: (value) => set(() => ({ gameDataCount: value })),
  setGameDataLoading: (value) => set(() => ({ gameDataLoading: value })),
  setRecentGamesList: (value) => set(() => ({ recentGamesList: value })),
  setFavoriteIds: (value) => set(() => ({ favoriteIds: value })),
  setFavorites: (value) => set(() => ({ favorites: value })),
  resetGamesStore: () =>
    set((state) => ({
      ...initialState,
      featuredSubcategory: state.featuredSubcategory, // preserve featuredSubcategory
      subCategories: state.subCategories // preserve subCategories
    }))
}))
