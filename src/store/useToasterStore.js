import {create} from 'zustand';

const useToasterStore = create((set)=> ({
    openToaster:false,
    toasterMessage :'',
    toasterType:'success',

    handleToaster: ({ openToaster, toasterMessage, toasterType }) => set({
        openToaster,
        toasterMessage,
        toasterType,
      }),
    clearToaster : ()=>set({
      openToaster:false,
      toasterMessage :'',
      toasterType:'success',}),

}))

export default useToasterStore;