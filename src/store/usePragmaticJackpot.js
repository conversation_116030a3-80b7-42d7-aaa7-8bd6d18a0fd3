import { create } from 'zustand'

const initialState = {
  pragmaticJackpotSc: {},
  pragmaticJackpotGc: {}
}

export const usePragmaticJackpotStore = create((set) => ({
  ...initialState,

  // Setter to update SC jackpot values
  setPragmaticJackpotSc: (scData) => {
    set({ pragmaticJackpotSc: scData })
  },

  // Setter to update GC jackpot values
  setPragmaticJackpotGc: (gcData) => {
    set({ pragmaticJackpotGc: gcData })
  },

  // Unified setter from full data object
  setPragmaticJackpotData: (data) => {
    const { scType = {}, gcType = {} } = data || {}
    set({
      pragmaticJackpotSc: scType,
      pragmaticJackpotGc: gcType
    })
  }
}))
