
import { create } from 'zustand'


export const useFullPageDialog = create((set) => ({
    openFullPageDialog: { dialog: 'isOpenHomeDialog' },
    openDialog: { dialog: null },
    modalTraversInfo: {},
    setOpenFullPageDialog: (data) => {
      set(() => ({ openFullPageDialog: data }))
    },
    setCloseFullPageDialog: () => {
      set(() => ({ openFullPageDialog: { dialog: 'isOpenHomeDialog' } }))
    },
    setOpenDialog: (data) => {
      set(() => ({ openDialog: data }))
    },
    setCloseDialog: () => {
      set(() => ({ openDialog: { dialog: null } }))
    },
    setAllowArrow: (data) => {
      set(() => ({ modalTraversInfo: data }))
    }
  }))