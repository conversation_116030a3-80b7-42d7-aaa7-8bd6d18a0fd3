import { create } from 'zustand'

import { useDepositInitStore, usePaymentProcessStore, usePromocodeAppliedStore } from './store'
import { usePaymentStore } from './usePaymentStore'
import { deletePromocode, deleteDeposit } from '../utils/apiCalls'
// import usePaysafePayment from '../pages/Store/PaymentModal/hooks/usePaysafe'
// import toast from 'react-hot-toast'

export const usePortalStore = create((set, get) => ({
  isVisible: false,
  isFullyOpen: false,
  Component: null,
  theme: '',
  options: {
    persistent: false,
    contentClassName: '',
    onClose: null,
    refetchGameUrl: false
  },
  setFullyOpen: (isFullyOpen) => {
    set(() => ({ isFullyOpen }))
  },
  setComponent: (Component) => {
    set(() => ({ Component }))
  },
  setOptions: (options) => {
    set(() => ({ options }))
  },
  setVisible: (isVisible) => {
    set(() => ({ isVisible }))
  },
  setTheme: (theme) => {
    set(() => ({ theme }))
  },
  closePortal: () => {
    const promocodeApplied = usePromocodeAppliedStore.getState().promocodeApplied
    const setCancelDeposit = usePaymentProcessStore.getState().setCancelDeposit
    const transactionId = window.localStorage.getItem('transactionId')
    const trustlyLoaded = window.localStorage.getItem('isTrustlyLoaded')
    const transactionDetails = useDepositInitStore.getState().transactionDetails
    const resetPaymentStore = usePaymentStore.getState().resetPaymentStore

    if (get()?.theme === 'StepperModal' && promocodeApplied && transactionId && !trustlyLoaded) {
      deletePromocode({ transactionId: transactionId })
      usePromocodeAppliedStore.setState({ promocodeApplied: false, promocode: '' })
    }
    if (get()?.theme === 'StepperModal' && transactionId && transactionDetails?.success !== false) {
      setCancelDeposit(true)
      deleteDeposit({ transactionId: transactionId })
        .then(() => {
          window.localStorage.removeItem('transactionId')
          window.localStorage.removeItem('paymentMethod')
          window.localStorage.removeItem('isTrustlyLoaded')
          setCancelDeposit(false)
        })
        .catch((err) => {
          console.log(err)
          window.localStorage.removeItem('transactionId')
          window.localStorage.removeItem('paymentMethod')
          window.localStorage.removeItem('isTrustlyLoaded')
          setCancelDeposit(false)
        })
    } else {
      window.localStorage.removeItem('isTrustlyLoaded')
      window.localStorage.removeItem('transactionId')
    }

    // Reset Payment Store
    resetPaymentStore()

    if (typeof get()?.options?.onClose === 'function') {
      get().options.onClose()
    }
    get().setVisible(false)
    get().setFullyOpen(false)
    get().setTheme('')
  },
  openPortal: (Component, theme, options = {}) => {
    if (typeof Component !== 'function') {
      throw new Error('Component must be a React component')
    }
    get().setVisible(true)
    get().setComponent(Component)
    get().setTheme(theme)
    get().setOptions({ refetchGameUrl: !!get()?.options?.refetchGameUrl, ...options })
  }
}))
