// store/blogStore.js
import { create } from 'zustand';

const useBlogStore = create((set, get) => ({
  blogs: [],
  selectedBlog: null,

  setBlogs: (blogs) => set({ blogs }),

  setSelectedBlog: (blog) => set({ selectedBlog: blog }),

  // Get blog by id or slug from current blogs
  getBlogByIdOrSlug: (idOrSlug) => {
    const blogs = get().blogs;
    return blogs.find((blog) => blog.id === idOrSlug || blog.slug === idOrSlug) || null;
  },
}));

export default useBlogStore;
