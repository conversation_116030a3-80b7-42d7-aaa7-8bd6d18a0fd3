import { create } from 'zustand'

export { useFullPageDialog } from './useFullPageDialogSlice'
export { useUserStore } from './useUserSlice'
export { usePortalStore } from './userPortalSlice'
export { userPortalSliceDaily } from './userPortalSliceDaily'
export { userPortalSliceWelcome } from './userPortalSliceWelcome'
export { useBannerStore } from './useBannerSlice'
export { userPortalPaymentSlice } from './userPortalPaymentSlice'
export { useGamesStore } from './useGamesSlice'
export { useJackpotStore } from './useJackpotStore'

export const usePaymentStore = create((set) => ({
  paymentStatus: '',
  setPaymentStatus: (data) => {
    set(() => ({
      paymentStatus: data
    }))
  }
}))

export const useStateStore = create((set) => ({
  stateList: [],
  setStateList: (data) => {
    set(() => ({
      stateList: data
    }))
  }
}))

export const useCoinStore = create((set) => {
  const coinType = localStorage.getItem('coin') || 'SC'
  function saveCoinType(data) {
    localStorage.setItem('coin', data)
  }

  return {
    coinType,
    setCoinType: (data) => {
      saveCoinType(data)
      set(() => ({
        coinType: data
      }))
    }
  }
})

export const useResponsibleStore = create((set) => ({
  messageObj: {},
  setSuccessInfo: (data) => {
    set(() => ({
      messageObj: data
    }))
  }
}))

export const useCMSStore = create((set) => ({
  cmsData: [],
  setCMSData: (data) => {
    set(() => ({
      cmsData: data
    }))
  }
}))

export const useDefaultLoading = create((set) => ({
  isLoginLoading: false,
  setLoginLoading: (data) => {
    set(() => ({
      isLoginLoading: data
    }))
  }
}))

export const useSelectedProviderStore = create((set) => ({
  selectedProviderId: '',
  setSelectedProviderId: (data) => {
    set(() => ({
      selectedProviderId: data
    }))
  }
}))

export const useLandingPageHit = create((set) => ({
  isLandingPageHit: false,
  setLandingPageHit: (data) => {
    set(() => ({
      isLandingPageHit: data
    }))
  }
}))

export const useSearchDialogStore = create((set) => ({
  isDialog: false,
  isFavBySearch: false,
  setSearchDialog: (data) => {
    set(() => ({
      isDialog: data
    }))
  },
  setFavBySearch: (data) => {
    set(() => ({
      isFavBySearch: data
    }))
  }
}))

export const useSearchInputStore = create((set) => ({
  searchQuery: '',
  setSearchText: (data) => {
    set(() => ({
      searchQuery: data
    }))
  }
}))

export const useSubCategoryOnLoadStore = create((set) => ({
  subCategories: [],
  isLoading: false,
  refetch: false,
  setSubCategories: (data) => {
    set(() => ({
      subCategories: data
    }))
  },
  setIsLoading: (data) => {
    set(() => ({
      isLoading: data
    }))
  },
  setRefetch: (data) => {
    set(() => ({
      refetch: data
    }))
  }
}))

export const useSeoCategoriesStore = create((set) => ({
  seoSubCategories: [],
  setSeoSubCategories: (data) => {
    set(() => ({
      seoSubCategories: data
    }))
  }
}))

export const useFavoriteGamesStore = create((set) => ({
  favorites: [],
  updateFavorites: (item) => {
    set((state) => {
      const isPresent = state.favorites?.includes(item)
      if (isPresent) {
        // Item exists, remove it
        return {
          favorites: state.favorites?.filter((i) => i !== item)
        }
      } else {
        // Item does not exist, add it
        return {
          favorites: [...state.favorites, item]
        }
      }
    })
  }
}))

export const useGeolocationGameplayUpdate = create((set) => ({
  geolocationGameplay: 0,
  geocomplyErrorMsg: '',
  troubleShoot: '',
  preCheckError: '',
  setGeoLocationGameplay: (data) => {
    set(() => ({
      geolocationGameplay: data
    }))
  },
  setGeocomplyErrorMsg: (data) => {
    set(() => ({
      geocomplyErrorMsg: data
    }))
  },
  setGeoErrorResponse: (data) => {
    set(() => ({
      geoErrorResponse: data
    }))
  },
  setErrorTroubleShoot: (data) => {
    set(() => ({
      troubleShoot: data
    }))
  },
  setPreCheckError: (data) => {
    set(() => ({
      preCheckError: data
    }))
  }
}))

export const useSeoGameData = create((set) => ({
  selectedGameData: [],
  setSelectedGameData: (data) => {
    set(() => ({
      selectedGameData: data
    }))
  }
}))
export const usePromocodeAppliedStore = create((set) => ({
  promocodeApplied: false,
  promocode: '',
  paysafeOpen: false,
  setPromocodeApplied: (data) => {
    set(() => ({
      promocodeApplied: data.promocodeApplied,
      promocode: data.promocode
    }))
  },
  setPaysafeOpen: (data) => {
    set(() => ({
      paysafeOpen: data.paysafeOpen
    }))
  }
}))

export const useDepositInitStore = create((set) => ({
  depositTransactionId: '',
  isDepositInitLoading: false,
  transactionDetails: {},
  setDepositTransactionId: (data) => {
    set(() => ({
      depositTransactionId: data.depositTransactionId,
      isDepositInitLoading: data.isDepositInitLoading,
    }))
  },
  setDepositDetails: (data) => {
    set(() => ({
      transactionDetails: data
    }))
  }
}))

// export const usePaymentProcessStore = create((set) => ({
//   paymentMethod: '',
//   paymentDepositTransactionId: '',
//   paymentError: {},
//   paymentErrorMessage: '',
//   cancelDepositOpen: false,
//   paymentState: {},
//   setPaymentMethod: (data) => {
//     set(() => ({
//       paymentMethod: data
//     }))
//     window.localStorage.setItem('paymentMethod', data)
//   },
//   setPaymentDepositTransactionId: (data) => {
//     set(() => ({
//       paymentDepositTransactionId: data.paymentDepositTransactionId
//     }))
//   },
//   setPaymentErrorState: (data) => {
//     set(() => ({
//       paymentError: data.paymentError,
//       paymentErrorMessage: data.paymentErrorMessage
//     }))
//   },
//   setCancelDeposit: (data) => {
//     set(() => ({
//       cancelDepositOpen: data
//     }))
//   },
//   setPaymentState: (data) => {
//     set(() => ({
//       paymentState: data
//     }))
//   }
// }))

// export const useSiteLogoStore = create((set) => ({
//   desktopLogo: '',
//   mobileLogo: '',
//   setDesktopLogoData: (data) => {
//     set(() => ({
//       desktopLogo: data
//     }))
//   },
//   setMobileLogoData: (data) => {
//     set(() => ({
//       mobileLogo: data
//     }))
//   }
// }))

export const usePaymentProcessStore = create((set) => ({
  paymentMethod: '',
  paymentDepositTransactionId: '',
  paymentError: {},
  paymentErrorMessage: '',
  cancelDepositOpen: false,
  paymentState: {},
  setPaymentMethod: (data) => {
    set(() => ({
      paymentMethod: data
    }))
    window.localStorage.setItem('paymentMethod', data)
  },
  setPaymentDepositTransactionId: (data) => {
    set(() => ({
      paymentDepositTransactionId: data.paymentDepositTransactionId
    }))
  },
  setPaymentErrorState: (data) => {
    set(() => ({
      paymentError: data.paymentError,
      paymentErrorMessage: data.paymentErrorMessage
    }))
  },
  setCancelDeposit: (data) => {
    set(() => ({
      cancelDepositOpen: data
    }))
  },
  setPaymentState: (data) => {
    set(() => ({
      paymentState: data
    }))
  },
  
}))

export const useSiteLogoStore = create((set) => ({
  desktopLogo: '',
  mobileLogo: '',
  setDesktopLogoData: (data) => {
    set(() => ({
      desktopLogo: data
    }))
  },
  setMobileLogoData: (data) => {
    set(() => ({
      mobileLogo: data
    }))
  },
  setIsDepositInitLoading: (data) => {
    set(() => ({
      isDepositInitLoading: data
    }))
  }
}))

export const useVipQuetsions = create((set) => ({
  vipQuestions: [],
  vipAnswers: [],
  setVipQuestions: (data) => {
    set(() => ({
      vipQuestions: data
    }))
  },
  setVipAnswers: (data) => {
    set(() => ({
      vipAnswers: data
    }))
  }
}))
