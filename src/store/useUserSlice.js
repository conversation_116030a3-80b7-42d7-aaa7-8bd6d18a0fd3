import { create } from 'zustand'

import { useGamesStore } from './useGamesSlice'
import { useJackpotStore } from './useJackpotStore'
import { deleteAccessTokenCookies } from '../utils/cookiesCollection'

const initialState = {
  userDetails: null,
  walletSocketConnection: false,
  liveWinnerSocketConnection: false,
  tournamentEndSocketConnection: false,
  TournamentLeaderboardSocketConnection: false,
  isAuthenticate: localStorage.getItem('username') ? true : false,
  onPurchaseModal: null,
  specialPackageData: null,
  kycStatus: false,
  gameName: null,
  MaintenanceSocketConnection: false
}

export const useUserStore = create((set, get) => ({
  ...initialState,
  setSpecialPackageData: (data) => {
    set(() => ({ specialPackageData: data }))
  },
  setUserDetails: (data) => {
    set(() => ({ userDetails: data }))
  },
  setWalletSocketConnection: (data) => {
    set(() => ({ walletSocketConnection: data }))
  },
  setLiveWinnerSocketConnection: (data) => {
    set(() => ({ liveWinnerSocketConnection: data }))
  },
  setTournamentEndSocketConnection: (data) => {
    set(() => ({ tournamentEndSocketConnection: data }))
  },
  setTournamentLeaderboardSocketConnection: (data) => {
    set(() => ({ TournamentLeaderboardSocketConnection: data }))
  },
  setMaintenanceSocketConnection: (data) => {
    set(() => ({ MaintenanceSocketConnection: data }))
  },
  setIsAuthenticate: (data) => {
    set(() => ({ isAuthenticate: data }))
  },
  setPurchaseItem: (data) => {
    set(() => ({ onPurchaseModal: data }))
  },
  setKycStatus: (data) => {
    set(() => ({ kycStatus: data }))
  },
  setGameName: (data) => {
    set(() => ({ gameName: data }))
  },

  logout: () => {
    const isAllowedUserAccess = localStorage.getItem('allowedUserAccess')
    window.localStorage.clear()
    localStorage.setItem('allowedUserAccess', isAllowedUserAccess)
    deleteAccessTokenCookies('accessToken')
    get().setUserDetails(null)
    get().setIsAuthenticate(false)
    useGamesStore.getState().resetGamesStore()
    useJackpotStore.getState().resetJackpotStore()
    set(() => ({ ...initialState }))
  }
}))
