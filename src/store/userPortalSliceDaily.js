import { create } from 'zustand'

export const userPortalSliceDaily = create((set, get) => ({
  isVisible: false,
  isFullyOpen: false,
  Component: null,
  theme: '',
  options: {
    persistent: false,
    contentClassName: '',
    onClose: null,
    refetchGameUrl: false
  },
  setFullyOpen: (isFullyOpen) => {
    set(() => ({ isFullyOpen }))
  },
  setComponent: (Component) => {
    set(() => ({ Component }))
  },
  setOptions: (options) => {
    set(() => ({ options }))
  },
  setVisible: (isVisible) => {
    set(() => ({ isVisible }))
  },
  setTheme: (theme) => {
    set(() => ({ theme }))
  },
  closePortal: () => {
    if (typeof get()?.options?.onClose === 'function') {
      get().options.onClose()
    }
    get().setVisible(false)
    get().setFullyOpen(false)
    get().setTheme('')
  },
  openPortal: (Component, theme, options = {}) => {
    if (typeof Component !== 'function') {
      throw new Error('Component must be a React component')
    }
    get().setVisible(true)
    get().setComponent(Component)
    get().setTheme(theme)
    get().setOptions({ refetchGameUrl: !!get()?.options?.refetchGameUrl, ...options })
  }
}))
