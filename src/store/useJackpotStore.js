import { create } from 'zustand'

const initialState = {
  jackpotOn: false,
  jackpotMultiplier: 1,
  jackpotData: {
    jackpotPoolAmount: 0,
    entryAmount: 0,
    recentJackpotWinners: []
  },
  jackpotWin: false,
  jackpotWinAmount: 0,
  newJackpot: false
}

export const useJackpotStore = create((set, get) => ({
  ...initialState,

  // Setters
  setJackpotOn: (data) => {
    set(() => ({ jackpotOn: data }))
  },
  setJackpotData: (key, value) =>
    set((state) => ({
      jackpotData: {
        ...state.jackpotData,
        [key]: value
      }
    })),
  setJackpotPoolAmount: (amount) =>
    set((state) => ({
      jackpotData: {
        ...state.jackpotData,
        jackpotPoolAmount: amount
      }
    })),
  setJackpotWin: (status, data) => {
    set(() => ({ jackpotWin: status, jackpotWinAmount: data }))
  },
  setNewJackpot: (data) => {
    set(() => ({ newJackpot: data }))
  },
  setJackpotMultiplier: (data) => {
    set(() => ({ jackpotMultiplier: data }))
  },
  // Reset
  resetJackpotStore: () => {
    const { jackpotData } = get()
    set(() => ({
      ...initialState,
      jackpotData: {
        ...initialState.jackpotData,
        jackpotPoolAmount: jackpotData.jackpotPoolAmount // preserve this
      }
    }))
  }
}))
