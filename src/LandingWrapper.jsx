import React, { useEffect } from 'react'
import useAuthStore from './store/useAuthStore'
import Lobby from './pages/Lobby/Lobby'
import { useNavigate } from 'react-router-dom'

const LandingWrapper = () => {
  const navigate = useNavigate()
  const { pathCookieCheck } = useAuthStore()

  // Use useEffect to handle navigation after render
  useEffect(() => {
    if (!pathCookieCheck) {
      navigate('/online-social-casino-games')
    }
  }, [pathCookieCheck, navigate])

  // If pathCookieCheck is true, render Lobby
  if (pathCookieCheck) {
    return <Lobby />
  }

  // Return null if navigation is happening
  return null
}

export default LandingWrapper
