import { useCallback } from 'react'
import { generatePath, useNavigate } from 'react-router-dom'

export default function useStartGame () {
  const navigateTo = useNavigate()

  const startGame = useCallback(({ gameName, gameId }) => {
    const path = generatePath('/gameplay', {
      gameName,
      gameId: String(gameId)
    })

    navigateTo(path)
  }, [navigateTo])

  return {
    startGame
  }
}
