import { toast } from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'

import { useGamesStore } from '../store/store'
import { deleteCookie, deleteRefferalCookie, setCookie } from '../utils/cookiesCollection'
import { signoutEvent } from '../utils/optimoveHelper'

export default function useLogout () {
  // const geolocationContext = useGeolocation();
  // const stopIpService = geolocationContext?.stopIpService;
  const setSelectedSubCat = useGamesStore((state) => state.setSelectedSubCat)
  const navigate = useNavigate()
  const logoutHandler = (path = '/') => {
    deleteCookie('affiliateCode')
    deleteCookie('affiliateId')
    deleteCookie('affiliatePromocode')
    deleteRefferalCookie('referralcode')
    setCookie('onloadGameApi', true)
    window.localStorage.removeItem('loginTime')
    setSelectedSubCat('Lobby')
    toast.success('You have successfully logged out.')
    signoutEvent()
    navigate(path)
    // if (stopIpService) {
    //   stopIpService();
    // } else {
    //   console.warn("stopIpService is undefined");
    // }
  }
  return {
    logoutHandler
  }
}
