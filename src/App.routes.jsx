import React, { Suspense, lazy, useEffect, useState } from 'react'
import { Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom'
import { usePortalStore, useUserStore } from './store/store'
import Loader from './components/Loader'
import './App.css'
import { Grid } from '@mui/material'
import useStyles from './MainPage.styles'
import {
  deleteAccessTokenCookies,
  deleteCookie,
  getCookie,
  setCookie,
  setReferralSocialCookie,
  setreferralCode
} from './utils/cookiesCollection'
import LayoutWrapper from './LayoutWrapper'
import { useDynamoKey } from './reactQuery'
import { shouldShowHeader, shouldShowSideBarAndFooter } from './withLobbyHeaderAndSidebar'
import appRoutes from './appRoutes'
import Header from './components/Header'
import SideBar from './components/SideBar'

const Footer = lazy(() => import('./components/Footer'))


let isUserAuthenticate = localStorage.getItem('username')

export const createRoute = (path, component, onlyWithoutAuth, privateRoute, props, level) => {
  const auth = useUserStore((state) => state)
  const userStore = useUserStore((state) => state)

  if (level) {
    return (
      <Route path={path} key={path} element={component} {...props}>
        {getApplicationRoutes(level)}
      </Route>
    )
  } else {
    // for private route
    if (privateRoute) {
      if (!isUserAuthenticate || !auth.isAuthenticate) {
        return <Route path={path} key={path} element={<Navigate replace to='/' />} />
      }
      return <Route path={path} key={path} element={component} {...props} />
    }
    // for only when user not logged in
    if (onlyWithoutAuth && isUserAuthenticate && auth.isAuthenticate) {
      return <Route path={path} key={path} element={<Navigate replace to='/' />} />
    }
    return <Route path={path} key={path} element={component} {...props} />
  }
}

export const getApplicationRoutes = (routes) => {
  return routes.map((route) =>
    createRoute(route.path, route.element, route.onlyWithoutAuth, route.private, route.props, route.level)
  )
}

export const buildRouter = (routesConfig, mapRoutes = getApplicationRoutes) => {
  const applicationRoutes = mapRoutes(routesConfig)
  const location = useLocation()
  const pathname = location.pathname
  const classes = useStyles()
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)

  const auth = useUserStore((state) => state)
  const [redirectHandled, setRedirectHandled] = useState(false)
  const [socketRibbonData, setSocketData] = useState(() => {
    const storedData = getCookie('socketRibbonData')
    return storedData ? JSON.parse(storedData) : null
  })
  const params = new URLSearchParams(window.location.search)
  const paramsData = {}
  const refferalKey = params.get('referralcode')
  const userStore = useUserStore((state) => state)
  const accessCookie = getCookie('accessToken')
  const dynamoKey = params.get('d10x_link_id')
  const showHeader = shouldShowHeader(pathname)
  const showSidebarAndFooter = shouldShowSideBarAndFooter(pathname)
  const privateRoutes = routesConfig.filter((route) => route.private).map((route) => route.path)
  params.forEach((value, key) => {
    paramsData[key] = value
  })
  const dynamoKeySend = useDynamoKey({
    onSuccess: (res) => {
      if (res.data.success) {
        toast.success('Message Sent')
        deleteCookie('dynamoKey')
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  useEffect(() => {
    if (Object.keys(paramsData).length > 0) {
      const paramsString = JSON.stringify(paramsData)
      document.cookie = `urlParams=${paramsString};`
    }
  }, [paramsData])
  useEffect(() => {
    if (dynamoKey !== null) {
      setCookie('dynamoKey', dynamoKey)
      if (userStore?.userDetails !== null && dynamoKey !== '') {
        dynamoKeySend.mutate({ d10x_link_id: dynamoKey })
      }
    }
  }, [dynamoKey, userStore])
  useEffect(() => {
    if (refferalKey !== null) {
      setreferralCode('referralcode', refferalKey)
      setReferralSocialCookie('referralcode', refferalKey, '.themoneyfactory.com')
      deleteCookie('affiliateCode')
      deleteCookie('affiliateId')
      deleteCookie('affiliatePromocode')
    }
  }, [refferalKey])

  useEffect(() => {
    if (redirectHandled) return
    setRedirectHandled(true)

    if(location.pathname==='/blogs'){
      navigate('/blog',{replace:true})
      return;
    }

    if(location.pathname==='/games/all-games'){
      navigate('/games',{replace:true})
      return;
    }

    if(location.pathname==='/games/table-games'){
      navigate('/games/casino-table-games',{replace:true})
      return;
    }

    if(location.pathname==='/games/live-dealer'){
      navigate('/games/live-dealer-casino-games',{replace:true})
      return;
    }

    if(location.pathname==='/games/hot-games'){
      navigate('/games',{replace:true})
      return;
    }

    if(location.pathname==='/home'){
      navigate('/online-social-casino-games',{replace:true})
      return;
    }

    const isVipApproved = Boolean(auth?.isVipApproved)
    const pathCookie = getCookie('path')
    const searchParams = location.search
    const fullPath = `${pathname}${searchParams}`
    const cleanedPath = pathname.endsWith('/') ? pathname.slice(0, -1) : pathname
    const paymentCheck = pathname.includes('/in-progress')
    const isForgotPassword = pathname.includes('/forgotPassword')
    const isPublicPage = ['/games', '/blog', '/faq', '/about-us', '/contact-us'].some((path) => pathname.includes(path))
    const isCasinoGamesPage = pathname.includes('/online-social-casino-games')
    const params = new URLSearchParams(location.search); 
    const vipEmail = params.get('email');
    const isPrivate = privateRoutes.some((route) => pathname.startsWith(route))
    const isVipRoute = location.pathname.includes('/vip-player-interests')

    if (isVipRoute ) {
      document.cookie = `vipRoute=${location.pathname}${location.search}; path=/;`
      if(auth?.userDetails?.email && vipEmail !== auth?.userDetails?.email){
        handleLogoutWithToast("/")
        return
      }
    }
    if (isForgotPassword) {
      localStorage.setItem('forgotPassword', params.get('token'))
    } else {
      if (isPublicPage) {
        handleLogoutWithToast(pathname)
        return
      }
      if (pathCookie) {
        if (isPrivate && localStorage.getItem('username')) {
          navigate(pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : fullPath, {
            replace: pathname.endsWith('/')
          })
        } else {
          if (paymentCheck) {
            navigate(pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : fullPath, {
              replace: pathname.endsWith('/')
            })
          } else if (isCasinoGamesPage || isPrivate) {
            if (!isVipApproved) {
              navigate('/')
            } else navigate(`/${searchParams}`)
          } else {
            navigate(pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : fullPath, {
              replace: pathname.endsWith('/')
            })
          }
        }
        return
      }

      // No cookie present
      if (isCasinoGamesPage) {
        handleLogoutAndNavigate(
          pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : `/online-social-casino-games${searchParams}`
        )
      } 
      // else {
      //   if (!isVipApproved) {
      //     navigate('/online-social-casino-games')
      //   } else handleLogoutAndNavigate(`/online-social-casino-games${searchParams}`)
      // }
    }

    const publicRoutes = ['/games', '/blog', '/faq', '/about-us', '/contact-us']
    if (publicRoutes.some((route) => pathname.includes(route))) {
      handleLogoutAndNavigate(pathname)
      return
    }

    if (!pathname.includes('/game') && !pathname.includes('/cms')) {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [location.pathname, location.search, redirectHandled, navigate, isUserAuthenticate])

  function handleLogoutAndNavigate(navigateTo = '/') {
    portalStore.closePortal()
    if (accessCookie) {
      deleteAccessTokenCookies('accessToken')
    }
    userStore.logout()
    navigate(navigateTo)
  }

  function handleLogoutWithToast(navigateTo = '/') {
    portalStore.closePortal()
    if (accessCookie) {
      deleteAccessTokenCookies('accessToken')
    }
    userStore.logout()
    navigate(navigateTo)
  }

  return (
    <div>
      <Grid className='main-page'>
        {showHeader && (
          <LayoutWrapper>
            <Header />
          </LayoutWrapper>
        )}
        <Grid className={`${classes.lobbyWrap} ${socketRibbonData?.isRibbon ? 'msg-header' : ''}`}>
          {showSidebarAndFooter && <SideBar />}
          <Suspense fallback={<Loader />}>
            <Routes>{applicationRoutes}</Routes>
          </Suspense>
        </Grid>
        {showSidebarAndFooter && (
          <LayoutWrapper>
            <Footer />
          </LayoutWrapper>
        )}
      </Grid>
    </div>
  )
}

const AppRouter = () => {
  const auth = useUserStore((state) => state)
  isUserAuthenticate = auth.isAuthenticate
  return buildRouter(appRoutes)
}

export default AppRouter
