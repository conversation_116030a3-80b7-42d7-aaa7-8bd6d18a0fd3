import { useMutation, useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import {
  updateResponsibleGame,
  getSingleGambling,
  removeResponsibleGame,
  getResponsibleGambling
} from '../utils/apiCalls'

const useUpdateResponsibleMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.UPDATE_RESPONSIBLE_GAMBLING],
    mutationFn: (data) => updateResponsibleGame(data),
    onSuccess: (data, variables) => onSuccess(data, variables),
    onError
  })
}

const useRemoveResponsibleLimitMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.REMOVE_RESPONSIBLE_GAMBLING],
    mutationFn: (data) => removeResponsibleGame(data),
    onSuccess: (data, variables) => onSuccess(data, variables),
    onError
  })
}

const getSingleGamblingQuery = ({ params, enabled, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_SINGLE_RESPONSIBLE, params.responsibleGamblingType],
    queryFn: () => {
      return getSingleGambling(params)
    },
    enabled,
    select: (data) => {
      return data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data, params)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const useGetGamblingData= ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.GET_RESPONSIBLE_GAMBLING],
    mutationFn: () => getResponsibleGambling(),
    onSuccess: (data, variables) => onSuccess(data, variables),
      onError
  })
}
export const responsibleQuery = {
  useUpdateResponsibleMutation,
  useRemoveResponsibleLimitMutation,
  getSingleGamblingQuery,
  useGetGamblingData
}
export default responsibleQuery
