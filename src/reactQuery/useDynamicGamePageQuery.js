import { useQuery } from '@tanstack/react-query'

import useDynamicGamepageStore from '../store/useDynamicGamepageStore'
import { getDynamicGamePages } from '../utils/apiCalls'

const useDynamicGamePageQuery = (params = {}) => {
  const { setDynamicGamePageData, setAllGamePages } = useDynamicGamepageStore()

  const slugKey = params?.slug || 'default'

  return useQuery({
    queryKey: ['dynamicGamePageData', slugKey],
    queryFn: () => getDynamicGamePages(params),
    select: (res) => {
      console.log('test_data', res)
      return res?.data || {}
    },
    onSuccess: (data) => {
      console.log('dynamicGamePageData', data)
      if (params?.slug) {
        setDynamicGamePageData(data)
      } else {
        setAllGamePages(data)
      }
    },
    onError: (error) => {
      console.error('error', error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: false
  })
}

export default useDynamicGamePageQuery
