import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import {
  getPackagesTypesListing
} from '../utils/apiCalls'

const getPackageListQuery = ({ params, successToggler, errorToggler, enabled }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_PACKAGES_TYPES_LISTING],
    queryFn: () => {
      return getPackagesTypesListing(params)
    },
    enabled,
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const packageQuery = {
  getPackageListQuery
}
export default packageQuery
