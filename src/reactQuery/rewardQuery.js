import { useMutation } from '@tanstack/react-query'

import {
  claimTierBonus,
  getTierBonus,
    getVipTiers
} from '../utils/apiCalls'

export const useVipTiersMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['userProfile'],
    mutationFn: () => getVipTiers(),
    onSuccess: (data, variables) => onSuccess(data, variables),
    onError
  })
}
export const usegetTierbonus = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['getTierBonus'],
    mutationFn: () => getTierBonus(),
    onSuccess: (data, variables) => onSuccess(data, variables),
    onError
  })
}
export const useclaimTierBonus = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimTierBonus'],
    mutationFn: (data) => claimTierBonus(data),
    onSuccess,
    onError
  })
}

