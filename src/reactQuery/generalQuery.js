import { useMutation, useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import {
  getStateListing,
  getCityListing,
  getBannersListing,
  updateProfile,
  updateSsn,
  getPopupListing,
  getUserBonuses,
  getAllowUser,
  getCheckSession,
  getHallOfFame,
  getSiteLogo,
  getMaintenanceModeData,
  getDynamicBlogPages,
  getDynamoPopupData,
  getPromotionThumbnail,
  claimScratchCard
} from '../utils/apiCalls'

const getStateListQuery = ({ params, successStateToggler, errorToggler, enabled }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_STATE_LISTING],
    queryFn: () => {
      return getStateListing(params)
    },
    enabled,
    select: (data) => {
      return data?.data?.data || []
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successStateToggler && successStateToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}
const getSiteLogoQuery = ({ successLogoToggler, errorLogoToggler }) => {
  return useQuery({
    queryKey: ['SITE_LOGO'],
    queryFn: () => {
      return getSiteLogo()
    },
    select: (res) => {
      return res || {}
    },
    onSuccess: (data) => {
      successLogoToggler && successLogoToggler(data)
    },
    onError: (error) => {
      errorLogoToggler && errorLogoToggler(error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: false
  })
}

const getAccessQuery = ({ params, successAccessToggler, errorAccessToggler }) => {
  return useMutation({
    mutationFn: () => {
      return getAllowUser(params)
    },
    onSuccess: (data) => {
      if (successAccessToggler) {
        successAccessToggler(data)
      }
    },
    onError: (error) => {
      if (errorAccessToggler) {
        errorAccessToggler(error)
      }
    }
  })
}
const getCityListQuery = ({ params, successToggler, errorToggler, enabled }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_CITY_LISTING, params.stateId],
    queryFn: () => {
      return getCityListing(params)
    },
    enabled,
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const usePersonalFormMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.UPDATE_PERSONAL_FORM],
    mutationFn: (data) => updateProfile(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const useSsnMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.UPDATE_SSN],
    mutationFn: (data) => updateSsn(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const getBannersListQuery = ({ successToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_BANNERS_LISTING],
    queryFn: () => {
      return getBannersListing()
    },
    select: (data) => {
      return data?.data?.data || []
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    }
  })
}

const getPopupListQuery = ({ successPopupToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_POPUP_LISTING],
    queryFn: () => {
      return getPopupListing()
    },
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successPopupToggler && successPopupToggler(data)
    }
  })
}

const getBonusListQuery = ({ successBonusToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_USER_BONUS],
    queryFn: () => {
      return getUserBonuses()
    },
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successBonusToggler && successBonusToggler(data)
    }
  })
}

const getCheckSessionQuery = () => {
  return useMutation({
    mutationKey: [KeyTypes.GET_CHECK_SESSION],
    mutationFn: (data) => getCheckSession(data)
  })
}

const getHallOfFameQuery = ({ params, successToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_HALL_OF_FAME, params?.coinType],
    queryFn: () => {
      return getHallOfFame(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    }
  })
}

const getMaintenanceModeQuery = () => {
  return useQuery({
    queryKey: [KeyTypes.MAINTENANCE_MODE],
    queryFn: () => {
      return getMaintenanceModeData()
    },
    select: (data) => data?.data,
    refetchOnMount: true,
    refetchOnWindowFocus: false
  })
}

const getBlogsQuery = ({blogSuccessToggler,params}) => {  
  return useQuery({
    queryKey: [KeyTypes.GET_DYNAMIC_BLOG_PAGES,params],
    queryFn: () => {
      return getDynamicBlogPages(params)
    },
    onSuccess: (data) => { 
      blogSuccessToggler&&blogSuccessToggler(data?.data?.dynamicBlogDetails)
     },
    onError: () => { },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: false
  })
}
const getDynamoPopupDataQuery = () =>{
  return useQuery({
    queryKey: ['DYNAMO_DATA'],
    queryFn: () => {
      return getDynamoPopupData()
    },
    select: (data) => data?.data,
    refetchOnMount: true,
    refetchOnWindowFocus: false
  })
}

const getPromotionThumbnailQuery = ({ successThumbnailToggler, errorThumbnailToggler }) => {
  return useQuery({
    queryKey: ['PROMOTION_THUMBNAIL'],
    queryFn: () => {
      return getPromotionThumbnail()
    },
    select: (res) => {
      return res || {}
    },
    onSuccess: (data) => {
      successThumbnailToggler && successThumbnailToggler(data)
    },
    onError: (error) => {
      errorThumbnailToggler && errorThumbnailToggler(error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: false
  })
}
const useClaimScratchCardMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.CLAIM_SCRATCH_CARD],
    mutationFn: (data) => claimScratchCard(data),
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}
export const generalQuery = {
  usePersonalFormMutation,
  getStateListQuery,
  getCityListQuery,
  getBannersListQuery,
  useSsnMutation,
  getPopupListQuery,
  getBonusListQuery,
  getAccessQuery,
  getCheckSessionQuery,
  getHallOfFameQuery,
  getSiteLogoQuery,
  getMaintenanceModeQuery,
  getBlogsQuery,
  getDynamoPopupDataQuery,
  getPromotionThumbnailQuery,
  useClaimScratchCardMutation
}
export default generalQuery
