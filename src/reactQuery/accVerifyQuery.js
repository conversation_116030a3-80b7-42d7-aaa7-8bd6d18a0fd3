import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import { initKYC } from '../utils/apiCalls'

const initKYCQuery = ({ errorHandler, successHandler }) => {
  return useQuery({
    queryKey: [KeyTypes.INIT_KYC],
    queryFn: () => {
      return initKYC()
    },
    select: (res) => {
      return res?.data || {}
    },
    onSuccess: (data) => {
      successHandler(data)
    },
    onError: (error) => {
      errorHandler(error?.response?.data?.errors?.[0])
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    retry: false
  })
}

export const accVerifyQuery = {
  initKYCQuery
}

export default accVerifyQuery
