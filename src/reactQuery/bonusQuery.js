import { useMutation } from '@tanstack/react-query'

import {
  claimWelcomBonus,
  getWelcomBonus,
  claimDailyBonus,
  getDailyBonus,
  claimReferralBonus,
  personalBonus,
  cliamPersonalBonus,
  getPromotionBonus,
  claimPromotionBonus,
  showCmsChanges,
  getFreeSpin,
  claimFreeSpinBonus
} from '../utils/apiCalls'

export const useGetWelcomeBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['getWelcomBonus'],
    mutationFn: () => getWelcomBonus(),
    onSuccess,
    onError
  })
}

export const useGetPromotionBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['getPromotionBonus'],
    mutationFn: () => getPromotionBonus(),
    onSuccess,
    onError
  })
}
export const useGetFreeSpinMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['getFreeSpin'],
    mutationFn: () => getFreeSpin(),
    onSuccess,
    onError
  })
}


export const useClaimWelcomBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimWelcomBonus'],
    mutationFn: (data) => claimWelcomBonus(data),
    onSuccess,
    onError
  })
}
export const useClaimFreeSpinBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimFreeSpinBonus'],
    mutationFn: (data) => claimFreeSpinBonus(data),
    onSuccess,
    onError
  })
}
export const useClaimPromotionBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimPromotionBonus'],
    mutationFn: (data) => claimPromotionBonus(data),
    onSuccess,
    onError
  })
}
// refer bonus
export const useClaimReferralBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimReferralBonus'],
    mutationFn: (data) => claimReferralBonus(data),
    onSuccess,
    onError
  })
}


//

export const useGetDailyBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['getDailyBonus'],
    mutationFn: () => getDailyBonus(),
    onSuccess,
    onError
  })
}
export const useClaimDailyBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimDailyBonus'],
    mutationFn: (data) => claimDailyBonus(data),
    onSuccess,
    onError
  })
}

export const useCmsMutation = ({ onSuccess, onError, }) => {
  return useMutation({
    mutationKey: ['showCmsChanges'],
    mutationFn: (data) => showCmsChanges(data),
    onSuccess,
    onError
  })
}
export const usePersonalBonusMutation = ({ onSuccess, onError}) => {
  return useMutation({
    mutationKey: ['personalBonus'],
    mutationFn: (data) => personalBonus(data),
    onSuccess,
    onError
  })
}

export const useClaimPersonalBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['personalBonus'],
    mutationFn: (data) => cliamPersonalBonus(data),
    onSuccess,
    onError
  })
}