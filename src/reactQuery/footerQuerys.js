import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import { useCMSStore } from '../store/store'
import { getCms, getCmsContent, getCmsPage } from '../utils/apiCalls'

const getCmsQuery = () => {
  const { setCMSData } = useCMSStore(state => state)
  return useQuery({
    queryKey: [KeyTypes.GET_CMS],
    queryFn: () => {
      return getCms()
    },
    select: (res) => {
      return res?.data?.data || {}
    },
    onSuccess: (data) => {
      setCMSData(data)
    },
    onError: (error) => {
      console.error('error', error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: false
  })
}

const getCmsPageQuery = (params) => {
  return useQuery({
    queryKey: [KeyTypes.GET_CMS_PAGE, params.cmsId],
    queryFn: () => {
      return getCmsPage(params)
    },
    select: (res) => {
      return res?.data?.data || {}
    },
    onError: (error) => {
      console.error('error', error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: false
  })
}


const getCmsContentQuery = (params) => {
  return useQuery({
    queryKey: [KeyTypes.GET_CMS_CONTENT, params.pageSlug],
    queryFn: () => {
      return getCmsContent(params)
    },
    select: (res) => {
      return res?.data?.data || {}
    },

    onError: (error) => {
      console.error('error', error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: false
  })
}



export const footerQuery = {
  getCmsQuery,
  getCmsPageQuery,
  getCmsContentQuery,
}

export default footerQuery
