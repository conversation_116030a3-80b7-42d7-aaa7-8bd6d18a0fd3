import { useMutation, useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import {
  getPostalApi,
  createPostalCode
} from '../utils/apiCalls'

const getPostalCodeQuery = ({ enabled, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_POSTAL_TYPE, enabled],
    queryFn: () => {
      return getPostalApi()
    },
    retry: false,
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const useCreatePostalMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.CREATE_POSTAL_TYPE],
    mutationFn: () => createPostalCode(),
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const generalQuery = {
  getPostalCodeQuery,
  useCreatePostalMutation,
}

export default generalQuery
