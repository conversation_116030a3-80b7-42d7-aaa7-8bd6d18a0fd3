import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import {  getReferredDetail } from '../utils/apiCalls'

const referDetailsQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_REFER_PAGE_DETAIL, params?.page, params?.limit],
    queryFn: () => {
      return getReferredDetail(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export default referDetailsQuery
