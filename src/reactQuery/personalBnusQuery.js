import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import { getBetTransactions, getPaymentStatus, getPersonalBonusList
} from '../utils/apiCalls'

const getPersonalBonusListQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_TRANSACTIONS, params?.page],
    queryFn: () => {
      return getPersonalBonusList(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const getBetTransactionsQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_BET_TRANSACTIONS, params?.page, params?.limit, params?.startDate, params?.endDate, params?.coinType],
    queryFn: () => {
      return getBetTransactions(params)
    },
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const getPaymentStatusQuery = ({ params, enabled, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_PAYMENT_STATUS, params?.providerType, params?.paymentReference],
    queryFn: () => {
      return getPaymentStatus(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    enabled : enabled,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const packageQuery = {
  getPersonalBonusListQuery,
  getBetTransactionsQuery,
  getPaymentStatusQuery
}
export default packageQuery
