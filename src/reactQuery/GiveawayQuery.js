import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import { getGiveawayHisory } from '../utils/apiCalls'

const getGiveawayHistoryQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_GIVEAWAY_HISTORY, params?.page, params?.startDate, params?.endDate],
    queryFn: () => {
      return getGiveawayHisory(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    retry: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const giveAwayQuery = {
  getGiveawayHistoryQuery
}

export default giveAwayQuery
