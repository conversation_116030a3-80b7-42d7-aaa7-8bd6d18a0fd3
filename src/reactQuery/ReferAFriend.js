import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import { getReferAFriendDetail } from '../utils/apiCalls'
const referAFriendQuery = ({ OnSuccess, onError }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_REFER_A_FRIEND_PAGE_DETAIL],
    queryFn: () => {
      return getReferAFriendDetail()
    },
    select: (data) => {
      return data?.data || {}
    },
    onSuccess: OnSuccess,
    onError: onError
  })
}

export default referAFriendQuery
