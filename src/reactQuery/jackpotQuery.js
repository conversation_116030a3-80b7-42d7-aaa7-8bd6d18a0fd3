import { useQuery, useMutation } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import { getJackpotData, optJackpot } from '../utils/apiCalls'

const getJackpotDataQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_JACKPOT_DATA],
    queryFn: () => {
      return getJackpotData(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const useJackpotOptInMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['optJackpot'],
    mutationFn: (data) => optJackpot(data),
    onSuccess,
    onError
  })
}

export const jackpotQuery = {
  getJackpotDataQuery,
  useJackpotOptInMutation
}
export default jackpotQuery
