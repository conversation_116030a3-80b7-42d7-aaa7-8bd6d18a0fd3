import { useMutation } from '@tanstack/react-query'

import {
  getPlayer,
  getAllowUser,
  forgetPassword,
  resendVerificationEmail,
  userLogin,
  userSignUp,
  verifyForgetPassword,
  userGoogleLogin,
  userLogout,
  phoneVerifyCall,
  resetPhoneOtp,
  getOtpCall,
  getIsUserNameExists,
  getEmailVerified,
  changePassword,
  getGameLink,
  uploadProfileImage,
  phoneForgetVerifyCall,
  emailOTPVerified,
  addUserName,
  createAffiliateUser,
  getReferAFriendDetail,
  getRaffleDetail,
  getRafflePromotion,
  applyPackagePromoCode,
  getPromoCode,
  depositVaultCoins,
  withdrawVaultCoins,
  verifyOtp2FA,
  getVaultDetails,
  generateOtp2FA,
  disabled2FA,
  marketingSetting,
  getDemoGameLaunch,
  postLowGCBonus,
  contactUsData,
  dynamoKeyData,
  addVipUserAnswers
} from '../utils/apiCalls'
export { default as GeneralQuery } from './generalQuery'
export { default as PackageQuery } from './packageQuery'
export { default as ResponsibleQuery } from './responsibleQuery'
export { default as CasinoQuery } from './casinoQuery'
export { default as PaymentQuery } from './paymentQuery'
export { default as AccVerifyQuery } from './accVerifyQuery'
export { default as TransactionsQuery } from './transactionsQuery'
export { default as PersonalBnusQuery } from './personalBnusQuery'
export { default as AmoeQuery } from './AmoeQuery'
export { default as FooterQuerys } from './footerQuerys'

export const errorHandler = (err) => {
  if (err?.response?.data?.errors.length > 0) {
    const { errors } = err.response.data
    errors.forEach((error) => {
      if (error?.description) {
        // toast.error(error?.description)
      }
    })
  }
}

export const useUserNameExistMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['userNameExists'],
    mutationFn: (data) => getIsUserNameExists(data),
    onSuccess,
    onError
  })
}

export const usePromoCodeMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['promoCodeExists'],
    mutationFn: (data) => getPromoCode(data),
    onSuccess,
    onError
  })
}

export const usePackagePromoCodeMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['packagePromoCode'],
    mutationFn: (data) => applyPackagePromoCode(data),
    onSuccess,
    onError
  })
}

export const useVaultDetailsMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['vaultDetails'],
    mutationFn: (data) => getVaultDetails(data),
    onSuccess,
    onError
  })
}

export const useGenerateOtp2FAMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['generateOtp2FA'],
    mutationFn: (data) => generateOtp2FA(data),
    onSuccess,
    onError
  })
}

export const useDepositVaultCoinsMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['depositVaultCoins'],
    mutationFn: (data) => depositVaultCoins(data),
    onSuccess,
    onError
  })
}

export const useWithdrawVaultCoinsMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['withdrawVaultCoins'],
    mutationFn: (data) => withdrawVaultCoins(data),
    onSuccess,
    onError
  })
}

export const useVerifyOtp2FAMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['otp2FA'],
    mutationFn: (data) => verifyOtp2FA(data),
    onSuccess,
    onError
  })
}

export const useDisabled2FAMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['deactivate2FA'],
    mutationFn: (data) => disabled2FA(data),
    onSuccess,
    onError
  })
}

export const useSignUpMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['signUp'],
    mutationFn: (data) => userSignUp(data),
    onSuccess,
    onError
  })
}

export const useUserNameMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['addUserName'],
    mutationFn: (data) => addUserName(data),
    onSuccess,
    onError
  })
}

export const useLoginMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['logIn'],
    mutationFn: (data) => userLogin(data),
    onSuccess,
    onError
  })
}

export const useAffiliateMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['addAffiliate'],
    mutationFn: (data) => createAffiliateUser(data),
    onSuccess,
    onError
  })
}

export const useProfileImageMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['uploadImage'],
    mutationFn: (data) => uploadProfileImage(data),
    onSuccess,
    onError
  })
}
export const useLogOutMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['logout'],
    mutationFn: () => userLogout(),
    onSuccess,
    onError
  })
}

export const useForgetPasswordMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['forgetPassword'],
    mutationFn: (data) => forgetPassword(data),
    onSuccess,
    onError
  })
}

export const useGoogleLoginMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['logIn'],
    mutationFn: (data) => userGoogleLogin(data),
    onSuccess,
    onError
  })
}

export const useVerifyEmailMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['verifyEmail'],
    mutationFn: (data) => getEmailVerified(data),
    onSuccess,
    onError
  })
}

export const useVerifyEmailOTPMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['verifyEmailOTP'],
    mutationFn: (data) => emailOTPVerified(data),
    onSuccess,
    onError
  })
}

export const usePhoneVerifyMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['phone'],
    mutationFn: (data) => phoneVerifyCall(data),
    onSuccess,
    onError
  })
}

export const usePhoneForgetVerifyMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['phone'],
    mutationFn: (data) => phoneForgetVerifyCall(data),
    onSuccess,
    onError
  })
}

export const getResendfogetOtpMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['resend otp'],
    mutationFn: (data) => forgetPassword(data),
    onSuccess,
    onError
  })
}

export const getResendOtpMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['resend otp'],
    mutationFn: (data) => resetPhoneOtp(data),
    onSuccess,
    onError
  })
}
export const getOtpMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['get otp'],
    mutationFn: (data) => getOtpCall(data),
    onSuccess,
    onError
  })
}
export const useVerifyPasswordMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['verifyForgotPassword'],
    mutationFn: (data) => verifyForgetPassword(data),
    onSuccess,
    onError: (error) => onError(error)
  })
}

export const useGetProfileMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['userProfile'],
    mutationFn: () => getPlayer(),
    onSuccess: (data, variables) => onSuccess(data, variables),
    onError
  })
}

export const useAllowedUserMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['userAllow'],
    mutationFn: () => getAllowUser(),
    onSuccess: (data, variables) => onSuccess(data, variables),
    onError
  })
}

export const useResendEmailMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['resendEmail'],
    mutationFn: (data) => resendVerificationEmail(data),
    onSuccess,
    onError: (error) => onError(error)
  })
}

export const useResendEmailOTPMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['resendEmailOTP'],
    mutationFn: (data) => resendVerificationEmail(data),
    onSuccess,
    onError: (error) => onError(error)
  })
}

export const useChangePasswordMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['changePassword'],
    mutationFn: (data) => changePassword(data),
    onSuccess,
    onError
  })
}

export const useReferFriendMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['referAFriend'],
    mutationFn: (data) => getReferAFriendDetail(data),
    onSuccess,
    onError
  })
}

export const useGetGameLink = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['getGameLink'],
    mutationFn: (data) => getGameLink(data),
    onSuccess,
    onError
  })
}

export const useRaffleDetailMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['raffleDetail'],
    mutationFn: (data) => getRaffleDetail(data),
    onSuccess,
    onError
  })
}
export const useRafflePromotionMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['raffleDetail'],
    mutationFn: (data) => getRafflePromotion(data),
    onSuccess,
    onError
  })
}

export const useMarketingMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['marketingSetting'],
    mutationFn: (data) => marketingSetting(data),
    onSuccess,
    onError
  })
}

export const useGetDemoGameLaunch = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['getDemoGameLaunch'],
    mutationFn: (data) => getDemoGameLaunch(data),
    onSuccess,
    onError
  })
}

export const usePostLowGcBonus = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['postLowGCBonus'],
    mutationFn: (data) => postLowGCBonus(data),
    onSuccess,
    onError
  })
}

export const useContactUsInfo = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['contactUsData'],
    mutationFn: (data) => contactUsData(data),
    onSuccess,
    onError
  })
}

export const useDynamoKey = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['dynamoKeyData'],
    mutationFn: (data) => dynamoKeyData(data),
    onSuccess,
    onError
  })
}


export const useVipUserAnswerMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['addVipAnswers'],
    mutationFn: (data) => addVipUserAnswers(data),
    onSuccess,
    onError
  })
}
