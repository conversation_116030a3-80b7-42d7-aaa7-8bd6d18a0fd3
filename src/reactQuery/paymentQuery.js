import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import { usePaymentProcessStore } from '../store/store'
import {
  addBankAccount,
  addPaymentErrors,
  cancelRedemption,
  confirmRedemption,
  deleteBankAccount,
  getBankDetails,
  getPayByBank,
  getPostalCode,
  initPay, paysafePay, processPayment, processWithdraw, updateBankData, deletePaySafe, deletePromocode,
  deleteDeposit,
  skrillPayment,
  pbbPayment,
  savedPbbPayment,
  paymentProcess,
  deleteSavedBank,
  deleteSavedCard,
  savedCardInitDepsit,
  savedTrustlyPayment,
  initializeTrustlyPayment,
  getRedeemTrustly,
  getUserPreferredPayments,
  deleteUserPreferredPayments
} from '../utils/apiCalls'

const initPayMutation = ({ successToggler, errorToggler, loadingToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.INIT_PAY],
    mutationFn: (data) => initPay(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
    onSettled: () => {
      loadingToggler && loadingToggler(false)
    }
  })
}

const setCancelDeposit = usePaymentProcessStore.getState().setCancelDeposit
const cancelDepositMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.CANCEL_INIT_PAY],
    mutationFn: (data) => {
      setCancelDeposit(true)
      return deleteDeposit(data)
    },
    onSuccess,
    onError
  })
}

const skrillPaymentMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.SKRILL_PAYMENT],
    mutationFn: (data) => skrillPayment(data),
    onSuccess,
    onError
  })
}

const pbbPaymentMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.PBB_PAYMENT],
    mutationFn: (data) => pbbPayment(data),
    onSuccess,
    onError
  })
}

const savedPbbPaymentMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.PBB_PAYMENT],
    mutationFn: (data) => savedPbbPayment(data),
    onSuccess,
    onError
  })
}

const deletePbbSavedAccountMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.PBB_PAYMENT],
    mutationFn: (data) => deleteSavedBank(data),
    onSuccess,
    onError
  })
}

const deleteSavedCardMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.PBB_PAYMENT],
    mutationFn: (data) => deleteSavedCard(data),
    onSuccess,
    onError
  })
}

const paymentProcessMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.PROCESS_PAYMENT],
    mutationFn: (data) => paymentProcess(data),
    onSuccess: onSuccess,
    onError
  })
}

const savedCardInitDepositMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.INIT_SAVEDCARD_DEPOSIT],
    mutationFn: (data) => savedCardInitDepsit(data),
    onSuccess,
    onError
  })
}

const initPaymentPaysafeMutation = ({ successToggler, errorToggler, loadingToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.PAYMENT_PAY],
    mutationFn: (data) => paysafePay(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error?.response?.data?.errors?.[0])
    },
    onSettled: () => {
      loadingToggler && loadingToggler(false);
    },
  })
}

const initFiatPayMutation = ({ successFiatToggler, errorFiatToggler, loadingToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.INIT_PAY],
    mutationFn: (data) => initPay(data),
    onSuccess: (data) => {
      successFiatToggler && successFiatToggler(data?.data)
    },
    onError: (error) => {
      errorFiatToggler && errorFiatToggler(error?.response?.data?.errors?.[0])
    },
    onSettled: () => {
      loadingToggler && loadingToggler(false);
    },
  })
}


const initPayRedeemMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.INIT_PAY],
    mutationFn: (data) => processWithdraw(data),
    onSuccess,
    onError
  })
}

const processPaymentMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.INIT_PAY],
    mutationFn: (data) => data?.packageId ? processPayment(data) : processWithdraw(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data?.success)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const useBankingFormMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.UPDATE_BANKING_FORM],
    mutationFn: (data) => updateBankData(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data?.bankDetails)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}
const useCancelRedeemMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: ["CANCEL_REDEMPTION"],
    mutationFn: (data) => cancelRedemption(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error?.response?.data?.errors?.[0])
    },
  })
}

const deleteBankAccountMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.DELETE_BANK_ACCOUNT],
    mutationFn: (data) => deleteBankAccount(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
  })
}

const useConfirmRedeemMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: ["CONFIRM_REDEMPTION"],
    mutationFn: (data) => confirmRedemption(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error?.response?.data?.errors?.[0])
    },
  })
}
const getBankDetailQuery = ({ params, successToggler, errorToggler, enabled }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_BANKING_FORM],
    queryFn: () => {
      return getBankDetails(params)
    },
    enabled,
    select: (data) => {
      return data?.data?.bankDetails || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data?.data?.bankDetails)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const getPayByBankQuery = ({ successToggler, errorToggler }) => {
  return useMutation ({
    mutationKey : [KeyTypes.PAY_BY_BANK],
    mutationFn : () => getPayByBank(),
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
  })
}

export const getTrustlyQuery = ({ successToggler, errorToggler }) => {
  return useMutation ({
    mutationKey : [KeyTypes.TRUSTLY_REDEEM],
    mutationFn : () => getRedeemTrustly(),
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
  })
}

export const getPostalCodeMutation = ({ successToggler, errorToggler }) => {
  return useMutation ({
    mutationKey : [KeyTypes.GET_POSTAL_CODE],
    mutationFn : () => getPostalCode(),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
  })
}



const addBankAccountMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.ADD_BANK_ACCOUNT],
    mutationFn: () => addBankAccount(),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
  })
}

const addPaymentErrorsMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.ADD_PAYMENT_ERROR],
    mutationFn: (data) => {
      addPaymentErrors(data)
    },
    onSuccess,
    onError
  })
}

const deletePaySafeMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.DELETE_PAY_SAFE],
    mutationFn: () => {
      deletePaySafe()
    },
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const deleteAppliedPromocodeMutation = ({ successToggler, errorToggler }) => {
  return useMutation({
    mutationKey: [KeyTypes.DELETEPROMOCODE],
    mutationFn: (data) => {
      deletePromocode(data)
    },
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const savedTrustlyPaymentMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.PBB_PAYMENT],
    mutationFn: (data) => savedTrustlyPayment(data),
    onSuccess,
    onError
  })
}

const initializeTrustlyPaymentMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.PBB_PAYMENT],
    mutationFn: (data) => initializeTrustlyPayment(data),
    onSuccess,
    onError
  })
}

const getPrefferedPaymentQuery = ({ successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_USER_PREFERRED_PAYMENTS],
    queryFn: () => {
      return getUserPreferredPayments()
    },
    select: (data) => {
      return data?.data?.data || {}
    },
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    retry: false
  })
}

const deletePreferredPaymentMutation = ({ onSuccess, onError }) => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: [KeyTypes.DELETE_USER_PREFERRED_PAYMENTS],
    mutationFn: (data) => deleteUserPreferredPayments(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries(KeyTypes.GET_USER_PREFERRED_PAYMENTS)
      onSuccess && onSuccess(data)
    },
    onError: (data) => {
      onError && onError(data)
    }
  })
}

export const paymentQuery = {
  initPayMutation,
  cancelDepositMutation,
  initFiatPayMutation,
  initPayRedeemMutation,
  processPaymentMutation,
  useBankingFormMutation,
  getBankDetailQuery,
  initPaymentPaysafeMutation,
  useCancelRedeemMutation,
  useConfirmRedeemMutation,
  getPayByBankQuery,
  getTrustlyQuery,
  addBankAccountMutation,
  deleteBankAccountMutation,
  getPostalCodeMutation,
  addPaymentErrorsMutation,
  deletePaySafeMutation,
  deleteAppliedPromocodeMutation,
  skrillPaymentMutation,
  pbbPaymentMutation,
  savedPbbPaymentMutation,
  deletePbbSavedAccountMutation,
  paymentProcessMutation,
  deleteSavedCardMutation,
  savedCardInitDepositMutation,
  savedTrustlyPaymentMutation,
  initializeTrustlyPaymentMutation,
  getPrefferedPaymentQuery,
  deletePreferredPaymentMutation
}
export default paymentQuery
